{"id": "f17dc301-682d-45ed-a2d1-adc34a53317d", "prevId": "095ad9bd-2357-4670-b57c-dfed00d342c4", "version": "7", "dialect": "postgresql", "tables": {"public.candles": {"name": "candles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "interval": {"name": "interval", "type": "<PERSON><PERSON><PERSON>(16)", "primaryKey": false, "notNull": true}, "market_type": {"name": "market_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "open_time": {"name": "open_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "open": {"name": "open", "type": "numeric(18, 8)", "primaryKey": false, "notNull": true}, "high": {"name": "high", "type": "numeric(18, 8)", "primaryKey": false, "notNull": true}, "low": {"name": "low", "type": "numeric(18, 8)", "primaryKey": false, "notNull": true}, "close": {"name": "close", "type": "numeric(18, 8)", "primaryKey": false, "notNull": true}, "volume": {"name": "volume", "type": "numeric(18, 8)", "primaryKey": false, "notNull": true}}, "indexes": {"symbol_interval_market_time_idx": {"name": "symbol_interval_market_time_idx", "columns": [{"expression": "symbol", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "interval", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "market_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "open_time", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"candles_unq": {"name": "candles_unq", "nullsNotDistinct": false, "columns": ["symbol", "interval", "market_type", "open_time"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tickers_24h": {"name": "tickers_24h", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "symbol": {"name": "symbol", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "market_type": {"name": "market_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "last_price": {"name": "last_price", "type": "numeric(18, 8)", "primaryKey": false, "notNull": false}, "price_change": {"name": "price_change", "type": "numeric(18, 8)", "primaryKey": false, "notNull": false}, "price_change_percent": {"name": "price_change_percent", "type": "numeric(18, 4)", "primaryKey": false, "notNull": false}, "high_price": {"name": "high_price", "type": "numeric(18, 8)", "primaryKey": false, "notNull": false}, "low_price": {"name": "low_price", "type": "numeric(18, 8)", "primaryKey": false, "notNull": false}, "volume": {"name": "volume", "type": "numeric(24, 8)", "primaryKey": false, "notNull": false}, "quote_volume": {"name": "quote_volume", "type": "numeric(24, 8)", "primaryKey": false, "notNull": false}, "open_time": {"name": "open_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "close_time": {"name": "close_time", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": false}, "last_updated": {"name": "last_updated", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"market_type_idx": {"name": "market_type_idx", "columns": [{"expression": "market_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quote_volume_idx": {"name": "quote_volume_idx", "columns": [{"expression": "quote_volume", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "price_change_percent_idx": {"name": "price_change_percent_idx", "columns": [{"expression": "price_change_percent", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tickers_24h_unq": {"name": "tickers_24h_unq", "nullsNotDistinct": false, "columns": ["symbol", "market_type"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}