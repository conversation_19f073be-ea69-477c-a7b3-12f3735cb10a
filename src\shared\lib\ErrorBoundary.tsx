'use client';

import Re<PERSON>, { Component, ErrorInfo, ReactNode } from 'react';
import { But<PERSON> } from '@/shared/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/ui/card';
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, Bug, Home, ExternalLink } from 'lucide-react';

// --- Types --- TODO: Consider moving to a .types.ts file if they grow
interface ErrorMonitoringService {
  captureException: (error: Error, errorInfo?: ErrorInfo, errorId?: string) => void;
  // Add other methods if needed, e.g., setUserContext, addBreadcrumb
}

// --- Placeholder for your actual error monitoring service integration --- TODO: Implement this!
// Example (replace with your actual service, e.g., Sentry, LogRocket, etc.)
const errorMonitoring: ErrorMonitoringService | null = (() => {
  if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
    // Example: Sentry integration (ensure Sentry SDK is initialized elsewhere)
    // if ((window as any).Sentry) {
    //   return {
    //     captureException: (error, errorInfo, errorId) => {
    //       (window as any).Sentry.withScope((scope: any) => {
    //         if (errorInfo) {
    //           scope.setExtras(errorInfo as Record<string, any>);
    //         }
    //         if (errorId) {
    //           scope.setTag('error_id', errorId);
    //         }
    //         (window as any).Sentry.captureException(error);
    //       });
    //     },
    //   };
    // }
    console.warn('ErrorMonitoringService: Production environment detected but no service is configured/initialized. Errors will only be logged to console.');
  }
  // In development or if no service, return a mock or null
  return {
    captureException: (error, errorInfo, errorId) => {
      console.info('[MockErrorMonitoring] captureException called:', { error, errorInfo, errorId });
    }
  };
})();

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  showDetailsInProd?: boolean;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId: string;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    errorId: 'N/A',
  };

  public static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: Math.random().toString(36).substring(2, 11),
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', {
      error,
      errorInfo,
      errorId: this.state.errorId,
    });
    
    this.setState({
      error,
      errorInfo,
    });

    // Log to external error monitoring service
    errorMonitoring?.captureException(error, errorInfo, this.state.errorId);

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo, this.state.errorId);
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorId: 'N/A',
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  public render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const showErrorDetails = process.env.NODE_ENV === 'development' || this.props.showDetailsInProd;

      return (
        <div className="min-h-screen w-full flex items-center justify-center p-4 bg-background text-foreground">
          <Card className="w-full max-w-2xl shadow-2xl">
            <CardHeader className="text-center border-b border-border">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10 mb-4">
                <AlertTriangle className="h-8 w-8 text-destructive" />
              </div>
              <CardTitle className="text-3xl font-bold">Что-то пошло не так</CardTitle>
              <CardDescription className="text-muted-foreground mt-2">
                Произошла неожиданная ошибка. Мы уже уведомлены (ID: {this.state.errorId}).
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              {showErrorDetails && this.state.error && (
                <details className="p-3 bg-muted/50 rounded-lg border border-border">
                  <summary className="cursor-pointer font-medium text-sm mb-2 text-destructive">
                    Техническая информация (для отладки)
                  </summary>
                  <div className="space-y-2 text-xs">
                    <p className="font-mono text-destructive-foreground bg-destructive/20 p-2 rounded">
                      <strong>Сообщение:</strong> {this.state.error.message}
                    </p>
                    {this.state.error.stack && (
                      <div>
                        <p className="font-medium mt-2">Stack trace:</p>
                        <pre className="whitespace-pre-wrap break-all p-2 bg-background rounded mt-1 border border-border overflow-auto max-h-40">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                    {this.state.errorInfo?.componentStack && (
                      <div>
                        <p className="font-medium mt-2">Component stack:</p>
                        <pre className="whitespace-pre-wrap break-all p-2 bg-background rounded mt-1 border border-border overflow-auto max-h-40">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}

              <div className="text-center text-muted-foreground text-sm">
                <p>Вы можете попробовать следующие действия:</p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                <Button onClick={this.handleRetry} variant="default" className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" /> Попробовать снова
                </Button>
                <Button onClick={this.handleReload} variant="outline" className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" /> Перезагрузить
                </Button>
                <Button onClick={this.handleGoHome} variant="outline" className="w-full">
                  <Home className="h-4 w-4 mr-2" /> На главную
                </Button>
              </div>

              {process.env.NODE_ENV === 'production' && !this.props.showDetailsInProd && (
                <p className="text-center text-xs text-muted-foreground pt-4">
                  Если проблема не исчезнет, пожалуйста, свяжитесь со службой поддержки, указав ID ошибки: 
                  <strong className="text-foreground">{this.state.errorId}</strong>.
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// HOC for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  boundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...boundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name || 'Component'})`;
  return WrappedComponent;
}

// Hook for error reporting in functional components
export function useErrorHandler() {
  return React.useCallback((error: Error, errorInfo?: ErrorInfo, context?: Record<string, any>) => {
    const errorId = Math.random().toString(36).substring(2, 11);
    console.error('Error caught by useErrorHandler:', {
      error,
      errorInfo,
      context,
      errorId
    });
    errorMonitoring?.captureException(error, { ...(errorInfo || {}), ...context }, errorId);
    // Here you might also want to show a global notification to the user
    // For example, using a toast library: toast.error(`An error occurred (ID: ${errorId}). Please try again.`);
  }, []);
}