import { pino } from "pino";
import { useEffect, useState } from "react";

// Configure logger
const logger = pino({ 
  name: 'websocket-manager',
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  enabled: process.env.NODE_ENV !== 'test',
});

// WebSocket URL configuration
const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3000/ws';

// WebSocket connection states
export enum WebSocketState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
}

// WebSocket message types
export enum MessageType {
  SUBSCRIBE = 'subscribe',
  UNSUBSCRIBE = 'unsubscribe',
  SUBSCRIBED = 'subscribed',
  UNSUBSCRIBED = 'unsubscribed',
  UPDATE = 'update',
  ERROR = 'error',
}

// Subscription types
export enum SubscriptionType {
  KLINE = 'kline',
}

// Message parameters interfaces
export interface KlineParams {
  symbol: string;
  interval: string;
  marketType: string;
}

// Message for subscribing to klines
export interface SubscribeMessage<T> {
  action: 'subscribe' | 'unsubscribe';
  type: SubscriptionType;
  params: T;
}

// Messages from server
export interface ServerMessage {
  type: MessageType;
  channel?: string;
  message?: string;
  data?: unknown;
}

// Subscription callback type
export type SubscriptionCallback<T> = (data: T) => void;

// Subscription handler interface
interface SubscriptionHandler<T> {
  callback: SubscriptionCallback<T>;
  channel: string;
}

/**
 * Singleton class to manage WebSocket connection and subscriptions
 */
export class WebSocketManager {
  private static instance: WebSocketManager;
  private ws: WebSocket | null = null;
  private state: WebSocketState = WebSocketState.DISCONNECTED;
  private reconnectAttempts: number = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private reconnectInterval: number = 1000; // Start with 1s, will increase with backoff
  private maxReconnectInterval: number = 30000; // Max 30s interval between reconnect attempts
  private subscriptions: Map<string, SubscriptionHandler<unknown>> = new Map();
  private pendingMessages: Array<string> = []; // Messages to send when connection is established
  private autoReconnect: boolean = true;

  // Status callback to notify UI of connection changes
  private statusCallback?: (status: WebSocketState) => void;

  private constructor() {
    // Private constructor to ensure singleton
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  /**
   * Set a callback to be notified about connection status changes
   */
  public setStatusCallback(callback: (status: WebSocketState) => void): void {
    this.statusCallback = callback;
    // Immediately call with current status if connection exists
    if (this.statusCallback) {
      this.statusCallback(this.state);
    }
  }

  /**
   * Connect to the WebSocket server
   */
  public connect(): void {
    if (this.ws && (this.state === WebSocketState.CONNECTED || this.state === WebSocketState.CONNECTING)) {
      logger.debug('WebSocket already connected or connecting');
      return;
    }

    this.updateState(WebSocketState.CONNECTING);
    
    try {
      this.ws = new WebSocket(`${WS_BASE_URL}/market`);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      
      logger.debug('WebSocket connection initialized');
    } catch (error) {
      logger.error(error, 'Failed to initialize WebSocket connection');
      this.scheduleReconnect();
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    this.autoReconnect = false;
    this.clearReconnectTimer();
    
    if (this.ws) {
      this.ws.onopen = null;
      this.ws.onmessage = null;
      this.ws.onclose = null;
      this.ws.onerror = null;
      
      if (this.state === WebSocketState.CONNECTED) {
        this.ws.close(1000, 'Client initiated disconnect');
      }
      
      this.ws = null;
    }
    
    this.updateState(WebSocketState.DISCONNECTED);
    logger.debug('WebSocket disconnected by client');
  }

  /**
   * Subscribe to kline updates
   */
  public subscribeKline(
    symbol: string,
    interval: string,
    marketType: string,
    callback: SubscriptionCallback<unknown>
  ): void {
    const channel = this.getKlineChannel(symbol, interval, marketType);
    
    // Store the subscription
    this.subscriptions.set(channel, { callback, channel });
    
    // Prepare subscription message
    const message: SubscribeMessage<KlineParams> = {
      action: 'subscribe',
      type: SubscriptionType.KLINE,
      params: { symbol, interval, marketType }
    };
    
    // Send or queue the message
    this.sendOrQueueMessage(message);
    logger.debug({ channel }, 'Subscribing to kline updates');
  }

  /**
   * Unsubscribe from kline updates
   */
  public unsubscribeKline(
    symbol: string,
    interval: string,
    marketType: string
  ): void {
    const channel = this.getKlineChannel(symbol, interval, marketType);
    
    // Prepare unsubscription message
    const message: SubscribeMessage<KlineParams> = {
      action: 'unsubscribe',
      type: SubscriptionType.KLINE,
      params: { symbol, interval, marketType }
    };
    
    // Send or queue the message
    this.sendOrQueueMessage(message);
    
    // Remove the subscription
    this.subscriptions.delete(channel);
    logger.debug({ channel }, 'Unsubscribing from kline updates');
  }

  /**
   * Send a message or queue it if not connected
   */
  private sendOrQueueMessage(message: unknown): void {
    const serialized = JSON.stringify(message);
    
    if (this.state === WebSocketState.CONNECTED && this.ws) {
      this.ws.send(serialized);
      logger.debug({ message }, 'Message sent');
    } else {
      // Queue the message to send when connected
      this.pendingMessages.push(serialized);
      logger.debug({ message }, 'Message queued (not connected)');
      
      // Ensure connection is established
      if (this.state === WebSocketState.DISCONNECTED) {
        this.connect();
      }
    }
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(): void {
    this.updateState(WebSocketState.CONNECTED);
    this.reconnectAttempts = 0;
    this.reconnectInterval = 1000; // Reset reconnect interval
    this.autoReconnect = true;
    
    logger.info('WebSocket connected');
    
    // Send any queued messages
    this.sendQueuedMessages();
    
    // Resubscribe to all active subscriptions
    this.resubscribeAll();
  }

  /**
   * Handle WebSocket message event
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data) as ServerMessage;
      
      if (message.type === MessageType.UPDATE && message.channel && message.data) {
        // Handle update message - call the appropriate callback
        const handler = this.subscriptions.get(message.channel);
        if (handler && handler.callback) {
          handler.callback(message.data);
        }
      } else if (message.type === MessageType.ERROR) {
        // Handle error message
        logger.error({ message }, 'Received error from server');
      } else {
        // Handle other message types (subscribed, unsubscribed, etc.)
        logger.debug({ message }, 'Received message from server');
      }
    } catch (error) {
      logger.error({ error, data: event.data }, 'Failed to parse message');
    }
  }

  /**
   * Handle WebSocket close event
   */
  private handleClose(event: CloseEvent): void {
    const wasConnected = this.state === WebSocketState.CONNECTED;
    this.updateState(WebSocketState.DISCONNECTED);
    
    // Log appropriate message based on close code
    if (event.code === 1000) {
      logger.info('WebSocket closed normally');
    } else {
      logger.warn({ code: event.code, reason: event.reason }, 'WebSocket closed unexpectedly');
    }
    
    // Attempt to reconnect if it wasn't a normal closure
    if (wasConnected && this.autoReconnect && event.code !== 1000) {
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket error event
   */
  private handleError(event: Event): void {
    logger.error(event, 'WebSocket error occurred');
    
    // State will be updated by the close handler which is typically called after an error
  }

  /**
   * Schedule a reconnection attempt with exponential backoff
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer !== null || !this.autoReconnect) {
      return;
    }
    
    this.updateState(WebSocketState.RECONNECTING);
    this.reconnectAttempts++;
    
    // Calculate backoff time (cap at maxReconnectInterval)
    const delay = Math.min(
      this.reconnectInterval * Math.pow(1.5, Math.min(this.reconnectAttempts - 1, 10)),
      this.maxReconnectInterval
    );
    
    logger.debug({ attempt: this.reconnectAttempts, delay }, 'Scheduling reconnection');
    
    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null;
      this.connect();
    }, delay);
  }

  /**
   * Clear the reconnect timer
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer !== null) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Send all queued messages
   */
  private sendQueuedMessages(): void {
    if (this.ws && this.state === WebSocketState.CONNECTED) {
      while (this.pendingMessages.length > 0) {
        const message = this.pendingMessages.shift();
        if (message) {
          this.ws.send(message);
          logger.debug('Sent queued message');
        }
      }
    }
  }

  /**
   * Resubscribe to all active subscriptions
   */
  private resubscribeAll(): void {
    if (this.subscriptions.size === 0) {
      return;
    }
    
    logger.debug({ count: this.subscriptions.size }, 'Resubscribing to all active channels');
    
    // Extract channel from subscription keys to parse parameters
    for (const [channel] of this.subscriptions.entries()) {
      // Parse channel name to extract parameters
      const parts = channel.split(':');
      if (parts.length === 5 && parts[1] === 'kline') {
        const type = parts[1];
        const symbol = parts[2];
        const interval = parts[3];
        const marketType = parts[4];
        
        const message: SubscribeMessage<KlineParams> = {
          action: 'subscribe',
          type: SubscriptionType.KLINE,
          params: { symbol, interval, marketType }
        };
        
        this.sendOrQueueMessage(message);
      }
    }
  }

  /**
   * Update the connection state and notify listeners
   */
  private updateState(newState: WebSocketState): void {
    if (this.state !== newState) {
      this.state = newState;
      
      // Notify status callback if present
      if (this.statusCallback) {
        this.statusCallback(newState);
      }
    }
  }

  /**
   * Generate a kline channel name from parameters
   */
  private getKlineChannel(symbol: string, interval: string, marketType: string): string {
    return `pubsub:kline:${symbol}:${interval}:${marketType}`;
  }

  /**
   * Get the current connection state
   */
  public getState(): WebSocketState {
    return this.state;
  }

  /**
   * Get the number of active subscriptions
   */
  public getSubscriptionCount(): number {
    return this.subscriptions.size;
  }

  /**
   * Check if the client is connected
   */
  public isConnected(): boolean {
    return this.state === WebSocketState.CONNECTED;
  }

  /**
   * Enable auto-reconnect behavior
   */
  public enableAutoReconnect(): void {
    this.autoReconnect = true;
    if (this.state === WebSocketState.DISCONNECTED) {
      this.connect();
    }
  }

  /**
   * Disable auto-reconnect behavior
   */
  public disableAutoReconnect(): void {
    this.autoReconnect = false;
  }
}

// Export singleton instance
export const wsManager = WebSocketManager.getInstance();

/**
 * Hook to ensure WebSocket is connected when component is mounted
 */
export function useWebSocketAutoConnect(): WebSocketState {
  const [connectionState, setConnectionState] = useState<WebSocketState>(
    wsManager.getState()
  );

  useEffect(() => {
    // Set up status callback to update our state
    wsManager.setStatusCallback(setConnectionState);
    
    // Connect if not already connected
    if (!wsManager.isConnected()) {
      wsManager.enableAutoReconnect();
      wsManager.connect();
    }
    
    // Cleanup function
    return () => {
      // Remove our status callback when component unmounts
      wsManager.setStatusCallback(() => {});
      // We don't disconnect on unmount as other components may be using the connection
    };
  }, []);

  return connectionState;
} 