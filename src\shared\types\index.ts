/**
 * Shared type definitions for MetaCharts application
 * This file contains additional types not covered by schemas
 */

import type { MarketType, Kline, Ticker } from '../schemas/market.schema';

// Re-export types from schemas to avoid conflicts
export type { MarketType, Kline, Ticker, FullTicker } from '../schemas/market.schema';
export type { Ticker as BaseTicker } from '../schemas/market.schema';

// WebSocket Types
export interface SocketStream {
  socket: any; // Raw WebSocket connection
  send: (data: any) => void;
  close: () => void;
  on: (event: string, handler: Function) => void;
  off: (event: string, handler: Function) => void;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

export interface KlineUpdateMessage extends WebSocketMessage {
  type: 'kline_update';
  data: Kline;
}

export interface TickerUpdateMessage extends WebSocketMessage {
  type: 'ticker_update';
  data: Ticker;
}

// Subscription Types
export interface Subscription {
  id: string;
  symbol: string;
  marketType: MarketType;
  interval?: string;
  type: 'kline' | 'ticker' | 'depth';
  active: boolean;
  createdAt: number;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
}

// Database Types
export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
}

// Redis Types
export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
}

// Exchange Types
export interface ExchangeConfig {
  name: string;
  apiKey?: string;
  apiSecret?: string;
  sandbox?: boolean;
  rateLimit?: number;
}

// Utility Types
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Re-export commonly used types
export type { MarketType as Market };
export type { Kline as Candlestick };
