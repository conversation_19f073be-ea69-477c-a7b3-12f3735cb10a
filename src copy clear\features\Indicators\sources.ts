import type { KlineData } from '@/shared/index';

/**
 * Standard data sources available for indicator calculations.
 */
export const INDICATOR_SOURCES = [
  'open',
  'high',
  'low',
  'close',
  'hl2',    // (high + low) / 2
  'hlc3',   // (high + low + close) / 3
  'ohlc4',  // (open + high + low + close) / 4
  'volume'
] as const; // Use 'as const' for strict typing

export type IndicatorSource = typeof INDICATOR_SOURCES[number];

/**
 * Calculates the value for a given data point based on the selected source.
 *
 * @param dataPoint - The Kline data point (OHLCV).
 * @param source - The selected indicator source.
 * @returns The calculated value for the source, or NaN if source is invalid or data is missing.
 */
export function getIndicatorSourceValue(dataPoint: KlineData | null | undefined, source: IndicatorSource): number {
  if (!dataPoint) {
    return NaN;
  }

  switch (source) {
    case 'open':
      return dataPoint.open;
    case 'high':
      return dataPoint.high;
    case 'low':
      return dataPoint.low;
    case 'close':
      return dataPoint.close;
    case 'hl2':
      return (dataPoint.high + dataPoint.low) / 2;
    case 'hlc3':
      return (dataPoint.high + dataPoint.low + dataPoint.close) / 3;
    case 'ohlc4':
      return (dataPoint.open + dataPoint.high + dataPoint.low + dataPoint.close) / 4;
    case 'volume':
      // Ensure volume exists and is a number, return NaN otherwise
      return typeof dataPoint.volume === 'number' ? dataPoint.volume : NaN;
    default:
      // Should not happen with strict typing, but handle defensively
      console.warn(`[getIndicatorSourceValue] Unknown source: ${source}`);
      return NaN;
  }
} 