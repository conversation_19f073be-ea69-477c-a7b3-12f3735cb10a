// src/components/SettingButton/Tabs/ChartTab.tsx
import React, { memo, useMemo, useCallback } from 'react';
import { AppSettings } from '../../../shared'; // Исправляем путь
import { Select, SimpleColorPicker, Slider, MainToggleButton } from './Controls';
import { debounce } from '@/shared/lib/utils'; // Import debounce
import { 
    useAppSettingsStore,
    selectVolumeEnabled,
    selectChartGridVertLinesVisible,
    selectChartGridHorzLinesVisible,
    selectChartVolumeHeightRatio,
    selectChartGridVertLinesColor,
    selectChartGridHorzLinesColor,
    selectChartFontFamily,
    selectChartFontSize,
    selectChartVolumeUpColor,
    selectChartVolumeDownColor,
    selectSetSetting
} from '@/shared/store/settingsStore';

// UPDATED: Interface ChartTabProps
interface ChartTabProps {
  systemFonts: string[];
  // REMOVED: Props for volume, grid, font, volume colors - will be fetched from store
  // REMOVED: handleGenericSettingChange - will use store actions directly
}

// Определяем типы для новой, более структурированной настройки линий сетки
interface GridLineSetting {
  id: string;
  label: string;
  visibleKey: 'chartGridVertLinesVisible' | 'chartGridHorzLinesVisible';
  colorKey: 'chartGridVertLinesColor' | 'chartGridHorzLinesColor';
}

// --- ChartTab --- 
const ChartTab: React.FC<ChartTabProps> = memo(({ systemFonts }) => {
  // --- Get data from Zustand store ---
  const volumeEnabled = useAppSettingsStore(selectVolumeEnabled) ?? true;
  const chartGridVertLinesVisible = useAppSettingsStore(selectChartGridVertLinesVisible) ?? false;
  const chartGridHorzLinesVisible = useAppSettingsStore(selectChartGridHorzLinesVisible) ?? false;
  const chartVolumeHeightRatio = useAppSettingsStore(selectChartVolumeHeightRatio) ?? 0.2;
  const chartGridVertLinesColor = useAppSettingsStore(selectChartGridVertLinesColor) || 'rgba(70, 130, 180, 0.2)';
  const chartGridHorzLinesColor = useAppSettingsStore(selectChartGridHorzLinesColor) || 'rgba(70, 130, 180, 0.2)';
  const chartFontFamily = useAppSettingsStore(selectChartFontFamily) || 'Verdana';
  const chartFontSize = useAppSettingsStore(selectChartFontSize) || 10;
  const chartVolumeUpColor = useAppSettingsStore(selectChartVolumeUpColor) || 'rgba(0, 150, 136, 0.8)';
  const chartVolumeDownColor = useAppSettingsStore(selectChartVolumeDownColor) || 'rgba(255, 82, 82, 0.8)';
  const setSetting = useAppSettingsStore(selectSetSetting);
  
  // Структура настроек линий сетки
  const gridLineSettings: GridLineSetting[] = useMemo(() => [
    { id: 'vert', label: 'Vertical Grid Lines', visibleKey: 'chartGridVertLinesVisible', colorKey: 'chartGridVertLinesColor' },
    { id: 'horz', label: 'Horizontal Grid Lines', visibleKey: 'chartGridHorzLinesVisible', colorKey: 'chartGridHorzLinesColor' }
  ], []);

  // Собираем текущие значения цветов для удобства, если в будущем понадобится (сейчас не критично, т.к. SimpleColorPicker берет напрямую)
  const currentGridColors = useMemo(() => ({
      chartGridVertLinesColor,
      chartGridHorzLinesColor
  }), [chartGridVertLinesColor, chartGridHorzLinesColor]);

  // Собираем текущие состояния видимости для удобства
  const currentVisibilityStates = useMemo(() => ({
    chartGridVertLinesVisible,
    chartGridHorzLinesVisible,
  }), [chartGridVertLinesVisible, chartGridHorzLinesVisible]);
  
  // --- Local Handlers that call store actions ---
  const handleSettingChange = useCallback(<K extends keyof AppSettings>(key: K, value: AppSettings[K]) => {
    setSetting(key, value);
  }, [setSetting]);

  // === Create debounced handlers for sliders ===
  const debouncedVolumeHeightChange = useMemo(
    () => debounce((value: number) => handleSettingChange('chartVolumeHeightRatio', value), 150),
    [handleSettingChange]
  );

  const debouncedFontSizeChange = useMemo(
    () => debounce((value: number) => handleSettingChange('chartFontSize', value), 150),
    [handleSettingChange]
  );

  return (
    <div className="space-y-6">
      <div className="setting-group">
         <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-4 pb-2 border-b border-border/10">Chart Options</h3>

         {/* --- Grouped Volume Settings --- */}
         <div className="setting-group subgroup mb-4">
            <MainToggleButton 
                label="Volume" 
                active={volumeEnabled}
                onChange={(checked) => handleSettingChange('volumeEnabled', checked)}
            />
            {volumeEnabled && (
                <div className="mt-2 pl-2">
                    <Slider 
                        label="Volume Height Ratio"
                        min={0} 
                        max={0.5} 
                        step={0.01}
                        value={chartVolumeHeightRatio}
                        onChange={debouncedVolumeHeightChange}
                    />
                </div>
            )}
         </div>

         {/* --- Информация о шкалах --- */}
         <div className="setting-group subgroup mb-4">
            <div className="bg-settings-primary/10 border border-settings-primary/20 text-settings-primary-foreground/80 rounded-md p-3 my-2 flex items-start gap-2 text-[13px] leading-normal">
               <span className="text-base mt-px">ℹ️</span>
               <span className="flex-1">Шкалы времени и цены теперь автоматически появляются при наведении курсора на график и скрываются при отведении курсора.</span>
            </div>
         </div>

         {/* --- Подгруппа Font (Шрифт графика) --- */}
         <div className="setting-group subgroup mb-4">
            <Select 
                label="Font Family" 
                value={chartFontFamily}
                onChange={(value: string) => handleSettingChange('chartFontFamily', value)}
                options={systemFonts} 
                isFont
            />
            <Slider 
                label="Font Size" 
                min={8} 
                max={16} 
                step={1} 
                unit="px"
                value={chartFontSize}
                onChange={debouncedFontSizeChange}
            />
        </div>

        {/* --- Подгруппа Grid (бывшая Grid Colors) --- */}
        <div className="setting-group subgroup mb-4">
            <div className="flex flex-col gap-2">
                {gridLineSettings.map(setting => (
                   <div key={setting.id} className="flex items-center gap-4">
                       <div className="flex-1">
                          <MainToggleButton 
                             label={setting.label}
                             active={currentVisibilityStates[setting.visibleKey]}
                             onChange={(checked) => handleSettingChange(
                                setting.visibleKey,
                                checked
                             )}
                          />
                      </div>
                      {currentVisibilityStates[setting.visibleKey] && (
                         <div className="flex-shrink-0">
                             <SimpleColorPicker
                                value={currentGridColors[setting.colorKey]}
                                onChange={(color: string) => 
                                   handleSettingChange(setting.colorKey, color)
                                }
                                aria-label={`${setting.label} Color`}
                             />
                         </div>
                      )}
                   </div>
                ))}
            </div>
        </div>

        {/* --- Подгруппа Volume Colors --- */}
        <div className="setting-group subgroup">
            <div className="grid grid-cols-2 gap-4 items-end">
                <SimpleColorPicker 
                    label="Up Volume Color"
                    value={chartVolumeUpColor}
                    onChange={(color: string) => handleSettingChange('chartVolumeUpColor', color)}
                />
                <SimpleColorPicker 
                    label="Down Volume Color"
                    value={chartVolumeDownColor}
                    onChange={(color: string) => handleSettingChange('chartVolumeDownColor', color)}
                />
            </div>
        </div>
      </div>
    </div>
  );
});

export default ChartTab;
