<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>
			Lightweight Charts - Delta Tooltip Plugin & Brushable Area Series Example
		</title>
		<link href="../../examples-base.css" rel="stylesheet" />
		<style>
			#chart {
				height: 450px;
				max-width: 800px;
			}
		</style>
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Delta Tooltip and Brushable Area Series</h1>
			<p class="hint-message">HINT: Use multi-touch, or click and drag</p>
			<p>
				An example showcasing two plugins being used together. The delta tooltip
				is used to show the difference between two points (defined by
				multi-touch or click and drag), while the brushable area series plugin
				will adjust the colours of the area plot to accentuate the effect.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>
