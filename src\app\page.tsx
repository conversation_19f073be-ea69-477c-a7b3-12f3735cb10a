'use client';

import React, { useEffect, useMemo, useRef, useCallback, useState, memo, Suspense, useTransition } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { TickerTable } from '@/features/TickerManagement/Table';
import ControlPanel from '@/features/ControlPanel/ControlPanel';
import SimpleChart from '@/features/Chart/Chart';
import { DevTools } from '@/shared/lib/DevTools';
import { useAppSettingsStore, selectSetSetting, selectSelectedTickerSymbol, selectIsTableCollapsed, selectSetTableCollapsed, selectMarketTypes, selectUpdateSortConfigs, selectAggregateVolumeAndTrades, selectChartVolumeUpColor, selectChartVolumeDownColor, selectViewMode, selectSetViewMode, selectLayoutTypeForCurrentMode, selectSelectedInterval, selectSelectedPairs, selectSetLayoutTypeForMode } from '@/shared/store/settingsStore';
import { useScreenerTickers } from '@/features/ModeControl/useScreenerTickers';
import {
  useMarketStore,
  useWebSocketConnection,
  WSConnectionStatus,
  MarketType,
  LayoutType,
  Ticker,
  Kline,
  useMarketDataManager
} from '@/shared/index';

// Import WebSocket auto-connect from websocket module specifically
import { useWebSocketAutoConnect } from '@/shared/market/websocket';
import { clsx } from 'clsx';
import { ErrorBoundary } from '@/shared/lib/ErrorBoundary';
import { haveSameKeys, isError } from '@/shared/lib/utils';
import ChartDisplayManager from '@/features/Chart/ChartDisplay';

// Memoize the SimpleChart component
const MemoizedSimpleChart = memo(SimpleChart);

// --- Constants ---
const DEFAULT_TIMEFRAME = '1h';
const DEFAULT_TICKER = 'BTCUSDT';
const DEFAULT_MARKET_TYPE = MarketType.Futures;
const AUTO_UPDATE_INTERVAL = 3000;
const FOCUS_AUTO_RECONNECT = true;

// Типы для группировки состояний
type ChartState = {
  intervals: Record<number, string>;
  fullscreenIndex: number | null;
};

type TableState = {
  searchQuery: string;
  counts: { total: number; filtered: number };
  filteredData: Ticker[];
};

function HomePageContent() {
  const [isPending, startTransition] = useTransition();
  
  // Селекторы из глобального стора
  const setSetting = useAppSettingsStore(selectSetSetting);
  const setViewMode = useAppSettingsStore(selectSetViewMode);
  const selectedTickerSymbol = useAppSettingsStore(selectSelectedTickerSymbol);
  const selectedInterval = useAppSettingsStore(selectSelectedInterval);
  const isTableCollapsed = useAppSettingsStore(selectIsTableCollapsed);
  const setTableCollapsed = useAppSettingsStore(selectSetTableCollapsed);
  const selectedMarketTypes = useAppSettingsStore(selectMarketTypes);
  const updateSortConfigs = useAppSettingsStore(selectUpdateSortConfigs);
  const aggregateVolumeAndTrades = useAppSettingsStore(selectAggregateVolumeAndTrades);
  const currentModeLayoutType = useAppSettingsStore(selectLayoutTypeForCurrentMode);
  const currentViewMode = useAppSettingsStore(selectViewMode);
  const selectedPairs = useAppSettingsStore(selectSelectedPairs);
  const setLayoutTypeForMode = useAppSettingsStore(selectSetLayoutTypeForMode);

  // Скринер хук для получения топ тикеров
  const { screenerTickers, isScreenerMode, screenerSettings } = useScreenerTickers();

  // Получаем централизованный менеджер данных
  const marketDataManager = useMarketDataManager();
  const { 
    isInitialLoading, 
    isWsConnected, 
    error: marketDataError, 
    connectWebSocket, 
    wsStatus: wsConnectionStatus 
  } = marketDataManager;
  
  // Store данные
  const spotTickers = useMarketStore(state => state.processedSpotTickers);
  const futuresTickers = useMarketStore(state => state.processedFuturesTickers);
  
  // Группировка связанных состояний
  const [chartState, setChartState] = useState<ChartState>({
    intervals: {},
    fullscreenIndex: null
  });
  
  const [tableState, setTableState] = useState<TableState>({
    searchQuery: '',
    counts: { total: 0, filtered: 0 },
    filteredData: []
  });
  
  const [localSymbolMarketMap, setLocalSymbolMarketMap] = useState<Map<string, MarketType>>(new Map());
  
  const mainContainerRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Вспомогательные функции для обновления состояния
  const updateChartState = useCallback((updates: Partial<ChartState>) => {
    setChartState(prev => ({ ...prev, ...updates }));
  }, []);
  
  const updateTableState = useCallback((updates: Partial<TableState>) => {
    setTableState(prev => ({ ...prev, ...updates }));
  }, []);

  // Мемоизированная функция определения типа рынка для тикера
  const getMarketTypeForSymbol = useCallback((symbol: string): MarketType => {
    if (!symbol) return DEFAULT_MARKET_TYPE;
    
    // Проверяем карту соответствий
    const marketType = localSymbolMarketMap.get(symbol);
    if (marketType) return marketType;
    
    // Проверяем доступность тикера в обработанных тикерах
    return (
      spotTickers.find(t => t.symbol === symbol) ? MarketType.Spot :
      futuresTickers.find(t => t.symbol === symbol) ? MarketType.Futures :
      DEFAULT_MARKET_TYPE
    );
  }, [localSymbolMarketMap, spotTickers, futuresTickers]);

  // Используем менеджер для подписки на свечи выбранного символа
  useEffect(() => {
    if (!selectedTickerSymbol || !selectedInterval) return;
    
    const marketType = getMarketTypeForSymbol(selectedTickerSymbol);
    return marketDataManager.subscribeToSymbol(selectedTickerSymbol, selectedInterval, marketType);
  }, [selectedTickerSymbol, selectedInterval, getMarketTypeForSymbol, marketDataManager]);

  // Автоматическое соединение с WebSocket
  useWebSocketAutoConnect();

  // Обработка данных и состояния загрузки
  useEffect(() => {
    // Если есть данные, то выходим из загрузки
    if (spotTickers.length > 0 || futuresTickers.length > 0 || isWsConnected) {
      return;
    }
    
    // Если еще загружаем данные, то ничего не делаем
    if (isInitialLoading) {
      return;
    }
    
    // Если нет данных и не загружаем, попробуем загрузить данные тикеров
    marketDataManager.loadInitialTickers();
  }, [isInitialLoading, isWsConnected, spotTickers.length, futuresTickers.length, marketDataManager]);

  // Обновление карты соответствия символов и типов рынка
  useEffect(() => {
    const newMap = new Map<string, MarketType>();
    spotTickers.forEach(ticker => newMap.set(ticker.symbol, MarketType.Spot));
    futuresTickers.forEach(ticker => newMap.set(ticker.symbol, MarketType.Futures));

    if (!haveSameKeys(localSymbolMarketMap, newMap)) {
      setLocalSymbolMarketMap(newMap);
    }
  }, [spotTickers, futuresTickers, localSymbolMarketMap]);

  // Установка дефолтного тикера если необходимо
  useEffect(() => {
    const shouldSetDefaultTicker = !selectedTickerSymbol && 
                                  !isInitialLoading && 
                                  localSymbolMarketMap.size > 0;
    
    if (shouldSetDefaultTicker) {
      const symbolToSelect = localSymbolMarketMap.has(DEFAULT_TICKER) 
        ? DEFAULT_TICKER 
        : Array.from(localSymbolMarketMap.keys())[0];
      
      setSetting('selectedTickerSymbol', symbolToSelect);
    }
  }, [isInitialLoading, localSymbolMarketMap, selectedTickerSymbol, setSetting]);

  // Логика определения размера сетки для графиков
  const { rows, cols, totalCharts } = useMemo(() => {
    const activeLayoutType = currentModeLayoutType || '1x1';
    
    if (!activeLayoutType || typeof activeLayoutType !== 'string') {
      return { rows: 1, cols: 1, totalCharts: 1 };
    }
    
    const [r, c] = activeLayoutType.split('x').map(Number);
    const layoutRows = isNaN(r) || r < 1 ? 1 : r;
    const layoutCols = isNaN(c) || c < 1 ? 1 : c;
    
    return { 
      rows: layoutRows, 
      cols: layoutCols, 
      totalCharts: layoutRows * layoutCols 
    };
  }, [currentModeLayoutType]);

  // Обновляем интервалы при изменении количества графиков
  useEffect(() => {
    // This effect ensures that the chartState.intervals object is updated
    // correctly when the total number of charts changes, while preserving
    // existing interval settings for charts that remain.
    setChartState(prevChartState => {
      const newIntervals = Array.from({ length: totalCharts }).reduce<Record<number, string>>((acc, _, i) => {
        acc[i] = prevChartState.intervals[i] || DEFAULT_TIMEFRAME;
        return acc;
      }, {});

      // Check if newIntervals is actually different from prevChartState.intervals.
      // For Record<number, string> where keys are effectively 0-indexed, Object.values() maintains order.
      const prevIntervalValues = Object.values(prevChartState.intervals);
      const newIntervalValues = Object.values(newIntervals); // newIntervals has totalCharts entries, keys 0..N-1

      // Compare lengths first, then values if lengths are equal using Array.some().
      if (prevIntervalValues.length !== newIntervalValues.length ||
          newIntervalValues.some((value, index) => value !== prevIntervalValues[index])) {
        return { ...prevChartState, intervals: newIntervals };
      }

      // If lengths are the same and all values are the same, no change needed.
      return prevChartState;
    });
  }, [totalCharts]); // chartState.intervals and updateChartState (via setChartState) removed from deps to break the loop.

  // Сброс полноэкранного режима при переходе в режим Screener
  useEffect(() => {
    if (currentViewMode === 'screener' && chartState.fullscreenIndex !== null) {
      updateChartState({ fullscreenIndex: null });
    }
  }, [currentViewMode, chartState.fullscreenIndex, updateChartState]);

  // Объединяем все обработчики в единый объект
  const handlers = useMemo(() => ({
    symbolSelect: (symbol: string) => {
      startTransition(() => {
        setSetting('selectedTickerSymbol', symbol);
        setViewMode('focus');
        updateChartState({ fullscreenIndex: null });
      });
    },
    
    toggleTableCollapse: () => setTableCollapsed(!isTableCollapsed),
    
    setLayoutType: (layout: LayoutType) => {
      setLayoutTypeForMode(currentViewMode, layout);
      updateChartState({ fullscreenIndex: null });
    },
    
    searchChange: (query: string) => {
      startTransition(() => {
        updateTableState({ searchQuery: query });
      });
    },
    
    countsChange: (counts: { total: number; filtered: number }) => {
      updateTableState({ counts });
    },
    
    intervalChange: (chartIndex: number, newInterval: string) => {
      updateChartState({
        intervals: {
          ...chartState.intervals,
          [chartIndex]: newInterval
        }
      });
    },
    
    fullscreenToggle: (chartIndex: number) => {
      if (isScreenerMode) {
        const tickerToFocus = screenerTickers[chartIndex];
        if (tickerToFocus) {
          startTransition(() => {
            setSetting('selectedTickerSymbol', tickerToFocus.symbol);
            setViewMode('focus');
            updateChartState({ fullscreenIndex: null });
          });
        }
      } else {
        updateChartState({ 
          fullscreenIndex: chartState.fullscreenIndex === chartIndex ? null : chartIndex 
        });
      }
    },
    
    filteredDataChange: (filteredData: Ticker[]) => {
      startTransition(() => {
        updateTableState({ filteredData });
      });
    }
  }), [
    isScreenerMode,
    isTableCollapsed,
    screenerTickers, 
    setSetting,
    setTableCollapsed,
    setViewMode,
    chartState.fullscreenIndex,
    chartState.intervals,
    startTransition,
    updateChartState,
    updateTableState,
    currentViewMode,
    setLayoutTypeForMode
  ]);

  // Детали соединения
  const connectionDetails = useMemo(() => [{
    name: 'WebSocket',
    status: wsConnectionStatus,
    details: marketDataError ?
      (isError(marketDataError) ? marketDataError.message : String(marketDataError)) :
      (isWsConnected ?
        'Connected' :
        wsConnectionStatus === WSConnectionStatus.Connecting ?
          'Connecting...' :
          'Closed/Error'),
  }], [wsConnectionStatus, marketDataError, isWsConnected]);

  // Определяем текущее состояние загрузки
  const isLoading = isInitialLoading && !isWsConnected && !marketDataError;

  // UI-элементы, вычисляемые свойства и состояния
  const uiElements = useMemo(() => {
    // Объединенные тикеры для панели управления
    const combinedTickers = [...spotTickers, ...futuresTickers];
    
    // Стили для сетки графиков
    const gridStyle = chartState.fullscreenIndex === null ? {
      gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))`,
      gridTemplateRows: `repeat(${rows}, minmax(0, 1fr))`,
    } : {};
    
    // Сообщение для пустого состояния
    const emptyChartMessage = (
      <div className="flex items-center justify-center h-full text-muted-foreground col-span-full row-span-full">
        Select a symbol from the table to display the chart.
      </div>
    );
    
    // Массив индексов для графиков
    const chartIndices = Array.from({ length: totalCharts }, (_, i) => i);
    
    // Обработчики для графиков
    const intervalHandlers = chartIndices.reduce((acc, index) => {
      acc[index] = (newInterval: string) => handlers.intervalChange(index, newInterval);
      return acc;
    }, {} as Record<number, (newInterval: string) => void>);
    
    const fullscreenHandlers = chartIndices.reduce((acc, index) => {
      acc[index] = () => handlers.fullscreenToggle(index);
      return acc;
    }, {} as Record<number, () => void>);
    
    return {
      combinedTickers,
      gridStyle,
      emptyChartMessage,
      chartIndices,
      intervalHandlers,
      fullscreenHandlers
    };
  }, [cols, rows, chartState.fullscreenIndex, totalCharts, spotTickers, futuresTickers, handlers]);

  // Log WebSocket connection status for debugging
  useEffect(() => {
    console.log(`[WebSocket Status]: ${wsConnectionStatus}`);
  }, [wsConnectionStatus]);

  return (
    <DndProvider backend={HTML5Backend}>
      {process.env.NODE_ENV === 'development' && <DevTools />}
      <div ref={mainContainerRef} className="flex h-screen flex-row bg-background text-foreground overflow-hidden gap-[3px]">
        <div
          className={clsx(
            "ticker-table-wrapper h-full transition-all duration-300 ease-in-out overflow-hidden flex-shrink-0 min-w-0",
            !isTableCollapsed 
              ? 'max-w-xl border-r border-border'
              : 'max-w-0 border-r-0'
          )}
        >
          <TickerTable
            searchQuery={tableState.searchQuery}
            aggregateVolumeAndTrades={aggregateVolumeAndTrades}
            onCountsChange={handlers.countsChange}
            onSymbolSelect={handlers.symbolSelect}
            selectedSymbol={selectedTickerSymbol || null}
          />
        </div>
        <div className="flex flex-col flex-grow overflow-hidden">
          <ControlPanel
            ref={scrollContainerRef}
            dragConstraintsRef={mainContainerRef}
            selectedSymbol={selectedTickerSymbol ?? 'N/A'}
            loading={isLoading}
            error={isError(marketDataError) ? marketDataError.message : null}
            lastUpdate={null}
            onRefresh={connectWebSocket}
            isWsConnected={isWsConnected}
            connectionDetails={connectionDetails}
            totalTickers={tableState.counts.total}
            filteredTickers={tableState.counts.filtered}
            isTableCollapsed={isTableCollapsed}
            onToggleTableCollapse={handlers.toggleTableCollapse}
            isFiltering={false}
            layoutType={currentModeLayoutType as LayoutType}
            selectedMarketTypes={selectedMarketTypes || []}
            aggregateVolumeAndTrades={aggregateVolumeAndTrades}
            setLayoutType={handlers.setLayoutType}
            onSearchChange={handlers.searchChange}
          />
          <div
            className={`flex-grow grid gap-[3px] mt-[3px] overflow-hidden relative`}
            style={uiElements.gridStyle}
          >
            <ChartDisplayManager
              isScreenerMode={isScreenerMode}
              screenerTickers={screenerTickers}
              screenerTimeframe={screenerSettings.timeframe}
              screenerCurrentPage={screenerSettings.currentPage}
              selectedTickerSymbol={selectedTickerSymbol || null}
              chartIndices={Array.from({ length: totalCharts }, (_, i) => i)}
              chartIntervals={chartState.intervals}
              getMarketTypeForSymbol={getMarketTypeForSymbol}
              intervalHandlers={uiElements.intervalHandlers}
              fullscreenHandlers={uiElements.fullscreenHandlers}
              fullscreenChartIndex={chartState.fullscreenIndex}
              emptyChartMessage={uiElements.emptyChartMessage}
              totalCharts={totalCharts}
            />
          </div>
        </div>
      </div>
    </DndProvider>
  );
}

export default function HomePage() {
  return (
    <ErrorBoundary>
      <Suspense fallback={
        <div className="flex h-screen items-center justify-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div>
        </div>
      }>
        <HomePageContent />
      </Suspense>
    </ErrorBoundary>
  );
}