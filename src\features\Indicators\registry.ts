// src/indicators/registry.ts
import type { IndicatorDefinition, IndicatorParams } from '@/shared/index';

// Импортируем определения индикаторов из папки definitions
import { SMA } from './definitions/sma';
import { RSI } from './definitions/rsi';
// TODO: Импортировать другие определения...

/**
 * Централизованный реестр всех доступных индикаторов в системе.
 * Ключ - ID индикатора, значение - его определение.
 */
export const indicatorRegistry: Record<string, IndicatorDefinition<any>> = {
  [SMA.id]: SMA,
  [RSI.id]: RSI,
  // TODO: Добавить другие индикаторы
};

/**
 * Получает список всех определений индикаторов из реестра.
 * @returns Массив определений индикаторов.
 */
export const getAvailableIndicators = (): IndicatorDefinition<any>[] => {
  return Object.values(indicatorRegistry);
};

/**
 * Находит определение индикатора по его ID.
 * @param indicatorId - ID искомого индикатора.
 * @returns Определение индикатора или undefined, если не найден.
 */
export const findIndicatorDefinitionById = (
  indicatorId: string
): IndicatorDefinition<any> | undefined => {
  return indicatorRegistry[indicatorId];
}; 