import { TimeframeOption } from '../types/settings.types';
import type { MarketType } from '../types';

export const DEFAULT_TIMEFRAMES: TimeframeOption[] = [
  { value: '1m', label: '1m' },
  { value: '3m', label: '3m' },
  { value: '5m', label: '5m' },
  { value: '15m', label: '15m' },
  { value: '30m', label: '30m' },
  { value: '1h', label: '1h' },
  { value: '2h', label: '2h' },
  { value: '4h', label: '4h' },
  { value: '6h', label: '6h' },
  { value: '8h', label: '8h' },
  { value: '12h', label: '12h' },
  { value: '1d', label: '1d' },
  { value: '3d', label: '3d' },
  { value: '1w', label: '1w' },
  { value: '1M', label: '1M' }
];

// Chart count options for screener
export const SCREENER_CHART_COUNTS = [4, 6, 9, 12, 16] as const;
export type ScreenerChartCount = typeof SCREENER_CHART_COUNTS[number];

// Update intervals for auto-update (in seconds)
export const SCREENER_UPDATE_INTERVALS = [
  { value: 0, label: 'Off' },
  { value: 3, label: '3s' },
  { value: 5, label: '5s' },
  { value: 10, label: '10s' },
  { value: 15, label: '15s' },
  { value: 30, label: '30s' },
  { value: 60, label: '1m' },
  { value: 120, label: '2m' },
  { value: 300, label: '5m' }
] as const;

export const CHART_EVENT_NAMESPACE = 'chart';

export enum ChartEventType {
  CROSSHAIR_MOVE = 'crosshair-move',
  VISIBLE_RANGE_CHANGE = 'visible-range-change',
  TIME_SCALE_RESET = 'time-scale-reset',
}

// API endpoints
export const BASE_API_URL = '/api/market';

// API Config
export const API_PORT = 3005;
export const API_BASE_URL = typeof window === 'undefined'
  ? `http://localhost:${API_PORT}`
  : `${window.location.protocol}//${window.location.hostname}:${API_PORT}`;

// WebSocket settings

// Components for fallback URL construction or for debugging purposes.
// These are derived once and logged.
const FALLBACK_WS_PORT = '3005'; // Default port if NEXT_PUBLIC_WS_PORT is not set for fallback construction
const _WS_PORT_COMPONENT = process.env.NEXT_PUBLIC_WS_PORT || FALLBACK_WS_PORT;
const _WS_HOST_COMPONENT = typeof window !== 'undefined' ? window.location.hostname : 'localhost';
const _WS_PROTOCOL_COMPONENT = typeof window !== 'undefined' && window.location.protocol === 'https:' ? 'wss:' : 'ws:';

// Log the components that would be used if NEXT_PUBLIC_WS_URL is not set, or for general debugging.
// This preserves the intent of the original log line.
console.log(`[WS Constants Components] For fallback/debug - Port: ${_WS_PORT_COMPONENT}, Host: ${_WS_HOST_COMPONENT}, Protocol: ${_WS_PROTOCOL_COMPONENT}`);

let determinedWsUrl: string;

if (typeof window !== 'undefined') {
  // Client-side: Prioritize NEXT_PUBLIC_WS_URL if it is defined.
  if (process.env.NEXT_PUBLIC_WS_URL) {
    determinedWsUrl = process.env.NEXT_PUBLIC_WS_URL;
  } else {
    // Fallback to constructing the URL if NEXT_PUBLIC_WS_URL is not set.
    determinedWsUrl = `${_WS_PROTOCOL_COMPONENT}//${_WS_HOST_COMPONENT}:${_WS_PORT_COMPONENT}/ws`;
  }
} else {
  // Server-side: Construct a default URL.
  // This URL is for scenarios where server-side code might act as a WS client (e.g., in tests running in Node).
  // It typically wouldn't use client-specific NEXT_PUBLIC_ environment variables.
  determinedWsUrl = `ws://localhost:${FALLBACK_WS_PORT}/ws`;
}

export const WS_URL = determinedWsUrl;

// Log the final, effective WS_URL that will be used by the application.
console.log(`[WS Constants] Effective WS_URL: ${WS_URL}`);

export const WS_RECONNECT_DELAY = 1000; // Initial reconnect delay (ms)
export const WS_MAX_RECONNECT_DELAY = 30000; // Maximum reconnect delay (ms)
export const WS_RECONNECT_BACKOFF_FACTOR = 1.5; // Exponential backoff factor
export const WS_PING_INTERVAL = 30000; // Ping interval (ms)
export const WS_PING_TIMEOUT = 10000; // Timeout for ping response (ms)

// Default values
export const DEFAULT_MARKET_TYPE: MarketType = 'spot';
export const DEFAULT_INTERVAL: KlineInterval = '1h';
export const DEFAULT_LIMIT = 1000;
export const DEFAULT_VOLUME_FILTER = 1000; // Minimum 24h volume
export const DEFAULT_TICK_PRECISION = 8; // Decimal precision

// Kline intervals
export const KLINE_INTERVALS = [
  '1m', '3m', '5m', '15m', '30m',
  '1h', '2h', '4h', '6h', '8h', '12h',
  '1d', '3d', '1w', '1M'
] as const;

export type KlineInterval = typeof KLINE_INTERVALS[number];



