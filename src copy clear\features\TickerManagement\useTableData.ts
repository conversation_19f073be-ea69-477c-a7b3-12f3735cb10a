import { useMemo } from 'react';
import { Ticker, MarketType, ProcessedTicker, CalculatedIndicatorResult, IndicatorColumnConfig } from '@/shared/index';
import {
  useAppSettingsStore,
  selectMarketTypes,
  selectShowVolumeInUSD,
  selectMinVolume,
  selectMinTrades,
  selectAvailablePairs,
  selectIndicatorColumns,
} from '@/shared/store/settingsStore';
import {
  useMarketStore,
} from '@/shared/store/marketStore';

// --- Constants for filtering ---
const MIN_ACTIVITY_INTERVAL_MS = 24 * 60 * 60 * 1000; // 24 hours

// --- Helper Functions for Ticker Processing and Filtering ---

/**
 * Filters an array of tickers based on various criteria like market type, selected pairs,
 * volume, trades, and a search query.
 * @param tickers Array of ProcessedTicker objects to filter.
 * @param selectedMarketTypes Array of selected market types.
 * @param selectedPairs Array of selected trading pairs (e.g., 'USDT', 'BTC').
 * @param availablePairs Array of all available quote assets.
 * @param minVolume Minimum volume filter (in millions).
 * @param minTrades Minimum number of trades.
 * @param showVolumeInUSD Boolean indicating if volume should be checked in USD.
 * @param searchQuery Optional search query string.
 * @returns A filtered array of ProcessedTicker objects.
 */
const filterTickers = (
  tickers: ProcessedTicker[],
  selectedMarketTypes: MarketType[],
  selectedPairs: string[],
  availablePairs: string[],
  minVolume: number,
  minTrades: number,
  showVolumeInUSD: boolean,
  searchQuery?: string
): ProcessedTicker[] => {
  if (!tickers) return [];

  const now = Date.now();
  const marketTypeSet = new Set(selectedMarketTypes.map(mt => mt));
  const minVolumeThreshold = minVolume * 1e6; // Pre-calculate threshold for volume in millions

  const lowerCaseQuery = searchQuery?.toLowerCase();
  // Ensure selectedPairs is always an array
  const currentSelectedPairs = selectedPairs || [];
  const concreteSelectedPairs = currentSelectedPairs.filter(p => p !== 'OTHR'); // Pairs other than 'OTHR'
  const isOthrSelected = currentSelectedPairs.includes('OTHR'); // Check if 'OTHR' (other pairs) is selected
  const hasConcreteSelection = concreteSelectedPairs.length > 0;
  const hasAnyPairSelection = currentSelectedPairs.length > 0;
  // All available quote assets excluding 'OTHR'
  const allAvailableConcreteQuotes = availablePairs.filter(p => p !== 'OTHR');

  return tickers.filter(ticker => {
    // Filter by search query
    if (lowerCaseQuery && !ticker.symbol.toLowerCase().includes(lowerCaseQuery)) {
      return false;
    }
    // Filter by market type
    if (marketTypeSet.size > 0 && !marketTypeSet.has(ticker.marketType as MarketType)) {
      return false;
    }
    // Filter by last activity time (e.g., remove inactive tickers)
    const lastActivityTime = ticker.lastUpdated;
    if (lastActivityTime && (now - lastActivityTime > MIN_ACTIVITY_INTERVAL_MS)) {
      return false;
    }
    // Filter by selected trading pairs
    if (hasAnyPairSelection) {
      const endsWithSelectedConcrete = hasConcreteSelection && concreteSelectedPairs.some(pair => ticker.symbol.endsWith(pair));
      const endsWithAvailableConcrete = allAvailableConcreteQuotes.some(pair => ticker.symbol.endsWith(pair));
      // Logic for 'OTHR': include if it doesn't end with any available concrete quote
      // Logic for concrete pairs: include if it ends with one of the selected concrete pairs
      const shouldKeep = (isOthrSelected && !endsWithAvailableConcrete) || (hasConcreteSelection && endsWithSelectedConcrete);
      if (!shouldKeep) {
        return false;
      }
    }
    // Filter by minimum volume
    if (minVolume > 0) {
      const volumeToCheck = showVolumeInUSD ? ticker.quoteVolume : ticker.volume;
      if (volumeToCheck == null || volumeToCheck < minVolumeThreshold) {
        return false;
      }
    }
    // Filter by minimum trades
    if (minTrades > 0) {
      const tradesValue = ticker.count;
      if (tradesValue == null || tradesValue < minTrades) {
        return false;
      }
    }
    return true;
  });
};

/**
 * Aggregates volume and trade counts for tickers that exist in both spot and futures markets.
 * If a ticker has a counterpart in the other market type, their volumes and counts are summed.
 * @param tickers Array of ProcessedTicker objects to aggregate.
 * @param spotTickers Array of all spot processed tickers.
 * @param futuresTickers Array of all futures processed tickers.
 * @returns An array of ProcessedTicker objects with aggregated data where applicable.
 */
const aggregateTickers = (
  tickers: ProcessedTicker[],
  spotTickers: ProcessedTicker[],
  futuresTickers: ProcessedTicker[]
): ProcessedTicker[] => {
  return tickers.map(ticker => {
    const { symbol, marketType } = ticker;
    let counterpartTicker: ProcessedTicker | undefined;

    // Find the counterpart in the other market
    if (marketType === 'spot') {
      counterpartTicker = futuresTickers.find(t => t.symbol === symbol);
    } else if (marketType === 'futures') {
      counterpartTicker = spotTickers.find(t => t.symbol === symbol);
    }

    // If no counterpart, return the original ticker
    if (!counterpartTicker) return ticker;

    // Aggregate volume and trades
    return {
      ...ticker,
      volume: (ticker.volume ?? 0) + (counterpartTicker.volume ?? 0),
      quoteVolume: (ticker.quoteVolume ?? 0) + (counterpartTicker.quoteVolume ?? 0),
      count: (ticker.count ?? 0) + (counterpartTicker.count ?? 0),
    };
  });
};

/**
 * Props for the useProcessedTableData hook.
 */
interface UseProcessedTableDataProps {
  searchQuery: string;
  aggregateVolumeAndTrades: boolean;
  selectedPairs?: string[]; // Allow selectedPairs to be initially undefined or an empty array
}

/**
 * Custom hook to process and filter ticker data for the table.
 * It combines spot and futures tickers, filters them based on user settings and search query,
 * optionally aggregates data for symbols present in both markets, and then processes them for display.
 * @param props - The properties for processing table data.
 * @returns An object containing combined raw tickers and the final filtered & processed data.
 */
export const useProcessedTableData = ({
  searchQuery,
  aggregateVolumeAndTrades,
  selectedPairs: propsSelectedPairs 
}: UseProcessedTableDataProps) => {
  // Retrieve raw ticker data from the market store
  const spotTickers = useMarketStore(state => state.processedSpotTickers); // Assuming these are Ticker[] not ProcessedTicker[] from source
  const futuresTickers = useMarketStore(state => state.processedFuturesTickers); // Same assumption as above

  // Retrieve filter settings from the app settings store
  const selectedMarketTypes = useAppSettingsStore(selectMarketTypes);
  const showVolumeInUSD = useAppSettingsStore(selectShowVolumeInUSD);
  const minVolume = useAppSettingsStore(selectMinVolume);
  const minTrades = useAppSettingsStore(selectMinTrades);
  const availablePairs = useAppSettingsStore(selectAvailablePairs);
  // Use selectedPairs from props if provided, otherwise default to empty array for filtering logic
  const selectedPairsForFilter = propsSelectedPairs ?? [];

  // Combine spot and futures tickers into a single array
  const combinedTickers: ProcessedTicker[] = useMemo(() => [
    ...spotTickers, // Получаем ProcessedTicker[] из хранилища
    ...futuresTickers
  ], [spotTickers, futuresTickers]);

  // Memoized final filtered and processed data
  const finalFilteredData = useMemo(() => {
    // Step 1: Initial filtering based on settings and search query
    const initiallyFiltered = filterTickers(
      combinedTickers,
      selectedMarketTypes,
      selectedPairsForFilter,
      availablePairs,
      minVolume,
      minTrades,
      showVolumeInUSD,
      searchQuery
    );

    // Step 2: Aggregate data if the option is enabled
    const aggregatedOrOriginal: ProcessedTicker[] = aggregateVolumeAndTrades
      ? aggregateTickers(initiallyFiltered, spotTickers, futuresTickers) as ProcessedTicker[]
      : initiallyFiltered as ProcessedTicker[];

    // Данные уже предварительно обработаны в marketStore, не нужно обрабатывать их снова
    // const processed = aggregatedOrOriginal.map(memoizedProcessTickerData);
    
    return aggregatedOrOriginal;
  }, [
    combinedTickers,
    selectedMarketTypes,
    selectedPairsForFilter,
    availablePairs,
    minVolume,
    minTrades,
    showVolumeInUSD,
    searchQuery,
    aggregateVolumeAndTrades,
    spotTickers,
    futuresTickers
    // Удалено: memoizedProcessTickerData
  ]);

  return { combinedTickers, finalFilteredData };
};

// --- Hook for Indicator Data ---

/**
 * Result type for the useIndicatorDataForTable hook.
 */
interface UseIndicatorDataForTableResult {
  indicatorDataMap: Map<string, Map<string, CalculatedIndicatorResult>>;
  indicatorQueryStatusMap: Map<string, 'pending' | 'success' | 'error'>;
}

// Placeholder for useMarketIndicators hook, which would fetch indicator data.
// This function is a stand-in and needs to be replaced with actual implementation.
// const useMarketIndicators = (indicatorId: string, timeframe: string, marketType: MarketType): {loading: boolean, error: Error | null, indicators: any[]} => ({loading: true, error: null, indicators: []});

/**
 * Custom hook to prepare indicator data for the table.
 * This is currently a placeholder and needs to be implemented to fetch and process
 * indicator data based on selected indicator columns and market types.
 * @returns An object containing maps for indicator data and their query statuses.
 */
export const useIndicatorDataForTable = (): UseIndicatorDataForTableResult => {
  const indicatorColumns = useAppSettingsStore(selectIndicatorColumns);
  const selectedMarketTypes = useAppSettingsStore(selectMarketTypes); 

  // Temporarily return empty maps and pending status as useMarketIndicators is not available or implemented.
  // This section needs to be updated once indicator data fetching is in place.
  const indicatorDataMap = useMemo(() => new Map<string, Map<string, CalculatedIndicatorResult>>(), []);
  const indicatorQueryStatusMap = useMemo(() => new Map<string, 'pending' | 'success' | 'error'>(), []);

  /* 
  // Original logic commented out as useMarketIndicators is not resolved.
  // This demonstrates how it might be structured.
  const indicatorHooks = useMemo(() => {
    const hooks: Array<{
      key: string;
      // hook: ReturnType<typeof useMarketIndicators>; // This would be the actual type
      hook: any; // Temporary type to avoid error with placeholder
    }> = [];
    
    (selectedMarketTypes || []).forEach(marketType => { 
      indicatorColumns.forEach(col => {
        const timeframe = col.timeframe || '1h'; // Default timeframe if not specified
        const key = `${col.indicatorId}_${timeframe}_${marketType}`;
        
        // Example of how a real hook might be used:
        // hooks.push({
        //   key,
        //   hook: useMarketIndicators(col.indicatorId, timeframe, marketType) 
        // });
      });
    });
    
    return hooks;
  }, [indicatorColumns, selectedMarketTypes]);

  const { indicatorDataMap, indicatorQueryStatusMap } = useMemo(() => {
    const dataMap = new Map<string, Map<string, CalculatedIndicatorResult>>();
    const statusMap = new Map<string, 'pending' | 'success' | 'error'> ();

    indicatorHooks.forEach(({ key, hook }) => {
      // Based on the hook's state, populate statusMap and dataMap
      if (hook.loading) {
        statusMap.set(key, 'pending');
      } else if (hook.error) {
        statusMap.set(key, 'error');
      } else {
        statusMap.set(key, 'success');
      }

      if (hook.indicators && hook.indicators.length > 0) {
        const symbolMap = new Map<string, CalculatedIndicatorResult>();
        // Assuming hook.indicators is an array of CalculatedIndicatorResult
        hook.indicators.forEach((item: CalculatedIndicatorResult) => symbolMap.set(item.symbol, item)); 
        dataMap.set(key, symbolMap);
      }
    });

    return { indicatorDataMap: dataMap, indicatorQueryStatusMap: statusMap };
  }, [indicatorHooks]);
  */

  return { indicatorDataMap, indicatorQueryStatusMap };
};
