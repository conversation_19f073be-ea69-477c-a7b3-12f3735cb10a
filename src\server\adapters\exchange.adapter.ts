import {
  MarketType,
  ExchangeTickerData,
  ExchangeKlineData,
  ExchangeSymbolData,
  CoreTicker,
  CoreKline,
  CoreSymbol,
  ExchangeName
} from '@/shared/index';

/**
 * Base adapter interface for exchange data
 * All exchange-specific adapters should implement this interface
 */
export interface ExchangeAdapter {
  /**
   * Name of the exchange
   */
  readonly exchangeName: ExchangeName;
  
  /**
   * Convert exchange ticker data to core ticker format
   */
  adaptTicker(data: ExchangeTickerData, marketType: MarketType): CoreTicker;
  
  /**
   * Convert exchange kline data to core kline format
   */
  adaptKline(data: ExchangeKlineData, marketType: MarketType, interval: string): CoreKline;
  
  /**
   * Convert exchange symbol data to core symbol format
   */
  adaptSymbol(data: ExchangeSymbolData, marketType: MarketType): CoreSymbol;
  
  /**
   * Convert exchange ticker data array to core ticker format array
   */
  adaptTickers(data: ExchangeTickerData[], marketType: MarketType): CoreTicker[];
  
  /**
   * Convert exchange kline data array to core kline format array
   */
  adaptKlines(data: ExchangeKlineData[], marketType: MarketType, interval: string): CoreKline[];
  
  /**
   * Convert exchange symbol data array to core symbol format array
   */
  adaptSymbols(data: ExchangeSymbolData[], marketType: MarketType): CoreSymbol[];
}

/**
 * Abstract base class for exchange adapters
 * Provides default implementations for array conversions
 */
export abstract class BaseExchangeAdapter implements ExchangeAdapter {
  abstract readonly exchangeName: ExchangeName;
  
  /**
   * Convert a single ticker from exchange format to core format
   * Must be implemented by specific exchange adapters
   */
  abstract adaptTicker(data: ExchangeTickerData, marketType: MarketType): CoreTicker;
  
  /**
   * Convert a single kline from exchange format to core format
   * Must be implemented by specific exchange adapters
   */
  abstract adaptKline(data: ExchangeKlineData, marketType: MarketType, interval: string): CoreKline;
  
  /**
   * Convert a single symbol from exchange format to core format
   * Must be implemented by specific exchange adapters
   */
  abstract adaptSymbol(data: ExchangeSymbolData, marketType: MarketType): CoreSymbol;
  
  /**
   * Convert an array of tickers from exchange format to core format
   * Uses the single ticker adapter internally
   */
  adaptTickers(data: ExchangeTickerData[], marketType: MarketType): CoreTicker[] {
    return data.map(item => this.adaptTicker(item, marketType));
  }
  
  /**
   * Convert an array of klines from exchange format to core format
   * Uses the single kline adapter internally
   */
  adaptKlines(data: ExchangeKlineData[], marketType: MarketType, interval: string): CoreKline[] {
    return data.map(item => this.adaptKline(item, marketType, interval));
  }
  
  /**
   * Convert an array of symbols from exchange format to core format
   * Uses the single symbol adapter internally
   */
  adaptSymbols(data: ExchangeSymbolData[], marketType: MarketType): CoreSymbol[] {
    return data.map(item => this.adaptSymbol(item, marketType));
  }
  
  /**
   * Safely parse a string to number, returning 0 if invalid
   */
  protected safeParseFloat(value: any): number {
    if (value === undefined || value === null) return 0;
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }
  
  /**
   * Safely convert a value to number, with a default fallback
   */
  protected toNumber(value: any, defaultValue: number = 0): number {
    if (value === undefined || value === null) return defaultValue;
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? defaultValue : parsed;
    }
    return defaultValue;
  }
} 