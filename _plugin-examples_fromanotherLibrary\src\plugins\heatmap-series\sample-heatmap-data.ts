import { Time } from 'lightweight-charts';
import { HeatMapData } from './data';

const rawData = [
	[12.8, 10.6, 11.7, 12.2, 8.9, 4.4, 7.2, 10, 9.4, 6.1],
	[6.1, 6.1, 5, 4.4, 1.1, 1.7, 3.3, 0, -1.1, 7.2],
	[8.3, 6.7, 8.3, 10, 8.9, 8.9, 6.7, 6.7, 9.4, 8.3],
	[9.4, 8.9, 8.3, 14.4, 15.6, 13.9, 16.1, 15.6, 10, 11.1],
	[12.8, 8.9, 8.3, 7.2, 6.7, 7.2, 7.2, 10, 6.7, 6.7],
	[7.8, 10, 10, 8.3, 6.7, 7.2, 5, 6.7, 6.7, 5],
	[6.1, 6.7, 12.2, 10.6, 7.8, 6.7, 8.9, 15.6, 9.4, 7.2],
	[6.7, 8.3, 5.6, 7.8, 11.1, 8.9, 10, 5, 7.2, 7.8],
	[8.9, 10, 12.2, 15, 13.3, 12.8, 14.4, 10.6, 10, 9.4],
	[10, 8.9, 16.7, 11.7, 10.6, 9.4, 11.1, 16.1, 21.1, 20],
	[17.8, 11.1, 13.9, 15, 15.6, 16.1, 13.3, 10, 13.3, 13.9],
	[13.3, 20, 23.3, 21.7, 13.9, 16.7, 13.9, 13.3, 16.1, 15.6],
	[12.8, 11.7, 13.3, 11.1, 12.2, 13.3, 17.8, 23.9, 18.3, 13.3],
	[14.4, 18.3, 24.4, 25.6, 26.7, 24.4, 19.4, 17.8, 15.6, 19.4],
	[14.4, 16.7, 12.8, 14.4, 17.2, 22.2, 22.2, 17.2, 16.7, 16.1],
	[18.9, 17.8, 20, 18.9, 17.2, 12.8, 13.3, 16.1, 16.1, 15],
	[17.2, 18.9, 23.3, 18.3, 16.1, 17.2, 22.2, 21.1, 18.9, 17.2],
	[19.4, 24.4, 23.9, 13.9, 15.6, 19.4, 19.4, 18.3, 22.8, 22.2],
	[21.7, 20, 20, 18.9, 18.3, 20.6, 24.4, 25, 26.7, 28.3],
	[25, 23.9, 27.8, 25.6, 23.3, 25, 18.9, 26.1, 21.7, 21.1],
	[25, 19.4, 23.9, 20.6, 18.9, 23.3, 26.7, 25.6, 18.9, 22.2],
	[22.8, 19.4, 22.8, 23.9, 23.3, 27.2, 33.9, 33.9, 28.3, 21.1],
	[22.2, 24.4, 25.6, 28.3, 30.6, 30.6, 28.9, 31.1, 34.4, 32.8],
	[21.7, 23.3, 25.6, 23.3, 22.2, 21.1, 22.2, 26.1, 21.1, 23.9],
	[22.8, 22.8, 22.8, 22.2, 21.7, 21.1, 22.8, 24.4, 26.1, 28.3],
	[32.2, 25, 18.9, 20, 20, 22.2, 27.8, 26.1, 22.2, 24.4],
	[27.8, 27.8, 23.9, 19.4, 16.1, 19.4, 19.4, 21.1, 19.4, 19.4],
	[22.8, 25, 20.6, 21.1, 23.3, 17.8, 18.9, 18.9, 21.7, 23.9],
	[23.9, 21.1, 16.1, 12.2, 13.9, 13.9, 15.6, 17.8, 17.2, 16.1],
	[14.4, 17.8, 15, 11.1, 11.7, 7.8, 11.1, 11.7, 11.7, 11.1],
	[14.4, 14.4, 15.6, 15, 15.6, 15, 15, 15.6, 17.8, 15],
	[12.8, 12.2, 10, 8.9, 7.8, 8.9, 12.8, 11.1, 11.1, 9.4],
	[9.4, 12.2, 10, 13.3, 11.1, 8.3, 8.9, 9.4, 8.9, 8.3],
	[9.4, 10, 9.4, 12.8, 15, 13.3, 8.3, 9.4, 11.7, 8.9],
	[7.2, 7.8, 6.7, 6.7, 7.2, 7.8, 6.7, 7.2, 6.1, 4.4],
	[6.7, 8.3, 3.9, 8.3, 7.2, 8.3, 8.3, 7.2, 5.6, 5.6],
	[6.7, 7.8, 8.3, 5, 4.4, 3.3, 5, 6.1, 6.7, 10],
	[6.7, 7.2, 10, 11.7, 10, 3.3, 2.8, 2.8, 2.2, 3.3],
	[6.7, 6.1, 3.9, 3.3, 1.1, 3.3, 2.2, 3.3, 7.2, 7.2],
	[10.6, 8.3, 5.6, 6.1, 8.3, 8.9, 9.4, 11.7, 6.1, 8.9],
	[10.6, 10, 10.6, 9.4, 7.8, 8.3, 8.9, 8.3, 11.1, 9.4],
	[9.4, 13.3, 11.1, 9.4, 7.8, 10.6, 7.8, 6.7, 7.8, 10],
	[8.9, 10.6, 8.9, 10, 11.7, 15, 13.9, 11.1, 13.3, 9.4],
	[7.2, 12.2, 11.7, 12.8, 7.8, 10.6, 12.8, 11.7, 11.7, 14.4],
	[10.6, 8.9, 11.7, 12.8, 11.1, 10, 9.4, 10, 12.2, 16.7],
	[16.7, 13.3, 16.1, 18.3, 20, 20.6, 17.2, 13.9, 16.7, 14.4],
	[13.9, 12.2, 8.3, 13.3, 12.2, 15, 12.2, 7.8, 10.6, 12.8],
	[13.9, 13.9, 15, 11.7, 13.3, 13.9, 12.2, 16.1, 17.8, 21.1],
	[21.7, 20.6, 13.9, 15, 13.9, 12.8, 18.3, 20.6, 21.7, 25],
	[28.9, 30.6, 20.6, 19.4, 22.8, 26.1, 27.2, 21.7, 18.9, 18.3],
	[17.2, 21.7, 17.2, 16.7, 18.3, 19.4, 15.6, 11.1, 12.2, 16.7],
	[17.8, 18.3, 16.7, 17.2, 16.1, 16.7, 19.4, 22.8, 20.6, 22.2],
	[26.1, 26.7, 26.7, 21.7, 20.6, 20.6, 21.7, 20, 20.6, 21.1],
	[20, 25.6, 23.9, 25.6, 23.3, 20, 17.2, 20.6, 25.6, 22.2],
	[21.1, 23.3, 22.2, 21.1, 30.6, 30, 33.9, 31.7, 28.3, 26.1],
	[21.7, 23.3, 26.1, 23.9, 26.7, 30, 22.2, 22.8, 19.4, 26.1],
	[27.8, 27.8, 31.1, 22.2, 26.1, 27.8, 25, 23.9, 26.1, 31.1],
	[31.1, 31.1, 31.1, 25.6, 21.1, 25, 25, 21.7, 20.6, 17.2],
	[25, 28.9, 30, 30.6, 31.1, 28.3, 28.3, 25.6, 25, 25.6],
	[27.8, 27.2, 21.1, 28.9, 25.6, 26.1, 26.7, 25.6, 27.8, 28.9],
	[25, 25, 22.2, 24.4, 26.7, 26.7, 23.9, 26.1, 27.8, 27.8],
	[27.8, 25, 22.8, 20, 21.7, 23.3, 26.7, 26.1, 26.7, 33.9],
	[25.6, 18.9, 21.7, 18.9, 21.7, 17.8, 21.1, 25.6, 23.3, 21.1],
	[17.2, 16.1, 17.8, 16.1, 17.2, 13.9, 16.7, 14.4, 13.9, 14.4],
	[12.8, 14.4, 17.8, 20, 22.8, 16.1, 13.9, 15, 14.4, 13.9],
	[14.4, 15, 15.6, 15.6, 12.8, 14.4, 12.8, 10.6, 10.6, 11.7],
	[14.4, 12.8, 10, 12.2, 11.7, 13.9, 14.4, 13.3, 15, 14.4],
	[17.8, 14.4, 12.2, 10.6, 13.3, 12.8, 11.1, 13.3, 11.1, 11.1],
	[16.1, 15.6, 13.9, 11.1, 10.6, 10, 11.7, 12.8, 13.3, 7.8],
	[7.8, 9.4, 11.1, 11.7, 12.2, 12.2, 14.4, 11.7, 9.4, 11.1],
	[13.3, 7.8, 5, 4.4, 1.1, 1.1, 0, 2.2, 1.1, 5.6],
	[5, 5.6, 9.4, 9.4, 11.7, 10, 8.3, 7.8, 5, 8.3],
	[8.9, 10.6, 11.7, 8.3, 6.7, 6.7, 8.9, 9.4, 7.2, 8.9],
	[8.3, 7.2, 10.6, 8.9, 7.8, 8.3, 7.8, 8.3, 10, 9.4],
	[12.8, 14.4, 11.1, 10.6, 11.1, 11.1, 6.7, 5.6, 9.4, 6.1],
	[10, 10, 9.4, 10, 12.8, 12.2, 8.3, 9.4, 11.1, 11.1],
	[8.3, 7.8, 7.8, 8.9, 5, 2.8, -0.5, -1.6, 3.3, 5.6],
	[3.9, 10, 12.2, 12.2, 12.8, 11.7, 11.1, 9.4, 8.3, 8.9],
	[8.3, 10, 6.7, 5.6, 7.2, 6.7, 12.2, 13.9, 12.8, 14.4],
	[7.2, 11.1, 14.4, 13.9, 15.6, 13.3, 15.6, 12.8, 15, 12.2],
	[14.4, 16.1, 13.9, 14.4, 16.7, 10.6, 10, 10, 11.1, 11.1],
	[10.6, 11.1, 12.8, 18.9, 13.9, 11.1, 12.2, 11.7, 11.7, 11.1],
	[15.6, 14.4, 14.4, 13.3, 12.8, 11.7, 13.9, 21.1, 15.6, 14.4],
	[15, 17.2, 16.1, 20.6, 20, 14.4, 11.1, 11.7, 14.4, 11.7],
	[15.6, 17.2, 12.2, 11.7, 13.9, 14.4, 15, 11.1, 16.1, 25],
	[27.8, 29.4, 18.3, 15, 14.4, 15.6, 16.7, 18.3, 13.9, 13.3],
	[15.6, 18.9, 24.4, 26.7, 27.8, 26.7, 20, 20, 20, 21.1],
	[22.2, 20, 24.4, 20, 18.3, 15, 18.3, 20, 18.9, 18.9],
	[20.6, 23.3, 22.2, 23.3, 18.3, 19.4, 22.2, 25, 24.4, 23.3],
	[21.1, 20, 23.9, 21.7, 15.6, 17.8, 18.3, 17.8, 17.8, 18.9],
	[25.6, 20, 22.2, 25, 25, 24.4, 26.1, 21.1, 21.1, 20],
	[20.6, 25.6, 34.4, 27.2, 21.7, 23.9, 24.4, 28.9, 27.2, 30],
	[26.7, 28.9, 31.1, 32.2, 29.4, 27.8, 31.1, 31.1, 26.7, 23.9],
	[25.6, 19.4, 23.9, 21.1, 18.9, 20.6, 22.8, 26.1, 28.3, 30.6],
	[30, 29.4, 30.6, 28.9, 29.4, 31.7, 32.8, 25, 26.1, 25.6],
	[25.6, 27.2, 30.6, 35.6, 27.2, 23.3, 21.1, 24.4, 25.6, 27.8],
	[29.4, 27.2, 21.7, 21.1, 23.9, 27.8, 25, 28.9, 31.1, 28.9],
	[23.3, 22.8, 17.8, 21.1, 23.3, 20, 20.6, 23.9, 27.8, 32.2],
	[28.3, 21.1, 21.7, 22.2, 24.4, 24.4, 28.3, 30, 30.6, 22.2],
	[22.8, 19.4, 23.9, 24.4, 26.1, 22.2, 18.9, 18.9, 21.7, 20],
	[20.6, 18.9, 16.7, 19.4, 18.3, 19.4, 22.2, 21.7, 23.9, 25.6],
	[18.9, 20.6, 17.2, 18.3, 18.3, 17.8, 21.1, 16.7, 16.1, 20.6],
	[16.7, 19.4, 22.2, 16.1, 16.1, 15.6, 14.4, 14.4, 16.7, 12.8],
	[15.6, 15, 16.7, 15.6, 12.8, 11.1, 13.3, 13.9, 14.4, 15],
	[16.7, 14.4, 12.8, 13.3, 11.1, 7.8, 6.7, 7.2, 7.2, 8.3],
	[9.4, 10.6, 7.2, 11.1, 11.1, 11.1, 9.4, 12.8, 11.7, 13.9],
	[15, 14.4, 12.8, 4.4, 2.8, 4.4, 5.6, 10, 8.3, 12.8],
	[11.7, 14.4, 14.4, 16.1, 18.9, 14.4, 11.1, 10, 12.8, 12.2],
	[10, 8.9, 9.4, 11.1, 12.8, 12.8, 10.6, 12.2, 7.2, 7.8],
	[5.6, 9.4, 6.7, 6.1, 3.3, 3.3, 5.6, 5.6, 5, 10.6],
	[12.2, 12.2, 7.8, 7.8, 10, 7.8, 9.4, 11.1, 9.4, 6.1],
	[7.8, 11.7, 13.3, 13.9, 10, 10, 7.2, 9.4, 12.2, 14.4],
	[17.2, 16.1, 11.1, 12.2, 12.2, 8.3, 7.2, 9.4, 11.1, 10],
	[10.6, 13.3, 14.4, 12.2, 15, 13.3, 12.8, 12.8, 16.7, 15.6],
	[14.4, 12.2, 15, 16.1, 12.2, 10.6, 11.1, 12.2, 11.7, 12.8],
	[11.1, 10, 11.7, 10, 12.2, 11.1, 11.1, 10.6, 12.8, 13.3],
	[15, 16.7, 17.2, 14.4, 13.3, 14.4, 17.8, 17.2, 13.9, 10.6],
	[13.9, 13.3, 15.6, 15.6, 13.9, 13.3, 11.7, 11.1, 12.8, 14.4],
	[20.6, 18.3, 15.6, 15.6, 17.8, 12.8, 12.8, 13.3, 11.1, 12.8],
	[16.7, 13.9, 14.4, 17.2, 17.2, 13.9, 11.7, 13.3, 11.7, 11.7],
	[13.9, 17.8, 18.9, 18.9, 21.1, 22.8, 17.2, 15.6, 12.2, 12.2],
	[13.3, 15.6, 25, 15.6, 16.1, 17.2, 18.3, 18.3, 20.6, 17.2],
	[14.4, 16.7, 20.6, 23.9, 26.7, 19.4, 13.9, 15.6, 12.2, 17.8],
	[20, 15.6, 19.4, 25.6, 21.7, 23.3, 25.6, 16.7, 16.1, 17.8],
	[15.6, 21.7, 24.4, 27.8, 26.1, 22.8, 25, 16.1, 17.8, 20],
	[22.8, 26.7, 29.4, 31.1, 30.6, 28.9, 25.6, 24.4, 20, 23.9],
	[27.8, 30, 22.8, 25, 24.4, 23.9, 25, 25.6, 25, 26.1],
	[25.6, 30.6, 31.7, 33.3, 28.3, 28.9, 30.6, 32.2, 33.9, 33.3],
	[33.3, 32.8, 29.4, 27.2, 30, 28.9, 21.1, 22.2, 26.1, 25.6],
	[27.8, 26.1, 26.1, 27.8, 33.3, 35, 26.7, 23.9, 23.9, 26.1],
	[22.8, 21.1, 22.2, 23.3, 27.8, 32.2, 34.4, 34.4, 33.3, 30.6],
	[28.3, 26.1, 23.3, 25, 28.3, 25, 28.3, 28.9, 30, 28.3],
	[28.3, 18.3, 21.7, 25, 27.2, 30, 31.7, 22.8, 22.2, 26.7],
	[27.8, 23.9, 25.6, 28.3, 29.4, 23.3, 22.2, 20, 18.9, 19.4],
	[19.4, 18.3, 18.3, 20.6, 16.1, 21.1, 22.8, 24.4, 25, 27.2],
	[26.7, 20.6, 16.7, 17.8, 20, 18.3, 19.4, 21.1, 22.8, 18.3],
	[18.9, 20.6, 22.2, 15.6, 18.3, 17.8, 21.1, 21.7, 18.3, 21.1],
	[15.6, 19.4, 22.8, 23.3, 18.3, 16.1, 18.9, 19.4, 21.1, 17.8],
	[18.3, 16.7, 15, 21.1, 20, 19.4, 15, 17.2, 17.8, 16.1],
	[16.1, 12.8, 15, 19.4, 12.2, 16.1, 13.9, 15, 17.2, 15.6],
	[12.2, 11.1, 10.6, 10, 11.7, 15.6, 12.2, 11.1, 10, 11.1],
	[11.1, 11.1, 13.3, 9.4, 8.9, 8.9, 13.3, 8.9, 8.9, 8.3],
	[8.9, 10, 6.7, 6.7, 7.2, 9.4, 9.4, 7.2, 1.7, 5.6],
	[10, 10.6, 15.6, 10.6, 10, 12.8, 11.1, 15.6, 12.2, 11.7],
	[9.4, 8.9, 7.8, 7.8, 6.7, 6.1, 6.7, 8.9, 8.3, 7.8],
	[5.6, 7.8, 5, 5.6, 5, 4.4, 4.4, 5, 7.2, 5.6],
];

export function generateHeatmapData(): HeatMapData[] {
	const date = new Date(Date.UTC(2018, 0, 1, 12, 0, 0, 0));
	return rawData.map(data => {
		const time = (date.getTime() / 1000) as Time;
		date.setUTCDate(date.getUTCDate() + 1);
		return {
			time,
			cells: data.map((cellAmount: number, index: number) => {
				return {
					high: 10 + index * 10,
					low: 0 + index * 10,
					amount: cellAmount,
				};
			}),
		};
	});
}
