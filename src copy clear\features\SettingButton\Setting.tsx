import React, { useState, useEffect, useCallback, useMemo, useRef, memo } from 'react';
import { createPortal } from 'react-dom';
import { AppSettings, MarketType, VisibleColumns } from '@/shared/index';
import TableTab from './Tabs/TableTab';
import GeneralTab from './Tabs/GeneralTab';
import ChartTab from './Tabs/ChartTab';
import CandlesTab from './Tabs/CandlesTab';
import { Icon, type IconName } from '@/shared/ui/icons/all_Icon';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { Button } from '@/shared/ui/button';
import {
    useAppSettingsStore,
    selectResetSettings,
} from '@/shared/store/settingsStore';
import { cn } from '@/shared/lib/utils';

// --- Заглушки для состояния (значения по умолчанию) --- 

// --- Типы для вкладок --- 
type ActiveTab = 'table' | 'main' | 'chart' | 'candles';

// Анимации для панели - теперь пустые объекты
const panelVariants = {};

// Анимации для контента вкладок - теперь пустые объекты
const tabContentVariants = {};

// --- SVG Source Helper ---
const getSvgSrc = (iconUrl: any): string => {
    if (iconUrl && typeof iconUrl === 'object' && iconUrl.src) {
        return iconUrl.src;
    }
    if (typeof iconUrl === 'string') {
        return iconUrl;
    }
    return '';
};

// --- Пропсы для SettingsButton --- 
interface SettingsButtonProps {
  dragConstraintsRef?: React.RefObject<HTMLDivElement | null>; 
}

// --- Основной компонент SettingsButton --- 
const SettingsButton: React.FC<SettingsButtonProps> = ({ dragConstraintsRef }) => {
  // --- Zustand Store Access ---
  const resetSettingsStore = useAppSettingsStore(selectResetSettings);

  // --- Локальное состояние --- 
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<ActiveTab>('table');
  const [resetClickCount, setResetClickCount] = useState(0);
  const resetTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const panelRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [isClient, setIsClient] = useState(false);

  // State for initial position
  const [initialPosition, setInitialPosition] = useState<{ top: number | string; left: number | string }>({ top: '50px', left: '50px' });

  // Эффект, который выполняется только при монтировании на клиенте
  useEffect(() => {
    setIsClient(true);
  }, []);

  // --- ОБРАБОТЧИКИ ---
  const handleResetClick = useCallback(() => {
    const newCount = resetClickCount + 1;
    setResetClickCount(newCount);
    if (resetTimeoutRef.current) clearTimeout(resetTimeoutRef.current);

    if (newCount >= 5) {
      resetSettingsStore();
      setIsOpen(false); 
      setResetClickCount(0);
    } else {
      resetTimeoutRef.current = setTimeout(() => {
        setResetClickCount(0);
        resetTimeoutRef.current = null;
      }, 2000); 
    }
  }, [resetClickCount, resetSettingsStore]);

  // --- МЕМОИЗАЦИЯ ПРОПСОВ ДЛЯ ВКЛАДОК --- 
  const systemFonts = useMemo(() => 
    ['Inter', 'Verdana', 'Roboto', 'Arial', 'Trebuchet MS', 'Ubuntu', 'system-ui', 'sans-serif'],
    []
  );

  const tableTabSettings = useMemo(() => ({}), []);
  
  const generalTabProps = useMemo(() => ({
    systemFonts: systemFonts,
  }), [
    systemFonts,
  ]);

  const chartTabProps = useMemo(() => ({
    systemFonts: systemFonts,
  }), [
    systemFonts,
  ]);

  const candlesTabProps = useMemo(() => ({}), []);

  // Effect to calculate initial position only when opening
  useEffect(() => {
    if (isOpen && buttonRef.current) {
      requestAnimationFrame(() => { 
        if (buttonRef.current) { 
          const rect = buttonRef.current.getBoundingClientRect();
          setInitialPosition({
            top: rect.bottom + 4,
            left: rect.left
          });
        }
      });
    }
  }, [isOpen]);

  // --- РЕНДЕР КОМПОНЕНТА --- 
  return (
    <> 
      {/* Trigger Button - Replaced with shadcn Button */}
      <Button
        ref={buttonRef}
        variant="ghost"
        size="icon" // Standard icon button size
        className={[
            "group", // Keep group for potential icon styling if needed in future
            isOpen ? "bg-primary/15 rotate-180" : "", // Apply rotation when open
        ].filter(Boolean).join(' ')}
        onClick={() => setIsOpen(!isOpen)}
        title={isOpen ? "Close Settings" : "Open Settings"}
        data-state={isOpen ? 'open' : 'closed'}
        aria-label="Open settings panel"
      >
        <Icon 
          name="Settings" 
          className={cn(
              "text-muted-foreground", // Default color
              "group-hover:text-foreground" // Change color on Button hover
          )} 
        />
      </Button>

      {/* Portal for Panel */}
      {isClient && isOpen && typeof document !== 'undefined' && createPortal(
          <div 
            ref={panelRef} 
            className="w-[600px] h-[600px] bg-[rgba(35,38,46,0.9)] backdrop-blur-md backdrop-saturate-160 border border-border/10 rounded-lg shadow-lg overflow-hidden flex flex-col text-sm text-foreground z-40 cursor-default"
            style={{ 
                position: 'fixed',
                top: initialPosition.top, 
                left: initialPosition.left,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between h-8 px-2 bg-muted/80 border-b border-border/10 flex-shrink-0">
              <div 
                  className="flex items-center flex-grow h-full gap-1.5"
              > 
                  <Icon name="Settings" className="group relative h-5 w-5 shrink-0 animate-[spin_4s_linear_infinite] pointer-events-none" />
                  <h3 className="m-0 text-[13px] font-semibold text-foreground select-none leading-none pointer-events-none">Settings</h3>
              </div>
              <div className="flex items-center gap-1 h-full" onPointerDown={(e) => e.stopPropagation()}> 
                  <button 
                      className="bg-transparent border-none text-muted-foreground leading-none cursor-pointer p-0 transition-all duration-150 ease-in-out rounded-full flex items-center justify-center w-5 h-5 flex-shrink-0 hover:text-foreground hover:scale-105 hover:bg-accent/10" 
                      onClick={handleResetClick}
                      title={`Reset (${resetClickCount}/5)`}
                  >
                      <Icon name="SettingsReset" className="mr-2 pointer-events-none" />
                  </button>
                  <button 
                    className="bg-transparent border-none text-muted-foreground leading-none cursor-pointer p-0 transition-all duration-150 ease-in-out rounded-full flex items-center justify-center w-5 h-5 flex-shrink-0 hover:text-foreground hover:scale-105 hover:bg-accent/10 text-lg font-light" 
                    onClick={() => setIsOpen(false)}
                    title="Close"
                  >
                    ✕
                  </button>
              </div>
            </div>
            <div className="flex flex-grow">
              <div 
                  className="flex flex-col w-[100px] flex-shrink-0 p-1.5 border-r border-border/10 overflow-y-auto gap-1 bg-muted/60" 
                  onPointerDown={(e) => e.stopPropagation()}
              >
                 {(['table', 'main', 'chart', 'candles'] as ActiveTab[]).map((tabId) => (
                    <button 
                        key={tabId}
                        className={[
                            "w-full text-left px-2 py-1 bg-transparent border-none text-muted-foreground text-[13px] font-medium rounded-md cursor-pointer transition-all duration-150 ease-in-out flex items-center gap-1.5 relative",
                            "hover:bg-accent/10 hover:text-foreground",
                            activeTab === tabId && "bg-settings-primary/15 text-settings-primary font-semibold"
                        ].filter(Boolean).join(' ')}
                        onClick={() => setActiveTab(tabId)}
                        data-active={activeTab === tabId}
                        onPointerDown={(e) => e.stopPropagation()} 
                    >
                        {tabId.charAt(0).toUpperCase() + tabId.slice(1)}
                    </button>
                 ))}
              </div>
              <ScrollArea className="flex-grow">
                <div className="p-3 h-full">
                  <div
                    key={activeTab}
                    className="h-full"
                  >
                    {activeTab === 'table' && <TableTab {...tableTabSettings} />} 
                    {activeTab === 'main' && <GeneralTab {...generalTabProps} />} 
                    {activeTab === 'chart' && <ChartTab {...chartTabProps} />} 
                    {activeTab === 'candles' && <CandlesTab {...candlesTabProps} />} 
                  </div>
                </div>
              </ScrollArea>
            </div>
          </div>,
          document.body
      )}
    </>
  );
};

export default memo(SettingsButton);