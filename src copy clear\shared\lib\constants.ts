import { TimeframeOption } from '../types/settings.types';
import type { MarketType } from '../types';

export const DEFAULT_TIMEFRAMES: TimeframeOption[] = [
  { value: '1m', label: '1m' },
  { value: '3m', label: '3m' },
  { value: '5m', label: '5m' },
  { value: '15m', label: '15m' },
  { value: '30m', label: '30m' },
  { value: '1h', label: '1h' },
  { value: '2h', label: '2h' },
  { value: '4h', label: '4h' },
  { value: '6h', label: '6h' },
  { value: '8h', label: '8h' },
  { value: '12h', label: '12h' },
  { value: '1d', label: '1d' },
  { value: '3d', label: '3d' },
  { value: '1w', label: '1w' },
  { value: '1M', label: '1M' }
];

// Chart count options for screener
export const SCREENER_CHART_COUNTS = [4, 6, 9, 12, 16] as const;
export type ScreenerChartCount = typeof SCREENER_CHART_COUNTS[number];

// Update intervals for auto-update (in seconds)
export const SCREENER_UPDATE_INTERVALS = [
  { value: 0, label: 'Off' },
  { value: 3, label: '3s' },
  { value: 5, label: '5s' },
  { value: 10, label: '10s' },
  { value: 15, label: '15s' },
  { value: 30, label: '30s' },
  { value: 60, label: '1m' },
  { value: 120, label: '2m' },
  { value: 300, label: '5m' }
] as const;

export const CHART_EVENT_NAMESPACE = 'chart';

export enum ChartEventType {
  CROSSHAIR_MOVE = 'crosshair-move',
  VISIBLE_RANGE_CHANGE = 'visible-range-change',
  TIME_SCALE_RESET = 'time-scale-reset',
}



