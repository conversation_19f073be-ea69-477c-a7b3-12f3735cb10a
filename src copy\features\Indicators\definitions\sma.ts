// src/indicators/definitions/sma.ts
import type {
  IndicatorDefinition,
  IndicatorParam,
  IndicatorOutput,
  IndicatorCalculationResult,
  KlineData,
} from '@/shared/index';
import { LineStyle } from 'lightweight-charts';

// --- Calculation Function (Moved from calculations.ts) ---
/**
 * Calculates Simple Moving Average (SMA).
 * @param data - Array of numbers (e.g., closing prices).
 * @param period - The period over which to calculate the SMA.
 * @returns Array of SMA values or undefined for periods where calculation isn't possible.
 */
const calculateSMA = (
  data: number[],
  period: number
): (number | undefined)[] => {
  if (period <= 0 || data.length < period) {
    return Array(data.length).fill(undefined);
  }

  const results: (number | undefined)[] = Array(period - 1).fill(undefined);
  let sum = 0;

  // Calculate sum for the first period
  for (let i = 0; i < period; i++) {
    sum += data[i];
  }
  results.push(sum / period);

  // Calculate subsequent SMAs using a rolling window
  for (let i = period; i < data.length; i++) {
    sum -= data[i - period];
    sum += data[i];
    results.push(sum / period);
  }

  return results;
};

// Define specific parameter types for SMA
interface SmaParams {
  source: 'open' | 'high' | 'low' | 'close'; // Добавим выбор источника цены
  length: number;
  [key: string]: unknown; // Add index signature as workaround
}

const smaParameters: ReadonlyArray<IndicatorParam> = [
  {
    id: 'source',
    name: 'Source',
    type: 'select',
    defaultValue: 'close',
    options: ['open', 'high', 'low', 'close'],
  },
  {
    id: 'length',
    name: 'Length',
    type: 'number',
    defaultValue: 14,
    min: 1,
    step: 1,
  },
];

const smaOutputs: ReadonlyArray<IndicatorOutput> = [
  {
    id: 'sma', // Unique ID for this output line
    name: 'SMA', // Display name for this output
    type: 'line',
    color: '#2962FF', // Example blue color
    lineWidth: 1,
    lineStyle: LineStyle.Solid,
  },
];

// This function now acts as a wrapper to call the local calculateSMA
const calculateSmaWrapper = (
  ohlcv: ReadonlyArray<KlineData>,
  params: SmaParams
): IndicatorCalculationResult[] => {
  const sourceData = ohlcv.map((kline) => kline[params.source]);
  const smaValues = calculateSMA(sourceData, params.length); // Calls the local function

  return smaValues.map((value) => ({
    sma: value, // The key matches the output id 'sma'
  }));
};

export const SMA: IndicatorDefinition<SmaParams> = {
  id: 'SMA',
  name: 'Simple Moving Average',
  shortName: 'SMA', // Added short name
  description: 'Calculates the average of a selected range of prices over a period.',
  parameters: smaParameters,
  outputs: smaOutputs,
  calculate: calculateSmaWrapper, // Use the wrapper function
  group: 'trend', // Example group
  pane: 'overlay', // Render SMA on the main chart overlay
}; 