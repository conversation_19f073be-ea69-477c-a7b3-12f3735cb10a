import { db, schema } from '@/db';
import { Kline, KlineIntervalSchema } from '@/shared/schemas/market.schema';
import { MarketType } from '@/shared/schemas/market.schema';
import { desc, eq, and } from 'drizzle-orm';

// The parameters for fetching kline data from the API routes
export interface KlineParams {
  symbol: string;
  interval: string;
  marketType: MarketType;
  limit?: number;
  endTime?: number;
}

/**
 * @module klineService
 * @description This module provides functions for fetching Kline data from the database.
 */

/**
 * Fetches historical Kline data for a given symbol, interval, and market type.
 * It queries the database and converts the records to the application's standard Kline format (with numbers).
 *
 * @param params - The parameters for fetching the Kline data.
 * @returns {Promise<Partial<Kline>[]>} A promise that resolves to an array of partial Kline data, sorted from oldest to newest.
 */
export async function getKlines(params: KlineParams): Promise<Partial<Kline>[]> {
  const { symbol, interval, marketType, limit = 1000 } = params;

  // Validate interval against the schema
  const parsedInterval = KlineIntervalSchema.safeParse(interval);
  if (!parsedInterval.success) {
    throw new Error(`Invalid interval: ${interval}`);
  }

  const klinesFromDb = await db
    .select({
      symbol: schema.candles.symbol,
      interval: schema.candles.interval,
      marketType: schema.candles.marketType,
      openTime: schema.candles.openTime,
      open: schema.candles.open,
      high: schema.candles.high,
      low: schema.candles.low,
      close: schema.candles.close,
      volume: schema.candles.volume,
    })
    .from(schema.candles)
    .where(
      and(
        eq(schema.candles.symbol, symbol),
        eq(schema.candles.interval, interval),
        eq(schema.candles.marketType, marketType)
      )
    )
    .orderBy(desc(schema.candles.openTime))
    .limit(limit);

  // Map the database records to a format that matches the Kline type as much as possible.
  const klines = klinesFromDb.map((candle) => {
    return {
      symbol: candle.symbol,
      interval: parsedInterval.data,
      marketType: candle.marketType as MarketType,
      openTime: candle.openTime.getTime(),
      open: parseFloat(candle.open),
      high: parseFloat(candle.high),
      low: parseFloat(candle.low),
      close: parseFloat(candle.close),
      volume: parseFloat(candle.volume),
    };
  });

  return klines.reverse();
} 