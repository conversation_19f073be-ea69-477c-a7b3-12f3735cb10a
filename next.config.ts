import type { NextConfig } from "next";

// Define common SVGR options
const svgrOptions = {
  icon: false,
  dimensions: false,
  exportType: 'default',
  svgProps: {
    width: '{props.width}',
    height: '{props.height}',
    stroke: '{props.stroke}',
    fill: '{props.fill}',
    strokeWidth: '{props.strokeWidth}',
    className: '{props.className}',
    style: '{props.style}',
  },
  expandProps: 'end',
  ref: true,
  svgo: true,
  svgoConfig: {
    plugins: [
      {
        name: 'preset-default',
        params: {
          overrides: {
            removeViewBox: false,
            convertColors: { currentColor: false },
            cleanupIds: false,
          },
        },
      },
      'removeDimensions',
      {
        name: 'removeAttrs',
        params: {
          attrs: '(fill|stroke|stroke-width)',
        },
      },
    ],
  },
};


const nextConfig: NextConfig = {
  reactStrictMode: true,

  // Настройка API прокси для перенаправления запросов на правильный порт
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:3005/api/:path*',
      },
    ];
  },

  // Turbopack specific configuration for SVGR
  turbopack: {
    rules: {
      '*.svg': { // Use glob pattern
        loaders: [{
          loader: '@svgr/webpack',
          options: svgrOptions as any, // Use common options
        }],
        as: '*.js', // Treat output as JavaScript module
      },
    },
  },

  // Add webpack config to fix source map issues in dev
  // AND for non-turbopack builds (`next build`)
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      config.devtool = 'eval-source-map';
    }

    // Enable polling for file changes inside Docker
    // This is needed for hot reload to work properly with mounted volumes
    if (dev) {
      // Check if we're in a Docker environment or if polling is explicitly enabled
      if (process.env.WATCHPACK_POLLING === "true") {
        console.log("🔄 Enabling polling for file changes (Docker compatibility)");
        config.watchOptions = {
          poll: 1000, // Check for changes every 1000ms (1 second)
          aggregateTimeout: 300, // Delay before rebuilding once the first file changed
          ignored: /node_modules/,
        };
      }
    }

    // Add SVGR loader for Webpack
    // Find the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule: any) =>
      rule.test?.test?.('.svg'),
    );

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...(fileLoaderRule as object), // Cast to object to satisfy spread type
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: /\.[jt]sx?$/,
        resourceQuery: { not: /url/ }, // exclude if *.svg?url
        use: [{
          loader: '@svgr/webpack',
          options: svgrOptions, // Apply common options here
        }],
      }
    );

     // Modify the file loader rule to ignore *.svg, since we have it handled now.
     if (fileLoaderRule && typeof fileLoaderRule === 'object' && fileLoaderRule !== null) {
      (fileLoaderRule as { exclude?: RegExp }).exclude = /\.svg$/i; // Type assertion for exclude
     }

    return config;
  },

  // Ensure other configurations are preserved
  // `serverExternalPackages` теперь должен быть на верхнем уровне, а не внутри experimental
  serverExternalPackages: ['@prisma/client', 'bcrypt'],
};

export default nextConfig;
