import { getBaseUrl } from '../lib/utils';
import type { MarketType, AppSymbolInfo, Ticker, Kline } from '../types';
import { useState, useCallback, useEffect } from 'react';
import { useMarketStore, getKlinesCacheKey } from '../store/marketStore';

// --- Content from status.ts ---
interface HealthCheckResponse {
  status: string;
  message: string;
}

interface SystemStatsResponse {
  status: string;
  cacheStats?: {
    size: number;
    hitRate: number;
    memoryUsage: number;
  };
  dbStats?: {
    connections: number;
    queriesPerSecond: number;
    latency: number;
  };
  systemInfo?: {
    uptime: number;
    memory: {
      total: number;
      used: number;
      free: number;
    };
    cpu: {
      usage: number;
      cores: number;
    };
  };
}

interface SystemStatusResponse {
  status: string;
  services: {
    cache: 'ok' | 'error' | 'warning';
    database: 'ok' | 'error' | 'warning';
    exchange: 'ok' | 'error' | 'warning';
  };
  websocket: {
    status: 'online' | 'offline';
    connections: number;
  };
  message?: string;
}

export const statusApi = {
  async healthCheck(): Promise<HealthCheckResponse> {
    try {
      const url = `${getBaseUrl()}/`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`API health check failed: ${response.status} ${response.statusText}`);
      }
      return await response.json() as HealthCheckResponse;
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  },
  async getSystemStats(): Promise<SystemStatsResponse> {
    try {
      const url = `${getBaseUrl()}/db-test`; // This endpoint provides DB connection status
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to get system stats: ${response.status} ${response.statusText}`);
      }
      return await response.json() as SystemStatsResponse;
    } catch (error) {
      console.error('Failed to get system stats:', error);
      throw error;
    }
  },
  async getSystemStatus(): Promise<SystemStatusResponse> {
    try {
      // The db-test endpoint provides detailed status of database connections
      const url = `${getBaseUrl()}/db-test`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to get system status: ${response.status} ${response.statusText}`);
      }
      const dbResponse = await response.json();
      return {
        status: dbResponse.status,
        services: {
          cache: dbResponse.dragonfly === 'connected' ? 'ok' : 'error',
          database: dbResponse.questdb === 'connected' ? 'ok' : 'error',
          exchange: 'ok', // Currently hardcoded as we don't have a direct exchange status check
        },
        websocket: {
          status: 'online', // Currently hardcoded - future: can derive from WS connection state
          connections: 0,   // Currently hardcoded - future: endpoint could return active connection count
        },
        message: dbResponse.message,
      };
    } catch (error) {
      console.error('Failed to get system status:', error);
      throw error;
    }
  },
};

// --- Content from symbols.ts ---
interface SymbolsResponse {
  source: 'cache' | 'exchange';
  symbols: AppSymbolInfo[];
}

export const symbolsApi = {
  async getSymbols(marketType: MarketType): Promise<AppSymbolInfo[]> {
    try {
      const url = `${getBaseUrl()}/api/symbols/${marketType}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json() as SymbolsResponse;
      return data.symbols;
    } catch (error) {
      console.error(`Failed to fetch symbols for ${marketType}:`, error);
      throw error;
    }
  },
};

// --- Content from tickers.ts ---
interface TickersResponse {
  source: 'cache' | 'exchange';
  tickers: Ticker[];
}

export interface TickersParams {
  marketType: MarketType;
}

export const tickersApi = {
  async getTickers(params: TickersParams): Promise<Ticker[]> {
    const { marketType } = params;
    try {
      const url = `${getBaseUrl()}/api/tickers/${marketType}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json() as TickersResponse;
      return data.tickers;
    } catch (error) {
      console.error(`Failed to fetch tickers for ${marketType}:`, error);
      throw error;
    }
  },
  async getTickerBySymbol(marketType: MarketType, symbol: string): Promise<Ticker | null> {
    try {
      const tickers = await this.getTickers({ marketType });
      return tickers.find(ticker => ticker.symbol === symbol) || null;
    } catch (error) {
      console.error(`Failed to fetch ticker for ${marketType}/${symbol}:`, error);
      throw error;
    }
  },
};

// --- Content from klines.ts ---
interface KlinesResponse {
  source: 'cache' | 'db' | 'exchange';
  candles: Kline[];
}

export interface KlineParams {
  symbol: string;
  interval: string;
  marketType: MarketType;
  limit?: number;
  startTime?: number;
  endTime?: number;
}

export const klinesApi = {
  async getKlines(params: KlineParams): Promise<Kline[]> {
    const { symbol, interval, marketType, limit = 1000, startTime, endTime } = params;
    try {
      let url = `${getBaseUrl()}/api/candles/${marketType}/${symbol}/${interval}?limit=${limit}`;
      if (startTime) url += `&startTime=${startTime}`;
      if (endTime) url += `&endTime=${endTime}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      const data = await response.json() as KlinesResponse;
      return data.candles;
    } catch (error) {
      console.error(`Failed to fetch klines for ${marketType}/${symbol}/${interval}:`, error);
      throw error;
    }
  },
  async getLastKline(params: Omit<KlineParams, 'limit' | 'startTime' | 'endTime'>): Promise<Kline | null> {
    try {
      const candles = await this.getKlines({ ...params, limit: 1 });
      return candles.length > 0 ? candles[0] : null;
    } catch (error) {
      console.error(`Failed to fetch last kline for ${params.marketType}/${params.symbol}/${params.interval}:`, error);
      throw error;
    }
  },
};

/**
 * Hook for fetching historical klines data and storing them in market store
 * @param symbol - Trading symbol
 * @param interval - Kline interval
 * @param marketType - Market type (spot or futures)
 * @param limit - Number of candles to fetch
 */
export function useHistoricalKlinesQuery(
  symbol: string,
  interval: string,
  marketType: MarketType,
  limit: number = 1000
) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<Kline[]>([]);
  
  const setKlines = useMarketStore(state => state.setKlines);
  const cacheKey = getKlinesCacheKey(symbol, interval, marketType);
  
  const fetchData = useCallback(async () => {
    if (!symbol || !interval || !marketType) {
      setIsLoading(false);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const klines = await klinesApi.getKlines({
        symbol,
        interval,
        marketType,
        limit
      });
      
      setData(klines);
      // Save to store
      setKlines(cacheKey, klines);
      setIsLoading(false);
    } catch (err) {
      console.error("Failed to fetch historical klines:", err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setIsLoading(false);
    }
  }, [symbol, interval, marketType, limit, cacheKey, setKlines]);
  
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  
  return {
    isLoading,
    error,
    data,
    refetch: fetchData
  };
}

// Export all relevant types and interfaces that might be used externally
export type {
  HealthCheckResponse,
  SystemStatsResponse,
  SystemStatusResponse,
  SymbolsResponse,
  TickersResponse,
  KlinesResponse,
  // KlineParams is already exported
  // TickersParams is already exported
  AppSymbolInfo, // Assuming this is used externally
  Ticker,        // Assuming this is used externally
  Kline,         // Assuming this is used externally
  MarketType     // Assuming this is used externally
}; 