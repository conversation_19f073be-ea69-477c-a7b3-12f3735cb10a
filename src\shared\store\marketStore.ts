import { create } from 'zustand';
import { Kline, MarketType, ProcessedTicker, Ticker } from '@/shared/index';

// Default market type
export const DEFAULT_MARKET_TYPE: MarketType = MarketType.Spot;

// Типы для ключей кеша
type KlineCacheKey = string;

// Interface for the market data state
interface MarketState {
  // Ticker data
  tickers: Record<string, Ticker>;
  // Processed tickers by market type
  processedSpotTickers: ProcessedTicker[];
  processedFuturesTickers: ProcessedTicker[];
  // Selected market type
  selectedMarketType: MarketType;
  // Kline data by symbol, interval and market type
  klines: Record<KlineCacheKey, Kline[]>;
  // Loading states
  isLoadingTickers: boolean;
  isLoadingKlines: Record<KlineCacheKey, boolean>;
  // Error states
  tickersError: string | null;
  klinesError: Record<KlineCacheKey, string | null>;
  // Last update timestamps
  tickersLastUpdate: number | null;
  klinesLastUpdate: Record<KlineCacheKey, number | null>;

  // Actions
  setTickers: (tickers: Ticker[]) => void;
  setSelectedMarketType: (marketType: MarketType) => void;
  setKlines: (symbol: string, interval: string, klines: Kline[]) => void;
  appendKlines: (symbol: string, interval: string, klines: Kline[]) => void;
  updateKline: (symbol: string, interval: string, kline: Kline) => void;
  clearData: () => void;
  setLoadingTickers: (isLoading: boolean) => void;
  setLoadingKlines: (symbol: string, interval: string, isLoading: boolean) => void;
  setTickersError: (error: string | null) => void;
  setKlinesError: (symbol: string, interval: string, error: string | null) => void;
}

// Helper function to create cache key for klines
export const getKlinesCacheKey = (symbol: string, interval: string, marketType: MarketType = DEFAULT_MARKET_TYPE): KlineCacheKey => {
  return `${symbol}:${interval}:${marketType}`;
};

// Initialize the market store
export const useMarketStore = create<MarketState>((set) => ({
  // Initial state
  tickers: {},
  processedSpotTickers: [],
  processedFuturesTickers: [],
  selectedMarketType: DEFAULT_MARKET_TYPE,
  klines: {},
  isLoadingTickers: false,
  isLoadingKlines: {},
  tickersError: null,
  klinesError: {},
  tickersLastUpdate: null,
  klinesLastUpdate: {},

  // Actions
  setTickers: (tickers: Ticker[]) => set((state) => {
    const newTickers = { ...state.tickers };
    tickers.forEach((ticker) => {
      newTickers[ticker.symbol] = ticker;
    });

    // Обновляем также обработанные тикеры
    const processedTickers = tickers.map(ticker => {
      const symbol = ticker.symbol;
      const parts = symbol.match(/^(.+?)([A-Z]{3,}|[1-9A-Z]{4,})$/);
      const baseAsset = parts ? parts[1] : symbol;
      const quoteAsset = parts ? parts[2] : '';
      const priceChangeAbs = Math.abs(ticker.priceChange);

      return {
        ...ticker,
        baseAsset,
        quoteAsset,
        priceChangeAbs,
      } as ProcessedTicker;
    });

    const processedSpotTickers = processedTickers.filter(ticker => ticker.marketType === MarketType.Spot);
    const processedFuturesTickers = processedTickers.filter(ticker => ticker.marketType === MarketType.Futures);

    return { 
      tickers: newTickers, 
      processedSpotTickers,
      processedFuturesTickers,
      tickersLastUpdate: Date.now(), 
      tickersError: null 
    };
  }),

  setSelectedMarketType: (marketType: MarketType) => set({ selectedMarketType: marketType }),

  setKlines: (symbol: string, interval: string, klines: Kline[]) => set((state) => {
    const cacheKey = getKlinesCacheKey(symbol, interval);
    const newKlines = { ...state.klines };
    newKlines[cacheKey] = klines;

    const newLastUpdate = { ...state.klinesLastUpdate };
    newLastUpdate[cacheKey] = Date.now();

    const newErrors = { ...state.klinesError };
    newErrors[cacheKey] = null;

    return { 
      klines: newKlines, 
      klinesLastUpdate: newLastUpdate, 
      klinesError: newErrors 
    };
  }),

  appendKlines: (symbol: string, interval: string, newKlinesToAppend: Kline[]) => set((state) => {
    const cacheKey = getKlinesCacheKey(symbol, interval);
    const existingKlines = state.klines[cacheKey] || [];
    const combinedKlines = [...existingKlines, ...newKlinesToAppend];

    // Sort by openTime for consistency
    combinedKlines.sort((a, b) => a.openTime - b.openTime);

    // Remove duplicates based on openTime
    const uniqueKlines = combinedKlines.filter((kline, index, array) => {
      return index === 0 || kline.openTime !== array[index - 1].openTime;
    });

    const newKlines = { ...state.klines };
    newKlines[cacheKey] = uniqueKlines;

    const newLastUpdate = { ...state.klinesLastUpdate };
    newLastUpdate[cacheKey] = Date.now();

    return { 
      klines: newKlines, 
      klinesLastUpdate: newLastUpdate 
    };
  }),

  updateKline: (symbol: string, interval: string, updatedKline: Kline) => set((state) => {
    const cacheKey = getKlinesCacheKey(symbol, interval);
    const existingKlines = state.klines[cacheKey] || [];
    
    // Find if this kline already exists (by openTime)
    const index = existingKlines.findIndex((k: Kline) => k.openTime === updatedKline.openTime);
    
    let newKlinesList;
    if (index >= 0) {
      // Update existing kline
      newKlinesList = [...existingKlines];
      newKlinesList[index] = updatedKline;
    } else {
      // Add new kline and sort
      newKlinesList = [...existingKlines, updatedKline];
      newKlinesList.sort((a, b) => a.openTime - b.openTime);
    }
    
    const newKlines = { ...state.klines };
    newKlines[cacheKey] = newKlinesList;

    const newLastUpdate = { ...state.klinesLastUpdate };
    newLastUpdate[cacheKey] = Date.now();

    return { 
      klines: newKlines, 
      klinesLastUpdate: newLastUpdate 
    };
  }),
  
  clearData: () => set({
    tickers: {},
    processedSpotTickers: [],
    processedFuturesTickers: [],
    klines: {},
    tickersLastUpdate: null,
    klinesLastUpdate: {},
    tickersError: null,
    klinesError: {}
  }),

  setLoadingTickers: (isLoading: boolean) => set({ isLoadingTickers: isLoading }),
  
  setLoadingKlines: (symbol: string, interval: string, isLoading: boolean) => set((state) => {
    const cacheKey = getKlinesCacheKey(symbol, interval);
    const newLoadingState = { ...state.isLoadingKlines };
    newLoadingState[cacheKey] = isLoading;
    return { isLoadingKlines: newLoadingState };
  }),
  
  setTickersError: (error: string | null) => set({ tickersError: error }),
  
  setKlinesError: (symbol: string, interval: string, error: string | null) => set((state) => {
    const cacheKey = getKlinesCacheKey(symbol, interval);
    const newErrors = { ...state.klinesError };
    newErrors[cacheKey] = error;
    return { klinesError: newErrors };
  })
}));
