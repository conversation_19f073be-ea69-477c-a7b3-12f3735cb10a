import React, { memo, useMemo, useCallback } from 'react';
import { AppSettings } from '@/shared/index'; // Импортируем AppSettings
import { MainToggleButton, Select, Slider } from './Controls';
import { debounce } from '@/shared/lib/utils'; // Import debounce
import { 
    useAppSettingsStore,
    selectIsSyncEnabled,
    selectUiFontFamily,
    selectUiFontSize,
    selectSetSetting
} from '@/shared/store/settingsStore';

// UPDATED: Interface for the tab component
interface GeneralTabProps {
  systemFonts: string[]; // systemFonts can still be passed if generated in Setting.tsx and shared
  // REMOVED: isSyncEnabled, uiFontFamily, uiFontSize - will be fetched from store
  // REMOVED: handleGenericSettingChange, handleSyncChange - will use store actions directly
}

// --- GeneralTab ---
const GeneralTab: React.FC<GeneralTabProps> = memo(({
  systemFonts,
}) => {
  // --- Get data from Zustand store ---
  const isSyncEnabled = useAppSettingsStore(selectIsSyncEnabled) ?? false;
  const uiFontFamily = useAppSettingsStore(selectUiFontFamily) || 'Inter';
  const uiFontSize = useAppSettingsStore(selectUiFontSize) || 12;
  const setSetting = useAppSettingsStore(selectSetSetting);

  // --- Local Handlers that call store actions ---
  const handleSyncChange = useCallback(() => {
    setSetting('isSyncEnabled', !isSyncEnabled);
  }, [isSyncEnabled, setSetting]);

  const handleUiFontFamilyChange = useCallback((value: string) => {
    setSetting('uiFontFamily', value);
  }, [setSetting]);

  // === Create debounced handler for slider ===
  const debouncedFontSizeChange = useMemo(
    () => debounce((value: number) => setSetting('uiFontSize', value), 150),
    [setSetting]
  );

  return (
      <div className="space-y-6">
          <div className="setting-group">
              <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-4 pb-2 border-b border-border/10">Appearance & Sync</h4>
              <MainToggleButton
                  label="Sync Crosshair"
                  active={isSyncEnabled}
                  onChange={handleSyncChange} // Use local handler
              />
          </div>

          <div className="setting-group">
              <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-4 pb-2 border-b border-border/10">UI Font</h4>
              <Select
                  label="Font Family"
                  value={uiFontFamily}
                  onChange={handleUiFontFamilyChange} // Use local handler
                  options={systemFonts}
                  isFont
              />
              <Slider
                  label="Font Size"
                  min={8}
                  max={16}
                  step={1}
                  unit="px"
                  value={uiFontSize}
                  onChange={debouncedFontSizeChange} // Use local handler
              />
          </div>
      </div>
  );
});

export default GeneralTab; 