# Volatility Indicators

Volatility indicators measure the rate of movement regardless of its direction.

- [Volatility Indicators](#volatility-indicators)
  - [Acceleration Bands (AB)](#acceleration-bands-ab)
  - [Average True Range (ATR)](#average-true-range-atr)
  - [Bollinger Bands (BB)](#bollinger-bands-bb)
  - [Bollinger Band Width (BBW)](#bollinger-band-width-bbw)
  - [Chandelier Exit (CE)](#chandelier-exit-ce)
  - [Donchian Channel (DC)](#donchian-channel-dc)
  - [Keltner Channel (KC)](#keltner-channel-kc)
  - [Moving Standard Deviation (MSTD)](#moving-standard-deviation-mstd)
  - [Projection Oscillator (PO)](#projection-oscillator-po)
  - [True Range (TR)](#true-range-tr)
  - [Ulcer Index (UI)](#ulcer-index-ui)
  - [Disclaimer](#disclaimer)
  - [License](#license)

**NOTE:** All configuration objects for all indicators are optional. If no configuration object is passed, the default configuration will be used. Likewise, you may also partially pass a configuration object, and the default values will be used for the missing properties.

## Acceleration Bands (AB)

The [accelerationBands](./accelerationBands.ts) plots upper and lower envelope bands around a simple moving average.

```
Upper Band = SMA(High * (1 + 4 * (High - Low) / (High + Low)))
Middle Band = SMA(Closing)
Lower Band = SMA(Low * (1 + 4 * (High - Low) / (High + Low)))
```

```TypeScript
import { ab } from 'indicatorts';

const defaultConfig = { period: 20, multiplier: 4 };
const { upper, middle, lower } = ab(highs, lows, closings, defaultConfig);

// Alternatively:
// const { upper, middle, lower } = accelerationBands(highs, lows, closings, defaultConfig);
```

## Average True Range (ATR)

The [atr](./averageTrueRange.ts) function calculates a technical analysis indicator that measures market volatility by decomposing the entire range of stock prices for that period.

```
TR = Max((High - Low), (High - Closing), (Closing - Low))
ATR = 14-Period SMA TR
```

```TypeScript
import { atr } from 'indicatorts';

const defaultConfig = { period: 14 };
const { trLine, atrLine } = atr(highs, lows, closings, defaultConfig);

// Alternatively:
// const { trLine, atrLine } = averageTrueRange(highs, lows, closings, defaultConfig);
```

## Bollinger Bands (BB)

The [bollingerBands](./bollingerBands.ts) function calculates the bollinger bands, middle band, upper band, lower band, provides identification of when a stock is oversold or overbought.

```
Middle Band = 20-Period SMA.
Upper Band = 20-Period SMA + 2 (20-Period Std)
Lower Band = 20-Period SMA - 2 (20-Period Std)
```

```TypeScript
import { bb } from 'indicatorts';

const defaultConfig = { period: 20 };
const { upper, middle, lower } = bb(closings, defaultConfig);

// Alternatively:
// const { upper, middle, lower } = bollingerBands(closings, defaultConfig);
```

## Bollinger Band Width (BBW)

The [bollingerBandsWidth](./bollingerBandsWidth.ts) function measures the percentage difference between the upper band and the lower band. It decreases as Bollinger Bands narrows and increases as Bollinger Bands widens.

During a period of rising price volatility the band width widens, and during a period of low market volatility band width contracts.

```
Band Width = (Upper Band - Lower Band) / Middle Band
```

```TypeScript
import { bb, bbw } from 'indicatorts';

const bbResult = bb(closings);

const defaultConfig = { period: 90 };
const { width, widthEma } = bbw(bbResult, defaultConfig);

// Alternatively:
// const { width, widthEma } = bollingerBandsWidth(bbResult, defaultConfig);
```

## Chandelier Exit (CE)

The [chandelierExit](./chandelierExit.ts) function sets a trailing stop-loss based on the Average True Value (ATR).

```
Chandelier Exit Long = 22-Period SMA High - ATR(22) * 3
Chandelier Exit Short = 22-Period SMA Low + ATR(22) * 3
```

```TypeScript
import { ce } from 'indicatorts';

const defaultConfig = { period: 22 };
const { long, short } = ce(highs, lows, closings, defaultConfig);

// Alternatively:
// const { long, short } = chandelierExit(highs, lows, closings, defaultConfig);
```

## Donchian Channel (DC)

The [donchianChannel](./donchianChannel.ts) calculates three lines generated by moving average calculations that comprise an indicator formed by upper and lower bands around a midrange or median band. The upper band marks the highest price of an asset while the lower band marks the lowest price of an asset, and the area between the upper and lower bands represents the Donchian Channel.

```
Upper Channel = Mmax(closings, { period })
Lower Channel = Mmin(closings, { period })
Middle Channel = (Upper Channel + Lower Channel) / 2
```

```TypeScript
import { dc } from 'indicatorts';

const defaultConfig = { period: 4 };
const { upper, middle, lower } = dc(closings, defaultConfig);

// Alternatively:
// const { upper, middle, lower } = donchianChannel(closings, defaultConfig);
```

## Keltner Channel (KC)

The [keltnerChannel](./keltnerChannel.ts) provides volatility-based bands that are placed on either side of an asset's price and can aid in determining the direction of a trend.

```
Middle Line = EMA(period, closings)
Upper Band = EMA(period, closings) + 2 * ATR(period, highs, lows, closings)
Lower Band = EMA(period, closings) - 2 * ATR(period, highs, lows, closings)
```

```TypeScript
import { kc } from 'indicatorts';

const defaultConfig = { period: 20 };
const { upper, middle, lower } = kc(highs, lows, closings, defaultConfig);

// Alternatively:
// const { upper, middle, lower } = keltnerChannel(highs, lows, closings, defaultConfig);
```

## Moving Standard Deviation (MSTD)

The [mstd](./movingStandardDeviation.ts) function calculates the moving standard deviation for a given period.

```TypeScript
import { mstd } from 'indicatorts';

const defaultConfig = { period: 4 };
const result = mstd(values, defaultConfig);

// Alternatively:
// const result = movingStandardDeviation(values, defaultConfig);
```

## Projection Oscillator (PO)

The [projectionOscillator](./projectionOscillator.ts) calculates the Projection Oscillator (PO). The PO uses the linear regression slope, along with highs and lows.

Period defines the moving window to calculates the PO, and the smooth period defines the moving windows to take EMA of PO.

```
PL = Min(period, (high + MLS(period, x, high)))
PU = Max(period, (low + MLS(period, x, low)))
PO = 100 * (Closing - PL) / (PU - PL)
SPO = EMA(smooth, PO)
```

```TypeScript
import { po } from 'indicatorts';

const defaultConfig = { period: 14, smooth: 3 };
const { poResult, spoResult } = po(highs, lows, closings, defaultConfig);

// Alternatively:
// const { poResult, spoResult } = projectionOscillator(highs, lows, closings, defaultConfig);
```

## True Range (TR)

The [trueRange](./trueRange.ts) function calculates the True Range (TR) for a given period.

```
TR = Max((High - Low), Abs(High - Closing[-1]), Abs(Low - Closing[-1]))
```

```TypeScript
import { tr } from 'indicatorts';

const result = tr(highs, lows, closings);
```

## Ulcer Index (UI)

The [ulcerIndex](./ulcerIndex.ts) measures downside risk. The index increases in value as the price moves farther away from a recent high and falls as the price rises to new highs.

```
High Closings = Max(period, Closings)
Percentage Drawdown = 100 * ((Closings - High Closings) / High Closings)
Squared Average = Sma(period, Percent Drawdown * Percent Drawdown)
Ulcer Index = Sqrt(Squared Average)
```

```TypeScript
import { ui } from 'indicatorts';

const defaultConfig = { period: 14 };
const result = ulcerIndex(closings, defaultConfig);

// Alternatively:
// const result = ulcerIndex(closings, defaultConfig);
```

## Disclaimer

The information provided on this project is strictly for informational purposes and is not to be construed as advice or solicitation to buy or sell any security.

## License

Copyright (c) 2022 Onur Cinar. All Rights Reserved.

The source code is provided under MIT License.
