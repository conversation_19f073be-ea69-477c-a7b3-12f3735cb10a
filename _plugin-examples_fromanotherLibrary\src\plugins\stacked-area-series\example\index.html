<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - StackedArea Series Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Stacked Area Series</h1>
			<p>
				Multiple filled areas stacked on top of each other, representing the
				proportion of each variable at different points along the axis.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>
