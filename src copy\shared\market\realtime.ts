import { EventEmitter } from 'events';
import { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { WS_URL, WS_RECONNECT_DELAY, WS_MAX_RECONNECT_DELAY, WS_RECONNECT_BACKOFF_FACTOR, WS_PING_INTERVAL } from '../lib/constants';
import { useMarketStore, WSConnectionStatus, getKlinesCacheKey } from '../store/marketStore';
import type { Ticker, Kline, MarketType } from '../types';
import { tickersApi } from './api';

/**
 * Enhanced WebSocket client with subscription management and auto-reconnection
 * Significantly simplified compared to the original implementation
 */
class WebSocketClient extends EventEmitter {
  // Core WebSocket properties
  private ws: WebSocket | null = null;
  private status: WSConnectionStatus = WSConnectionStatus.CLOSED;
  private url: string;
  
  // Reconnection properties
  private reconnectAttempts = 0;
  private reconnectTimer: ReturnType<typeof setTimeout> | null = null;
  private pingTimer: ReturnType<typeof setTimeout> | null = null;
  
  // Subscription management
  private subscriptions = new Set<string>();
  private topicSubscribers = new Map<string, Set<string>>();
  private pendingUnsubscriptions = new Map<string, ReturnType<typeof setTimeout>>();
  private static TOPIC_CLEANUP_DELAY = 5000; // 5 seconds delay before unsubscribing
  
  // Singleton instance
  private static instance: WebSocketClient | null = null;

  private constructor(url: string = WS_URL) {
    super();
    this.url = url;
    this.setMaxListeners(100); // Increased from default 10
  }

  /**
   * Get the singleton instance of the WebSocketClient
   */
  public static getInstance(): WebSocketClient | null {
    if (typeof window === 'undefined') return null; // Only create in browser environment
    if (!WebSocketClient.instance) {
      WebSocketClient.instance = new WebSocketClient();
    }
    return WebSocketClient.instance;
  }

  /**
   * Connect to the WebSocket server
   */
  public connect(): void {
    if (this.status === WSConnectionStatus.CONNECTING || this.status === WSConnectionStatus.OPEN) return;
    
    this.setStatus(WSConnectionStatus.CONNECTING);
    
    try {
      this.ws = new WebSocket(this.url);
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
    } catch (error) {
      console.error('[WS] Connection error:', error);
      this.setStatus(WSConnectionStatus.ERROR);
      this.scheduleReconnect();
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    if (this.status === WSConnectionStatus.CLOSED || this.status === WSConnectionStatus.CLOSING) return;
    
    this.setStatus(WSConnectionStatus.CLOSING);
    this.clearTimers();
    
    if (this.ws) {
      try {
        this.ws.close();
      } catch (error) {
        console.error('[WS] Error during disconnect:', error);
      }
      this.ws = null;
    }
    
    this.setStatus(WSConnectionStatus.CLOSED);
  }

  /**
   * Get the current connection status
   */
  public getStatus(): WSConnectionStatus {
    return this.status;
  }

  /**
   * Subscribe a component to one or more topics
   */
  public subscribeComponent(componentId: string, topics: string | string[]): void {
    const topicList = Array.isArray(topics) ? topics : [topics];
    if (topicList.length === 0) return;
    
    // Add topics to component's subscriptions
    for (const topic of topicList) {
      // Initialize set for topic if it doesn't exist
      if (!this.topicSubscribers.has(topic)) {
        this.topicSubscribers.set(topic, new Set());
      }
      
      // Add component as subscriber
      this.topicSubscribers.get(topic)!.add(componentId);
      
      // Cancel any pending unsubscription
      if (this.pendingUnsubscriptions.has(topic)) {
        clearTimeout(this.pendingUnsubscriptions.get(topic)!);
        this.pendingUnsubscriptions.delete(topic);
      }
      
      // Subscribe to the topic if not already subscribed
      if (!this.subscriptions.has(topic)) {
        this.subscribe(topic);
      }
    }
  }

  /**
   * Unsubscribe a component from all its topics
   */
  public unsubscribeComponent(componentId: string): void {
    // Find all topics this component is subscribed to
    const componentTopics: string[] = [];
    
    for (const [topic, subscribers] of this.topicSubscribers.entries()) {
      if (subscribers.has(componentId)) {
        subscribers.delete(componentId);
        componentTopics.push(topic);
        
        // If no subscribers left, schedule topic for cleanup
        if (subscribers.size === 0) {
          this.scheduleTopicCleanup(topic);
        }
      }
    }
  }

  /**
   * Get all current subscriptions
   */
  public getSubscriptions(): string[] {
    return Array.from(this.subscriptions);
  }

  /**
   * Get all active subscriber IDs for a topic
   */
  public getTopicSubscribers(topic: string): string[] {
    return Array.from(this.topicSubscribers.get(topic) || []);
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(): void {
    this.reconnectAttempts = 0;
    this.setStatus(WSConnectionStatus.OPEN);
    this.emit('open');
    
    // Resubscribe to all topics
    this.resubscribeAll();
    
    // Start sending periodic pings
    this.startPingTimer();
  }

  /**
   * Handle WebSocket message event
   */
  private handleMessage(event: MessageEvent): void {
    try {
      // Parse message data
      let data: any;
      if (typeof event.data === 'string') {
        data = JSON.parse(event.data);
      } else if (event.data instanceof ArrayBuffer) {
        data = JSON.parse(new TextDecoder().decode(new Uint8Array(event.data)));
      } else {
        console.warn('[WS] Received unsupported message format:', typeof event.data);
        return;
      }
      
      // Process the message
      this.processMessage(data);
    } catch (error) {
      console.error('[WS] Error processing message:', error);
      this.emit('error', error);
    }
  }

  /**
   * Handle WebSocket close event
   */
  private handleClose(event: CloseEvent): void {
    this.setStatus(WSConnectionStatus.CLOSED);
    this.emit('close', { code: event.code, reason: event.reason });
    
    // Only reconnect for abnormal closures
    if (event.code !== 1000 && event.code !== 1001) {
      this.scheduleReconnect();
    }
    
    this.clearTimers();
    this.ws = null;
  }

  /**
   * Handle WebSocket error event
   */
  private handleError(event: Event): void {
    console.error('[WS] WebSocket error:', event);
    this.setStatus(WSConnectionStatus.ERROR);
    this.emit('error', event);
  }

  /**
   * Process a parsed WebSocket message
   */
  private processMessage(data: any): void {
    if (!data || typeof data !== 'object') return;
    
    const type = data.type;
    const topic = data.topic;
    
    // Handle system messages
    if (['connection_ack', 'subscribe_ack', 'unsubscribe_ack', 'error', 'info', 'pong'].includes(type)) {
      this.emit(type, data);
      return;
    }
    
    // Handle data messages (with topics)
    if (type && topic && ['ticker_data', 'kline_data'].includes(type)) {
      this.emit(`message:${topic}`, data);
      return;
    }
    
    // Unknown message type
    console.warn('[WS] Received message with unknown format:', data);
  }

  /**
   * Subscribe to a topic
   */
  private subscribe(topic: string | string[]): void {
    const topics = Array.isArray(topic) ? topic : [topic];
    if (topics.length === 0) return;
    
    // Add topics to subscriptions set
    const newTopics = topics.filter(t => !this.subscriptions.has(t));
    newTopics.forEach(t => this.subscriptions.add(t));
    
    // Send subscribe message if connected
    if (this.status === WSConnectionStatus.OPEN && this.ws && newTopics.length > 0) {
      this.sendMessage({ type: 'subscribe', topics: newTopics });
    }
  }

  /**
   * Unsubscribe from a topic
   */
  private unsubscribe(topic: string): void {
    if (!this.subscriptions.has(topic)) return;
    
    // Remove from subscriptions
    this.subscriptions.delete(topic);
    
    // Send unsubscribe message if connected
    if (this.status === WSConnectionStatus.OPEN && this.ws) {
      this.sendMessage({ type: 'unsubscribe', topic });
    }
  }

  /**
   * Resubscribe to all topics after reconnection
   */
  private resubscribeAll(): void {
    const topics = Array.from(this.subscriptions);
    if (topics.length === 0) return;
    
    // Batch subscribe to all topics at once
    this.sendMessage({ type: 'subscribe', topics });
  }

  /**
   * Schedule a topic for cleanup if no new subscribers appear
   */
  private scheduleTopicCleanup(topic: string): void {
    // Clear existing timeout if any
    if (this.pendingUnsubscriptions.has(topic)) {
      clearTimeout(this.pendingUnsubscriptions.get(topic)!);
    }
    
    // Set new timeout
    const timeoutId = setTimeout(() => {
      // Check if topic still has no subscribers
      const subscribers = this.topicSubscribers.get(topic);
      if (!subscribers || subscribers.size === 0) {
        this.unsubscribe(topic);
        this.topicSubscribers.delete(topic);
      }
      this.pendingUnsubscriptions.delete(topic);
    }, WebSocketClient.TOPIC_CLEANUP_DELAY);
    
    this.pendingUnsubscriptions.set(topic, timeoutId);
  }

  /**
   * Start the ping timer
   */
  private startPingTimer(): void {
    this.clearPingTimer();
    this.pingTimer = setInterval(() => {
      if (this.status === WSConnectionStatus.OPEN && this.ws) {
        this.sendMessage({ type: 'ping', timestamp: Date.now() });
      }
    }, WS_PING_INTERVAL);
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }
    
    const delay = Math.min(
      WS_RECONNECT_DELAY * Math.pow(WS_RECONNECT_BACKOFF_FACTOR, this.reconnectAttempts),
      WS_MAX_RECONNECT_DELAY
    );
    
    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      this.connect();
    }, delay);
  }

  /**
   * Send a message to the server
   */
  private sendMessage(message: any): void {
    if (this.status !== WSConnectionStatus.OPEN || !this.ws) return;
    
    try {
      this.ws.send(JSON.stringify(message));
    } catch (error) {
      console.error('[WS] Error sending message:', error);
      this.emit('error', error);
    }
  }

  /**
   * Set connection status and emit event
   */
  private setStatus(status: WSConnectionStatus): void {
    this.status = status;
    this.emit('status', status);
  }

  /**
   * Clear all timers
   */
  private clearTimers(): void {
    this.clearPingTimer();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    // Clear all pending unsubscription timers
    this.pendingUnsubscriptions.forEach(timer => clearTimeout(timer));
    this.pendingUnsubscriptions.clear();
  }

  /**
   * Clear the ping timer
   */
  private clearPingTimer(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }
}

// Export singleton instance
export const wsClient = WebSocketClient.getInstance();

// Hook types
export type WebSocketStatus = WSConnectionStatus | 'PENDING' | 'SUBSCRIBED' | 'DISCONNECTED' | 'RECEIVED_DATA' | 'ERROR_PROCESSING';

/**
 * React hook for WebSocket status
 */
export function useWebSocketStatus(): {
  status: WebSocketStatus;
  isConnected: boolean;
  isConnecting: boolean;
  isDisconnected: boolean;
  hasError: boolean;
} {
  const [status, setStatus] = useState<WebSocketStatus>(
    wsClient?.getStatus() || 'UNKNOWN' as WebSocketStatus
  );

  useEffect(() => {
    if (!wsClient) return;
    
    const handleStatusChange = useCallback((newStatus: string) => {
      setStatus(newStatus as WebSocketStatus);
    }, []);
    
    wsClient.on('status', handleStatusChange);
    setStatus(wsClient.getStatus());
    
    return () => {
      wsClient.off('status', handleStatusChange);
    };
  }, []);

  // Memoize the return value to prevent unnecessary rerenders
  return useMemo(() => ({
    status,
    isConnected: status === WSConnectionStatus.OPEN,
    isConnecting: status === WSConnectionStatus.CONNECTING,
    isDisconnected: status === WSConnectionStatus.CLOSED || status === WSConnectionStatus.CLOSING,
    hasError: status === WSConnectionStatus.ERROR
  }), [status]);
}

/**
 * React hook for WebSocket connection management
 */
export function useWebSocketConnection(): {
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;
  status: WSConnectionStatus;
} {
  const setWSStatus = useMarketStore(state => state.setWSStatus);
  const status = useMarketStore(state => state.wsStatus);

  // Memoize event handler
  const handleStatusChange = useCallback((newStatus: string) => {
    setWSStatus(newStatus as WSConnectionStatus);
  }, [setWSStatus]);

  useEffect(() => {
    if (!wsClient) return;
    
    wsClient.on('status', handleStatusChange);
    setWSStatus(wsClient.getStatus());
    
    return () => {
      wsClient.off('status', handleStatusChange);
    };
  }, [setWSStatus, handleStatusChange]);

  // Memoize actions
  const connect = useCallback(() => wsClient?.connect(), []);
  const disconnect = useCallback(() => wsClient?.disconnect(), []);
  const reconnect = useCallback(() => {
    wsClient?.disconnect();
    setTimeout(() => wsClient?.connect(), 300);
  }, []);

  // Memoize return value
  return useMemo(() => ({ 
    connect, 
    disconnect, 
    reconnect, 
    status 
  }), [connect, disconnect, reconnect, status]);
}

// Subscription options
export interface SubscriptionOptions {
  maxMessages?: number;
}

/**
 * React hook for component-based WebSocket subscription
 */
export function useWebSocketComponentSubscription<T>(
  componentId: string,
  topic: string,
  options?: SubscriptionOptions
): {
  data: T | null;
  messages: T[];
  status: WebSocketStatus;
} {
  const [data, setData] = useState<T | null>(null);
  const [messages, setMessages] = useState<T[]>([]);
  const [status, setStatus] = useState<WebSocketStatus>('PENDING');
  const maxMessages = options?.maxMessages || 10;

  // Subscribe to topic when component mounts
  useEffect(() => {
    if (!wsClient || !topic || !componentId) return;
    
    const handleStatusChange = (newStatus: WSConnectionStatus) => {
      if (newStatus === WSConnectionStatus.OPEN) {
        try {
          wsClient.subscribeComponent(componentId, topic);
          setStatus('SUBSCRIBED');
        } catch (error) {
          console.error(`[WS:${componentId}] Error subscribing to ${topic}:`, error);
          setStatus('ERROR_PROCESSING');
        }
      } else {
        // Simply update status based on connection state
        setStatus(prevStatus => {
          if (prevStatus === 'SUBSCRIBED' || prevStatus === 'RECEIVED_DATA') {
            return 'DISCONNECTED';
          }
          return prevStatus;
        });
      }
    };
    
    const handleMessage = (message: any) => {
      try {
        if (message && typeof message === 'object' && 
            (message.type === 'ticker_data' || message.type === 'kline_data')) {
          const actualData = message.data !== undefined ? message.data : message;
          setData(actualData as T);
          setMessages(prev => {
            const newMessages = [...prev, actualData as T];
            return newMessages.slice(-maxMessages);
          });
          setStatus('RECEIVED_DATA');
        }
      } catch (error) {
        console.error(`[WS:${componentId}:${topic}] Error processing message:`, error);
        setStatus('ERROR_PROCESSING');
      }
    };
    
    // Register event handlers
    wsClient.on('status', handleStatusChange);
    wsClient.on(`message:${topic}`, handleMessage);
    
    // Initial subscription if already connected
    if (wsClient.getStatus() === WSConnectionStatus.OPEN) {
      try {
        wsClient.subscribeComponent(componentId, topic);
        setStatus('SUBSCRIBED');
      } catch (error) {
        console.error(`[WS:${componentId}] Error subscribing to ${topic}:`, error);
        setStatus('ERROR_PROCESSING');
      }
    }
    
    // Cleanup on unmount
    return () => {
      if (wsClient) {
        wsClient.off('status', handleStatusChange);
        wsClient.off(`message:${topic}`, handleMessage);
        wsClient.unsubscribeComponent(componentId);
      }
    };
  }, [componentId, topic, maxMessages]);

  return { data, messages, status };
}

/**
 * React hook for simplified WebSocket subscription
 */
export function useWebSocketSubscription<T>(
  topic: string,
  options?: SubscriptionOptions
): {
  data: T | null;
  messages: T[];
  status: WebSocketStatus;
} {
  // Generate a stable unique ID for this subscription
  const componentIdRef = useRef<string>(`auto_${Math.random().toString(36).substring(2, 9)}`);
  
  return useWebSocketComponentSubscription<T>(componentIdRef.current, topic, options);
}

/**
 * React hook for automatic ticker updates
 */
export function useTickersUpdater() {
  const setTickers = useMarketStore(state => state.setTickers);
  const wsStatus = useMarketStore(state => state.wsStatus);
  const componentIdRef = useRef<string>('global_tickers_updater');

  // Memoize handlers to prevent recreation on each render
  const handleSpotTicker = useCallback((message: any) => {
    try {
      if (message.type !== 'ticker_data' || !message.data || !Array.isArray(message.data)) return;
      
      const tickersData = message.data;
      if (tickersData.length > 0) {
        const timestampedTickers = tickersData.map((ticker: any) => ({
          ...ticker,
          lastUpdated: Date.now()
        }));
        setTickers('spot', timestampedTickers);
      }
    } catch (error) {
      console.error(`[WS:Tickers] Error processing spot tickers:`, error);
    }
  }, [setTickers]);
  
  const handleFuturesTicker = useCallback((message: any) => {
    try {
      if (message.type !== 'ticker_data' || !message.data || !Array.isArray(message.data)) return;
      
      const tickersData = message.data;
      if (tickersData.length > 0) {
        const timestampedTickers = tickersData.map((ticker: any) => ({
          ...ticker,
          lastUpdated: Date.now()
        }));
        setTickers('futures', timestampedTickers);
      }
    } catch (error) {
      console.error(`[WS:Tickers] Error processing futures tickers:`, error);
    }
  }, [setTickers]);

  useEffect(() => {
    if (!wsClient) return;
    
    // Register event handlers
    wsClient.on('message:tickers_spot', handleSpotTicker);
    wsClient.on('message:tickers_futures', handleFuturesTicker);
    
    // Subscribe if connected
    if (wsStatus === WSConnectionStatus.OPEN) {
      try {
        wsClient.subscribeComponent(componentIdRef.current, ['tickers_spot', 'tickers_futures']);
      } catch (error) {
        console.error('[WS:TickersUpdater] Error subscribing to tickers:', error);
      }
    }
    
    // Cleanup on unmount
    return () => {
      if (wsClient) {
        wsClient.off('message:tickers_spot', handleSpotTicker);
        wsClient.off('message:tickers_futures', handleFuturesTicker);
        wsClient.unsubscribeComponent(componentIdRef.current);
      }
    };
  }, [wsStatus, handleSpotTicker, handleFuturesTicker]);
}

/**
 * React hook for klines/candles subscription
 */
export function useKlinesSubscription(
  symbol: string,
  interval: string,
  marketType: MarketType
) {
  const addKline = useMarketStore(state => state.addKline);
  const wsStatus = useMarketStore(state => state.wsStatus);
  
  // Memoize derived values
  const cacheKey = useMemo(() => 
    getKlinesCacheKey(symbol, interval, marketType),
    [symbol, interval, marketType]
  );
  
  const componentId = useMemo(() => 
    `klines_${marketType}_${symbol}_${interval}`,
    [marketType, symbol, interval]
  );
  
  // Create topic string only when all parameters are available
  const topic = useMemo(() => 
    symbol && interval && marketType 
      ? `kline_${marketType}_${symbol.toLowerCase()}_${interval}` 
      : '', 
    [symbol, interval, marketType]
  );
  
  // Subscribe to kline updates
  const { data, status } = useWebSocketComponentSubscription<Kline>(
    componentId,
    topic
  );

  // Update store when new data arrives
  useEffect(() => {
    if (data && 
        data.symbol === symbol && 
        data.interval === interval && 
        data.marketType === marketType) {
      addKline(cacheKey, data);
    }
  }, [data, symbol, interval, marketType, cacheKey, addKline]);

  // Memoize return value
  return useMemo(() => ({ 
    status, 
    isConnected: wsStatus === WSConnectionStatus.OPEN 
  }), [status, wsStatus]);
}

/**
 * React hook for automatic WebSocket connection
 */
export function useWebSocketAutoConnect() {
  const { connect, status } = useWebSocketConnection();

  // Memoize the visibility change handler
  const handleVisibilityChange = useCallback(() => {
    if (document.visibilityState === 'visible' && 
        status !== WSConnectionStatus.OPEN && 
        status !== WSConnectionStatus.CONNECTING) {
      connect();
    }
  }, [connect, status]);

  useEffect(() => {
    // Connect if not already connected or connecting
    if (status !== WSConnectionStatus.OPEN && status !== WSConnectionStatus.CONNECTING) {
      connect();
    }
    
    // Set up visibility change listener
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [connect, status, handleVisibilityChange]);
}

// Backward compatibility aliases
export type WebSocketHookStatus = WebSocketStatus;
export const useWSStatus = useWebSocketStatus;

/**
 * Централизованный менеджер данных рынка.
 * Управляет загрузкой начальных данных тикеров и подписками на обновления в реальном времени.
 * 
 * @returns Объект с методами и состоянием для управления рыночными данными
 */
export function useMarketDataManager() {
  const setTickers = useMarketStore(state => state.setTickers);
  const addKline = useMarketStore(state => state.addKline);
  const wsStatus = useMarketStore(state => state.wsStatus);
  const { connect, status: wsConnectionStatus } = useWebSocketConnection();

  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  // Автоматически устанавливаем подписки на тикеры
  // для получения постоянных обновлений по всем тикерам
  useTickersUpdater();
  
  // Метод для загрузки начальных данных тикеров
  const loadInitialTickers = useCallback(async () => {
    try {
      // Загружаем данные тикеров параллельно
      const [spotTickersData, futuresTickersData] = await Promise.all([
        tickersApi.getTickers({ marketType: 'spot' }),
        tickersApi.getTickers({ marketType: 'futures' })
      ]);
      
      // Обновляем состояние только если получены данные
      if (spotTickersData.length > 0) {
        setTickers('spot', spotTickersData);
        console.log(`[MarketDataManager] Loaded ${spotTickersData.length} spot tickers`);
      }
      
      if (futuresTickersData.length > 0) {
        setTickers('futures', futuresTickersData);
        console.log(`[MarketDataManager] Loaded ${futuresTickersData.length} futures tickers`);
      }
      
      setIsInitialLoading(false);
      setError(null);
      return { spotTickers: spotTickersData, futuresTickers: futuresTickersData };
    } catch (err) {
      console.error('[MarketDataManager] Error fetching initial tickers:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setIsInitialLoading(false);
      return { spotTickers: [], futuresTickers: [] };
    }
  }, [setTickers]);
  
  // Эффект для автоматической загрузки данных при необходимости
  useEffect(() => {
    const spotTickers = useMarketStore.getState().spotTickers;
    const futuresTickers = useMarketStore.getState().futuresTickers;
    
    // Загружаем данные только если нет WebSocket соединения и нет данных тикеров
    const needsInitialLoad = wsStatus !== WSConnectionStatus.OPEN && 
                           spotTickers.length === 0 && 
                           futuresTickers.length === 0;
    
    if (needsInitialLoad) {
      console.log('[MarketDataManager] Fetching initial tickers...');
      loadInitialTickers();
    } else if (spotTickers.length > 0 || futuresTickers.length > 0 || wsStatus === WSConnectionStatus.OPEN) {
      setIsInitialLoading(false);
    }
  }, [wsStatus, loadInitialTickers]);

  // Подписка на конкретный символ и интервал
  const subscribeToSymbol = useCallback((symbol: string, interval: string, marketType: MarketType) => {
    if (!symbol || !interval) return;
    
    const topicName = `kline_${marketType}_${symbol.toLowerCase()}_${interval}`;
    const cacheKey = getKlinesCacheKey(symbol, interval, marketType);
    
    const handleKlineUpdate = (data: any) => {
      const klineData = data.data as Kline;
      if (klineData?.symbol === symbol && 
          klineData?.interval === interval &&
          klineData?.marketType === marketType) {
        addKline(cacheKey, klineData);
      }
    };
    
    if (wsClient) {
      wsClient.on(`message:${topicName}`, handleKlineUpdate);
      if (wsStatus === WSConnectionStatus.OPEN) {
        wsClient.subscribeComponent(`manager_${symbol}_${interval}`, topicName);
      }
    }
    
    return () => {
      if (wsClient) {
        wsClient.off(`message:${topicName}`, handleKlineUpdate);
        wsClient.unsubscribeComponent(`manager_${symbol}_${interval}`);
      }
    };
  }, [addKline, wsStatus]);
  
  const isWsConnected = wsConnectionStatus === WSConnectionStatus.OPEN;
  const wsError = wsConnectionStatus === WSConnectionStatus.ERROR 
    ? new Error('WebSocket connection failed') 
    : null;

  return {
    isInitialLoading,
    isWsConnected,
    error: error || wsError,
    loadInitialTickers,
    subscribeToSymbol,
    connectWebSocket: connect,
    wsStatus: wsConnectionStatus
  };
} 