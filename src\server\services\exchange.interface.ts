import { EventEmitter } from 'events';
import {
  MarketType,
  CoreTicker,
  CoreKline,
  CoreSymbol,
  Kline,
  Ticker,
} from '@/shared/index';

/**
 * Information about a trading symbol
 */
export interface SymbolInfo {
  symbol: string;
  marketType: MarketType;
  baseAsset: string;
  quoteAsset: string;
  status?: string;
}

/**
 * Options for fetching klines
 */
export interface KlineOptions {
  limit?: number;
  startTime?: number;
  endTime?: number;
}

/**
 * Event data for kline updates
 */
export interface KlineEvent {
  marketType: MarketType;
  data: CoreKline[];
  symbol?: string;
  interval?: string;
}

/**
 * Event data for ticker updates
 */
export interface TickerEvent {
  marketType: MarketType;
  data: CoreTicker[];
}

/**
 * Interface for exchange services
 * All exchange implementations should follow this interface
 */
export interface ExchangeService extends EventEmitter {
  readonly exchangeName: string;
  
  /**
   * Initialize the exchange service
   */
  initialize(): Promise<void>;
  
  /**
   * Clean up resources and close connections
   */
  shutdown(): Promise<void>;
  
  /**
   * Fetch available trading symbols
   */
  fetchSymbols(marketType: MarketType): Promise<CoreSymbol[]>;
  
  /**
   * Fetch ticker data for all symbols
   */
  fetchTickers(marketType: MarketType): Promise<CoreTicker[]>;
  
  /**
   * Fetch historical kline data
   */
  fetchKlines(marketType: MarketType, symbol: string, interval: string, options?: KlineOptions): Promise<CoreKline[]>;
  
  /**
   * Subscribe to ticker updates
   * Returns an unsubscribe function
   */
  subscribeTickers(marketType: MarketType): () => void;
  
  /**
   * Subscribe to kline updates for a specific symbol and interval
   * Returns an unsubscribe function
   */
  subscribeKline(marketType: MarketType, symbol: string, interval: string): () => void;
} 