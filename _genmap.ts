// bun run genmap.ts
/**
 * @file generate-project-map.ts
 * @description Script to scan a TypeScript project, parse its files,
 * and generate a structured map of the project.
 * This is a TypeScript-based equivalent of the original map_gen.py script,
 * aiming for a very concise and high-level overview.
 * 
 * Update: Enhanced for Windows compatibility with multiple watcher strategies
 */

import * as ts from 'typescript';
import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';
import * as chokidar from 'chokidar';
import { Minimatch } from 'minimatch';
import chalk from 'chalk';
import { platform } from 'os';

// --- Configuration ---
interface ScriptConfig {
  rootDir: string;
  outputFile: string;
  includePatterns: string[];
  excludePatterns: string[];
  ignoreFiles: string[];
  maxDescriptionLength: number;
  showTypesInParameters: boolean;
  showReturnTypes: boolean;
  skipProcessingImports: boolean;
  copyFilesToTxtDirectory: boolean;
  debugMode: boolean; // Add debug mode option
  windowsCompatMode: boolean; // Special Windows compatibility mode
}

const CONFIG: ScriptConfig = {
  rootDir: process.cwd(),
  outputFile: '_PROJECT_MAP.md',
  includePatterns: ['src/**/*.ts', 'src/**/*.tsx'],
  excludePatterns: [
    '**/node_modules/**',
    '**/*.d.ts',
    '**/*.test.ts',
    '**/*.spec.ts',
    '**/dist/**',
    '**/.next/**',
    '**/coverage/**',
    '**/public/**',
    '**/*.config.js',
    '**/*.config.ts',
    '**/*.setup.ts',
    '**/generated/**',
    '**/migrations/**',
    '**/SettingButton/**',
    '**/plugins/**',
    '**/definitions/**',
    '**/ui/**',
  ],
  ignoreFiles: ['generate-project-map.ts', '_PROJECT_MAP.md', '_genmap.ts'],
  maxDescriptionLength: 70,
  showTypesInParameters: false,
  showReturnTypes: false,
  skipProcessingImports: true,
  copyFilesToTxtDirectory: true,
  debugMode: true, // Enable debug mode by default
  windowsCompatMode: platform() === 'win32', // Auto-detect Windows
};

// --- Data Structures for Project Map (Расширено) ---

interface ProjectNode {
  name: string;
  kind: 'Function' | 'Method' | 'Class' | 'Interface' | 'Enum' | 'EnumMember' | 'TypeAlias' | 'Variable' | 'Property' | 'Constructor' | 'FunctionExpression';
  description?: string;
  parameters?: string; // Combined parameters string: "(param1, param2)"
  returnOrType?: string; // Combined return type or variable/property type: ": type"
  isAsync?: boolean;
  isExported?: boolean;
  members?: ProjectNode[];
}

interface FileInfo {
  relativePath: string;
  description?: string; // File header comment (first line)
  nodes: ProjectNode[];
  charCount?: number;
  feature?: string; // Feature name, если применимо
  dependencies?: string[]; // Импорты
  linesOfCode?: number;
}

interface ProjectMap {
  generatedAt: string;
  configSummary: Pick<ScriptConfig, 'maxDescriptionLength' | 'showTypesInParameters' | 'showReturnTypes'>;
  files: FileInfo[];
  stats: {
    totalFiles: number;
    totalNodes?: number;
    totalCharCount: number;
    totalConstants: number;
    totalVariables: number;
    totalClasses: number;
    totalFunctions?: number;
    featuresCount?: number;
    totalFolders?: number;
    totalLoC?: number;
  };
}

// --- Helper Functions ---

function getFirstLine(text?: string, maxLength: number = CONFIG.maxDescriptionLength): string | undefined {
  if (!text) return undefined;
  const firstLine = text.split('\n')[0].trim();
  return firstLine.length > maxLength ? firstLine.substring(0, maxLength - 3) + '...' : firstLine;
}

function getShortDescriptionStrict(node: ts.Node): string | undefined {
  // Try JSDoc first
  const jsDocComments = ts.getJSDocCommentsAndTags(node);
  if (jsDocComments.length > 0 && jsDocComments[0].comment) {
    const commentText = typeof jsDocComments[0].comment === 'string' 
      ? jsDocComments[0].comment 
      : jsDocComments[0].comment.map(c => c.text).join('');
    const firstDocLine = getFirstLine(commentText);
    if (firstDocLine) return firstDocLine;
  }

  // Fallback to leading comments
  const fullText = node.getSourceFile().getFullText();
  const commentRanges = ts.getLeadingCommentRanges(fullText, node.getFullStart());
  if (commentRanges && commentRanges.length > 0) {
    const leadingCommentBlock = commentRanges.map(range => fullText.substring(range.pos, range.end)).join('\n');
    const cleanedBlock = leadingCommentBlock
      .replace(/\/\/\/?/g, '') // Remove single-line comment markers
      .replace(/\/\*[\s\S]*?\*\//g, (match) => match.replace(/\/\*+\s*|\s*\*+\//g, '').replace(/^\s*\*\s?/gm, '')); // Remove block comment markers and content
    return getFirstLine(cleanedBlock.trim());
  }
  return undefined;
}

function formatParameters(items: ts.NodeArray<ts.ParameterDeclaration> | readonly ts.Symbol[], checker: ts.TypeChecker, sourceFile: ts.SourceFile): string {
    const paramStrings: string[] = items.map(p => {
        let name: string;
        let typeNode: ts.TypeNode | undefined;
        let isOptional: boolean = false;

        if (ts.isParameter(p as ts.Node)) { // Directly check if it's a ParameterDeclaration
            const paramDecl = p as ts.ParameterDeclaration;
            name = paramDecl.name.getText(sourceFile).replace(/\s*\n\s*/g, ' ').replace(/\s\s+/g, ' ').trim();
            typeNode = paramDecl.type;
            isOptional = !!paramDecl.questionToken;
        } else { // Assume it's a Symbol
            const paramSymbol = p as ts.Symbol;
            name = paramSymbol.getName();
            const symbolDecl = paramSymbol.valueDeclaration;
            if (symbolDecl && ts.isParameter(symbolDecl)) {
                typeNode = symbolDecl.type;
                isOptional = !!symbolDecl.questionToken;
            }
        }
        const typeStr = CONFIG.showTypesInParameters && typeNode ? `: ${typeNode.getText(sourceFile).substring(0, 20)}` : '';
        return name + (isOptional ? '?' : '') + typeStr;
    });
    return `(${paramStrings.join(', ')})`;
}

function formatReturnType(signature: ts.Signature, checker: ts.TypeChecker, sourceFile: ts.SourceFile): string | undefined {
  if (!CONFIG.showReturnTypes) return undefined;
  const returnType = checker.getReturnTypeOfSignature(signature);
  const typeString = checker.typeToString(returnType, undefined, ts.TypeFormatFlags.NoTruncation | ts.TypeFormatFlags.UseFullyQualifiedType);
  return `: ${typeString.substring(0,30)}`;
}

// --- Категоризация файла и feature ---
function categorizeFile(relativePath: string, nodes: ProjectNode[]): { feature?: string } {
  const norm = relativePath.replace(/\\/g, '/');
  const match = norm.match(/features\/([^/]+)/);
  const feature = match ? match[1] : 'core';
  return { feature };
}

// --- Группировка по feature ---
function groupFilesByFeature(files: FileInfo[]): Map<string, FileInfo[]> {
  const map = new Map<string, FileInfo[]>();
  for (const file of files) {
    const feature = file.feature || 'core';
    if (!map.has(feature)) map.set(feature, []);
    map.get(feature)!.push(file);
  }
  return map;
}

// --- Улучшенный парсер описаний ---
function getEnhancedDescription(node: ts.Node, sourceFile: ts.SourceFile): string | undefined {
  // 1. JSDoc
  const jsDoc = ts.getJSDocCommentsAndTags(node);
  if (jsDoc.length > 0 && jsDoc[0].comment) {
    if (typeof jsDoc[0].comment === 'string') return jsDoc[0].comment.split('\n')[0];
    if (Array.isArray(jsDoc[0].comment)) return jsDoc[0].comment.map(c => c.text).join('').split('\n')[0];
  }
  // 2. Inline
  const fullText = sourceFile.getFullText();
  const commentRanges = ts.getLeadingCommentRanges(fullText, node.getFullStart());
  if (commentRanges && commentRanges.length > 0) {
    const block = commentRanges.map(range => fullText.substring(range.pos, range.end)).join('\n');
    const cleaned = block.replace(/\/\/\/?/g, '').replace(/\/\*[\s\S]*?\*\//g, '').replace(/^\s*\*\s?/gm, '').trim();
    if (cleaned) return cleaned.split('\n')[0];
  }
  // 3. React component (функция с props)
  if (ts.isFunctionDeclaration(node) || ts.isArrowFunction(node)) {
    if (node.parameters && node.parameters.length > 0) {
      const firstParam = node.parameters[0];
      if (firstParam.name && firstParam.name.getText(sourceFile) === 'props') {
        return 'React component';
      }
    }
  }
  // 4. Hook (type guard через any)
  if ((node as any).name && ts.isIdentifier((node as any).name) && (node as any).name.getText(sourceFile).startsWith('use')) {
    return 'React hook';
  }
  return undefined;
}

// --- Извлечение зависимостей и экспортов ---
function extractDependenciesAndExports(sourceFile: ts.SourceFile): { dependencies: string[] } {
  const dependencies: string[] = [];
  sourceFile.forEachChild(node => {
    if (ts.isImportDeclaration(node) && node.moduleSpecifier) {
      dependencies.push((node.moduleSpecifier as ts.StringLiteral).text);
    }
  });
  return { dependencies };
}

// --- Подсчет строк кода ---
function countLinesOfCode(source: string): number {
  return source.split('\n').filter(l => l.trim().length > 0).length;
}

function parseTypeScriptFile(filePath: string, relativePath: string, program: ts.Program): FileInfo {
  const sourceFile = program.getSourceFile(filePath);
  if (!sourceFile) throw new Error(`Could not get source file: ${filePath}`);
  const checker = program.getTypeChecker();

  const fileInfo: FileInfo = { relativePath, nodes: [] };
  const fileContent = fs.readFileSync(filePath, 'utf-8');
  fileInfo.charCount = fileContent.length;

  // Get file header comment
  const fullText = sourceFile.getFullText();
  const commentRanges = ts.getLeadingCommentRanges(fullText, 0);
  if (commentRanges && commentRanges.length > 0 && commentRanges[0].pos === 0) {
    let headerCommentBlock = fullText.substring(commentRanges[0].pos, commentRanges[0].end);
    
    // Remove block comment delimiters /* */ or /** */ and clean leading asterisks/slashes
    headerCommentBlock = headerCommentBlock
      .replace(/^\/\*\*?\s*|\s*\*\/$/g, '') // Remove /*, /**, */
      .split('\n').map(line => line.trim().replace(/^\*\s?|^\/\/\/?\s?/g, '').trim())
      .filter(line => line.length > 0).join('\n');

    fileInfo.description = getFirstLine(headerCommentBlock.trim());
  }

  function visit(node: ts.Node, parentName?: string) {
    // If skipProcessingImports is true, and node is an ImportDeclaration, skip it.
    if (CONFIG.skipProcessingImports && ts.isImportDeclaration(node)) return;

    const description = getShortDescriptionStrict(node);

    if (ts.isClassDeclaration(node) && node.name) {
      const className = node.name.getText(sourceFile);
      fileInfo.nodes.push({
        name: className,
        kind: 'Class',
        description,
      });
      node.members.forEach(member => visit(member, className));
      return;
    }

    if ((ts.isMethodDeclaration(node) || ts.isConstructorDeclaration(node)) && parentName) {
      const methodName = ts.isConstructorDeclaration(node) ? 'constructor' : node.name?.getText(sourceFile);
      if (!methodName) return; // Defensive check

      const signature = checker.getSignatureFromDeclaration(node as ts.FunctionLikeDeclaration);
      fileInfo.nodes.push({
        name: `${parentName}.${methodName}`,
        kind: ts.isConstructorDeclaration(node) ? 'Constructor' : 'Method',
        description,
        isAsync: !!(node.modifiers?.some(m => m.kind === ts.SyntaxKind.AsyncKeyword)),
        parameters: signature ? formatParameters((node as ts.FunctionLikeDeclaration).parameters, checker, sourceFile!) : undefined,
      });
      return;
    }

    if (ts.isPropertyDeclaration(node) && parentName && node.name) {
      fileInfo.nodes.push({
        name: `${parentName}.${node.name.getText(sourceFile)}`,
        kind: 'Property',
        description,
      });
      return;
    }

    if (ts.isFunctionDeclaration(node) && node.name) {
      const signature = checker.getSignatureFromDeclaration(node);
      fileInfo.nodes.push({
        name: node.name.getText(sourceFile),
        kind: 'Function',
        description,
        isAsync: !!(node.modifiers?.some(m => m.kind === ts.SyntaxKind.AsyncKeyword)),
        parameters: signature ? formatParameters(node.parameters, checker, sourceFile!) : undefined,
      });
      return;
    }

    if (ts.isVariableStatement(node)) {
      node.declarationList.declarations.forEach(declaration => {
        if (declaration.name && ts.isIdentifier(declaration.name)) {
          const declName = declaration.name.getText(sourceFile);
          let varKind: ProjectNode['kind'] = 'Variable';
          let sigParams: string | undefined;
          let sigIsAsync: boolean | undefined;

          if (declaration.initializer && (ts.isArrowFunction(declaration.initializer) || ts.isFunctionExpression(declaration.initializer))) {
            varKind = 'FunctionExpression';
            const signature = checker.getSignatureFromDeclaration(declaration.initializer);
            sigParams = signature ? formatParameters(declaration.initializer.parameters, checker, sourceFile!) : undefined;
            sigIsAsync = !!(declaration.initializer.modifiers?.some(m => m.kind === ts.SyntaxKind.AsyncKeyword));
          }

          fileInfo.nodes.push({
            name: declName,
            kind: varKind,
            description: getShortDescriptionStrict(declaration) || description,
            isAsync: sigIsAsync,
            parameters: sigParams,
          });
        }
      });
      return;
    }

    // Skip interfaces, type aliases, enums
    if (ts.isInterfaceDeclaration(node) || ts.isTypeAliasDeclaration(node) || ts.isEnumDeclaration(node)) {
      return;
    }

    // Recursively visit children for unhandled nodes or top-level nodes not yet processed
    ts.forEachChild(node, child => visit(child, parentName));
  }

  ts.forEachChild(sourceFile, child => visit(child));

  // Categorize, feature, LoC, dependencies
  const { feature } = categorizeFile(relativePath, fileInfo.nodes);
  fileInfo.feature = feature;
  const src = fs.readFileSync(filePath, 'utf-8');
  fileInfo.linesOfCode = countLinesOfCode(src);
  const { dependencies } = extractDependenciesAndExports(sourceFile);
  fileInfo.dependencies = dependencies;

  return fileInfo;
}


function formatNodeToMarkdown(node: ProjectNode): string {
  const baseIndent = '    '; 
  const commentAlignColumn = 60; // Adjusted alignment

  let line = `${baseIndent}`; 
  if (node.isAsync) line += `async `;
  line += `${node.name}`;
  if (node.kind === 'Function' || node.kind === 'Method' || node.kind === 'Constructor' || node.kind === 'FunctionExpression') {
    line += node.parameters ? `${node.parameters}` : '()';
  }

  let commentPart = '';
  if (node.description) {
    commentPart += `${node.description}`;
  }
  
  if (commentPart.trim() !== '') {
    const currentLength = line.trimEnd().length; // Length of content before comment
    const padding = Math.max(2, commentAlignColumn - currentLength);
    line += ' '.repeat(padding) + `# ${commentPart.trim()}`;
  }
  return line.trimEnd() + '\n';
}

function formatNodeAsTree(node: ProjectNode, isMember: boolean): string {
  const baseIndent = ' '.repeat(isMember ? 6 : 4);
  
  let prefix = '';
  switch(node.kind) {
      case 'Class': return ''; // Class nodes are handled separately
      case 'Method': case 'Constructor': prefix = '▸ ';
        break;
      case 'Function': case 'FunctionExpression': prefix = 'ƒ ';
        break;
      case 'Variable': case 'Property': prefix = '• ';
        break;
      default: prefix = '  '; // Default for other types if any
  }
  
  const displayName = isMember ? node.name.split('.').pop() : node.name;

  let line = `${baseIndent}${prefix}`;
  if (node.isAsync) line += `async `;
  
  line += `${displayName}`;

  if (['Function', 'Method', 'Constructor', 'FunctionExpression'].includes(node.kind) && node.parameters && node.parameters !== '()') {
      line += node.parameters;
  }

  if (node.description?.trim()) {
      line += ` *${node.description.trim()}*`;
  }
  return line.trimEnd() + '\n';
}

function formatProjectMapToMarkdown(projectMap: ProjectMap): string {
  // 1. Print overview
  let md = `# PROJECT MAP - Generated: ${projectMap.generatedAt}\n\n`;
  md += `## 📊 Project Overview\n`;
  md += `- **Total Files**: ${projectMap.stats.totalFiles}\n`;
  md += `- **Total Folders**: ${projectMap.stats.totalFolders || 0}\n`;
  md += `- **Total Characters**: ${projectMap.stats.totalCharCount.toLocaleString()}\n`;
  md += `- **Total Lines**: ${projectMap.stats.totalLoC?.toLocaleString() || 0}\n`;
  md += `- **Total Functions/Methods**: ${projectMap.stats.totalFunctions || 0}\n`;
  md += `- **Total Classes**: ${projectMap.stats.totalClasses}\n`;
  md += `- **Total Constants**: ${projectMap.stats.totalConstants}\n`;
  md += `- **Total Variables**: ${projectMap.stats.totalVariables}\n`;
  md += `- **Legend**: \`■\` Class, \`▸\` Method/Constructor, \`ƒ\` Function, \`•\` Variable/Property\n`;
  md += `\n`;

  // 2. Group files by feature
  const filesByFeature = groupFilesByFeature(projectMap.files);
  const sortedFeatures = [...filesByFeature.keys()].sort((a,b) => {
    if (a === 'core') return 1;
    if (b === 'core') return -1;
    return a.localeCompare(b);
  });

  // 3. Format output
  for (const feature of sortedFeatures) {
    md += `\n## ✨ Feature: \`${feature}\`\n`;
    const featureFiles = filesByFeature.get(feature)!;

    // Group files within the feature by directory
    const filesByDir = new Map<string, FileInfo[]>();
    for (const file of featureFiles) {
        const dir = path.dirname(file.relativePath).replace(/\\/g, '/');
        if (!filesByDir.has(dir)) {
          filesByDir.set(dir, []);
        }
        filesByDir.get(dir)!.push(file);
    }
    
    const sortedDirs = [...filesByDir.keys()].sort();

    for (const dir of sortedDirs) {
        const files = filesByDir.get(dir)!;
        const dirLoC = files.reduce((sum, f) => sum + (f.linesOfCode || 0), 0);
        const dirChars = files.reduce((sum, f) => sum + (f.charCount || 0), 0);

        let dirHeader = `### 📁 \`${dir}/\``;
        let dirMetaParts = [];
        if (dirLoC > 0) dirMetaParts.push(`${dirLoC} LoC`);
        if (dirChars > 0) dirMetaParts.push(`${dirChars.toString()} chars`);

        if (dirMetaParts.length > 0) {
            dirHeader += `  # ${dirMetaParts.join(' | ')}`;
        }
        md += dirHeader + '\n';
        
        files.sort((a, b) => a.relativePath.localeCompare(b.relativePath));

        for (const file of files) {
            const displayFileName = path.basename(file.relativePath);
            
            let fileHeader = `#### 📄 ${displayFileName}`;
            let fileMetaParts = [];
            if (file.linesOfCode) fileMetaParts.push(`${file.linesOfCode} LoC`);
            if (file.charCount) fileMetaParts.push(`${file.charCount.toString()} chars`);
            
            if (fileMetaParts.length > 0) {
                 fileHeader += `  # ${fileMetaParts.join(' | ')}`;
            }
            md += fileHeader + '\n';

            if (file.description) {
                md += `*${file.description}*\n`;
            }

            const localDeps = file.dependencies?.filter(d => d.startsWith('.'));
            if (localDeps && localDeps.length > 0) {
                md += `*Dependencies:*\n`;
                localDeps.forEach(dep => {
                    md += `  - \`${dep}\`\n`;
                });
            }
            
            if (file.nodes.length > 0) {
                const topLevelNodes = file.nodes.filter(n => !n.name.includes('.'));
                const classNodes = file.nodes.filter(n => n.kind === 'Class');

                for (const classNode of classNodes) {
                    let classLine = ' '.repeat(4) + `■ ${classNode.name}`;
                     if (classNode.description) {
                         classLine += ` *${classNode.description}*`;
                     }
                    md += classLine + '\n';
                    
                    const memberNodes = file.nodes.filter(n => n.name.startsWith(classNode.name + '.') && n.kind !== 'Class');
                    memberNodes.forEach(memberNode => {
                        md += formatNodeAsTree(memberNode, true);
                    });
                }

                const otherTopLevelNodes = topLevelNodes.filter(n => n.kind !== 'Class');
                otherTopLevelNodes.forEach(node => {
                    md += formatNodeAsTree(node, false);
                });
            }
        }
    }
  }

  return md.trimEnd();
}

// Manual Windows watcher
function setupWindowsManualWatcher(filePaths: string[], onChange: (filePath: string) => void) {
  console.log(chalk.yellow('Setting up manual Windows watcher for ' + filePaths.length + ' files'));
  
  const watchedFiles = new Map<string, fs.Stats>();
  
  // Initial capture of file stats
  filePaths.forEach(filePath => {
    try {
      const stats = fs.statSync(filePath);
      watchedFiles.set(filePath, stats);
    } catch (e) {
      console.error(`Error getting initial stats for ${filePath}:`, e);
    }
  });
  
  // Poll files every second
  const interval = setInterval(() => {
    filePaths.forEach(filePath => {
      try {
        if (!fs.existsSync(filePath)) {
          // File was deleted
          if (watchedFiles.has(filePath)) {
            console.log(chalk.red(`File deleted: ${filePath}`));
            watchedFiles.delete(filePath);
            // Handle file deletion if needed
          }
          return;
        }
        
        const stats = fs.statSync(filePath);
        const oldStats = watchedFiles.get(filePath);
        
        if (!oldStats) {
          // New file
          console.log(chalk.green(`New file detected: ${filePath}`));
          watchedFiles.set(filePath, stats);
          onChange(filePath);
        } else if (stats.mtimeMs !== oldStats.mtimeMs || stats.size !== oldStats.size) {
          // File changed
          console.log(chalk.yellow(`File changed: ${filePath}`));
          console.log(chalk.gray(`  Old time: ${oldStats.mtimeMs}, New time: ${stats.mtimeMs}`));
          console.log(chalk.gray(`  Old size: ${oldStats.size}, New size: ${stats.size}`));
          watchedFiles.set(filePath, stats);
          onChange(filePath);
        }
      } catch (e) {
        console.error(`Error checking ${filePath}:`, e);
      }
    });
    
    // Check for new files in src directory
    glob(CONFIG.includePatterns, {
      cwd: CONFIG.rootDir,
      ignore: [...CONFIG.excludePatterns, ...CONFIG.ignoreFiles.map(f => `**/${f}`)],
      nodir: true,
      absolute: true,
      windowsPathsNoEscape: true,
    }).then(newPaths => {
      const newFiles = newPaths.filter(p => !watchedFiles.has(p) && !filePaths.includes(p));
      
      if (newFiles.length > 0) {
        console.log(chalk.green(`Found ${newFiles.length} new files to watch`));
        
        newFiles.forEach(filePath => {
          try {
            const stats = fs.statSync(filePath);
            watchedFiles.set(filePath, stats);
            onChange(filePath);
            filePaths.push(filePath);
          } catch (e) {
            console.error(`Error getting initial stats for new file ${filePath}:`, e);
          }
        });
      }
    });
    
  }, 1000); // Check every second
  
  return {
    close: () => {
      clearInterval(interval);
    }
  };
}

async function main() {
  console.log('Starting project map generator in watch mode...');
  console.log(`Current directory: ${CONFIG.rootDir}`);
  console.log(`Watching patterns: ${CONFIG.includePatterns.join(', ')}`);
  console.log(`Platform detected: ${platform()}, Windows compat mode: ${CONFIG.windowsCompatMode ? 'enabled' : 'disabled'}`);

  const projectMap: ProjectMap = {
    generatedAt: new Date().toISOString(),
    configSummary: {
      maxDescriptionLength: CONFIG.maxDescriptionLength,
      showTypesInParameters: CONFIG.showTypesInParameters,
      showReturnTypes: CONFIG.showReturnTypes,
    },
    files: [],
    stats: { totalFiles: 0, totalCharCount: 0, totalConstants: 0, totalVariables: 0, totalClasses: 0 },
  };

  let filePaths: string[] = [];
  let program: ts.Program;

  const updateAndWriteMap = () => {
    projectMap.generatedAt = new Date().toISOString();
    projectMap.stats.totalFiles = projectMap.files.length;
    projectMap.stats.totalCharCount = projectMap.files.reduce((sum, file) => sum + (file.charCount || 0), 0);
    projectMap.stats.totalLoC = projectMap.files.reduce((sum, file) => sum + (file.linesOfCode || 0), 0);
    
    const allNodes = projectMap.files.flatMap(f => f.nodes);
    projectMap.stats.totalFunctions = allNodes.filter(n => ['Function', 'Method', 'Constructor', 'FunctionExpression'].includes(n.kind)).length;
    projectMap.stats.totalClasses = allNodes.filter(n => n.kind === 'Class').length;
    
    projectMap.stats.totalConstants = allNodes.filter(n => (n.kind === 'Variable' || n.kind === 'Property') && /^[A-Z0-9_]+$/.test(n.name.split('.').pop()!)).length;
    projectMap.stats.totalVariables = (allNodes.filter(n => n.kind === 'Variable' || n.kind === 'Property').length) - (projectMap.stats.totalConstants || 0);
    
    projectMap.stats.featuresCount = new Set(projectMap.files.map(f => f.feature).filter(f => f && f !== 'core')).size;
    
    const uniqueDirs = new Set(projectMap.files.map(f => path.dirname(f.relativePath)));
    projectMap.stats.totalFolders = uniqueDirs.size;

    const finalMarkdownOutput = formatProjectMapToMarkdown(projectMap);
    fs.writeFileSync(path.join(CONFIG.rootDir, CONFIG.outputFile), finalMarkdownOutput, 'utf-8');
    console.log(chalk.magenta(`[${new Date().toLocaleTimeString()}] Project map updated and saved to ${CONFIG.outputFile}`));

    if (CONFIG.copyFilesToTxtDirectory) {
      const txtDirPath = path.join(CONFIG.rootDir, 'src in txt');
      if (fs.existsSync(txtDirPath)) {
        fs.rmSync(txtDirPath, { recursive: true, force: true });
      }
      fs.mkdirSync(txtDirPath, { recursive: true });

      const filesToGroup = new Map<string, Array<{ path: string; content: string }>>();
      const combinedFile = 'all_src_files.txt'; // Unified file name

      for (const fileInfo of projectMap.files) {
        if (!filesToGroup.has(combinedFile)) {
          filesToGroup.set(combinedFile, []);
        }
        try {
          const content = fs.readFileSync(path.join(CONFIG.rootDir, fileInfo.relativePath), 'utf-8');
          filesToGroup.get(combinedFile)!.push({ path: fileInfo.relativePath, content });
        } catch (e) { /* ignore read errors if file was deleted */ }
      }

      for (const [fileName, fileData] of filesToGroup) {
        fileData.sort((a, b) => a.path.localeCompare(b.path));
        const fullContent = fileData
          .map(f => `--- START FILE: ${f.path} ---\n${f.content}\n--- END FILE: ${f.path} ---`)
          .join('\n\n');
        fs.writeFileSync(path.join(txtDirPath, fileName), fullContent, 'utf-8');
      }
      console.log(chalk.magenta(`[${new Date().toLocaleTimeString()}] Source files combined into ${path.join(txtDirPath, combinedFile)}`));
    }
  };

  const createOrUpdateProgram = () => {
    program = ts.createProgram(filePaths, {
      target: ts.ScriptTarget.ESNext,
      module: ts.ModuleKind.CommonJS,
      allowJs: true,
    });
  };

  const processFile = (filePath: string) => {
    // Normalize the file path for consistency
    const normalizedPath = path.normalize(filePath);
    const relativePath = path.relative(CONFIG.rootDir, normalizedPath);
    
    console.log(chalk.cyan(`Processing file: ${normalizedPath}`));
    console.log(chalk.cyan(`Relative path: ${relativePath}`));
    
    try {
      const fileInfo = parseTypeScriptFile(normalizedPath, relativePath, program);
      const index = projectMap.files.findIndex(f => f.relativePath === relativePath);
      if (index !== -1) {
        projectMap.files[index] = fileInfo;
        console.log(chalk.cyan(`Updated existing file in the map: ${relativePath}`));
      } else {
        projectMap.files.push(fileInfo);
        console.log(chalk.cyan(`Added new file to the map: ${relativePath}`));
      }
      return true;
    } catch (error) {
      console.error(`Error parsing ${relativePath}:`, error instanceof Error ? error.message : error);
      return false;
    }
  };

  // Initial Scan
  filePaths = await glob(CONFIG.includePatterns, {
    cwd: CONFIG.rootDir,
    ignore: [...CONFIG.excludePatterns, ...CONFIG.ignoreFiles.map(f => `**/${f}`)],
    nodir: true,
    absolute: true,
    windowsPathsNoEscape: true, // Add windowsPathsNoEscape for Windows support
  });

  console.log(chalk.yellow(`Found ${filePaths.length} files matching patterns: ${CONFIG.includePatterns.join(', ')}`));
  console.log(chalk.yellow(`Files found: ${filePaths.slice(0, 5).join(', ')}${filePaths.length > 5 ? '...' : ''}`));

  if (filePaths.length === 0) {
    console.warn("No files found. Check include/exclude patterns. Watching for new files.");
  } else {
    console.log(`Found ${filePaths.length} files. Performing initial scan...`);
    createOrUpdateProgram();
    for (const filePath of filePaths) {
      processFile(filePath);
    }
    updateAndWriteMap();
  }

  console.log('Initial scan complete. Watching for file changes...');
  
  // Handle file change (common implementation)
  const handleFileChange = (filePath: string) => {
    if (!filePath) {
      console.log(chalk.red('Empty file path received in handleFileChange'));
      return;
    }
    
    console.log(chalk.yellow(`File change detected: ${filePath}`));
    
    // Normalize path
    const absolutePath = path.isAbsolute(filePath) ? filePath : path.join(CONFIG.rootDir, filePath);
    const normalizedPath = path.normalize(absolutePath);
    const relativePath = path.relative(CONFIG.rootDir, normalizedPath);
    
    console.log(chalk.yellow(`Normalized path: ${normalizedPath}`));
    console.log(chalk.yellow(`Relative path: ${relativePath}`));
    
    // Ignore based on patterns
    if (CONFIG.ignoreFiles.some(pattern => new Minimatch(pattern).match(relativePath))) {
      console.log(chalk.red(`File ignored based on pattern: ${relativePath}`));
      return;
    }
    
    // Skip if file doesn't exist
    if (!fs.existsSync(absolutePath)) {
      console.log(chalk.red(`File not found: ${absolutePath}`));
      return;
    }
    
    // Process the file
    console.log(chalk.blue(`[${new Date().toLocaleTimeString()}] Updating TypeScript program...`));
    createOrUpdateProgram();
    console.log(chalk.blue(`[${new Date().toLocaleTimeString()}] Processing changed file...`));
    processFile(absolutePath);
    console.log(chalk.blue(`[${new Date().toLocaleTimeString()}] Updating project map...`));
    updateAndWriteMap();
    console.log(chalk.green(`[${new Date().toLocaleTimeString()}] ✓ Map updated successfully after file change`));
  };
  
  // Choose watching strategy based on platform
  if (CONFIG.windowsCompatMode) {
    console.log(chalk.yellow('Using Windows-compatible manual file watching strategy'));
    setupWindowsManualWatcher(filePaths, handleFileChange);
  } else {
    // Configure chokidar for non-Windows platforms
    console.log(chalk.yellow('Using standard chokidar watching strategy'));
    
    const watchOptions = {
      ignored: [...CONFIG.excludePatterns, ...CONFIG.ignoreFiles.map(f => `**/${f}`)],
      persistent: true,
      cwd: CONFIG.rootDir,
      ignoreInitial: true,
      usePolling: true,
      interval: 100,
      binaryInterval: 300,
      awaitWriteFinish: {
        stabilityThreshold: 200,
        pollInterval: 50
      },
      alwaysStat: true,
      atomic: 250,
      ignorePermissionErrors: true
    };
    
    console.log(chalk.blue(`Watch options: ${JSON.stringify(watchOptions, null, 2)}`));
    
    const watcher = chokidar.watch(CONFIG.includePatterns, watchOptions);
    
    watcher.on('ready', () => {
      console.log(chalk.green('Initial file scan complete, ready for changes'));
    });
    
    // Log all events with detailed info
    watcher.on('all', (event, filePath) => {
      console.log(chalk.gray(`[CHOKIDAR] Event: ${event} on path: ${filePath}`));
      
      if (event === 'change' || event === 'add') {
        const fullPath = path.resolve(CONFIG.rootDir, filePath);
        try {
          const stats = fs.statSync(fullPath);
          console.log(chalk.gray(`[CHOKIDAR] File ${fullPath} size: ${stats.size}, modified: ${stats.mtime}`));
        } catch (err) {
          console.error(chalk.red(`Error getting stats for ${fullPath}: ${err}`));
        }
      }
    });
    
    watcher
      .on('add', filePath => handleFileChange(filePath))
      .on('change', filePath => handleFileChange(filePath))
      .on('unlink', filePath => {
        console.log(chalk.red(`File deleted: ${filePath}`));
        const relativePath = path.relative(CONFIG.rootDir, path.normalize(path.isAbsolute(filePath) ? filePath : path.join(CONFIG.rootDir, filePath)));
        const index = projectMap.files.findIndex(f => f.relativePath === relativePath);
        if (index > -1) {
          projectMap.files.splice(index, 1);
          updateAndWriteMap();
        }
      })
      .on('error', error => console.error(`Watcher error: ${error}`));
  }
  
  // Add native fs watcher regardless of mode for extra detection
  const srcPath = path.join(CONFIG.rootDir, 'src');
  if (fs.existsSync(srcPath)) {
    fs.watch(srcPath, { recursive: true }, (eventType, filename) => {
      if (!filename) return;
      console.log(chalk.gray(`[FS.WATCH] Event: ${eventType} on file: ${filename}`));
      const fullPath = path.join(srcPath, filename);
      if (fs.existsSync(fullPath) && fullPath.endsWith('.ts') || fullPath.endsWith('.tsx')) {
        console.log(chalk.yellow(`Native watcher detected change: ${fullPath}`));
        handleFileChange(fullPath);
      }
    });
  }

  console.log(`Now watching for changes in '${CONFIG.includePatterns.join(', ')}'. Press Ctrl+C to stop.`);
}

main().catch((e: Error) => console.error("Critical error in main execution:", e));
