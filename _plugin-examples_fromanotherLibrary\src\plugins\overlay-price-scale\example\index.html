<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - Overlay Price Scale Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Overlay Price Scale</h1>
			<p>
				A price scale which appears on the main chart pane area. For use with
				series assigned to an overlay price scale.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>
