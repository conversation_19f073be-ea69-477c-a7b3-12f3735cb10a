import { create, StateCreator } from 'zustand'; // Add StateCreator
import { immer } from 'zustand/middleware/immer';
import { shallow } from 'zustand/shallow';
import { persist, createJSONStorage, PersistOptions } from 'zustand/middleware'; // Add PersistOptions
import { v4 as uuidv4 } from 'uuid'; // Для генерации уникальных ID экземпляров
import { LineStyle } from 'lightweight-charts'; // Add import
import { type WritableDraft } from 'immer';
import { IndicatorDefinition, IndicatorInstance, IndicatorParams, IndicatorOutput, IndicatorParamValue, IndicatorStyle } from '@/shared/index';
import { getAvailableIndicators, findIndicatorDefinitionById } from '@/features/Indicators/registry';

// Helper function to get default parameters and styles separately
const getDefaultIndicatorParams = (
  indicator: IndicatorDefinition<any> // Use <any> for TParams if not strictly typed here
): { parameters: Record<string, IndicatorParamValue>, styleOverrides: Record<string, Partial<IndicatorStyle>>, tableOutputIds: string[], tableTimeframe: string } => {
  const parameters = indicator.params.reduce((acc, param) => { // Changed to indicator.params
    acc[param.id as string] = param.defaultValue;
    return acc;
  }, {} as Record<string, IndicatorParamValue>);

  const styleOverrides: Record<string, Partial<IndicatorStyle>> = {};
  if (indicator.defaultStyle) {
    Object.entries(indicator.defaultStyle).forEach(([outputId, style]) => {
      styleOverrides[outputId] = { ...style };
    });
  }
  // Default styles from output definitions themselves, if not in defaultStyle block
  indicator.outputs.forEach(output => {
    if (!styleOverrides[output.id]) {
      styleOverrides[output.id] = {
        color: output.color,
        lineWidth: output.lineWidth,
        lineStyle: output.lineStyle,
        opacity: output.opacity, 
      };
    }
  });

  // Default table settings (e.g., show first output, default timeframe)
  const tableOutputIds = indicator.outputs.length > 0 ? [indicator.outputs[0].id] : [];
  const tableTimeframe = '1h'; // Or some other sensible default

  return { parameters, styleOverrides, tableOutputIds, tableTimeframe };
};

interface IndicatorState {
  /**
   * Активные экземпляры индикаторов, сгруппированные по ID графика.
   * key: chartId
   * value: Массив экземпляров индикаторов на этом графике.
   */
  indicatorsByChartId: Record<string, IndicatorInstance[]>;

  /**
   * Глобальные индикаторы, которые отображаются на всех графиках
   * независимо от chartId.
   */
  globalIndicators: IndicatorInstance[];
}

interface IndicatorActions {
  /**
   * Добавляет новый экземпляр индикатора на указанный график.
   * @param chartId - ID графика.
   * @param definition - Определение добавляемого индикатора.
   * @param params - Параметры для этого экземпляра индикатора.
   * @param isGlobal - Флаг, указывающий, должен ли индикатор быть глобальным (отображаться на всех графиках).
   */
  addIndicator: (
    chartId: string,
    definition: IndicatorDefinition,
    params: Record<string, IndicatorParamValue>,
    isGlobal?: boolean
  ) => void;

  /**
   * Удаляет экземпляр индикатора с графика или из глобальных индикаторов.
   * @param instanceId - ID удаляемого экземпляра.
   * @param chartId - ID графика (необязательно для глобальных индикаторов).
   */
  removeIndicator: (instanceId: string, chartId?: string) => void;

  /**
   * Обновляет параметры существующего экземпляра индикатора.
   * @param instanceId - ID обновляемого экземпляра.
   * @param updates - Новые значения параметров и стилей.
   * @param chartId - ID графика (необязательно для глобальных индикаторов).
   */
  updateIndicatorInstance: (instanceId: string, updates: IndicatorInstanceUpdate, chartId?: string) => void;

  /**
   * Преобразует обычный индикатор в глобальный, который будет отображаться на всех графиках.
   * @param instanceId - ID экземпляра индикатора.
   * @param chartId - ID графика, с которого берется индикатор.
   */
  makeIndicatorGlobal: (instanceId: string, chartId: string) => void;

  /**
   * Преобразует глобальный индикатор в обычный, привязанный к конкретному графику.
   * @param instanceId - ID экземпляра индикатора.
   * @param targetChartId - ID графика, к которому будет привязан индикатор.
   */
  makeIndicatorLocal: (instanceId: string, targetChartId: string) => void;

  /**
   * Удаляет все индикаторы с указанного графика.
   * @param chartId - ID графика.
   */
  resetIndicatorsForChart: (chartId: string) => void;

  /**
   * Удаляет все индикаторы со всех графиков, включая глобальные.
   */
  resetAllIndicators: () => void;
}

// Define a type for the updates allowed in updateIndicatorInstance
// Include table settings fields
type IndicatorInstanceUpdate = Partial<Pick<IndicatorInstance, 'params' | 'styleOverrides' | 'visible' | 'tableOutputIds' | 'tableTimeframe'>>;

// Define the initial state with explicit types
const initialState: IndicatorState = {
  indicatorsByChartId: {},
  globalIndicators: [],
};

// --- Persist Configuration ---
const LS_INDICATOR_SETTINGS_KEY = 'cryptoDashboardIndicatorSettings';

// Type for the persist middleware, ensuring it correctly wraps the immer-modified StateCreator
type IndicatorPersist = (
  config: StateCreator<IndicatorState & IndicatorActions, [["zustand/immer", never]], []>, // Expects immer's signature
  options: PersistOptions<IndicatorState, Partial<IndicatorState>>
) => StateCreator<IndicatorState & IndicatorActions, [], [["zustand/persist", Partial<IndicatorState>]]>; // Returns persist's signature

export const useIndicatorStore = create<IndicatorState & IndicatorActions>()(
  persist( // persist is the outer middleware
    immer((set) => ({ // immer is the inner middleware
      // Use the explicitly typed initial state
      ...initialState,

      addIndicator: (chartId, definition, params, isGlobal = false) => {
        console.log('[indicatorStore] addIndicator вызван:', { chartId, indicatorId: definition?.id, params, isGlobal });
        set((state) => {
          if (!definition) {
            console.error('[indicatorStore] Ошибка: Попытка добавить индикатор без определения!');
            return; // Ничего не делаем, если нет определения
          }
          // Get all defaults first
          const defaults = getDefaultIndicatorParams(definition);

          // Merge default parameters with provided params
          const mergedParams = { ...defaults.parameters, ...params };

          const instanceName = definition.getShortName
            ? definition.getShortName(mergedParams as IndicatorParams) // Cast needed if mergedParams is not strictly TParams
            : definition.name;

          const newInstance: IndicatorInstance = {
            instanceId: uuidv4(),
            chartId: isGlobal ? 'global' : chartId,
            indicatorId: definition.id,
            name: instanceName, // Added name property
            // Use the merged parameters
            params: mergedParams,
            visible: true,
            // Use default styles and table settings
            styleOverrides: defaults.styleOverrides,
            tableOutputIds: defaults.tableOutputIds,
            tableTimeframe: defaults.tableTimeframe,
          };

          if (isGlobal) {
            state.globalIndicators.push(newInstance);
            console.log('[indicatorStore] Добавлен глобальный индикатор:', newInstance);
          } else {
            if (!state.indicatorsByChartId[chartId]) {
              state.indicatorsByChartId[chartId] = [];
            }
            state.indicatorsByChartId[chartId].push(newInstance);
            console.log('[indicatorStore] Состояние обновлено для chartId:', chartId, state.indicatorsByChartId[chartId]);
          }
        });
      },

      removeIndicator: (instanceId, chartId) =>
        set((state) => {
          // Проверяем сначала в глобальных индикаторах
          const globalIndex = state.globalIndicators.findIndex(
            (indicator: IndicatorInstance) => indicator.instanceId === instanceId
          );
          
          if (globalIndex !== -1) {
            state.globalIndicators.splice(globalIndex, 1);
            return;
          }
          
          // Если не найден в глобальных и задан chartId, ищем в конкретном графике
          if (chartId && state.indicatorsByChartId[chartId]) {
            state.indicatorsByChartId[chartId] = state.indicatorsByChartId[chartId]
              .filter((indicator: IndicatorInstance) => indicator.instanceId !== instanceId);
          } else if (!chartId) {
            // Если chartId не задан, ищем во всех графиках
            Object.keys(state.indicatorsByChartId).forEach((id) => {
              state.indicatorsByChartId[id] = state.indicatorsByChartId[id]
                .filter((indicator: IndicatorInstance) => indicator.instanceId !== instanceId);
            });
          }
        }),

      updateIndicatorInstance: (instanceId, updates, chartId) =>
        set((state) => {
          // Find and update logic (global first, then specific or all charts)
          const updateInstance = (instance: IndicatorInstance): IndicatorInstance => {
            // Create a new object with the merged updates
            const updatedInstance = { ...instance };

            // Merge top-level properties
            if (updates.visible !== undefined) updatedInstance.visible = updates.visible;
            if (updates.tableTimeframe !== undefined) updatedInstance.tableTimeframe = updates.tableTimeframe;
            if (updates.tableOutputIds !== undefined) updatedInstance.tableOutputIds = updates.tableOutputIds;

            // Deep merge parameters
            if (updates.params) {
              updatedInstance.params = { ...instance.params, ...updates.params };
            }

            // Deep merge styleOverrides
            if (updates.styleOverrides) {
              updatedInstance.styleOverrides = { ...instance.styleOverrides, ...updates.styleOverrides };
            }

            return updatedInstance;
          };

          let found = false;

          // Проверяем сначала в глобальных индикаторах
          const globalIndex = state.globalIndicators.findIndex(
            (indicator: IndicatorInstance) => indicator.instanceId === instanceId
          );
          
          if (globalIndex !== -1) {
            state.globalIndicators[globalIndex] = updateInstance(state.globalIndicators[globalIndex]);
            found = true;
            return;
          }
          
          // Если не найден в глобальных и задан chartId, ищем в конкретном графике
          if (chartId && state.indicatorsByChartId[chartId]) {
            const indicatorIndex = state.indicatorsByChartId[chartId].findIndex(
              (indicator: IndicatorInstance) => indicator.instanceId === instanceId
            );
            if (indicatorIndex !== -1) {
              state.indicatorsByChartId[chartId][indicatorIndex] = updateInstance(state.indicatorsByChartId[chartId][indicatorIndex]);
              found = true;
            }
          } else if (!chartId) {
            // Если chartId не задан, ищем во всех графиках
            Object.keys(state.indicatorsByChartId).forEach((id) => {
              const indicatorIndex = state.indicatorsByChartId[id].findIndex(
                (indicator: IndicatorInstance) => indicator.instanceId === instanceId
              );
              if (indicatorIndex !== -1) {
                state.indicatorsByChartId[id][indicatorIndex] = updateInstance(state.indicatorsByChartId[id][indicatorIndex]);
                found = true;
              }
            });
          }

          if (!found) {
            console.warn(`[IndicatorStore] updateIndicatorInstance: Instance with ID ${instanceId} not found.`);
          }
        }),

      makeIndicatorGlobal: (instanceId, chartId) =>
        set((state) => {
          const chartIndicators = state.indicatorsByChartId[chartId];
          if (!chartIndicators) return;

          const indicatorIndex = chartIndicators.findIndex((indicator: IndicatorInstance) => indicator.instanceId === instanceId);
          if (indicatorIndex !== -1) {
            const [indicatorToMove] = chartIndicators.splice(indicatorIndex, 1);
            // Убедимся, что индикатор не дублируется в глобальных
            if (!state.globalIndicators.find((gi: IndicatorInstance) => gi.instanceId === indicatorToMove.instanceId)) {
              state.globalIndicators.push({ ...indicatorToMove, chartId: 'global' }); // Update chartId for clarity
            }
          }
        }),

      makeIndicatorLocal: (instanceId, targetChartId) =>
        set((state) => {
          const globalIndicatorIndex = state.globalIndicators.findIndex((indicator: IndicatorInstance) => indicator.instanceId === instanceId);

          if (globalIndicatorIndex !== -1) {
            const [indicatorToMove] = state.globalIndicators.splice(globalIndicatorIndex, 1);
            if (!state.indicatorsByChartId[targetChartId]) {
              state.indicatorsByChartId[targetChartId] = [];
            }
            // Убедимся, что индикатор не дублируется в локальных для этого графика
            if (!state.indicatorsByChartId[targetChartId].find((li: IndicatorInstance) => li.instanceId === indicatorToMove.instanceId)) {
              state.indicatorsByChartId[targetChartId].push({ ...indicatorToMove, chartId: targetChartId });
            }
          }
        }),

      resetIndicatorsForChart: (chartId) =>
        set((state) => {
          delete state.indicatorsByChartId[chartId];
        }),

      resetAllIndicators: () => set(initialState), // Reset to the initial state object
    })),
    { // Options for persist middleware
      name: LS_INDICATOR_SETTINGS_KEY,
      storage: createJSONStorage(() => {
        // Проверяем, что код выполняется в браузере
        const isClient = typeof window !== 'undefined';
        if (!isClient) return {
          getItem: () => Promise.resolve(null),
          setItem: () => Promise.resolve(),
          removeItem: () => Promise.resolve()
        };
        
        // Очищаем хранилище от старых несовместимых данных
        try {
          localStorage.removeItem(LS_INDICATOR_SETTINGS_KEY);
        } catch (e) {
          console.warn('[IndicatorStore] Error removing old settings:', e);
        }
        return localStorage;
      }),
      partialize: (state) => ({
        indicatorsByChartId: state.indicatorsByChartId,
        globalIndicators: state.globalIndicators,
      }),
      merge: (persisted, current) => {
        if (!persisted || typeof persisted !== 'object') {
          console.log('[IndicatorStore] Missing or invalid persisted state');
          return current;
        }
        
        try {
          // Базовое слияние с сохранением методов из текущего состояния
          const result = {
            ...current
          };
          
          // Безопасное копирование indicatorsByChartId с правильным типом
          const typedPersisted = persisted as Partial<IndicatorState>;
          
          if (typedPersisted.indicatorsByChartId && typeof typedPersisted.indicatorsByChartId === 'object') {
            result.indicatorsByChartId = {};
            // Копируем каждую группу индикаторов для каждого chartId
            for (const chartId in typedPersisted.indicatorsByChartId) {
              if (Object.prototype.hasOwnProperty.call(typedPersisted.indicatorsByChartId, chartId)) {
                const indicators = typedPersisted.indicatorsByChartId[chartId];
                if (Array.isArray(indicators)) {
                  result.indicatorsByChartId[chartId] = [...indicators];
                }
              }
            }
          } else {
            // Если нет данных в persisted, используем пустой объект
            result.indicatorsByChartId = {};
          }
          
          // Безопасное копирование globalIndicators
          if (typedPersisted.globalIndicators && Array.isArray(typedPersisted.globalIndicators)) {
            result.globalIndicators = [...typedPersisted.globalIndicators];
          } else {
            // Если нет данных в persisted, используем пустой массив
            result.globalIndicators = [];
          }
          
          return result;
        } catch (error) {
          console.error('[IndicatorStore] Error during state merge:', error);
          return current;
        }
      },
    }
  )
);
