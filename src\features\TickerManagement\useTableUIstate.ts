// Hooks specific to the TickerManagement feature will go here.
// export {}; // Placeholder - can be removed if other exports exist or added if truly empty.

import { useMemo } from 'react';
// Removed Ticker, MarketType, ProcessedTicker as they are not directly used by useTableStateConfig
// but ColumnWidths is used indirectly via AppSettingsColumnWidths.
import {
  useAppSettingsStore,
  selectSortConfigs,
  selectColumnOrder,
  selectVisibleColumns,
  selectColumnWidths,
  selectUpdateSortConfigs,
  selectSetColumnOrder,
  selectSetColumnWidths,
} from '@/shared/store/settingsStore';
// MarketStore is not used by useTableStateConfig, so it's removed.

// --- Hook for Table State Configuration ---
import {
  SortingState, ColumnOrderState, VisibilityState, ColumnSizingState,
} from '@tanstack/react-table';
import { ColumnWidths as AppSettingsColumnWidths } from '@/shared/index'; // Keep this for type casting

/**
 * Interface for the result of the useTableStateConfig hook.
 * Defines the shape of the table state configuration object.
 */
interface UseTableStateConfigResult {
  tableSortingState: SortingState;
  columnOrder: ColumnOrderState;
  tableVisibilityState: VisibilityState;
  tableSizingState: ColumnSizingState;
  // Zustand setters for updating table state in the global store
  updateZustandSortConfigs: (newSortingState: SortingState) => void;
  setZustandColumnOrder: (newOrder: ColumnOrderState) => void;
  setZustandColumnSizing: (newSizing: AppSettingsColumnWidths) => void;
}

/**
 * Custom hook to manage and retrieve table state configuration from Zustand store.
 * This hook centralizes the logic for accessing and updating table-related settings
 * such as sorting, column order, visibility, and sizing.
 * @returns An object containing the current table state and functions to update it.
 */
export const useTableStateConfig = (): UseTableStateConfigResult => {
  // Selectors for reading current table state from Zustand
  const sortConfigs = useAppSettingsStore(selectSortConfigs);
  const columnOrder = useAppSettingsStore(selectColumnOrder);
  const columnVisibility = useAppSettingsStore(selectVisibleColumns);
  const columnSizing = useAppSettingsStore(selectColumnWidths);

  // Selectors for action functions to update table state in Zustand
  const updateZustandSortConfigs = useAppSettingsStore(selectUpdateSortConfigs);
  const setZustandColumnOrder = useAppSettingsStore(selectSetColumnOrder);
  const zustandSetColumnSizing = useAppSettingsStore(selectSetColumnWidths);

  // Memoized transformation of sortConfigs from store to TanStack Table format
  const tableSortingState = useMemo<SortingState>(() =>
    sortConfigs.map(s => ({
      id: s.id === 'quoteVolumeRaw' ? 'volume' : s.id, // Handle potential legacy ID
      desc: s.desc
    })),
  [sortConfigs]);

  // Memoized transformation of columnVisibility from store to TanStack Table format
  const tableVisibilityState = useMemo<VisibilityState>(() => {
    return Object.entries(columnVisibility).reduce((acc, [key, value]) => {
      if (typeof value === 'boolean') acc[key] = value;
      return acc;
    }, {} as VisibilityState);
  }, [columnVisibility]);

  // Memoized transformation of columnSizing from store to TanStack Table format
  const tableSizingState = useMemo<ColumnSizingState>(() => {
    return Object.entries(columnSizing).reduce((acc, [key, value]) => {
      if (typeof value === 'number') {
        const correctedKey = key === 'quoteVolumeRaw' ? 'volume' : key; // Handle potential legacy ID
        acc[correctedKey] = value;
      }
      return acc;
    }, {} as ColumnSizingState);
  }, [columnSizing]);

  return {
    tableSortingState,
    columnOrder, // Directly use from store, as it's already in ColumnOrderState format
    tableVisibilityState,
    tableSizingState,
    updateZustandSortConfigs, // Pass through Zustand action
    setZustandColumnOrder,    // Pass through Zustand action
    setZustandColumnSizing: zustandSetColumnSizing, // Pass through Zustand action
  };
}; 