"use client";

import React, { useState, useEffect } from 'react';
import { Input } from '@/shared/ui/input';
import { Icon } from '@/shared/ui/icons/all_Icon';
import { Button } from '@/shared/ui/button';
import { cn } from '@/shared/lib/utils';

interface TickerSearchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  /** Initial search value */
  initialValue?: string;
  /** Callback when the search value changes */
  onSearchChange: (value: string) => void;
  /** Placeholder text for the input */
  placeholder?: string;
  /** Additional class names */
  className?: string;
  /** Debounce time in milliseconds */
  debounceMs?: number;
   /** Symbol to display when input is empty */
  selectedSymbol?: string | null;
   /** Indicator if filtering is active */
  isFiltering?: boolean;
}

const TickerSearch: React.FC<TickerSearchProps> = ({ 
  initialValue = '', 
  onSearchChange, 
  placeholder = "", 
  className, 
  debounceMs = 300,
  selectedSymbol,
  isFiltering,
  ...props 
}) => {
  const [localSearch, setLocalSearch] = useState(initialValue);
  const [hasFocus, setHasFocus] = useState(false);

  // Debounce effect
  useEffect(() => {
    const handler = setTimeout(() => {
      onSearchChange(localSearch);
    }, debounceMs);
    return () => clearTimeout(handler);
  }, [localSearch, onSearchChange, debounceMs]);

  // Update local state if initialValue changes externally (e.g., reset)
  useEffect(() => {
    setLocalSearch(initialValue);
  }, [initialValue]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearch(event.target.value.toUpperCase()); // Update local state immediately
  };

  const handleClearSearch = () => {
    setLocalSearch(''); // Clear local state, useEffect will trigger onSearchChange
  };

  const handleBlur = () => {
    setHasFocus(false);
  };

  return (
    <div className={cn(
      "relative w-full border border-input rounded-md",
      "h-full",
      className
      // Removed focus:outline-none as it might interfere with standard input focus behavior if needed later
      )}>
      <span className={cn(
        "absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground flex items-center justify-center pointer-events-none z-10",
        isFiltering && 'animate-pulse' // Keep pulse animation if desired
      )}>
         <Icon name="Search" className="h-4 w-4" />
      </span>
      {/* Show selected symbol only if input is empty and symbol exists */}
      {selectedSymbol && !localSearch && (
        <span className="absolute left-8 top-1/2 -translate-y-1/2 text-muted-foreground text-xs font-medium whitespace-nowrap cursor-default z-0 pointer-events-none select-none">
          {selectedSymbol}
        </span>
      )}
      <Input
        type="text"
        value={localSearch}
        onChange={handleInputChange}
        onFocus={() => setHasFocus(true)}
        onBlur={handleBlur}
        placeholder={placeholder}
        className={cn(
          // Remove default h-10, make input fill container height, adjust padding, remove border, set leading
          'h-full w-full rounded-md border-0 bg-background pl-8 pr-8 text-sm outline-none placeholder:text-muted-foreground py-0 leading-none', 
          isFiltering && 'text-primary', // Highlight input text when filtering
          'ring-0 focus:ring-0 focus-visible:ring-0 focus:outline-none focus-visible:outline-none' 
        )}
        {...props}
      />
      {(localSearch || hasFocus || isFiltering) && (
        <button
          className="absolute inset-y-0 right-2 flex items-center"
          onClick={handleClearSearch}
          aria-label="Clear search"
        >
          <Icon name="X" />
        </button>
      )}
    </div>
  );
};

export default TickerSearch; 