import React from 'react';

// Ensure this only runs in development and on the client side
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  const whyDidYouRender = require('@welldone-software/why-did-you-render');
  whyDidYouRender(React, {
    trackAllPureComponents: true, // Track all pure components
    trackHooks: true, // Track custom hooks
    logOwnerReasons: true, // Log reasons based on owner components
    collapseGroups: true, // Collapse console groups
  });
} 