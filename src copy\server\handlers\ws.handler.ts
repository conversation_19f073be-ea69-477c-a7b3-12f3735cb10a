import { Elysia } from 'elysia';
import { ZodError } from 'zod';
import { 
  ClientToServerMessageSchema, 
  ClientToServerMessage, 
  GenericWebSocketMessage as WebSocketMessage,
  PingMessage
} from '../../shared/schemas/market.schema';
import { logger as globalLogger } from '../lib/logger';
import broadcastService from '../services/broadcast.service';
import { exchangeStreamManager } from '../services/exchangeStreamManager';
import { MarketType } from '../../shared/types';

// Define custom WebSocket type with our extensions
interface CustomWebSocket {
  id: string;
  connectionTime: number;
  subscriptions: Set<string>;
  logger: any;
  broadcast: any;
  send: (data: string) => void;
  subscribe: (topic: string) => void;
  unsubscribe: (topic: string) => void;
  publish: (topic: string, data: string) => boolean;
}

// Define a type for WebSocket context
type WSContext = {
  logger: any;
  broadcast: any;
};

/**
 * WebSocket handlers exported as a reusable Elysia plugin
 */
export const wsHandlers = new Elysia()
  // Add decorators types
  .derive(() => {
    return {} as WSContext;
  })
  .ws('/ws', {
    // Configuration
    idleTimeout: 30_000, // 30 seconds
    maxPayloadLength: 1024 * 32, // 32KB
    
    // Event handlers with appropriate types
    open(ws: any) {
      // Initialize custom properties
      ws.connectionTime = Date.now();
      ws.subscriptions = new Set();
      
      // Initialize logger reference
      if (!ws.logger) {
        ws.logger = globalLogger;
      }
      
      ws.logger.info(`[WS] Client connected: ${ws.id}`);
      
      const ackMessage: WebSocketMessage = { 
        type: 'connection_ack', 
        message: 'Connected to MetaCharts WebSocket!' 
      };
      ws.send(JSON.stringify(ackMessage));
    },
    
    message(ws: any, messageInput: string | Record<string, any>) {
      // Ensure logger reference is available
      if (!ws.logger) {
        ws.logger = globalLogger;
      }
      
      let parsedData: Record<string, any>;

      try {
        // Parse message if it's a string
        if (typeof messageInput === 'string') {
          parsedData = JSON.parse(messageInput);
        } else if (typeof messageInput === 'object' && messageInput !== null) {
          parsedData = messageInput; // Already an object
        } else {
          ws.logger.error(`[WS] Invalid message type for processing: ${typeof messageInput}`);
          sendErrorResponse(ws, 'Invalid data type received');
          return;
        }
        
        if (typeof parsedData !== 'object' || parsedData === null) {
          throw new Error(`Invalid message format: expected JSON object, got ${typeof parsedData}`);
        }

        // Validate message against schema
        const validationResult = ClientToServerMessageSchema.safeParse(parsedData);
        if (!validationResult.success) {
          ws.logger.warn(`[WS] Validation failed for client ${ws.id}`);
          sendErrorResponse(ws, 'Invalid message format or content', validationResult.error.flatten());
          return;
        }

        const message = validationResult.data as ClientToServerMessage;
        
        // Process validated message
        switch (message.type) {
          case 'subscribe':
            handleSubscribe(ws, message);
            break;
            
          case 'unsubscribe':
            handleUnsubscribe(ws, message);
            break;
            
          case 'publish':
            handlePublish(ws, message);
            break;
            
          case 'ping':
            handlePing(ws, message as PingMessage);
            break;
            
          default:
            const unhandledType = (message as any).type || 'unknown';
            ws.logger.warn(`[WS] Unhandled message type: ${unhandledType} from client ${ws.id}`);
            ws.send(JSON.stringify({ 
              type: 'info', 
              message: `Message type '${unhandledType}' not handled`
            }));
        }
      } catch (e) {
        handleMessageError(ws, e, messageInput);
      }
    },
    
    close(ws: any, code: number, reason: string) {
      // Ensure logger reference is available
      if (!ws.logger) {
        ws.logger = globalLogger;
      }
      
      // Clean up any stored client data
      const subscriptions = ws.subscriptions ? Array.from(ws.subscriptions).join(', ') : 'none';
      ws.logger.info(`[WS] Client disconnected: ${ws.id}, Code: ${code}, Reason: ${reason || 'N/A'}, Subscriptions: ${subscriptions}`);
      
      // Clean up client data
      delete ws.subscriptions;
      delete ws.connectionTime;
    }
  });

/**
 * Handle subscribe request
 */
async function handleSubscribe(ws: CustomWebSocket, message: ClientToServerMessage) {
  if (!message.topics || !Array.isArray(message.topics) || message.topics.length === 0) {
    ws.logger.warn(`[WS] Client ${ws.id} tried to subscribe without valid topics`);
    sendErrorResponse(ws, 'No topics provided for subscription');
    return;
  }
  
  // Track subscriptions on client
  if (!ws.subscriptions) {
    ws.subscriptions = new Set();
  }
  
  // Process each topic
  for (const topic of message.topics) {
    // Add to client's subscriptions
    ws.subscriptions.add(topic);
    
    // Subscribe client to topic in the app's broadcast system
    try {
      ws.subscribe(topic);
      ws.logger.info(`[WS] Client ${ws.id} subscribed to internal topic ${topic}`);

      // Added: Dynamic subscription to exchange kline streams
      if (topic.startsWith('kline_')) {
        const parts = topic.split('_');
        if (parts.length === 4) { // kline_marketType_symbol_interval
          const marketTypeString = parts[1]; // Renamed for clarity
          const symbol = parts[2].toUpperCase(); // Ensure symbol is uppercase for exchange APIs
          const interval = parts[3];
          try {
            // Corrected: Cast marketTypeString to MarketType
            await exchangeStreamManager.subscribeToExchangeKlineStream(marketTypeString as MarketType, symbol, interval);
            ws.logger.info(`[WS] Client ${ws.id} triggered exchange kline stream subscription for: ${marketTypeString}:${symbol}:${interval}`);
          } catch (exchangeSubError) {
            ws.logger.error(`[WS] Failed to subscribe to exchange kline stream for ${marketTypeString}:${symbol}:${interval}:`, exchangeSubError);
            // Optionally notify client about failure to get exchange data
          }
        } else {
          ws.logger.warn(`[WS] Invalid kline topic format for exchange subscription: ${topic}`);
        }
      }
      
      // Send acknowledgment
      ws.send(JSON.stringify({
        type: 'subscribe_ack',
        topic,
        status: 'success'
      }));
    } catch (error) {
      ws.logger.error(`[WS] Failed to subscribe client ${ws.id} to ${topic}:`, error);
      ws.send(JSON.stringify({
        type: 'subscribe_ack',
        topic,
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  }
}

/**
 * Handle unsubscribe request
 */
async function handleUnsubscribe(ws: CustomWebSocket, message: ClientToServerMessage) {
  if (!message.topic || typeof message.topic !== 'string') {
    ws.logger.warn(`[WS] Client ${ws.id} tried to unsubscribe without a valid topic`);
    sendErrorResponse(ws, 'No topic provided for unsubscription');
    return;
  }
  
  const topic = message.topic;
  
  // Check if client was subscribed to this topic
  if (!ws.subscriptions || !ws.subscriptions.has(topic)) {
    ws.logger.warn(`[WS] Client ${ws.id} tried to unsubscribe from ${topic} but was not subscribed`);
    ws.send(JSON.stringify({
      type: 'unsubscribe_ack',
      topic,
      status: 'error',
      message: 'Not subscribed to this topic'
    }));
    return;
  }
  
  // Unsubscribe from topic
  try {
    ws.unsubscribe(topic);
    ws.subscriptions.delete(topic);
    
    ws.logger.info(`[WS] Client ${ws.id} unsubscribed from internal topic ${topic}`);

    // Added: Dynamic unsubscription from exchange kline streams
    if (topic.startsWith('kline_')) {
      const parts = topic.split('_');
      if (parts.length === 4) { // kline_marketType_symbol_interval
        const marketTypeString = parts[1]; // Renamed for clarity
        const symbol = parts[2].toUpperCase(); // Ensure symbol is uppercase
        const interval = parts[3];
        try {
          // Corrected: Cast marketTypeString to MarketType
          await exchangeStreamManager.unsubscribeFromExchangeKlineStream(marketTypeString as MarketType, symbol, interval);
          ws.logger.info(`[WS] Client ${ws.id} triggered exchange kline stream unsubscription check for: ${marketTypeString}:${symbol}:${interval}`);
        } catch (exchangeUnsubError) {
          ws.logger.error(`[WS] Failed to unsubscribe from exchange kline stream for ${marketTypeString}:${symbol}:${interval}:`, exchangeUnsubError);
          // Optionally notify client about failure
        }
      } else {
        ws.logger.warn(`[WS] Invalid kline topic format for exchange unsubscription: ${topic}`);
      }
    }
    
    // Send acknowledgment
    ws.send(JSON.stringify({
      type: 'unsubscribe_ack',
      topic,
      status: 'success'
    }));
  } catch (error) {
    ws.logger.error(`[WS] Failed to unsubscribe client ${ws.id} from ${topic}:`, error);
    ws.send(JSON.stringify({
      type: 'unsubscribe_ack',
      topic,
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }));
  }
}

/**
 * Handle publish request - note: client-initiated publishes should be restricted in production
 */
function handlePublish(ws: CustomWebSocket, message: ClientToServerMessage) {
  if (message.topic && typeof message.topic === 'string' && message.payload) {
    // Ensure payload is stringified if it's an object
    const payloadString = typeof message.payload === 'string' 
      ? message.payload 
      : JSON.stringify(message.payload);
      
    // Publish message to topic
    try {
      const publishResult = ws.publish(message.topic, payloadString);
      
      // Log only for non-kline topics (to reduce noise)
      if (!message.topic.startsWith('kline_')) {
        ws.logger.info(`[WS] Client ${ws.id} published to ${message.topic}`);
      }
      
      // Send acknowledgment to client
      ws.send(JSON.stringify({ 
        type: 'publish_ack', 
        topic: message.topic, 
        status: publishResult ? 'success' : 'error'
      }));
    } catch (error) {
      ws.logger.error(`[WS] Failed to publish from client ${ws.id} to ${message.topic}:`, error);
      ws.send(JSON.stringify({ 
        type: 'publish_ack', 
        topic: message.topic, 
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  } else {
    ws.logger.warn(`[WS] Client ${ws.id} tried to publish without a valid topic or payload`);
    sendErrorResponse(ws, 'No valid topic or payload provided for publishing');
  }
}

/**
 * Handle ping request
 */
function handlePing(ws: CustomWebSocket, message: PingMessage) {
  // Return a pong with the same timestamp if provided, or current time
  const timestamp = message.timestamp || Date.now();
  ws.send(JSON.stringify({ 
    type: 'pong', 
    timestamp 
  }));
}

/**
 * Handle message processing errors
 */
function handleMessageError(ws: CustomWebSocket, e: any, messageInput: any) {
  // Ensure logger reference is available
  if (!ws.logger) {
    ws.logger = globalLogger;
  }
  
  // Format different error types appropriately
  let errorDetails: any = { message: 'Unknown error' };
  
  if (e instanceof ZodError) {
    errorDetails = e.flatten();
    ws.logger.warn(`[WS] Schema validation error for client ${ws.id}:`, errorDetails);
  } else if (e instanceof SyntaxError) {
    errorDetails = { message: 'Invalid JSON format' };
    ws.logger.warn(`[WS] JSON parse error for client ${ws.id}: ${e.message}`);
  } else if (e instanceof Error) {
    errorDetails = { message: e.message };
    ws.logger.error(`[WS] Error processing message for client ${ws.id}: ${e.message}`);
  } else {
    ws.logger.error(`[WS] Unknown error type for client ${ws.id}:`, e);
  }
  
  // Send error response to client
  sendErrorResponse(ws, 'Error processing message', errorDetails);
}

/**
 * Send formatted error response to WebSocket client
 */
function sendErrorResponse(ws: CustomWebSocket, message: string, details: Record<string, any> = {}) {
  ws.send(JSON.stringify({ 
    type: 'error', 
    message,
    ...details
  }));
} 