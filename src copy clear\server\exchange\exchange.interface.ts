import { EventEmitter } from 'events';
import { MarketType } from '../../shared/types';

/**
 * Base interface for market data models
 */
export interface MarketDataModel {
  symbol: string;
  marketType: MarketType;
}

/**
 * Interface for symbol information
 */
export interface SymbolInfo extends MarketDataModel {
  baseAsset: string;
  quoteAsset: string;
  status: string;
}

/**
 * Interface for ticker data
 */
export interface Ticker extends MarketDataModel {
  price: number;
  priceChangePercent: number;
  volume: number;
  quoteVolume: number;
  count: number;
  lastUpdated: number;
}

/**
 * Interface for detailed ticker data
 */
export interface FullTicker extends Ticker {
  priceChange: number;
  weightedAvgPrice: number;
  prevClosePrice: number;
  lastQty: number;
  bidPrice: number;
  bidQty: number;
  askPrice: number;
  askQty: number;
  openPrice: number;
  highPrice: number;
  lowPrice: number;
  openTime: number;
  closeTime: number;
  firstId: number;
  lastId: number;
}

/**
 * Interface for candlestick/kline data
 */
export interface Kline extends MarketDataModel {
  interval: string;
  openTime: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  closeTime: number;
  quoteVolume: number;
  trades: number;
  isClosed: boolean;
}

/**
 * Type definition for common exchange event types
 */
export type ExchangeEventType = 
  | 'ticker'
  | 'kline'
  | 'symbol'
  | 'error'
  | 'reconnect';

/**
 * Base interface for exchange service events
 */
export interface ExchangeEvent<T = any> {
  type: ExchangeEventType;
  marketType: MarketType;
  data: T;
  timestamp: number;
  exchange: string;
}

/**
 * Specific event for ticker data
 */
export interface TickerEvent extends ExchangeEvent<FullTicker[]> {
  type: 'ticker';
}

/**
 * Specific event for kline data
 */
export interface KlineEvent extends ExchangeEvent<Kline> {
  type: 'kline';
  symbol: string;
  interval: string;
}

/**
 * Specific event for symbol data
 */
export interface SymbolEvent extends ExchangeEvent<SymbolInfo[]> {
  type: 'symbol';
}

/**
 * Options for fetching historical klines
 */
export interface KlineOptions {
  limit?: number;
  startTime?: number;
  endTime?: number;
}

/**
 * Core interface for any exchange service implementation
 * Extends EventEmitter to emit standardized events for data updates
 */
export interface ExchangeService extends EventEmitter {
  /**
   * The name of the exchange (e.g., "binance", "kucoin")
   */
  readonly exchangeName: string;
  
  /**
   * Initialize the exchange service
   */
  initialize(): Promise<void>;
  
  /**
   * Shutdown the exchange service gracefully
   */
  shutdown(): Promise<void>;
  
  /**
   * Fetch available symbols for a market type
   */
  fetchSymbols(marketType: MarketType): Promise<SymbolInfo[]>;
  
  /**
   * Fetch current tickers for a market type
   */
  fetchTickers(marketType: MarketType): Promise<FullTicker[]>;
  
  /**
   * Fetch historical klines for a specific symbol and interval
   */
  fetchKlines(marketType: MarketType, symbol: string, interval: string, options?: KlineOptions): Promise<Kline[]>;
  
  /**
   * Subscribe to real-time ticker updates for a market type
   * Returns an unsubscribe function
   */
  subscribeTickers(marketType: MarketType): () => void;
  
  /**
   * Subscribe to real-time kline updates for a symbol and interval
   * Returns an unsubscribe function
   */
  subscribeKline(marketType: MarketType, symbol: string, interval: string): () => void;
} 