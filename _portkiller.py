#!/usr/bin/env python
import os
import re
import signal
import subprocess
import sys
import json
from typing import List, Dict, Set, Optional, Tuple

# Helper function to get encoding based on platform
def get_platform_encoding() -> str:
    return 'cp866' if sys.platform.startswith('win') else 'utf-8'

def find_project_ports(project_dir: str = ".") -> Set[int]:
    """
    Automatically finds ports used in the project by analyzing configuration files.
    """
    ports = set()
    default_ports = {3000, 5432, 6379, 8000, 9092} # Common ports
    ports.update(default_ports)

    # Search in package.json
    package_path = os.path.join(project_dir, "package.json")
    if os.path.exists(package_path):
        try:
            with open(package_path, "r", encoding="utf-8") as f:
                package_data = json.load(f)
                if "scripts" in package_data:
                    scripts_text = json.dumps(package_data["scripts"])
                    port_matches = re.findall(r'(?:PORT=|--port[ =])(\\d+)', scripts_text)
                    ports.update(int(port) for port in port_matches)
        except (json.JSONDecodeError, IOError):
            print("⚠️ Could not read or parse package.json")

    # Search in .env files
    env_files = [".env", ".env.local", ".env.development"]
    for env_file in env_files:
        env_path = os.path.join(project_dir, env_file)
        if os.path.exists(env_path):
            try:
                with open(env_path, "r", encoding="utf-8") as f:
                    for line in f:
                        port_match = re.search(r'(?:^|_)PORT=(\\d+)', line)
                        if port_match:
                            ports.add(int(port_match.group(1)))
            except IOError:
                print(f"⚠️ Could not read {env_file}")

    # Search in next.config.js or next.config.mjs
    for config_file_name in ["next.config.js", "next.config.mjs"]:
        next_config_path = os.path.join(project_dir, config_file_name)
        if os.path.exists(next_config_path):
            try:
                with open(next_config_path, "r", encoding="utf-8") as f:
                    content = f.read()
                    port_matches = re.findall(r'port[\\s:]*(\\d+)', content) # More flexible regex for port
                    ports.update(int(port) for port in port_matches)
            except IOError:
                print(f"⚠️ Could not read {config_file_name}")
            break # Found one, no need to check the other

    # Recursive search for ports in various config files
    print("\\n🔍 Performing recursive search for ports in configuration files...")
    config_patterns = [
        re.compile(r'\\.env.*$'),
        re.compile(r'(config\\.[jt]s|\\.config\\.[jt]s|docker-compose\\.ya?ml)$'),
        re.compile(r'\\.(ya?ml)$'),
        re.compile(r'\\.json$') # Added JSON files
    ]
    
    found_files_count = 0
    scanned_files_count = 0

    for root, dirs, files in os.walk(project_dir):
        if 'node_modules' in dirs:
            dirs.remove('node_modules')
        if '.git' in dirs:
            dirs.remove('.git')
            
        for file_name in files:
            if any(pattern.search(file_name) for pattern in config_patterns):
                file_path = os.path.join(root, file_name)
                scanned_files_count += 1
                try:
                    with open(file_path, "r", encoding="utf-8", errors='ignore') as f: # Added errors='ignore'
                        content = f.read()
                        
                        # More comprehensive port searching regex
                        # Looks for port assignments, port numbers in URLs, port arguments, etc.
                        # Examples: port: 3000, port=3000, "port": 3000, localhost:3000, --port 3000, ports: ["3000"]
                        # Avoids matching very large numbers or numbers in irrelevant contexts
                        current_file_ports = set()
                        # General port assignments and declarations
                        matches = re.findall(r'(?:port(?:s)?\"?[\s:]*\"?|listen(?:ing)?_port\"?[\s:]*\"?|server_port\"?[\s:]*\"?|application_port\"?[\s:]*\"?|containerPort\"?[\s:]*\"?|targetPort\"?[\s:]*\"?|nodePort\"?[\s:]*\"?|--port(?:[= ]|[\s"]*))([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])', content, re.IGNORECASE)
                        current_file_ports.update(int(p) for p in matches)

                        # Ports in URLs like localhost:3000, 0.0.0.0:8080
                        matches = re.findall(r':([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])(?:\"|\'|\s|\/|$|,)', content)
                        current_file_ports.update(int(p) for p in matches)

                        # Ports in lists/arrays: ports: [ "3000", "3001" ] or ports: - "3000"
                        if file_name.endswith(('.yml', '.yaml')):
                            matches = re.findall(r'ports?:\s*(?:-\s*\"?\'?(\d+)\"?\'?|\s*\[\s*(?:\"?\'?(\d+)\"?\'?\s*,?\s*)+\])', content, re.IGNORECASE | re.MULTILINE)
                            for match_group in matches:
                                for port_val in match_group:
                                    if port_val:
                                        current_file_ports.add(int(port_val))
                        
                        if current_file_ports:
                            valid_ports = {p for p in current_file_ports if 1 <= p <= 65535}
                            if valid_ports:
                                ports.update(valid_ports)
                                found_files_count += 1
                                relative_path = os.path.relpath(file_path, project_dir)
                                print(f"  📄 Found ports in {relative_path}: {', '.join(map(str, sorted(list(valid_ports)) ))}")
                except (UnicodeDecodeError, IOError):
                    continue # Skip binary or inaccessible files
    
    print(f"✅ Recursive search complete. Scanned {scanned_files_count} files, found ports in {found_files_count} files.")
    return ports

def get_docker_containers() -> List[Dict]:
    """
    Gets a list of running Docker containers and their ports.
    """
    containers = []
    try:
        encoding = get_platform_encoding()
        # Added --no-trunc to get full container IDs if needed, and simplified format
        output = subprocess.check_output('docker ps --format "{{.ID}};;{{.Names}};;{{.Ports}}"', shell=True, stderr=subprocess.PIPE).decode(encoding, errors='replace')
        
        for line in output.strip().splitlines():
            parts = line.split(';;')
            if len(parts) == 3:
                container_id, container_name, ports_info = parts[0].strip(), parts[1].strip(), parts[2].strip()
                
                # Extract exposed ports (host ports)
                # Regex to find host ports in mappings like 0.0.0.0:3000->3000/tcp or :::3000->3000/tcp
                port_numbers = set()
                # Matches 0.0.0.0:XXXX->, :::XXXX->, or just XXXX/tcp (less common for host port but good to catch)
                matches = re.findall(r'(?:0\.0\.0\.0:|:::)(\d+)->|\b(\d+)\/tcp', ports_info)
                for match_group in matches:
                    for port_str in match_group:
                        if port_str:
                            try:
                                port_num = int(port_str)
                                if 1 <= port_num <= 65535:
                                    port_numbers.add(port_num)
                            except ValueError:
                                continue # Should not happen with this regex but good to be safe
                
                if port_numbers:
                    containers.append({
                        'id': container_id,
                        'name': container_name,
                        'ports': sorted(list(port_numbers)) # Store as sorted list
                    })
    except subprocess.CalledProcessError as e:
        if "Cannot connect to the Docker daemon" in e.stderr.decode(get_platform_encoding(), errors='replace'):
            print("⚠️ Docker daemon is not running or Docker is not installed.")
        else:
            print(f"⚠️ Could not get Docker container list: {e.stderr.decode(get_platform_encoding(), errors='replace').strip()}")
    except FileNotFoundError:
        print("⚠️ Docker command not found. Is Docker installed and in PATH?")
    except UnicodeDecodeError:
        print("⚠️ Unicode decoding error while fetching Docker containers.")
        
    return containers

def stop_docker_container(container_id_or_name: str) -> bool:
    """
    Stops a Docker container by ID or name (force kill).
    """
    try:
        # Using kill for immediate stop as requested
        subprocess.check_call(f'docker kill {container_id_or_name}', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to kill Docker container {container_id_or_name}: {e.stderr.decode(get_platform_encoding(), errors='replace').strip()}")
        return False

def get_processes_on_port(port: int) -> List[Dict]:
    """
    Finds processes using the specified port.
    """
    processes = []
    encoding = get_platform_encoding()
    
    try:
        if sys.platform.startswith('win'):
            # Using netstat -ano -p TCP to be more specific and findstr for filtering
            cmd = f'netstat -ano -p TCP | findstr /R /C:"TCP.*:{port}.*LISTENING"'
            output = subprocess.check_output(cmd, shell=True, stderr=subprocess.PIPE).decode(encoding, errors='replace')
            for line in output.strip().splitlines():
                parts = line.strip().split()
                if len(parts) >= 4: # Simplified check
                    pid_str = parts[-1]
                    if pid_str.isdigit():
                        pid = int(pid_str)
                        # Get process name using tasklist
                        try:
                            tasklist_out = subprocess.check_output(f'tasklist /FI "PID eq {pid}" /NH /FO CSV', shell=True, stderr=subprocess.PIPE).decode(encoding, errors='replace')
                            if tasklist_out.strip():
                                process_name = tasklist_out.split(',')[0].strip('"')
                                processes.append({'pid': pid, 'name': process_name, 'port': port})
                        except subprocess.CalledProcessError:
                             processes.append({'pid': pid, 'name': 'N/A (tasklist failed)', 'port': port})
                        except UnicodeDecodeError:
                            print(f"⚠️ Unicode decoding error for tasklist PID {pid}")
                            processes.append({'pid': pid, 'name': 'N/A (decode error)', 'port': port})
        else: # Linux/macOS
            # Using lsof -i TCP:{port} -sTCP:LISTEN -P -n -t to get PIDs directly
            # -P: inhibits the conversion of port numbers to port names for network files.
            # -n: inhibits the conversion of network numbers to host names for network files.
            # -t: specifies that lsof should produce terse output with process identifiers only
            cmd = f'lsof -i TCP:{port} -sTCP:LISTEN -P -n -t'
            output = subprocess.check_output(cmd, shell=True, stderr=subprocess.PIPE).decode('utf-8', errors='replace')
            for pid_str in output.strip().splitlines():
                if pid_str.isdigit():
                    pid = int(pid_str)
                    # Get process name using ps
                    try:
                        ps_out = subprocess.check_output(f'ps -p {pid} -o comm=', shell=True, stderr=subprocess.PIPE).decode('utf-8', errors='replace').strip()
                        processes.append({'pid': pid, 'name': ps_out if ps_out else 'N/A', 'port': port})
                    except subprocess.CalledProcessError:
                        processes.append({'pid': pid, 'name': 'N/A (ps failed)', 'port': port})

    except subprocess.CalledProcessError:
        # This is normal if no process is on the port
        pass
    except FileNotFoundError:
        print(f"⚠️ Command for getting processes not found (netstat/lsof). Ensure they are installed and in PATH.")
    except UnicodeDecodeError:
        print(f"⚠️ Unicode decoding error while fetching processes for port {port}")
            
    return processes

def kill_process_by_pid(pid: int) -> bool:
    """
    Kills a process by PID (force kill).
    """
    try:
        if sys.platform.startswith('win'):
            subprocess.check_call(f'taskkill /F /PID {pid}', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        else:
            os.kill(pid, signal.SIGKILL) # SIGKILL for forceful termination
        return True
    except (subprocess.CalledProcessError, OSError) as e:
        error_msg = ""
        if isinstance(e, subprocess.CalledProcessError) and e.stderr:
            error_msg = e.stderr.decode(get_platform_encoding(), errors='replace').strip()
        elif isinstance(e, OSError):
             error_msg = str(e)
        print(f"❌ Failed to kill process {pid}: {error_msg}")
        return False

def display_info(project_ports: Set[int], docker_containers: List[Dict], running_processes: List[Dict]):
    """
    Displays the gathered information about ports, containers, and processes.
    """
    print("\\n--- Project Ports Identified ---")
    if project_ports:
        print(f"🔢 Ports found in project files: {', '.join(map(str, sorted(list(project_ports)) ))}")
    else:
        print("🤷 No specific ports identified in project files. Default ports and common service ports will be checked.")

    print("\\n--- Running Docker Containers ---")
    if docker_containers:
        print("🐳 Active Docker Containers:")
        for container in docker_containers:
            ports_str = ', '.join(map(str, container['ports'])) if container['ports'] else "No exposed ports"
            print(f"  - Name: {container['name']}, ID: {container['id'][:12]}, Ports: {ports_str}")
    else:
        print("ℹ️ No active Docker containers found or Docker is not running.")

    print("\\n--- Processes on Ports ---")
    # Consolidate all ports to check: project ports + docker exposed ports
    all_ports_to_check = set(project_ports)
    for container in docker_containers:
        all_ports_to_check.update(container['ports'])
    
    # Add common development ports if not already present, to be thorough
    all_ports_to_check.update([3000, 8000, 8080, 5000, 5173, 5432, 6379, 9000, 9092])


    if not all_ports_to_check:
        print("🤷 No ports to check for running processes.")
        # running_processes will be empty if all_ports_to_check is empty.
    
    # We already have running_processes passed as an argument, which should be pre-filtered
    # by main(). Let's just display it.

    if running_processes:
        print("💻 Processes currently using identified or common ports:")
        # Sort by port then PID for consistent display
        sorted_processes = sorted(running_processes, key=lambda p: (p['port'], p['pid']))
        for proc in sorted_processes:
            print(f"  - Port: {proc['port']}, PID: {proc['pid']}, Name: {proc['name']}")
    elif all_ports_to_check: # If we checked ports but found nothing
        print(f"✅ No processes found on ports: {', '.join(map(str, sorted(list(all_ports_to_check)) ))}")
    # If all_ports_to_check was empty and running_processes is empty, we've already printed a message.


def main():
    """
    Main function to scan, display, and kill processes/containers.
    """
    if sys.platform.startswith('win'):
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("⚠️ For best results, run this script as an Administrator on Windows to ensure all processes can be terminated.")

    print("🚀 PortKiller Lite - Starting Scan...")

    project_ports = find_project_ports()
    docker_containers = get_docker_containers()
    
    # Gather processes on all relevant ports (project specified + Docker exposed + common dev ports)
    ports_to_scan_for_processes = set(project_ports)
    for c in docker_containers:
        ports_to_scan_for_processes.update(c['ports'])
    # Add a few more common dev ports just in case
    ports_to_scan_for_processes.update([3000, 8000, 8080, 5000, 5173, 5432, 6379, 9000, 9092, 27017])


    active_processes_on_ports = []
    if ports_to_scan_for_processes:
        print(f"🔍 Checking for processes on ports: {', '.join(map(str, sorted(list(ports_to_scan_for_processes)) ))}")
        for port in sorted(list(ports_to_scan_for_processes)):
            active_processes_on_ports.extend(get_processes_on_port(port))
    
    display_info(project_ports, docker_containers, active_processes_on_ports)

    print("\\n--- Termination Input ---")
    try:
        # Consolidate all potential targets for easier user input
        kill_targets_info = []
        
        # Docker containers by name or ID
        for container in docker_containers:
            kill_targets_info.append(f"Docker Container: {container['name']} (ID: {container['id'][:12]})")
        
        # Processes by PID (and show port)
        # Use a set to avoid listing the same PID multiple times if it's on multiple scanned ports (though rare)
        listed_pids = set()
        for proc in active_processes_on_ports:
            if proc['pid'] not in listed_pids:
                 kill_targets_info.append(f"Process PID: {proc['pid']} (Name: {proc['name']}, Port: {proc['port']})")
                 listed_pids.add(proc['pid'])

        if not kill_targets_info:
            print("🤷 No running processes or Docker containers found that are using known/scanned ports. Nothing to kill.")
            print("👋 Exiting.")
            return

        print("\\n🎯 Identified potential targets for termination:")
        for item in kill_targets_info:
            print(f"  - {item}")
        
        print("\\n👉 Enter comma-separated PIDs, Docker container names/IDs to kill.")
        print("   Example: 1234,my-docker-container,5678,another-container-id")
        target_input = input("   Targets to kill (or press Enter to skip): ").strip()

        if not target_input:
            print("🚫 No targets specified. Exiting.")
            return

        targets_to_kill = [t.strip() for t in target_input.split(',') if t.strip()]
        
        killed_count = 0
        failed_count = 0

        for target in targets_to_kill:
            killed_this_target = False
            # Check if it's a PID
            if target.isdigit():
                pid = int(target)
                print(f"🔪 Attempting to kill process PID: {pid}...")
                if kill_process_by_pid(pid):
                    print(f"✅ Successfully killed process PID: {pid}")
                    killed_count += 1
                    killed_this_target = True
                else:
                    # kill_process_by_pid already prints failure
                    failed_count +=1
            
            # Check if it's a Docker container name or ID (even if it was also a PID)
            # This allows killing a container even if its name is numeric.
            # A bit of overlap is fine; killing something twice harmlessly is better than missing it.
            is_docker_target = False
            for container in docker_containers:
                if target == container['name'] or target == container['id'] or target == container['id'][:12]: # Check short ID too
                    is_docker_target = True
                    print(f"🐳 Attempting to kill Docker container: {container['name']} (ID: {container['id'][:12]})...")
                    if stop_docker_container(container['id']): # Use full ID for kill command
                        print(f"✅ Successfully killed Docker container: {container['name']}")
                        killed_count += 1
                        killed_this_target = True # Mark as killed to avoid double counting if it was also a PID
                    else:
                        # stop_docker_container already prints failure
                        failed_count +=1
                    break # Found and processed this Docker container
            
            if not killed_this_target and not target.isdigit() and not is_docker_target:
                print(f"⚠️ Target '{target}' is not a recognized PID or running Docker container name/ID. Skipping.")


        print("\\n--- Summary ---")
        if killed_count > 0:
            print(f"👍 Successfully killed {killed_count} target(s).")
        if failed_count > 0:
            print(f"👎 Failed to kill {failed_count} target(s). Check messages above for details.")
        if killed_count == 0 and failed_count == 0 and targets_to_kill:
             print("🤷 No specified targets were matched or successfully killed.")
        elif not targets_to_kill:
            print("🤷 No targets were specified to kill.")


    except (KeyboardInterrupt, EOFError):
        print("\\n⚠️ Operation cancelled by user. Exiting.")
    except Exception as e:
        print(f"💥 An unexpected error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
