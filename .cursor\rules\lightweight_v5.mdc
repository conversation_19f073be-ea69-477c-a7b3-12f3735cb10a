---
description: 
globs: 
alwaysApply: false
---
---
title: "Lightweight Charts - Ключевые Правила и Лучшие Практики"
description: "Краткий, действенный набор правил и лучших практик для Lightweight Charts (v5+). Цель - общность и переиспользуемость в проекте MetaCharts."
tags: ["lightweight-charts", "charts", "financial-charts", "rules", "best-practices", "frontend", "v5"]
---

# Lightweight Charts (v5+) – Ключевые Правила и Лучшие Практики

## 0. Введение

*   **Цель**: Набор правил для `Lightweight Charts` в проекте MetaCharts.
*   **Фокус**: Конкретные, действенные, проверяемые правила.
*   **Краткость**: Лаконичность, без избыточной информации.
*   **Адаптация**: Ориентация на MetaCharts, версия v5+.

## 1. Основные Принципы и Концепции

*   **Назначение**: Высокопроизводительные интерактивные финансовые графики для больших объемов временных рядов.
*   **Ключевые Идеи**:
    *   `Производительность`: Быстрая отрисовка, плавная интерактивность.
    *   `Легковесность`: Минимальный размер (v5 ~35KB).
    *   `Кастомизация`: Гибкая настройка вида и поведения.
    *   `Серии (Series)`: Отображение данных (линии, свечи, гистограммы).
    *   `Шкалы (Scales)`: Управление осями цены и времени.
    *   `Плагины (Primitives)`: Расширение через кастомные отрисовки. (Основной способ добавления кастомной функциональности в v5).
    *   `Панели (Panes)`: (Новое в v5) Возможность создания нескольких областей графика.
*   **Обязательно к Прочтению**:
    *   Официальная документация: `https://tradingview.github.io/lightweight-charts/docs`
    *   Ключевые разделы v5: Getting Started, Chart Options, Series Types, Customization, Plugins, Panes, Pixel Perfect Rendering.
*   **Актуальная Версия**: `v5.0.7+` (согласно стеку MetaCharts и последним релизам)
    *   Changelog: `https://github.com/tradingview/lightweight-charts/releases`

## 2. Конфигурация и Настройка Среды

*   **Конфигурация**: Через JavaScript/TypeScript при инициализации графика.
*   **Зависимости**: `lightweight-charts`.
*   **Инструменты**: `Next.js (React), Bun`.

## 3. Рекомендации по Написанию Кода

### 3.1. Структура и Именование

*   Инкапсулировать логику в хуки (`useChartCore`, `useChartDataHandling`).
*   Плагины (primitives) в `src/features/Chart/plugins/`.
*   Описательные имена (`chartInstance`, `candlestickSeries`).

### 3.2. Паттерны и Анти-паттерны

#### Хорошо: Оптимизация обновлений данных
*   **Действие**: Использовать `series.update()` для одиночных новых точек, `series.setData()` для полных или множественных обновлений. Группировать изменения.
*   **Почему**: Улучшает производительность, минимизируя перерисовки.
```typescript
// Good: Real-time update
// series.update(newTick);

// Good: Batching data
// const newKlines = await fetchMoreKlines();
// series.setData(currentKlines.concat(newKlines));
```

#### Хорошо: Предварительная обработка данных
*   **Действие**: Форматировать данные (особенно `time` в секундах Unix timestamp или `YYYY-MM-DD`) до передачи в график.
*   **Почему**: Снижает нагрузку на библиотеку.
```typescript
// Good: Pre-processing time
// const formattedData = rawData.map(d => ({ ...d, time: d.timestamp / 1000 }));
// series.setData(formattedData);
```
#### Хорошо: Использование Pixel Perfect Rendering для плагинов
*   **Действие**: При создании плагинов (Primitives) использовать целочисленные координаты и функции расчета из документации для `bitmap` координат.
*   **Почему**: Обеспечивает четкость и консистентность отрисовки с ядром библиотеки. См. `https://tradingview.github.io/lightweight-charts/docs/plugins/pixel-perfect-rendering`.

#### Плохо: Частое создание/удаление серий
*   **Проблема**: Динамическое создание/удаление серий вместо обновления данных или видимости.
*   **Почему**: Дорогие операции.
```typescript
// Bad: Recreating series frequently
// function toggleSeries(visible) {
//   if (series) chart.removeSeries(series);
//   if (visible) series = chart.addCandlestickSeries();
// }
// Instead: series.applyOptions({ visible: true/false }); or update data.
```

#### Плохо: Прямое манипулирование DOM
*   **Проблема**: Изменение DOM-элементов графика напрямую.
*   **Почему**: Нарушает инкапсуляцию, ведет к ошибкам. Использовать опции.

### 3.3. Производительность

*   **Видимый диапазон**: Использовать `chart.timeScale().subscribeVisibleLogicalRangeChange()` для динамической загрузки данных ("бесконечная" прокрутка).
*   **Обработчики событий**: Дебаунсить/троттлить ресурсоемкие обработчики (например, `resize`).
*   **Количество серий**: Минимизировать число активных серий.
*   **Плагины (Primitives)**: Оптимизировать логику отрисовки, избегать сложных вычислений в `renderer`.
*   **Перекрестие**: Отключать `priceLineVisible` / `timeLineVisible` если не нужны.
*   **Маркеры серий**: В v5.0.4 улучшена производительность. Использовать плагин маркеров (`Series Markers plugin`).
*   **Порядок серий**: (Новое в v5.0.6+) Использовать `series.setSeriesOrder()` для управления порядком отрисовки, если это необходимо.

### 3.4. Безопасность

*   `N/A` (Клиентская библиотека, санировать данные перед передачей).

## 4. Особенности API / Ключевые Функции (v5+)

*   **`createChart(container, options)`**: Инициализация графика.
*   **`chart.addSeries(seriesConstructor, options)`**: Новый унифицированный API в v5 для добавления серий (например `chart.addSeries(CandlestickSeries, options)`).
*   **`series.setData(data)`**: Полная замена данных.
*   **`series.update(bar)`**: Обновление/добавление точки.
*   **`chart.timeScale().subscribeVisibleLogicalRangeChange(handler)`**: Подписка на изменение видимого диапазона.
*   **`chart.getPaneElements()`**: (Новое в v5) Получение элементов панелей.
*   **`series.attachPrimitive(primitive)`**: (Новое в v5) Прикрепление плагина к серии.
*   **`priceScale.setVisibleRange(range)`**: (Новое в v5.0.7+) Программное управление видимым диапазоном ценовой шкалы.
*   **Плагины (Primitives API)**: `IPluginPrimitive`. Водяные знаки и маркеры серий теперь плагины.
    *   Примеры: `src/features/Chart/plugins/usePriceAndTimer.ts`, `useRuler.ts`.

## 5. НЕ ИСПОЛЬЗОВАТЬ / ИЗБЕГАТЬ

*   **НЕ ИСПОЛЬЗОВАТЬ**:
    *   Прямое манипулирование DOM.
    *   Устаревшие API (следить за Changelog).
*   **ИЗБЕГАТЬ**:
    *   Передача огромных датасетов без динамической подгрузки.
    *   Частое `chart.applyOptions()` без необходимости. Группировать изменения.
    *   Ресурсоемкие вычисления в обработчиках событий.
    *   Хранение ссылок на удаленные объекты графика/серий (утечки памяти).

## 6. ВСЕГДА ДЕЛАТЬ

*   **Очистка**: `chart.remove()` при размонтировании компонента React.
*   **Формат времени**: Данные времени в секундах (Unix timestamp) или `'YYYY-MM-DD'`.
*   **React интеграция**: Использовать `useEffect`, `useRef` для управления жизненным циклом.
*   **Кастомизация**: Через `ChartOptions` и `Series Options`.
*   **Проверка контейнера**: Перед `createChart`.
*   **Интерактивность**: Использовать Primitives или API библиотеки.

## 7. Инструменты и Рабочий Процесс

*   **Инструменты**: Браузерные DevTools, React DevTools.
*   **Линтинг/Форматирование**: ESLint, Prettier (проектные настройки).
*   **Тестирование**: `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ: UI-тесты для графиков, возможно Playwright/Cypress для E2E. Хуки - Jest/RTL]`.

## 8. Полезные Сниппеты / Шаблоны Кода

*   **Базовая инициализация в React (v5)**:
    ```typescript
    // // @/features/Chart/Chart.tsx (Conceptual)
    // import { useEffect, useRef } from 'react';
    // import { createChart, IChartApi, CandlestickSeries, OhlcData } from 'lightweight-charts';

    // function MyChartComponent({ data }: { data: OhlcData[] }) {
    //   const containerRef = useRef<HTMLDivElement>(null);
    //   const chartRef = useRef<IChartApi | null>(null);

    //   useEffect(() => {
    //     if (!containerRef.current) return;
    //     chartRef.current = createChart(containerRef.current, { /* ...options... */ });
    //     const candleSeries = chartRef.current.addSeries(CandlestickSeries, { /* ...seriesOptions... */ });
    //     candleSeries.setData(data);
    //     // ... resize listener ...
    //     return () => chartRef.current?.remove();
    //   }, []);

    //   useEffect(() => {
    //     // Logic to update series data when props.data changes, using setData or update
    //   }, [data]);
    //   return <div ref={containerRef} />;
    // }
    ```
*   **Простой плагин (Primitive) - концепт**:
    ```typescript
    // // @/features/Chart/plugins/MyCustomPlugin.ts
    // import { ICustomSeriesPaneView, PaneRendererData, Time, OhlcData } from 'lightweight-charts';
    // class MyCustomPlugin implements ICustomSeriesPaneView<Time, OhlcData, unknown> {
    //   update(data: PaneRendererData<Time, OhlcData>, primitiveOptions?: unknown) { /* ... */ }
    //   renderer() { /* ... return { draw(ctx, xConv, yConv, data) {} } ... */ }
    //   zOrder() { return 'top' as const; }
    //   // hitTest, autoscaleInfo, etc.
    // }
    ```

## 9. Самопроверка / Чек-лист для ИИ

Перед предоставлением решения, ИИ должен проверить:
*   [x] Соответствует ли решение актуальной версии `Lightweight Charts (v5+)?`
*   [x] Применены ли ключевые принципы (производительность, кастомизация, плагины) (см. Раздел 1)?
*   [x] Использованы ли рекомендованные паттерны и избегаются ли анти-паттерны (см. Раздел 3.2)?
*   [x] Учтены ли ограничения "НЕ ИСПОЛЬЗОВАТЬ / ИЗБЕГАТЬ" (см. Раздел 5)?
*   [x] Выполнены ли требования "ВСЕГДА ДЕЛАТЬ" (см. Раздел 6)?
*   [x] Код читаем, эффективен и безопасен (в контексте клиентской библиотеки)?
*   [x] Язык общения: Русский. Код и комментарии в коде: Английский.

## 10. Завершение Задачи и Отчетность

*   Обновлен и сокращен файл правил `lightweight-charts-rules.md`.
*   Добавлена информация о v5+, включая API панелей, новый метод `addSeries`, pixel-perfect rendering для плагинов и другие улучшения.
*   Сокращены формулировки для уменьшения общего объема токенов.

--- 