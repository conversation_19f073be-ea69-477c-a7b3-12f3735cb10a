'use client';

import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// Optional: If you want React Query DevTools
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Create a client instance (ensure it's only created once per session)
// Using useState ensures it's stable across renders
function makeQueryClient() {
	return new QueryClient({
		defaultOptions: {
			queries: {
				// Default query options can go here
				staleTime: 60 * 1000, // 1 minute
			},
		},
	});
}

let browserQueryClient: QueryClient | undefined = undefined;

function getQueryClient() {
	if (typeof window === 'undefined') {
		// Server: always make a new query client
		return makeQueryClient();
	} else {
		// Browser: make a new query client if we don't already have one
		// This is very important so we don't re-make a new client if React
		// suspends during the initial render. This may not be needed if using
		// React 18 Concurrent Features, but it's safer to keep.
		if (!browserQueryClient) browserQueryClient = makeQueryClient();
		return browserQueryClient;
	}
}

export default function Providers({ children }: { children: React.ReactNode }) {
	// NOTE: Avoid useState when initializing the query client if you are using
	//       concurrent rendering mode in React 18. Instead, create the client
	//       instance outside the component's render cycle.
	// const [queryClient] = React.useState(() => new QueryClient());
	// Use the getter function instead for better SSR/suspense compatibility
	const queryClient = getQueryClient();

	return (
		<QueryClientProvider client={queryClient}>
			{children}
			{/* Optional: React Query DevTools */}
			{/* <ReactQueryDevtools initialIsOpen={false} /> */}
		</QueryClientProvider>
	);
} 