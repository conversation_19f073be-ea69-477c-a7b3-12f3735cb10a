# MetaCharts High-Performance Dev Script
# This script launches infrastructure services via Docker
# and runs the high-performance market data server and frontend

Write-Host "[METACHARTS] Starting High-Performance Market Data System" -ForegroundColor Green
Write-Host "[INFO] Target: 10k+ concurrent WebSocket connections with sub-second latency" -ForegroundColor Cyan

# Starting infrastructure
Write-Host "[INFRA] Starting infrastructure services (Redis, QuestDB, PostgreSQL...)" -ForegroundColor Cyan
docker compose -f docker-compose.infra.yml up -d

# Service status check with better timing
Write-Host "[INFRA] Waiting for services to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

$redisRunning = docker ps --filter "name=metacharts_redis" --format "{{.Status}}" | Select-String "Up" -Quiet
$pgRunning = docker ps --filter "name=metacharts_postgres" --format "{{.Status}}" | Select-String "Up" -Quiet

if ($redisRunning -and $pgRunning) {
    Write-Host "[READY] ✅ All infrastructure services started successfully" -ForegroundColor Green
    Write-Host "[REDIS] Running on localhost:6379" -ForegroundColor Gray
    Write-Host "[POSTGRES] Running on localhost:5432" -ForegroundColor Gray
} else {
    Write-Host "[WARNING] ⚠️  Not all services are running properly!" -ForegroundColor Yellow
    Write-Host "[DEBUG] Check logs: docker compose -f docker-compose.infra.yml logs" -ForegroundColor Gray
    Write-Host "[DEBUG] Continuing with application startup..." -ForegroundColor Gray
}

# Wait a bit more for databases to be fully ready
Write-Host "[INFRA] Allowing services to fully initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# Starting high-performance application
Write-Host "[APP] 🚀 Starting MetaCharts High-Performance Server & Frontend" -ForegroundColor Cyan
Write-Host "[INFO] Server will be available at:" -ForegroundColor Magenta
Write-Host "  📊 API Endpoint: http://localhost:3005" -ForegroundColor Magenta
Write-Host "  🔌 WebSocket: ws://localhost:3005/ws" -ForegroundColor Magenta
Write-Host "  📖 API Docs: http://localhost:3005/docs" -ForegroundColor Magenta
Write-Host "  📈 Health Check: http://localhost:3005/health" -ForegroundColor Magenta
Write-Host "  ⚡ Metrics: http://localhost:3005/api/v1/metrics" -ForegroundColor Magenta
Write-Host "[INFO] Frontend will be available at:" -ForegroundColor Magenta
Write-Host "  🌐 Web UI: http://localhost:3001" -ForegroundColor Magenta
Write-Host "[PERF] Configuration:" -ForegroundColor White
Write-Host "  🎯 Max Connections: 150,000" -ForegroundColor White
Write-Host "  ⚡ Batch Processing: 200ms intervals" -ForegroundColor White
Write-Host "  High Performance Mode: ENABLED" -ForegroundColor White

# Run application with environment variables set for Windows
Write-Host "[APP] Starting development server and frontend..." -ForegroundColor Cyan
$env:WATCHPACK_POLLING = "true"
npm run dev