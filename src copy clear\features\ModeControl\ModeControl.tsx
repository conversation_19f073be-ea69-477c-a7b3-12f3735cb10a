import React, { useState, useEffect, useCallback, memo, useMemo } from 'react';
import { Icon } from '@/shared/ui/icons/all_Icon';
import { Button } from '@/shared/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/shared/ui/select';
import { Separator } from '@/shared/ui/separator';
import { Switch } from '@/shared/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/shared/ui/tooltip";
import { 
  ViewMode, 
  ScreenerSettings, 
  DEFAULT_TIMEFRAMES, 
  SCREENER_CHART_COUNTS,
  SCREENER_UPDATE_INTERVALS
} from '@/shared/index';
import { type ScreenerSortByZod as ScreenerSortBy } from '@/shared/schemas';
import { 
  useAppSettingsStore,
  selectViewMode,
  selectScreenerSettings,
  selectSetViewMode,
  selectUpdateScreenerSettings,
  selectLayoutTypeForCurrentMode
} from '@/shared/store/settingsStore';
import { cn } from '@/shared/lib/utils';
import { useScreenerTickers } from './useScreenerTickers';

interface ModeControlProps {
  className?: string;
}

const ScreenerSortOptions: { value: ScreenerSortBy; label: string; icon: string }[] = [
  { value: 'volume', label: 'Volume', icon: 'BarChart' },
  { value: 'trades', label: 'Trades', icon: 'CandlestickChart' },
  { value: 'price_change', label: 'Price Change', icon: 'ChevronUp' },
  { value: 'volume_change', label: 'Volume Change', icon: 'ChevronDown' },
];

const ModeControl: React.FC<ModeControlProps> = ({ className }) => {
  // Store access
  const currentMode = useAppSettingsStore(selectViewMode);
  const screenerSettings = useAppSettingsStore(selectScreenerSettings);
  const setViewMode = useAppSettingsStore(selectSetViewMode);
  const updateScreenerSettings = useAppSettingsStore(selectUpdateScreenerSettings);
  const currentLayoutType = useAppSettingsStore(selectLayoutTypeForCurrentMode);

  // Hook for screener tickers data (to get total count for pagination)
  const { screenerTickers } = useScreenerTickers();

  const [countdown, setCountdown] = useState<number | null>(null);

  // Handle mode switch
  const handleModeChange = useCallback((mode: ViewMode) => {
    setViewMode(mode);
    console.log(`Switched to ${mode} mode`);
  }, [setViewMode]);

  // Handle screener settings update (simplified: only updates passed values, no autoUpdate side-effects here)
  const handleScreenerUpdate = useCallback((updates: Partial<ScreenerSettings>) => {
    updateScreenerSettings(updates);
    console.log('Screener settings updated:', updates);
  }, [updateScreenerSettings]);

  // Handle sort option click with cycle logic
  const handleSortClick = useCallback((sortBy: ScreenerSortBy) => {
    if (screenerSettings.sortBy === sortBy) {
      // Same sort type - toggle order
      const newOrder = screenerSettings.sortOrder === 'desc' ? 'asc' : 'desc';
      handleScreenerUpdate({ sortOrder: newOrder });
    } else {
      // Different sort type - set new type with desc as default
      handleScreenerUpdate({ 
        sortBy, 
        sortOrder: 'desc' 
      });
    }
  }, [screenerSettings.sortBy, screenerSettings.sortOrder, handleScreenerUpdate]);

  // Auto-update effect
  useEffect(() => {
    let intervalId: NodeJS.Timeout | undefined = undefined;

    if (screenerSettings.autoUpdate && screenerSettings.updateInterval > 0) {
      // Start or reset countdown
      setCountdown(screenerSettings.updateInterval);

      intervalId = setInterval(() => {
        setCountdown(prevCountdown => {
          if (prevCountdown === null || prevCountdown <= 1) {
            // Time to trigger update and reset countdown
            console.log('Screener auto-updating via timer...'); 
            // TODO: Call actual update function here
            return screenerSettings.updateInterval; // Reset to full interval
          }
          return prevCountdown - 1;
        });
      }, 1000);
    } else {
      // Clear countdown if autoUpdate is off or interval is "Off"
      setCountdown(null);
      if (intervalId) {
        clearInterval(intervalId);
      }
    }

    // Cleanup function
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [screenerSettings.autoUpdate, screenerSettings.updateInterval]);

  // Manual update handler for the new Refresh button
  const handleManualRefreshClick = useCallback(() => {
    console.log('Manual refresh clicked, auto-update disabled, interval set to Off');
    updateScreenerSettings({ autoUpdate: false, updateInterval: 0 }); 
    setCountdown(null); // Stop countdown on manual refresh
  }, [updateScreenerSettings]);

  // Handler for the Update Interval Select
  const handleIntervalSelect = useCallback((intervalValue: string) => {
    const newInterval = parseInt(intervalValue);
    // Reset current page to 1 when interval changes
    if (newInterval === 0) { // "Off" is selected
      updateScreenerSettings({ updateInterval: 0, autoUpdate: false, currentPage: 1 });
      setCountdown(null); // Stop countdown if "Off" is selected
    } else {
      updateScreenerSettings({ updateInterval: newInterval, autoUpdate: true, currentPage: 1 });
      setCountdown(newInterval); // Start countdown immediately
    }
  }, [updateScreenerSettings]);

  // Pagination logic
  const itemsPerPage = useMemo(() => {
    const layout = currentMode === 'screener' ? currentLayoutType : '1x1';
    if (!layout) return 1;
    const [rows, cols] = layout.split('x').map(Number);
    return rows * cols;
  }, [currentMode, currentLayoutType]);

  const totalPages = useMemo(() => {
    if (screenerTickers.length === 0 || itemsPerPage === 0) return 1;
    return Math.ceil(screenerTickers.length / itemsPerPage);
  }, [screenerTickers.length, itemsPerPage]);

  const currentPage = screenerSettings.currentPage || 1;

  const handlePageChange = useCallback((newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      updateScreenerSettings({ currentPage: newPage });
    }
  }, [totalPages, updateScreenerSettings]);

  return (
    <div className={`relative flex items-center gap-1 ml-4 ${className || ''}`}>
      {/* Separator added to the left of the mode toggle button */}
      <Separator orientation="vertical" className="h-4" />
      
      {/* Mode Toggle Button */}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-auto px-2 transition-all duration-200 text-muted-foreground hover:text-foreground hover:bg-accent/50"
            onClick={() => handleModeChange(currentMode === 'focus' ? 'screener' : 'focus')}
          >
            {currentMode === 'focus' ? 'Focus' : 'Screener'}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>
            {currentMode === 'focus'
              ? 'Switch to Screener mode (Multiple tickers table)'
              : 'Switch to Focus mode (Detailed single ticker analysis)'}
          </p>
        </TooltipContent>
      </Tooltip>

      {/* Screener Settings */}
      {currentMode === 'screener' && (
        <>
          {/* <Separator orientation="vertical" className="h-4" /> */}
          {/* The above Separator has been removed (commented out for clarity, will be deleted by apply model) */}

          {/* Timeframe Selection */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Select
                value={screenerSettings.timeframe}
                onValueChange={(value) => handleScreenerUpdate({ timeframe: value })}
              >
                <SelectTrigger className="h-7 w-14 p-1 text-xs" icon={null}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="min-w-[12rem]">
                  <div className="grid grid-cols-2 gap-x-1">
                    <div>
                      {DEFAULT_TIMEFRAMES.slice(0, Math.ceil(DEFAULT_TIMEFRAMES.length / 2)).map((tf) => (
                        <SelectItem key={tf.value} value={tf.value}>
                          {tf.label}
                        </SelectItem>
                      ))}
                    </div>
                    <div>
                      {DEFAULT_TIMEFRAMES.slice(Math.ceil(DEFAULT_TIMEFRAMES.length / 2)).map((tf) => (
                        <SelectItem key={tf.value} value={tf.value}>
                          {tf.label}
                        </SelectItem>
                      ))}
                    </div>
                  </div>
                </SelectContent>
              </Select>
            </TooltipTrigger>
            <TooltipContent>
              <p>Timeframe for all screener charts</p>
            </TooltipContent>
          </Tooltip>

          {/* Sort Settings */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Select
                value={screenerSettings.sortBy}
                onValueChange={(value) => handleSortClick(value as ScreenerSortBy)}
              >
                <SelectTrigger 
                  className="h-7 w-28 pl-2 pr-1 py-1 text-xs"
                  icon={null}
                >
                  <div className="flex items-center justify-between w-full gap-1">
                    <SelectValue />
                    <div
                      role="button"
                      tabIndex={0}
                      className="h-4 w-4 p-0 opacity-70 hover:opacity-100 focus:ring-0 shrink-0 inline-flex items-center justify-center rounded-md cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        const newOrder = screenerSettings.sortOrder === 'desc' ? 'asc' : 'desc';
                        handleScreenerUpdate({ sortOrder: newOrder });
                      }}
                      onPointerDown={(e) => e.stopPropagation()}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          e.stopPropagation();
                          const newOrder = screenerSettings.sortOrder === 'desc' ? 'asc' : 'desc';
                          handleScreenerUpdate({ sortOrder: newOrder });
                        }
                      }}
                      aria-label="Toggle sort order"
                    >
                      <Icon 
                        name="Sorting"
                        className={cn( screenerSettings.sortOrder === 'asc' && "transform scale-y-[-1]" )}
                      />
                    </div>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {ScreenerSortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-1">
                        <Icon name={option.icon as any} className="w-3 h-3" />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </TooltipTrigger>
            <TooltipContent>
              <p>
                Sort by: {screenerSettings.sortBy}. Order: {screenerSettings.sortOrder}. Click icon to toggle order.
              </p>
            </TooltipContent>
          </Tooltip>

          {/* Auto-update Interval Select & Manual Update Button */}
          <div className="flex items-center gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Select
                  value={screenerSettings.updateInterval.toString()}
                  onValueChange={handleIntervalSelect}
                >
                  <SelectTrigger 
                    className="h-7 w-15 pl-2 pr-1 py-1 text-xs flex items-center justify-between gap-1"
                    icon={null}
                  >
                    <div className="flex-grow overflow-hidden whitespace-nowrap text-left">
                      {screenerSettings.autoUpdate && countdown !== null && screenerSettings.updateInterval !== 0 ? (
                        <span className="text-xs">{countdown}s</span>
                      ) : (
                        <SelectValue /> 
                      )}
                    </div>
                    <div
                      role="button"
                      tabIndex={0}
                      className="h-4 w-4 p-0 opacity-60 hover:opacity-100 focus:ring-0 shrink-0 inline-flex items-center justify-center rounded-md cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation(); 
                        handleManualRefreshClick();
                      }}
                      onPointerDown={(e) => { 
                        e.stopPropagation();
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          e.stopPropagation();
                          handleManualRefreshClick();
                        }
                      }}
                      aria-label="Manual refresh"
                    >
                      <Icon name="Refresh" className="w-2.5 h-2.5" /> 
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {SCREENER_UPDATE_INTERVALS.map((interval) => (
                      <SelectItem key={interval.value} value={interval.value.toString()}>
                        {interval.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </TooltipTrigger>
              <TooltipContent>
                <p>Top charts update interval. Click icon to refresh manually.</p>
              </TooltipContent>
            </Tooltip>
          </div>

          {/* Screener Pagination Control */}
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center h-7 border border-input bg-background rounded-md text-xs shrink-0">
                <Button
                  variant="ghost"
                  className="h-full w-7 p-0 rounded-r-none hover:bg-accent -mr-px z-10 flex items-center justify-center"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                  aria-label="Previous page"
                >
                  <Icon name="ChevronRight" className="h-3.5 w-3.5 transform scale-x-[-1]" />
                </Button>
                <div className="tabular-nums whitespace-nowrap text-muted-foreground text-center select-none px-0.5 min-w-[28px]">
                  {currentPage}/{totalPages}
                </div>
                <Button
                  variant="ghost"
                  className="h-full w-7 p-0 rounded-l-none hover:bg-accent -ml-px z-10 flex items-center justify-center"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                  aria-label="Next page"
                >
                  <Icon name="ChevronRight" className="h-3.5 w-3.5" />
                </Button>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Navigate screener pages</p>
            </TooltipContent>
          </Tooltip>
        </>
      )}
    </div>
  );
};

export default memo(ModeControl); 