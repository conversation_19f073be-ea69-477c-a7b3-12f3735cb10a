# Используем базовый образ Bun
FROM oven/bun:latest AS base

# Устанавливаем рабочую директорию
WORKDIR /app

# Устанавливаем переменные окружения для поддержки hot reload
ENV NODE_ENV=development
ENV WATCHPACK_POLLING=true
ENV CHOKIDAR_USEPOLLING=true
ENV FAST_REFRESH=true

# Копируем package.json и bun.lockb
COPY package.json ./
COPY bun.lockb ./

# Устанавливаем ВСЕ зависимости, включая devDependencies
# Bun автоматически устанавливает devDependencies, если NODE_ENV не 'production'
RUN bun install --frozen-lockfile

# Устанавливаем concurrently глобально для параллельного запуска команд
RUN bun install --global concurrently

# Копируем остальной код приложения в контейнер
COPY . .

# Указываем команду по умолчанию, которая будет выполняться при запуске контейнера
CMD ["bun", "run", "dev"] 