import { z } from 'zod';
import { <PERSON><PERSON>tyle, LineWidth } from 'lightweight-charts';
import { KlineDataSchema } from './market.schema';

/**
 * -----------------------------------------------
 * Indicator-Related Schemas
 * -----------------------------------------------
 * This file defines Zod schemas for technical indicator structures,
 * including parameters, styles, outputs, and definitions.
 */

// Basic value types for indicator parameters
export const IndicatorParamValueSchema = z.union([
  z.string(),
  z.number(),
  z.boolean(),
  z.null(),
]);
export type IndicatorParamValue = z.infer<typeof IndicatorParamValueSchema>;

// Schema for timeframe identifiers
export const TimeFrameSchema = z.string();
export type TimeFrame = z.infer<typeof TimeFrameSchema>;

// Schema for describing an indicator's input parameter
export const IndicatorParamSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['number', 'integer', 'string', 'boolean', 'select', 'source']),
  defaultValue: IndicatorParamValueSchema,
  min: z.number().optional(),
  max: z.number().optional(),
  step: z.number().optional(),
  options: z.array(z.object({
    value: IndicatorParamValueSchema,
    label: z.string(),
  })).optional(),
  description: z.string().optional(),
});
export type IndicatorParam = z.infer<typeof IndicatorParamSchema>;

// Schema for visual styling of indicator plots
export const IndicatorStyleSchema = z.object({
  color: z.string(),
  lineWidth: z.number().min(1).max(4).optional().transform(v => v as LineWidth | undefined),
  lineStyle: z.number().min(0).max(4).optional().transform(v => v as LineStyle | undefined),
  opacity: z.number().min(0).max(1).optional(),
  visible: z.boolean().optional(),
});
export type IndicatorStyle = z.infer<typeof IndicatorStyleSchema>;

// Schema for describing a single output of an indicator (e.g., a line or histogram)
export const IndicatorOutputInfoSchema = z.object({
  id: z.string(),
  name: z.string(),
  color: z.string(),
  type: z.enum(['line', 'histogram', 'area', 'point']),
  lineWidth: z.number().min(1).max(4).optional().transform(v => v as LineWidth | undefined),
  lineStyle: z.number().min(0).max(4).optional().transform(v => v as LineStyle | undefined),
  visibleInStatistics: z.boolean(),
  opacity: z.number().min(0).max(1).optional(),
  pane: z.string().optional(),
});
export type IndicatorOutputInfo = z.infer<typeof IndicatorOutputInfoSchema>;
export type IndicatorOutput = z.infer<typeof IndicatorOutputInfoSchema>; // Alias for compatibility

// Schema for a generic map of indicator parameters
export const IndicatorParamsSchema = z.record(IndicatorParamValueSchema);
export type IndicatorParams = z.infer<typeof IndicatorParamsSchema>;

// Schema for the result of an indicator calculation for a single time point
export const CalculatedIndicatorResultSchema = z.object({
  time: z.number(),
  values: z.record(z.union([z.number(), z.null()])),
});
export type CalculatedIndicatorResult = z.infer<typeof CalculatedIndicatorResultSchema>;

// Schema for an instance of an indicator on a chart
export const IndicatorInstanceSchema = z.object({
  instanceId: z.string(),
  chartId: z.union([z.string(), z.literal('global')]),
  indicatorId: z.string(),
  name: z.string(),
  params: IndicatorParamsSchema,
  visible: z.boolean(),
  styleOverrides: z.record(IndicatorStyleSchema.partial()),
  paneConfig: z.object({
    height: z.number().optional(),
  }).optional(),
  tableOutputIds: z.array(z.string()).optional(),
  tableTimeframe: z.string().optional(),
});
export type IndicatorInstance<TParams extends IndicatorParams = IndicatorParams> =
  Omit<z.infer<typeof IndicatorInstanceSchema>, 'params'> & { params: TParams };

// Schema for the full definition of an indicator
// Note: `calculate`, `formatValue`, and `getShortName` are functions and not part of the schema
// for validation purposes. They are added back to the derived TypeScript type.
export const IndicatorDefinitionSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  shortName: z.string().optional(),
  category: z.string(),
  hasPane: z.boolean(),
  plotOnMainPane: z.boolean().optional(),
  params: z.array(IndicatorParamSchema),
  outputs: z.array(IndicatorOutputInfoSchema),
  defaultStyle: z.record(IndicatorStyleSchema.partial()).optional(),
  defaultParams: IndicatorParamsSchema,
});

// The full TypeScript type for an indicator definition, including calculation functions
export type IndicatorDefinition<TParams extends IndicatorParams = IndicatorParams> =
  z.infer<typeof IndicatorDefinitionSchema> & {
  defaultParams: TParams;
  calculate: (data: (z.infer<typeof KlineDataSchema>)[] | number[], params: TParams) => CalculatedIndicatorResult[];
  formatValue?: (value: number, outputId: string) => string;
  getShortName?: (params: TParams) => string;
};

// Schema for calculated indicator data bundled with its metadata
export const CalculatedIndicatorWithMetadataSchema = IndicatorInstanceSchema.extend({
  symbol: z.string(),
  data: z.array(z.record(z.union([z.number(), z.null()]))),
  timestamps: z.array(z.number()),
});
export type CalculatedIndicatorWithMetadata = z.infer<typeof CalculatedIndicatorWithMetadataSchema>; 