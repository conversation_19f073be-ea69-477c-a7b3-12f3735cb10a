---
description: 
globs: 
alwaysApply: false
---
---
title: "Правила для DragonflyDB (Redis-совместимый)"
description: "Набор правил и лучших практик для DragonflyDB, высокопроизводительной замены Redis."
tags: ["dragonflydb", "redis", "in-memory", "database", "cache", "best-practices", "rules"]
---

# DragonflyDB – Ключевые Правила и Лучшие Практики

## 0. Введение для Пользователя Шаблона

*   **Цель**: Этот шаблон поможет вам создать четкий и полезный набор правил для работы с DragonflyDB.
*   **Фокус**: Правила должны быть **конкретными**, **действенными** и **легко проверяемыми**. DragonflyDB стремится к полной совместимости с Redis API, поэтому многие практики Redis применимы, но есть и свои особенности.
*   **Краткость**: Стремитесь к лаконичности.
*   **Примеры**: Включайте **конкретные примеры кода** или ссылки на официальную документацию.
*   **Адаптация**: Места, требующие специфичной для проекта информации, отмечены как `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ]`.

## 1. Основные Принципы и Концепции

*   **Назначение DragonflyDB**: Высокопроизводительное хранилище данных в памяти, полностью совместимое с API Redis и Memcached. Разработано для максимального использования современных многоядерных процессоров и эффективного управления памятью.
*   **Ключевые Идеи/Парадигмы**:
    *   **Многопоточность и Shared-Nothing Архитектура**: В отличие от однопоточного Redis, DragonflyDB использует многопоточность, где каждый шард данных управляется выделенным потоком. Это позволяет эффективно масштабироваться вертикально на многоядерных CPU.
    *   **Полная совместимость с Redis API**: Код, написанный для Redis, должен работать с DragonflyDB без изменений.
    *   **Эффективное использование памяти**: DragonflyDB демонстрирует лучшую эффективность использования памяти по сравнению с Redis, особенно при работе с большими наборами данных и во время операций сохранения (snapshotting).
    *   **Высокая пропускная способность и низкая задержка**: Благодаря своей архитектуре, DragonflyDB обеспечивает значительно более высокую пропускную способность (до 25x по сравнению с Redis) при сохранении низкой задержки.
    *   **Упрощенное масштабирование**: Фокус на вертикальном масштабировании упрощает эксплуатацию по сравнению с необходимостью управления кластерами Redis.
*   **Обязательно к Прочтению (Ссылки)**:
    *   Официальная документация DragonflyDB: `https://www.dragonflydb.io/docs`
    *   Блог DragonflyDB (много полезных статей и сравнений): `https://www.dragonflydb.io/blog`
    *   Репозиторий GitHub: `https://github.com/dragonflydb/dragonfly`
*   **Актуальная Версия**: `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ: Например, v1.21.2]`
    *   Ссылка на Changelog/Releases: `https://github.com/dragonflydb/dragonfly/releases`

## 2. Конфигурация и Настройка Среды

### 2.1. Системные Настройки (Linux) для Оптимальной Производительности

*   **`vm.overcommit_memory = 1`**: Установите в `/etc/sysctl.conf` и примените (`sysctl -p`). Позволяет DragonflyDB/Redis более эффективно использовать память и предотвращает ошибки нехватки памяти при определенных условиях.
    ```bash
    # Добавить в /etc/sysctl.conf
    # vm.overcommit_memory = 1
    # sudo sysctl -p
    ```
*   **Transparent Huge Pages (THP)**: Отключите THP, так как это может вызывать задержки и снижать производительность для баз данных в памяти.
    ```bash
    # Добавить в /etc/rc.local или использовать systemd unit
    # echo never > /sys/kernel/mm/transparent_hugepage/enabled
    # echo never > /sys/kernel/mm/transparent_hugepage/defrag
    ```
*   **`net.core.somaxconn`**: Увеличьте максимальное количество ожидающих соединений. Значение должно быть достаточно большим, например, `65535`. Устанавливается в `/etc/sysctl.conf`.
    ```bash
    # Добавить в /etc/sysctl.conf
    # net.core.somaxconn = 65535
    # sudo sysctl -p
    ```
*   **TCP Keepalive**: Убедитесь, что `tcp_keepalive_time` (например, 300 секунд) настроен в `/etc/sysctl.conf` для предотвращения разрыва неактивных, но нужных соединений.
    ```bash
    # Пример для /etc/sysctl.conf
    # net.ipv4.tcp_keepalive_time = 300
    # sudo sysctl -p
    ```
*   **Swappiness**: Уменьшите `vm.swappiness` (например, до 1 или 0) в `/etc/sysctl.conf`, чтобы система как можно реже использовала swap для процессов DragonflyDB.
    ```bash
    # Добавить в /etc/sysctl.conf
    # vm.swappiness = 1
    # sudo sysctl -p
    ```

### 2.2. Основные Файлы и Параметры Конфигурации DragonflyDB

*   **Основные Файлы Конфигурации**:
    *   DragonflyDB конфигурируется через параметры командной строки или файл конфигурации.
    *   Пример запуска с параметрами: `./dragonfly --logtostderr --requirepass=yourpassword --port=6379`
    *   Можно использовать файл флагов: `dragonfly --flagfile=path/to/flags.txt`
    *   Переменные окружения: Префикс `DFLY_`, например, `export DFLY_port=6379`.
    *   Полный список флагов: `dragonfly --helpfull`
*   **Ключевые параметры конфигурации**:
    *   `--port`: Порт Redis (по умолчанию 6379).
    *   `--memcached_port`: Порт Memcached (если нужен).
    *   `--bind`: Адрес для привязки (по умолчанию все интерфейсы, рекомендуется указывать конкретный для безопасности).
    *   `--requirepass`: Пароль для аутентификации.
    *   `--maxmemory`: Максимальный объем памяти. DragonflyDB эффективно управляет памятью, но установка предела важна.
    *   `--dbnum`: Количество баз данных (по умолчанию 16).
    *   `--dir`: Рабочая директория для сохранения дампов и логов.
    *   `--dbfilename`: Шаблон имени файла для снапшотов (например, `dump-{timestamp}.dfs`). DragonflyDB использует собственный формат `.dfs`, но может загружать `.rdb` от Redis.
    *   `--snapshot_cron`: Cron-выражение для автоматического создания снапшотов.
    *   `--proactor_threads`: Количество потоков для обработки запросов (по умолчанию 0 - автоопределение). Рекомендуется настроить в соответствии с количеством доступных ядер CPU, оставляя несколько ядер для ОС и других задач.
    *   `--max_client_connections`: Аналог `maxclients` в Redis, максимальное количество одновременных клиентских подключений.
    *   `--tcp_backlog`: Размер очереди ожидания для TCP-соединений, должен быть согласован с `net.core.somaxconn` системного уровня.
    *   `--cache_mode`: Если `true`, работает как кэш с вытеснением данных при приближении к `maxmemory`.
    *   `--cluster_mode`: Поддержка эмуляции кластера Redis (`emulated` или `yes`).
    *   `--tls_*`: Параметры для настройки TLS (например, `--tls_cert_file`, `--tls_key_file`).
    *   `--aclfile`: Путь к файлу ACL для управления доступом (аналогично Redis ACL).
*   **Типичные Зависимости (для проекта)**: Клиентские библиотеки для Redis на соответствующем языке программирования (например, `redis-py` для Python, `ioredis` для Node.js, `go-redis` для Go). Поскольку DragonflyDB совместим с Redis, используются те же клиенты.
*   **Инструменты Сборки/Разработки**: Docker является предпочтительным способом запуска DragonflyDB для разработки и часто для продакшена.

## 3. Рекомендации по Написанию Кода

### 3.1. Структура Проекта и Именование

*   **Именование ключей**: Придерживайтесь тех же практик, что и для Redis:
    *   Используйте осмысленные и структурированные имена ключей, например `objectType:id:field` (e.g., `user:1001:name`).
    *   Используйте `:` для разделения сегментов ключа для логической группировки (неймспейсы).
    *   Избегайте слишком длинных имен ключей для экономии памяти.
*   **Организация данных**:
    *   Используйте подходящие типы данных Redis (String, Hash, List, Set, Sorted Set, Streams) для ваших задач. DragonflyDB поддерживает их все.
    *   Hashes особенно эффективны для хранения объектов, так как позволяют атомарно обновлять отдельные поля.

### 3.2. Паттерны и Анти-паттерны (с Примерами)

#### Хорошо: Эффективное использование многопоточности DragonflyDB
*   **Действие**: Полагайтесь на способность DragonflyDB обрабатывать множество одновременных запросов без блокировок, характерных для однопоточного Redis при выполнении долгих операций.
*   **Почему**: DragonflyDB разработан для параллельной обработки, что снижает влияние "шумных соседей" и дорогих команд на общую производительность.
```bash
# DragonflyDB может обрабатывать множество таких запросов параллельно без значительного падения производительности для других клиентов
# В то время как в Redis долгая операция (например, KEYS * на большой базе) заблокирует все остальные.
redis-cli LPUSH mylist item1
redis-cli LPUSH mylist item2
# ... множество других команд от разных клиентов
```

#### Плохо: Слепое копирование некоторых "обходных путей" для однопоточного Redis
*   **Проблема**: Использование сложных клиентских стратегий для обхода блокирующих операций Redis (например, ручное удаление "больших ключей" маленькими порциями через HSCAN/HDEL) может быть излишним в DragonflyDB.
*   **Почему**: DragonflyDB обрабатывает удаление больших ключей (и другие потенциально долгие операции) значительно эффективнее и без длительных блокировок сервера благодаря своей архитектуре. Команда `UNLINK` (совместима с Redis >= 4) асинхронно удаляет ключи и является предпочтительной, если клиент ее поддерживает. DragonflyDB по своей природе обрабатывает это эффективнее, чем стандартный `DEL` в Redis.
```go
// Пример для Redis (может быть избыточен для DragonflyDB)
// func deleteBigHashInChunks(client *redis.Client, key string) {
//     cursor := uint64(0)
//     for {
//         fields, nextCursor, err := client.HScan(context.Background(), key, cursor, "*", 100).Result()
//         // ... error handling ...
//         if len(fields) > 0 {
//             // HDEL в Redis может быть блокирующим для большого количества полей
//             client.HDel(context.Background(), key, fields...) // В DragonflyDB это менее проблематично
//         }
//         cursor = nextCursor
//         if cursor == 0 {
//             break
//         }
//     }
// }

// Для DragonflyDB и современного Redis, используйте UNLINK для больших ключей:
// client.Unlink(context.Background(), "my_big_hash_key")
// Если UNLINK недоступен, DEL в DragonflyDB все равно будет эффективнее, чем в однопоточном Redis.
```

#### Хорошо: Использование подходящих структур данных
*   **Действие**: Выбирайте структуры данных, соответствующие вашей задаче (Hashes для объектов, Sets для уникальных элементов, Sorted Sets для ранжированных данных и т.д.).
*   **Почему**: Это оптимизирует как использование памяти, так и производительность операций. DragonflyDB поддерживает те же оптимизированные кодировки для малых коллекций, что и Redis (listpack, intset).
```redis
// Хорошо: хранение пользователя как Hash
HMSET user:1000 name "John Doe" email "<EMAIL>" age 30

// Плохо: хранение каждого поля пользователя как отдельный String ключ
SET user:1000:name "John Doe"
SET user:1000:email "<EMAIL>"
SET user:1000:age 30
```

#### Хорошо: Установка TTL для кэшируемых данных
*   **Действие**: Всегда устанавливайте время жизни (TTL) для ключей, которые используются для кэширования.
*   **Почему**: Это предотвращает переполнение памяти устаревшими данными. Используйте команды `EXPIRE`, `PEXPIRE` или `SETEX`.
```redis
SET cache:user:123 "{...data...}" EX 3600 // Кэш на 1 час
```
DragonflyDB также поддерживает команды `HSETEX` и `SADDEX` для установки TTL на отдельные поля хеша или элементы множества, что является его уникальной особенностью.

### 3.3. Производительность

*   **Вертикальное масштабирование**: DragonflyDB отлично масштабируется вертикально. Предоставляйте ему достаточное количество CPU ядер. Количество потоков (`--proactor_threads`) можно оставить на автоопределение или настроить.
*   **Big Keys**:
    *   Хотя DragonflyDB справляется с "большими ключами" (ключи с большим количеством элементов или большим объемом данных) лучше, чем Redis (меньше блокировок при чтении/удалении), всё же старайтесь избегать их без необходимости.
    *   Для чтения больших коллекций используйте команды сканирования (`SCAN`, `HSCAN`, `SSCAN`, `ZSCAN`) вместо команд, возвращающих все элементы (`KEYS`, `HGETALL`, `SMEMBERS`). DragonflyDB поддерживает их.
    *   `KEYS` в DragonflyDB имеет лимит на вывод (`--keys_output_limit`), что безопаснее.
*   **Pipeline**: Используйте pipelining для отправки нескольких команд за один сетевой запрос, чтобы уменьшить задержки. Клиентские библиотеки обычно поддерживают это.
*   **Соединения**: Используйте пулы соединений в ваших приложениях для эффективного управления подключениями.
*   **Параметр `--cache_mode`**: Если DragonflyDB используется исключительно как кэш, установите `--cache_mode=true`. Это включит политики вытеснения (eviction policies), когда память будет подходить к концу (`maxmemory`). Политики вытеснения настраиваются аналогично Redis (например, `allkeys-lru`, `volatile-lru`).
*   **Параметр `--migrate_connections`**: По умолчанию `true`. Позволяет DragonflyDB мигрировать соединения к потоку, на котором оперируют данные, что может улучшить производительность для Lua скриптов.
*   **Избегайте долгих Lua скриптов**: Как и в Redis, длительные Lua скрипты могут блокировать обработку других запросов на том потоке, где они выполняются. Оптимизируйте скрипты.
*   **RDB/AOF Persistence**: Если DragonflyDB используется как кэш и данные не критичны, или если есть репликация и бэкапы снимаются с реплик, рассмотрите возможность отключения или менее частого сохранения снапшотов/AOF на мастере для снижения нагрузки на I/O.

### 3.4. Безопасность

*   **Пароль**: Всегда устанавливайте пароль (`--requirepass yourpassword` или через ACL).
*   **Привязка к интерфейсу**: Используйте `--bind specific_ip_address` вместо привязки ко всем интерфейсам (`0.0.0.0`), если доступ извне не требуется.
*   **TLS**: Настраивайте TLS (`--tls_cert_file`, `--tls_key_file`, etc.) для шифрования трафика, если DragonflyDB доступен по недоверенной сети.
*   **ACL**: Используйте Access Control Lists (`--aclfile path/to/acl.conf`) для гранулярного контроля доступа пользователей к командам и ключам, аналогично Redis 6.0+.
    ```
    # Пример ACL правила в acl.conf
    user myuser on >mypassword +@read ~objects:* -@write
    ```
*   **Отключение опасных команд**: Если не используются, можно переименовать или ограничить доступ к опасным командам (например, `FLUSHALL`, `FLUSHDB`, `CONFIG`) через ACL или параметр `--rename_command`.
    Пример переименования: `dragonfly --rename_command FLUSHALL=DISABLED_FLUSHALL`
*   **Защита от сетевых атак**: Используйте файрволы и настройте правила доступа на уровне сети (например, security groups в облаке).
*   **Репликация**: Поддерживает репликацию в стиле Redis (master-replica).
    *   `REPLICAOF master_host master_port` (в DragonflyDB используется `REPLICAOF`)
    *   Параметры: `--replicaof`, `--masterauth`.
*   **Lua скрипты**: Полностью поддерживаются, как в Redis.
*   **Кластеризация (Эмуляция)**: DragonflyDB может эмулировать поведение кластера Redis (`--cluster_mode=emulated` или `--cluster_mode=yes`). Это позволяет существующим клиентам, ожидающим кластер Redis, работать с одним инстансом DragonflyDB.

## 4. Особенности Работы с API / Ключевые Функции

DragonflyDB стремится к 100% совместимости с Redis API. Это означает, что все стандартные команды Redis (GET, SET, HASH-команды, LIST-команды и т.д.) должны работать так же.

*   **Уникальные команды DragonflyDB (экспериментальные или специфичные)**:
    *   `HSETEX key field seconds value`: Устанавливает значение поля в хеше и TTL для этого конкретного поля.
    *   `SADDEX key seconds member [member ...]`: Добавляет элементы в сет и устанавливает TTL для этих конкретных элементов.
*   **Снапшоты**: DragonflyDB использует собственный формат снапшотов (`.dfs`), который более эффективен. Он может загружать RDB-файлы Redis, но сохраняет в `.dfs` (если не указано иное через флаги, относящиеся к формату RDB, но по умолчанию используется dfs).
    *   Команда `SAVE` создает снапшот.
    *   `BGSAVE` (которого нет как отдельной команды, `SAVE` в DragonflyDB уже неблокирующий) или автоматические снапшоты по cron (`--snapshot_cron`).
*   **Репликация**: Поддерживает репликацию в стиле Redis (master-replica).
    *   `REPLICAOF master_host master_port`
    *   Параметры: `--replicaof`, `--masterauth`.
*   **Lua скрипты**: Полностью поддерживаются, как в Redis.

## 5. НЕ ИСПОЛЬЗОВАТЬ / ИЗБЕГАТЬ

*   **Избегайте `KEYS *` в продакшене на больших базах**: Несмотря на то, что DragonflyDB может обрабатывать это лучше Redis, это всё равно ресурсоемкая операция. Используйте `SCAN`.
*   **Чрезмерное количество баз данных**: Хотя поддерживается (`--dbnum`), использование множества баз данных (SELECT index) часто усложняет управление. Рассмотрите использование префиксов ключей для разделения данных в одной базе.
*   **Неправильная конфигурация `maxmemory`**: Без `maxmemory` DragonflyDB попытается использовать всю доступную память. Всегда устанавливайте разумный лимит. Если используется как кэш, убедитесь, что `--cache_mode=true` и выбрана соответствующая `maxmemory-policy` (например, через параметр командной строки `--default_maxmemory_policy=allkeys-lru` или командами `CONFIG SET maxmemory-policy allkeys-lru`).
*   **Игнорирование сетевых задержек**: Если клиент и сервер DragonflyDB находятся в разных дата-центрах или сетях с высокой задержкой, это сильно повлияет на производительность. Старайтесь размещать их как можно ближе.
*   **Пренебрежение обновлениями ОС и ядра**: Устаревшие версии ядра или системных библиотек могут содержать баги или не иметь последних оптимизаций, влияющих на сетевую и дисковую производительность.

## 6. ВСЕГДА ДЕЛАТЬ

*   **Мониторинг**: Настройте мониторинг DragonflyDB. Он предоставляет метрики, совместимые с Prometheus, и информацию через команду `INFO`.
*   **Резервное копирование**: Регулярно делайте резервные копии снапшотов, особенно если DragonflyDB используется как основное хранилище данных, а не просто кэш.
*   **Тестирование конфигурации**: Перед запуском в продакшен тщательно тестируйте конфигурацию и производительность под вашей нагрузкой.
*   **Обновление**: Следите за новыми версиями DragonflyDB, так как проект активно развивается и приносит улучшения производительности и новые функции.
*   **Использование актуальных клиентов Redis**: Убедитесь, что ваши клиентские библиотеки Redis обновлены до последних стабильных версий, так как они могут содержать важные оптимизации и исправления.
*   **Тестирование массового удаления**: Если требуется удалить большое количество ключей по шаблону, используйте `redis-cli --scan --pattern "your_pattern:*" | xargs redis-cli UNLINK` или аналогичные скрипты, чтобы избежать блокировки сервера (хотя `UNLINK` уже асинхронный, `SCAN` помогает разбить работу на части). DragonflyDB лучше справляется с этим, но хорошая практика остается.

## 7. Инструменты и Рабочий Процесс

*   **Основные Инструменты**:
    *   `redis-cli`: Стандартный CLI-инструмент для Redis, полностью совместим с DragonflyDB.
    *   Docker: Рекомендуемый способ запуска DragonflyDB для разработки и развертывания.
    *   Инструменты мониторинга: Prometheus, Grafana. DragonflyDB предоставляет эндпоинт `/metrics` для Prometheus.
    *   Клиентские библиотеки Redis для вашего языка программирования.
*   **Линтинг и Форматирование**: `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ: Специфично для кода вашего приложения, не для DragonflyDB]`
*   **Тестирование**: `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ: Для вашего приложения. Для самого DragonflyDB используйте интеграционные тесты с реальным экземпляром DragonflyDB]`

## 8. Полезные Сниппеты / Шаблоны Кода

*   **Запуск DragonflyDB с Docker (Linux, host network для лучшей производительности)**:
    ```bash
    docker run --network=host --ulimit memlock=-1 docker.dragonflydb.io/dragonflydb/dragonfly --requirepass="yourStrongPassword" --maxmemory=8gb --logtostderr
    ```
*   **Запуск DragonflyDB с Docker (macOS/Windows, с маппингом порта)**:
    ```bash
    docker run -p 6379:6379 --ulimit memlock=-1 docker.dragonflydb.io/dragonflydb/dragonfly --requirepass="yourStrongPassword" --maxmemory=8gb --logtostderr
    ```
*   **Подключение через `redis-cli`**:
    ```bash
    redis-cli -a yourStrongPassword PING
    # Ожидаемый ответ: PONG
    ```
*   **Проверка информации об инстансе**:
    ```bash
    redis-cli -a yourStrongPassword INFO
    # Посмотреть секции Server, Memory, Stats и т.д.
    ```
*   **Настройка автоматического снапшота (пример параметра при запуске)**:
    ```bash
    docker run ... docker.dragonflydb.io/dragonflydb/dragonfly --snapshot_cron="0 * * * *" # Каждый час
    ```

## 9. Самопроверка / Чек-лист для ИИ

Перед предоставлением решения, ИИ должен проверить:
*   [ ] Соответствует ли решение актуальной версии DragonflyDB (насколько это возможно определить)?
*   [ ] Применены ли ключевые принципы (см. Раздел 1), особенно касающиеся многопоточности и совместимости с Redis? Учтены ли системные настройки для производительности (Раздел 2.1)?
*   [ ] Использованы ли рекомендованные паттерны и избегаются ли анти-паттерны (см. Раздел 3.2)? Учтены ли отличия от Redis там, где это важно (например, обработка больших ключей, `UNLINK`)?
*   [ ] Учтены ли ограничения "НЕ ИСПОЛЬЗОВАТЬ / ИЗБЕГАТЬ" (см. Раздел 5)?
*   [ ] Выполнены ли требования "ВСЕГДА ДЕЛАТЬ" (см. Раздел 6)?
*   [ ] Код (если есть) читаем, эффективен и безопасен? Конфигурации корректны?
*   [ ] Язык общения: Русский. Код и комментарии в коде: Английский.

## 10. Завершение Задачи и Отчетность

*   Кратко указать, что было сделано: Создан файл правил для DragonflyDB.
*   Привести ссылки на измененные/созданные файлы: `.cursor/rules/dragonfly_rules.md`.
*   **Обязательно прочитать релевантные файлы и зависимости перед внесением изменений (если это разрешено и возможно в текущем контексте).**

--- 