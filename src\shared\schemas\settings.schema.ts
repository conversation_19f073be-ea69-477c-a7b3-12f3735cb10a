import { z } from 'zod';
import { <PERSON>Style, LineWidth } from 'lightweight-charts';
import { SortingState } from '@tanstack/react-table';
import {
  MarketType,
  MarketTypeSchema,
  ViewModeSchema,
  KlineIntervalSchema,
} from './market.schema';

/**
 * -----------------------------------------------
 * Settings-Related Constants
 * -----------------------------------------------
 */

/**
 * Schema for a single timeframe option used in UI selectors.
 */
export const TimeframeOptionSchema = z.object({
  value: KlineIntervalSchema,
  label: z.string(),
});
export type TimeframeOption = z.infer<typeof TimeframeOptionSchema>;

/**
 * Default list of timeframes for UI components.
 * Derived from the KlineIntervalSchema to ensure consistency.
 */
export const DEFAULT_TIMEFRAMES: TimeframeOption[] = KlineIntervalSchema.options.map(val => ({ value: val, label: val }));

/**
 * Predefined chart count options for the screener view.
 */
export const SCREENER_CHART_COUNTS = [4, 6, 9, 12, 16] as const;
export const ScreenerChartCountSchema = z
  .enum(SCREENER_CHART_COUNTS.map(String) as [string, ...string[]])
  .transform(Number);
export type ScreenerChartCount = z.infer<typeof ScreenerChartCountSchema>;

/**
 * Predefined update intervals for the screener's auto-update feature.
 */
export const SCREENER_UPDATE_INTERVALS = [
  { value: 0, label: 'Off' },
  { value: 3, label: '3s' },
  { value: 5, label: '5s' },
  { value: 10, label: '10s' },
  { value: 15, label: '15s' },
  { value: 30, label: '30s' },
  { value: 60, label: '1m' },
  { value: 120, label: '2m' },
  { value: 300, label: '5m' }
] as const;

/**
 * -----------------------------------------------
 * Settings-Related Enums and Basic Types
 * -----------------------------------------------
 */

/**
 * Enum for different chart rendering types.
 */
export enum ChartType {
  Candles = 'Candlestick',
  Line = 'Line',
  Area = 'Area',
  Bars = 'Bars',
}
export const ChartTypeSchema = z.nativeEnum(ChartType);

/**
 * Zod schema for LayoutType.
 * Defines the grid layout options for charts.
 * 'single' is for focus mode. 'grid_*' are for screener mode.
 */
export const LayoutTypeSchema = z.enum([
  'single',
  'grid_4',
  'grid_6',
  'grid_9',
  'grid_12',
  'grid_16',
  '1x1', '1x2', '1x3', '1x4', '1x5', '1x6', '1x7',
  '2x1', '2x2', '2x3', '2x4', '2x5', '2x6', '2x7',
  '3x1', '3x2', '3x3', '3x4', '3x5', '3x6', '3x7',
  '4x1', '4x2', '4x3', '4x4', '4x5', '4x6', '4x7',
  '5x1', '5x2', '5x3', '5x4', '5x5', '5x6', '5x7',
  '6x1', '6x2', '6x3', '6x4', '6x5', '6x6', '6x7',
  '7x1', '7x2', '7x3', '7x4', '7x5', '7x6', '7x7',
]);
export type LayoutType = z.infer<typeof LayoutTypeSchema>;

/**
 * Schema for Screener Sort By options.
 */
export const ScreenerSortBySchema = z.enum(['volume', 'trades', 'price_change', 'volume_change']);
export type ScreenerSortBy = z.infer<typeof ScreenerSortBySchema>;

/**
 * -----------------------------------------------
 * Component and Feature Settings Schemas
 * -----------------------------------------------
 */

/**
 * Schema for visible columns in tables.
 */
export const VisibleColumnsSchema = z.object({
  symbol: z.boolean().default(true),
  price: z.boolean().default(true),
  change: z.boolean().default(true),
  volume: z.boolean().default(true),
  trade: z.boolean().default(true),
  spread: z.boolean().default(true),
}).catchall(z.boolean());
export type VisibleColumns = z.infer<typeof VisibleColumnsSchema>;

/**
 * Schema for sorting configuration.
 * Aligned with TanStack Table's SortingState.
 */
export const SortConfigSchema = z.object({
  id: z.string(),
  desc: z.boolean(),
});
export type SortConfig = z.infer<typeof SortConfigSchema>;

/**
 * Schema for column widths in tables.
 */
export const ColumnWidthsSchema = z.object({
  symbol: z.number().min(20),
  price: z.number().min(20),
  change: z.number().min(20),
  volume: z.number().min(20),
  trade: z.number().min(20),
  spread: z.number().min(20)
}).catchall(z.number().min(20));
export type ColumnWidths = z.infer<typeof ColumnWidthsSchema>;

/**
 * Schema for column filters in tables.
 */
export const ColumnFiltersSchema = z.record(z.union([
  z.string(),
  z.number(),
  z.boolean(),
  z.null()
]));
export type ColumnFilters = z.infer<typeof ColumnFiltersSchema>;

/**
 * Schema for a single indicator column configuration.
 */
export const IndicatorColumnConfigSchema = z.object({
  instanceId: z.string(),
  indicatorId: z.string(),
  name: z.string(),
  timeframe: z.string(),
  parameters: z.record(z.union([z.string(), z.number(), z.boolean()])),
  outputId: z.string(),
  columnLabel: z.string().optional()
});
export type IndicatorColumnConfig = z.infer<typeof IndicatorColumnConfigSchema>;

/**
 * Schema for Screener settings.
 */
export const ScreenerSettingsSchema = z.object({
  sortBy: ScreenerSortBySchema.default('volume'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  marketTypes: z.array(MarketTypeSchema).default([MarketType.Spot, MarketType.Futures]),
  minVolume: z.number().default(1000000),
  minTrades: z.number().default(100),
  showVolumeInUSD: z.boolean().default(true),
  autoUpdateInterval: z.number().default(5),
  autoUpdate: z.boolean().default(false),
  updateInterval: z.number().default(5),
  chartCount: ScreenerChartCountSchema.default('16'),
  currentPage: z.number().int().positive().default(1),
  timeframe: KlineIntervalSchema.default('1h'),
});
export type ScreenerSettings = z.infer<typeof ScreenerSettingsSchema>;

/**
 * Schema for Mode Control settings, managing different UI modes.
 */
export const ModeControlSettingsSchema = z.object({
  screenerSettings: ScreenerSettingsSchema,
  layoutTypes: z
    .object({
      focus: LayoutTypeSchema.default('single'),
      screener: LayoutTypeSchema.default('grid_16'),
    })
    .default({
      focus: 'single',
      screener: 'grid_16',
    }),
  currentMode: ViewModeSchema.default('focus'),
});
export type ModeControlSettings = z.infer<typeof ModeControlSettingsSchema>;

/**
 * -----------------------------------------------
 * Main Application Settings Schema
 * -----------------------------------------------
 * This is the master schema that combines all other settings schemas.
 * It is used for the main application state managed by Zustand.
 */
export const AppSettingsSchema = z.object({
  // General UI settings
  isDarkMode: z.boolean().default(true),
  isFiltersShown: z.boolean().default(false),
  isGridShown: z.boolean().default(false),
  isDebugMode: z.boolean().default(false),
  uiFontFamily: z.string().default('Inter'),
  uiFontSize: z.number().default(14),
  isSyncEnabled: z.boolean().default(true),
  globalSearchTerm: z.string().default(''),
  selectedTickerSymbol: z.string().optional(),
  selectedInterval: z.string().default('1h'),

  // Chart Appearance
  chartType: ChartTypeSchema.default(ChartType.Candles),
  chartBackgroundColor: z.string().default('rgba(23, 23, 23, 1)'),
  chartLayoutLineColor: z.string().default('rgba(41, 41, 41, 1)'),
  chartTextColor: z.string().default('rgba(212, 212, 212, 1)'),
  chartFontFamily: z.string().default('Inter'),
  chartFontSize: z.number().default(12),
  chartBorderColor: z.string().default('rgba(41, 41, 41, 0.5)'),

  // Chart Grid
  chartGridVertLinesVisible: z.boolean().default(true),
  chartGridHorzLinesVisible: z.boolean().default(true),
  chartGridVertLinesColor: z.string().default('rgba(41, 41, 41, 0.5)'),
  chartGridHorzLinesColor: z.string().default('rgba(41, 41, 41, 0.5)'),
  
  // Chart Scales
  autoHideScalesEnabled: z.boolean().default(true),

  // Candlestick Series
  chartCandleBodyEnabled: z.boolean().default(true),
  chartCandleBorderEnabled: z.boolean().default(true),
  chartCandleWickEnabled: z.boolean().default(true),
  chartCandleBodyUpColor: z.string().default('rgba(38, 166, 154, 1)'),
  chartCandleBodyDownColor: z.string().default('rgba(239, 83, 80, 1)'),
  chartCandleBorderUpColor: z.string().default('rgba(38, 166, 154, 1)'),
  chartCandleBorderDownColor: z.string().default('rgba(239, 83, 80, 1)'),
  chartCandleWickUpColor: z.string().default('rgba(38, 166, 154, 1)'),
  chartCandleWickDownColor: z.string().default('rgba(239, 83, 80, 1)'),

  // Line Series
  chartLineColor: z.string().default('rgba(33, 150, 243, 1)'),
  chartLineWidth: z.union([z.literal(1), z.literal(2), z.literal(3), z.literal(4)]).default(2),

  // Area Series
  chartAreaLineColor: z.string().default('rgba(33, 150, 243, 1)'),
  chartAreaTopColor: z.string().default('rgba(33, 150, 243, 0.4)'),
  chartAreaBottomColor: z.string().default('rgba(33, 150, 243, 0)'),
  chartAreaLineWidth: z.union([z.literal(1), z.literal(2), z.literal(3), z.literal(4)]).default(2),

  // Crosshair
  chartCrosshairColor: z.string().default('rgba(150, 150, 150, 0.5)'),
  chartCrosshairLabelBackgroundColor: z.string().default('rgba(40, 40, 40, 1)'),
  chartCrosshairLabelTextColor: z.string().default('rgba(255, 255, 255, 1)'),

  // Time and Price Scales
  chartTimeScaleBorderColor: z.string().default('rgba(41, 41, 41, 0.5)'),
  chartTimeScaleTextColor: z.string().default('rgba(212, 212, 212, 1)'),
  chartPriceScaleBorderColor: z.string().default('rgba(41, 41, 41, 0.5)'),
  chartPriceScaleTextColor: z.string().default('rgba(212, 212, 212, 1)'),
  
  // Price Line
  chartPriceLineVisible: z.boolean().default(true),
  chartLastValueVisible: z.boolean().default(true),
  chartPriceLineWidth: z.union([z.literal(1), z.literal(2), z.literal(3), z.literal(4)]).default(1),
  chartPriceLineStyle: z.union([z.literal(0), z.literal(1), z.literal(2), z.literal(3), z.literal(4)]).default(LineStyle.Dotted),
  chartPriceLineColor: z.string().default('rgba(212, 212, 212, 0.5)'),

  // Volume Pane
  volumeEnabled: z.boolean().default(true),
  chartVolumeHeightRatio: z.number().default(0.2), // 20% of main pane height
  chartVolumeUpColor: z.string().default('rgba(38, 166, 154, 0.5)'),
  chartVolumeDownColor: z.string().default('rgba(239, 83, 80, 0.5)'),

  // Chart Layout and Scaling
  chartRightOffset: z.number().default(5),
  chartBarSpacing: z.number().default(6.5),

  // Table Settings
  selectedPairs: z.array(z.string()).default(['USDT', 'USDC', 'FDUSD', 'BTC', 'ETH']),
  availablePairs: z.array(z.string()).default(['USDT', 'USDC', 'FDUSD', 'BTC', 'ETH', 'BNB', 'DAI', 'USDE', 'OTHR']),
  marketTypes: z.array(MarketTypeSchema).default([MarketType.Spot, MarketType.Futures]),
  minVolume: z.number().default(0),
  minTrades: z.number().default(0),
  showVolumeInUSD: z.boolean().default(true),
  aggregateVolumeAndTrades: z.boolean().default(false),
  tableCompactness: z.number().min(0).max(20).default(0),
  sortConfigs: z.array(SortConfigSchema).default([{ id: 'volume', desc: true }]),
  visibleColumns: VisibleColumnsSchema.default({}),
  columnWidths: ColumnWidthsSchema.default({
    symbol: 150, price: 120, change: 100, volume: 140, trade: 100, spread: 100
  }),
  columnOrder: z.array(z.string()).default(['symbol', 'price', 'change', 'volume', 'trade', 'spread']),
  columnFilters: ColumnFiltersSchema.default({}),
  isTableCollapsed: z.boolean().default(false),
  indicatorColumns: z.array(IndicatorColumnConfigSchema).default([]),
  
  // Mode Control
  modeControl: ModeControlSettingsSchema.default({
    screenerSettings: {},
    layoutTypes: {
      focus: 'single',
      screener: 'grid_16',
    },
    currentMode: 'focus',
  }),
});

export type AppSettings = z.infer<typeof AppSettingsSchema>; 