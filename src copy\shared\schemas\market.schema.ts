import { z } from 'zod';

/**
 * -----------------------------------------------
 * Market Data Type Definitions
 * -----------------------------------------------
 */

/**
 * Schema for market types (spot or futures)
 */
export const MarketTypeSchema = z.enum(['spot', 'futures']);
export type MarketTypeZod = z.infer<typeof MarketTypeSchema>;

/**
 * Schema for kline/candlestick intervals
 */
export const KlineIntervalSchema = z.enum([
  '1m', '3m', '5m', '15m', '30m',
  '1h', '2h', '4h', '6h', '8h', '12h',
  '1d', '3d', '1w', '1M'
]);
export type KlineIntervalZod = z.infer<typeof KlineIntervalSchema>;

/**
 * Schema for candlestick/kline data
 */
export const KlineSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  interval: KlineIntervalSchema,
  openTime: z.number(),
  open: z.number(),
  high: z.number(),
  low: z.number(),
  close: z.number(),
  volume: z.number(),
  closeTime: z.number(),
  quoteVolume: z.number(),
  trades: z.number(),
  isClosed: z.boolean().default(true)
});
export type KlineZod = z.infer<typeof KlineSchema>;

/**
 * Schema for detailed ticker data
 */
export const FullTickerSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  lastPrice: z.number(),
  priceChange: z.number(),
  priceChangePercent: z.number(),
  weightedAvgPrice: z.number(),
  prevClosePrice: z.number(),
  lastQty: z.number(),
  bidPrice: z.number(),
  bidQty: z.number(),
  askPrice: z.number(),
  askQty: z.number(),
  openPrice: z.number(),
  highPrice: z.number(),
  lowPrice: z.number(),
  volume: z.number(),
  quoteVolume: z.number(),
  openTime: z.number(),
  closeTime: z.number(),
  firstId: z.number(),
  lastId: z.number(),
  count: z.number(),
  lastUpdated: z.number(),
});
export type FullTickerZod = z.infer<typeof FullTickerSchema>;

/**
 * Schema for simplified ticker data
 */
export const TickerSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  price: z.number(), // Corresponds to lastPrice in FullTicker
  priceChangePercent: z.number(),
  volume: z.number(),
  quoteVolume: z.number(),
  count: z.number(), 
  lastUpdated: z.number()
});
export type TickerZod = z.infer<typeof TickerSchema>;

/**
 * -----------------------------------------------
 * WebSocket Message Schemas
 * -----------------------------------------------
 */

/**
 * Base schema for all WebSocket messages
 */
export const BaseWebSocketMessageSchema = z.object({
  type: z.string(),
  payload: z.any().optional(),
  data: z.any().optional(),
  topic: z.string().optional(),
  topics: z.array(z.string()).optional(),
  status: z.enum(['success', 'error']).optional(),
  message: z.string().optional(),
  timestamp: z.number().optional(),
});
export type BaseWebSocketMessage = z.infer<typeof BaseWebSocketMessageSchema>;

/**
 * Generic WebSocket message type for simple encoding/decoding
 */
export const GenericWebSocketMessageSchema = BaseWebSocketMessageSchema;
export type GenericWebSocketMessage = BaseWebSocketMessage;

/**
 * -----------------------------------------------
 * Client → Server Message Schemas
 * -----------------------------------------------
 */

/**
 * Subscribe to one or more topics
 */
export const SubscribeMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('subscribe'),
  topics: z.array(z.string()).min(1, 'Topics array cannot be empty for subscription'),
});
export type SubscribeMessage = z.infer<typeof SubscribeMessageSchema>;

/**
 * Unsubscribe from a topic
 */
export const UnsubscribeMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('unsubscribe'),
  topic: z.string().min(1, 'Topic cannot be empty for unsubscription'),
});
export type UnsubscribeMessage = z.infer<typeof UnsubscribeMessageSchema>;

/**
 * Publish to a topic (if clients are allowed to publish)
 */
export const ClientPublishMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('publish'),
  topic: z.string().min(1, 'Topic cannot be empty for publishing'),
  payload: z.any(), // Payload is required for publishing
});
export type ClientPublishMessage = z.infer<typeof ClientPublishMessageSchema>;

/**
 * Ping message to check connection and measure latency
 */
export const PingMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('ping'),
  timestamp: z.number(), // Required for ping messages
});
export type PingMessage = z.infer<typeof PingMessageSchema>;

/**
 * Union of all possible client-to-server messages
 */
export const ClientToServerMessageSchema = z.union([
  SubscribeMessageSchema,
  UnsubscribeMessageSchema,
  ClientPublishMessageSchema,
  PingMessageSchema,
]);
export type ClientToServerMessage = z.infer<typeof ClientToServerMessageSchema>;

/**
 * -----------------------------------------------
 * Server → Client Message Schemas
 * -----------------------------------------------
 */

/**
 * Acknowledgment of successful connection
 */
export const ConnectionAckMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('connection_ack'),
  message: z.string(),
});
export type ConnectionAckMessage = z.infer<typeof ConnectionAckMessageSchema>;

/**
 * Acknowledgment of subscription
 */
export const SubscriptionAckMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('subscribe_ack'),
  topic: z.string(),
  status: z.enum(['success', 'error']),
});
export type SubscriptionAckMessage = z.infer<typeof SubscriptionAckMessageSchema>;

/**
 * Acknowledgment of unsubscription
 */
export const UnsubscriptionAckMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('unsubscribe_ack'),
  topic: z.string(),
  status: z.enum(['success', 'error']),
});
export type UnsubscriptionAckMessage = z.infer<typeof UnsubscriptionAckMessageSchema>;

/**
 * Acknowledgment of publish
 */
export const PublishAckMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('publish_ack'),
  topic: z.string(),
  status: z.enum(['success', 'error']),
});
export type PublishAckMessage = z.infer<typeof PublishAckMessageSchema>;

/**
 * Error message
 */
export const ErrorMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('error'),
  message: z.string(),
  payload: z.object({
    name: z.string().optional(),
    details: z.any().optional(),
    originalMessage: z.string().optional(),
    receivedType: z.string().optional(),
    originalString: z.string().optional(),
  }).passthrough().optional(),
});
export type ErrorMessage = z.infer<typeof ErrorMessageSchema>;

/**
 * Informational message
 */
export const InfoMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('info'),
  message: z.string(),
});
export type InfoMessage = z.infer<typeof InfoMessageSchema>;

/**
 * Response to ping message
 */
export const PongMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('pong'),
  timestamp: z.number(), // To preserve original timestamp from ping
});
export type PongMessage = z.infer<typeof PongMessageSchema>;

/**
 * -----------------------------------------------
 * Data Message Schemas
 * -----------------------------------------------
 */

/**
 * Candlestick/kline data message
 */
export const CandleDataMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('kline_data'),
  topic: z.string(), // Format: kline_<marketType>_<symbol>_<interval>
  data: KlineSchema,
});
export type CandleDataMessage = z.infer<typeof CandleDataMessageSchema>;

/**
 * Ticker data message
 */
export const TickerDataMessageSchema = BaseWebSocketMessageSchema.extend({
  type: z.literal('ticker_data'),
  topic: z.string(), // Format: tickers_<marketType>
  data: z.array(FullTickerSchema),
});
export type TickerDataMessage = z.infer<typeof TickerDataMessageSchema>;

/**
 * Union of all possible server-to-client messages
 */
export const ServerToClientMessageSchema = z.union([
  ConnectionAckMessageSchema,
  SubscriptionAckMessageSchema,
  UnsubscriptionAckMessageSchema,
  PublishAckMessageSchema,
  ErrorMessageSchema,
  InfoMessageSchema,
  PongMessageSchema,
  CandleDataMessageSchema,
  TickerDataMessageSchema,
]);
export type ServerToClientMessage = z.infer<typeof ServerToClientMessageSchema>; 