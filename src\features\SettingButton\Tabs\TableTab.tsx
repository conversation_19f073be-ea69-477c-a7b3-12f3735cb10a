import React, { useCallback, useMemo, memo } from 'react';
import { AppSettings, MarketType, VisibleColumns } from '@/shared/index';
import { Slider, MainToggleButton } from './Controls';
import { cn, debounce } from '@/shared/lib/utils';
import { 
    useAppSettingsStore, 
    selectAvailablePairs,
    selectSelectedPairs,
    selectMinVolume,
    selectMinTrades,
    selectShowVolumeInUSD,
    selectVisibleColumns,
    selectMarketTypes,
    selectTableCompactness,
    selectAggregateVolumeAndTrades,
    selectSetSetting,
    selectToggleColumnVisibility
} from '@/shared/store/settingsStore';

// Helper functions - Consider moving to utils if used elsewhere or keeping them if specific to settings logic
/**
 * Определяет позицию слайдера ('spot', 'futures') 
 * на основе выбранных типов рынка. Если выбраны оба, возвращает 'futures'.
 */
const getSliderPosition = (marketTypes: MarketType[] = []): 'spot' | 'futures' => {
    const hasFutures = marketTypes.includes('futures');
    if (hasFutures) return 'futures';
    return 'spot';
};

/**
 * Возвращает массив типов рынка на основе позиции слайдера.
 */
const getTypesFromPosition = (position: 'spot' | 'futures'): MarketType[] => {
    if (position === 'futures') return ['futures'];
    return ['spot'];
};

// UPDATED: Removed props that will be accessed from the store
export interface FiltersTabProps {
  // Props that might still be needed from parent, if any.
  // For now, let's assume all data comes from the store or is local.
}

// Constants
const AVAILABLE_COLUMNS = [
  { id: 'symbol', label: 'Symbol' },
  { id: 'price', label: 'Price' },
  { id: 'change', label: 'Change' },
  { id: 'volume', label: 'Volume' },
  { id: 'trade', label: 'Trade' },
  { id: 'spread', label: 'Spread' },
];

// --- TableTab --- Adjusted Styles
const TableTab: React.FC<FiltersTabProps> = memo(() => {
  // --- Get data from Zustand store ---
  const availablePairs = useAppSettingsStore(selectAvailablePairs);
  const selectedPairs = useAppSettingsStore(selectSelectedPairs) ?? [];
  const minVolume = useAppSettingsStore(selectMinVolume) ?? 0;
  const minTrades = useAppSettingsStore(selectMinTrades) ?? 0;
  const showVolumeInUSD = useAppSettingsStore(selectShowVolumeInUSD) ?? false;
  const visibleColumns = useAppSettingsStore(selectVisibleColumns) ?? {};
  const selectedMarketTypes = useAppSettingsStore(selectMarketTypes) ?? [];
  const tableCompactness = useAppSettingsStore(selectTableCompactness) ?? 0;
  const aggregateVolumeAndTrades = useAppSettingsStore(selectAggregateVolumeAndTrades) ?? false;

  // --- Get actions from Zustand store ---
  const setSetting = useAppSettingsStore(selectSetSetting);
  const toggleColumnVisibilityAction = useAppSettingsStore(selectToggleColumnVisibility);

  // --- Local derived state ---
  const marketSliderPosition = useMemo(() => getSliderPosition(selectedMarketTypes), [selectedMarketTypes]);

  // --- Local Handlers that call store actions ---
  const handlePairToggle = useCallback((pair: string) => {
    const currentSelectedPairs = selectedPairs;
    const newPairs = currentSelectedPairs.includes(pair) 
      ? currentSelectedPairs.filter((p: string) => p !== pair) 
      : [...currentSelectedPairs, pair];
    setSetting('selectedPairs', newPairs);
  }, [selectedPairs, setSetting]);

  const handleMarketTypeChange = useCallback((position: 'spot' | 'futures') => {
      setSetting('marketTypes', getTypesFromPosition(position));
  }, [setSetting]);

  const toggleColumnVisibility = useCallback((columnKey: string) => {
    toggleColumnVisibilityAction(columnKey);
  }, [toggleColumnVisibilityAction]);

  const handleGenericSettingChange = useCallback(<K extends keyof AppSettings>(key: K, value: AppSettings[K]) => {
    setSetting(key, value);
  }, [setSetting]);

  // === Create debounced handlers for sliders ===
  const debouncedMinVolumeChange = useMemo(
    () => debounce((value: number) => setSetting('minVolume', value), 200),
    [setSetting]
  );

  const debouncedMinTradesChange = useMemo(
    () => debounce((value: number) => setSetting('minTrades', value * 1000), 200),
    [setSetting]
  );

  const debouncedCompactnessChange = useMemo(
    () => debounce((value: number) => setSetting('tableCompactness', value), 150),
    [setSetting]
  );

  return (
    // Reduced overall vertical spacing
    <div className="space-y-4">
      <div className="setting-group">
        <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2 pb-1.5 border-b border-border/10">Market Type</h4>
        {/* Compact Market Type Toggle */}
        <div className={cn(
              "relative inline-flex rounded-md bg-muted p-0.5 border border-border/10"
            )}
            data-state={marketSliderPosition}
        >
          <div className={cn(
                "absolute top-0.5 left-0.5 h-[calc(100%-4px)] w-[calc(50%-2px)]",
                "bg-settings-primary rounded-[4px] transition-transform duration-200 ease-in-out z-0 shadow-sm",
                marketSliderPosition === 'futures' && "translate-x-full"
            )}
          />
          <button
            className={cn(
                "flex-1 border-none bg-transparent text-muted-foreground px-3 py-1 text-[13px] font-medium cursor-pointer transition-colors duration-150 ease-out relative z-10 text-center rounded-[4px] whitespace-nowrap",
                "hover:text-foreground",
                marketSliderPosition === 'spot' && "text-settings-primary-foreground font-medium"
            )}
            onClick={() => handleMarketTypeChange('spot')}
            data-active={marketSliderPosition === 'spot'}
          >
            Spot
          </button>
          <button
            className={cn(
                "flex-1 border-none bg-transparent text-muted-foreground px-3 py-1 text-[13px] font-medium cursor-pointer transition-colors duration-150 ease-out relative z-10 text-center rounded-[4px] whitespace-nowrap",
                "hover:text-foreground",
                marketSliderPosition === 'futures' && "text-settings-primary-foreground font-medium"
            )}
            onClick={() => handleMarketTypeChange('futures')}
            data-active={marketSliderPosition === 'futures'}
          >
            Futures
          </button>
        </div>
      </div>

      <div className="setting-group">
        <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2 pb-1.5 border-b border-border/10">Filters</h4>
        {/* Wrapped toggles for better spacing if needed */}
        <div className="flex flex-col gap-0.5"> 
          <MainToggleButton
            label="Show Volume in USD"
            active={showVolumeInUSD}
            onChange={(checked) => handleGenericSettingChange('showVolumeInUSD', checked)}
          />
          <MainToggleButton
            label="Aggregate Volume & Trades"
            active={aggregateVolumeAndTrades}
            onChange={(checked) => handleGenericSettingChange('aggregateVolumeAndTrades', checked)}
          />
        </div>

        {/* Use updated Slider (defined in Controls) */}
        <Slider 
          label="Min Volume (M)"
          min={0}
          max={1000}
          step={5}
          value={minVolume}
          onChange={debouncedMinVolumeChange}
          unit="M"
          className="mt-3"
        />
        <Slider 
          label="Min Trades (K)"
          min={0}
          max={1000}
          step={5}
          value={(minTrades) / 1000}
          onChange={debouncedMinTradesChange}
          unit="K"
        />
      </div>

      <div className="setting-group">
        <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2 pb-1.5 border-b border-border/10">Pairs</h4>
        {/* Adjusted button styling */}
        <div className="flex flex-wrap gap-1.5 mb-2">
          {availablePairs.map((pair: string) => (
            <button
              key={pair}
              className={cn(
                  "bg-input text-muted-foreground border border-input rounded-md px-2 py-0.5 text-[11px] font-medium cursor-pointer transition-all duration-100 ease-in-out inline-flex items-center justify-center select-none",
                  "hover:bg-accent/60 hover:border-accent/80 hover:text-foreground",
                  selectedPairs.includes(pair) && "bg-settings-primary/80 text-settings-primary-foreground border-settings-primary/80 font-medium shadow-sm"
              )}
              onClick={() => handlePairToggle(pair)}
              data-active={selectedPairs.includes(pair)}
            >
              {pair === 'OTHR' ? 'OTHER' : pair}
            </button>
          ))}
        </div>
      </div>

      <div className="setting-group">
        <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2 pb-1.5 border-b border-border/10">Columns</h4>
        {/* Adjusted button styling */}
        <div className="flex flex-wrap gap-1.5 mb-2">
          {AVAILABLE_COLUMNS.map((column) => (
            <button
              key={column.id}
              className={cn(
                  "bg-input text-muted-foreground border border-input rounded-md px-2 py-0.5 text-[11px] font-medium cursor-pointer transition-all duration-100 ease-in-out inline-flex items-center justify-center select-none",
                  "hover:bg-accent/60 hover:border-accent/80 hover:text-foreground",
                  (visibleColumns && visibleColumns[column.id as keyof VisibleColumns]) === true && "bg-settings-primary/80 text-settings-primary-foreground border-settings-primary/80 font-medium shadow-sm"
              )}
              onClick={() => toggleColumnVisibility(column.id)}
              data-active={(visibleColumns && visibleColumns[column.id as keyof VisibleColumns]) === true}
            >
              {column.label}
            </button>
          ))}
        </div>
        
        <Slider
          label="Table Compact"
          min={0}
          max={20}
          step={1}
          value={tableCompactness}
          onChange={debouncedCompactnessChange}
        />
      </div>
    </div>
  );
});

export default TableTab;
