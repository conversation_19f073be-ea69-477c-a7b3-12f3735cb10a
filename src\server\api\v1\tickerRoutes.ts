import { FastifyInstance } from 'fastify';
import { getTickers, TickerQueryParams } from '@/server/services/tickerService.js';
import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { MarketTypeSchema } from '@/shared/schemas/market.schema.js';

// Zod schema for ticker query parameters validation
const GetTickersSchema = z.object({
  marketTypes: z.array(MarketTypeSchema).optional()
    .describe('Market types to filter by (e.g., "spot", "futures")'),
  quoteAssets: z.array(z.string()).optional()
    .describe('Quote assets to filter by (e.g., "USDT", "BTC")'),
  minVolume: z.coerce.number().positive().optional()
    .describe('Minimum volume filter'),
  minTrades: z.coerce.number().int().positive().optional()
    .describe('Minimum number of trades filter'),
  sortBy: z.string().optional()
    .describe('Field to sort by (e.g., "volume", "priceChangePercent")'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
    .describe('Sort order (asc or desc)'),
  limit: z.coerce.number().int().positive().optional().default(100)
    .describe('Number of tickers to retrieve'),
  offset: z.coerce.number().int().nonnegative().optional().default(0)
    .describe('Number of tickers to skip'),
  searchQuery: z.string().optional()
    .describe('Text to search for in symbol names'),
});

type GetTickersQuery = z.infer<typeof GetTickersSchema>;

// Convert Zod schema to JSON schema for Fastify
const GetTickersJsonSchema = zodToJsonSchema(GetTickersSchema, 'GetTickersSchema');

/**
 * @description Registers the ticker routes.
 * @param {FastifyInstance} fastify - The Fastify instance.
 */
export async function tickerRoutes(fastify: FastifyInstance) {
  /**
   * @api {get} /api/v1/tickers Get all tickers
   * @apiName GetTickers
   * @apiGroup Tickers
   * @apiDescription Get tickers with filtering, sorting and pagination
   * 
   * @apiQuery {String[]} [marketTypes] Market types to filter by (e.g., "spot", "futures")
   * @apiQuery {String[]} [quoteAssets] Quote assets to filter by (e.g., "USDT", "BTC")
   * @apiQuery {Number} [minVolume] Minimum volume filter
   * @apiQuery {Number} [minTrades] Minimum number of trades filter
   * @apiQuery {String} [sortBy] Sort by field
   * @apiQuery {String} [sortOrder=desc] Sort order ("asc" or "desc")
   * @apiQuery {Number} [limit=100] Limit number of results
   * @apiQuery {Number} [offset=0] Offset for pagination
   * @apiQuery {String} [searchQuery] Search by symbol name
   * 
   * @apiSuccess {Object[]} tickers List of tickers
   * 
   * @apiNote Current implementation uses in-memory filtering and sorting,
   * which is suitable for development but not for large datasets.
   * This should be optimized to use database queries when the data volume grows.
   */
  fastify.route<{ Querystring: GetTickersQuery }>({
    method: 'GET',
    url: '/tickers',
    schema: {
      querystring: GetTickersJsonSchema,
      response: {
        200: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              symbol: { type: 'string' },
              marketType: { type: 'string' },
              lastPrice: { type: 'number' },
              priceChange: { type: 'number' },
              priceChangePercent: { type: 'number' },
              highPrice: { type: 'number' },
              lowPrice: { type: 'number' },
              volume: { type: 'number' },
              quoteVolume: { type: 'number' },
              count: { type: 'number' },
              lastUpdated: { type: 'number' },
              // Additional fields that might be useful for the client
              openTime: { type: 'number' },
              closeTime: { type: 'number' },
              weightedAvgPrice: { type: 'number' },
              prevClosePrice: { type: 'number' },
              bidPrice: { type: 'number' },
              bidQty: { type: 'number' },
              askPrice: { type: 'number' },
              askQty: { type: 'number' },
            },
            required: ['symbol', 'marketType', 'lastPrice', 'priceChangePercent', 'volume', 'lastUpdated'],
          },
        },
      },
    },
    handler: async (request, reply) => {
      try {
        const params: TickerQueryParams = request.query;
        const tickers = await getTickers(params);
        
        return reply.send(tickers);
      } catch (error) {
        request.log.error(error, 'Failed to get tickers');
        return reply.status(500).send({ error: 'Internal Server Error' });
      }
    },
  });
} 