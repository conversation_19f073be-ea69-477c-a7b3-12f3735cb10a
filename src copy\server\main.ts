import { Elysia, t } from 'elysia';
import { cors } from '@elysiajs/cors';
import { swagger } from '@elysiajs/swagger';
import { DRAGONFLY_URL, QUESTDB_URL } from './config';
import { logger } from './lib/logger'; 
import { initializeDragonfly, disconnectDragonfly } from './services/dragonfly.service';
import { initializeQuestDB, disconnectQuestDB } from './services/questdb.service';
import { initializeIngestService, shutdownIngestService } from './services/ingest.service';
import { apiHandlers } from './handlers/api.handlers';
import { wsHandlers } from './handlers/ws.handler';
import broadcastService from './services/broadcast.service';

// Set default log level to 'info' if not specified
if (!process.env.LOG_LEVEL) {
    process.env.LOG_LEVEL = 'info';
}

// Log configuration information
logger.info(`Dragonfly URL: ${DRAGONFLY_URL}`);
logger.info(`QuestDB URL: ${QUESTDB_URL.replace(/:[^:]+@/, ':********@')}`);

// Create the Elysia app
const app = new Elysia()
    .use(cors()) 
    .use(swagger({ 
        path: '/api/docs',
        documentation: {
            info: {
                title: 'MetaCharts API',
                version: '1.0.0',
                description: 'API documentation for MetaCharts platform',
            },
        },
    }))
    .decorate('logger', logger)
    .decorate('dragonfly', await initializeDragonfly()) 
    .decorate('questDBPool', await initializeQuestDB())
    .decorate('broadcast', broadcastService)
    .get('/', ({ logger: appLogger }) => {
        appLogger.info('Health check endpoint hit');
        return { status: 'ok', message: 'Welcome to MetaCharts API!' };
    })
    .get('/db-test', async ({ dragonfly, questDBPool, logger: appLogger }) => {
        try {
            await dragonfly.ping(); 
            appLogger.info('Dragonfly/Redis PING successful.');
            
            const questDbResult = await questDBPool.query('SELECT 1 as test;'); 
            appLogger.info('QuestDB SELECT 1 successful:', questDbResult.rows);

            return { status: 'ok', dragonfly: 'connected', questdb: 'connected' };
        } catch (error) {
            appLogger.error('DB connection test failed:', error);
            return { status: 'error', message: 'DB connection failed', error };
        }
    })
    .use(apiHandlers)
    .use(wsHandlers);

async function startServer() {
    try {
        const port = process.env.PORT ? parseInt(process.env.PORT, 10) : 3005;
        
        // Listen for incoming connections
        app.listen(port, async (serverInstance) => {
            logger.info(`Server started on port ${port}`);
            logger.info(`API docs available at http://localhost:${port}/api/docs`);

            if (serverInstance && typeof serverInstance.publish === 'function') {
                // Create a publish function for the BroadcastService
                const wsPublish = (topic: string, payload: any) => {
                    try {
                        const serialized = JSON.stringify(payload);
                        serverInstance.publish(topic, serialized);
                    } catch (error) {
                        logger.error(`Error publishing to topic ${topic}:`, error);
                    }
                };
                
                // Initialize the BroadcastService with the publish function
                broadcastService.initialize(wsPublish);
                logger.info('BroadcastService initialized with server publish function');
                
                // Initialize the IngestService
                await initializeIngestService();
                logger.info('IngestService initialized');
                
                // Connect IngestService events to BroadcastService
                const ingestService = (await import('./services/ingest.service')).default;
                ingestService.on('app_event', (event) => {
                    broadcastService.handleAppEvent(event);
                });
                logger.info('Connected IngestService events to BroadcastService');
            } else {
                logger.error('Server publish function not available, cannot initialize BroadcastService');
                await initializeIngestService();
                logger.warn('IngestService initialized without BroadcastService');
            }
        });

        // Setup graceful shutdown
        const shutdown = async () => {
            logger.info('Shutting down server...');
            
            // Shutdown services in reverse order of initialization
            broadcastService.shutdown();
            await shutdownIngestService();
            await disconnectDragonfly();
            await disconnectQuestDB();
            
            process.exit(0);
        };

        // Register shutdown handlers
        process.on('SIGINT', shutdown);
        process.on('SIGTERM', shutdown);

    } catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
}

// Start the server
startServer().catch(err => {
    console.error("❌ Critical startup error:", err);
    process.exit(1);
});

export type App = typeof app;
