'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useWSStatus, useWebSocketConnection, wsClient } from '@/shared/index';
import { MarketType } from '@/shared/types';

// Убедимся, что wsClient существует, прежде чем использовать его
const getWsStatus = () => wsClient ? wsClient.getStatus() : 'CLOSED';
const connectWs = () => wsClient ? wsClient.connect() : null;
const disconnectWs = () => wsClient ? wsClient.disconnect() : null;

// Определение типов для тестов API
type TestStatus = 'success' | 'error' | 'running' | 'pending';
type ApiTestResult = {
  status: TestStatus;
  data?: any;
  errorMessage?: string;
  errorType?: string;
  errorSource?: string;
};

interface TestResult {
  id: string;
  name: string;
  status: TestStatus;
  result: ApiTestResult | null;
  error?: string;
  duration?: number;
  timestamp?: string;
  params: Record<string, any>;
}

// Определение API тестов
const apiTests: Array<{
  id: string;
  name: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params?: Record<string, any>;
  body?: any;
}> = [
  {
    id: 'get-spot-symbols',
    name: 'Get Spot Symbols',
    endpoint: '/api/market/symbols/spot',
    method: 'GET'
  },
  {
    id: 'get-futures-symbols',
    name: 'Get Futures Symbols',
    endpoint: '/api/market/symbols/futures',
    method: 'GET'
  },
  {
    id: 'get-spot-tickers',
    name: 'Get Spot Tickers',
    endpoint: '/api/market/tickers/spot',
    method: 'GET'
  },
  {
    id: 'get-futures-tickers',
    name: 'Get Futures Tickers',
    endpoint: '/api/market/tickers/futures',
    method: 'GET'
  },
  {
    id: 'get-btc-candles-1h',
    name: 'Get BTCUSDT 1h Candles',
    endpoint: '/api/market/candles/spot/BTCUSDT/1h',
    method: 'GET',
    params: { limit: '100' }
  },
  {
    id: 'get-eth-candles-15m',
    name: 'Get ETHUSDT 15m Candles',
    endpoint: '/api/market/candles/spot/ETHUSDT/15m',
    method: 'GET',
    params: { limit: '50' }
  },
  {
    id: 'get-btc-futures-candles',
    name: 'Get BTCUSDT Futures 4h Candles',
    endpoint: '/api/market/candles/futures/BTCUSDT/4h',
    method: 'GET',
    params: { limit: '30' }
  }
];

interface TestConsoleHeaderProps {
  successCount: number;
  errorCount: number;
  totalTests: number;
  completedTests: number;
  wsConnectionStatus: string;
  wsLastError: string;
  wsConnectionAttempts: number;
  wsStats: any;
  copiedStates: Record<string, boolean>;
  isRunning: boolean;
  progress: number;
  onCopyAllResults: () => void;
  onRunAllTests: () => void;
  onResetWsConnection: () => void;
  onResetStores?: () => void;
  onExportState?: () => void;
}

// Helper functions
const getStatusIcon = (status: TestStatus): string => {
  const icons: Record<TestStatus, string> = {
    success: '✅',
    error: '❌',
    running: '⏳',
    pending: '⏸️'
  };
  return icons[status] || icons.pending;
};

const getStatusColor = (status: TestStatus): string => {
  const colors: Record<TestStatus, string> = {
    success: 'text-green-400',
    error: 'text-red-400',
    running: 'text-yellow-400',
    pending: 'text-gray-500'
  };
  return colors[status] || colors.pending;
};

const TestConsoleHeader: React.FC<TestConsoleHeaderProps> = ({
  successCount,
  errorCount,
  totalTests,
  completedTests,
  wsConnectionStatus,
  wsLastError,
  wsConnectionAttempts,
  wsStats,
  copiedStates,
  isRunning,
  progress,
  onCopyAllResults,
  onRunAllTests,
  onResetWsConnection,
  onResetStores,
  onExportState,
}) => (
  <div className="bg-gray-900 border-b border-gray-700 px-4 py-3">
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <h1 className="text-lg font-semibold">API Test Console</h1>
        <div className="flex items-center space-x-3 text-sm">
          <span className="text-gray-400">Tests:</span>
          <span className="text-green-400">{successCount} passed</span>
          <span className="text-red-400">{errorCount} failed</span>
          <span className="text-gray-400">{totalTests - completedTests} pending</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-gray-400 text-sm">WebSocket:</span>
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${
              wsConnectionStatus === 'OPEN' ? 'bg-green-400 animate-pulse' :
              wsConnectionStatus === 'CONNECTING' ? 'bg-yellow-400 animate-ping' : 'bg-red-400'
            }`} />
            <span className={`text-sm font-medium ${
              wsConnectionStatus === 'OPEN' ? 'text-green-400' :
              wsConnectionStatus === 'CONNECTING' ? 'text-yellow-400' : 'text-red-400'
            }`}>
              {wsConnectionStatus}
            </span>
          </div>
          {wsLastError && (
            <span className="text-xs text-red-400 max-w-40 truncate bg-red-950 px-2 py-1 rounded" title={wsLastError}>
              Error: {wsLastError}
            </span>
          )}
          {wsConnectionAttempts > 0 && (
            <span className="text-xs text-yellow-400 bg-yellow-950 px-1 py-0.5 rounded">
              {wsConnectionAttempts} retries
            </span>
          )}
          {wsStats?.clientsCount && (
            <span className="text-xs text-gray-400">
              {wsStats.clientsCount} clients
            </span>
          )}
          <button
            onClick={onResetWsConnection}
            className="px-2 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 text-xs rounded transition-colors"
            title="Force WebSocket Reconnection"
          >
            🔄 Reconnect
          </button>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <button
          onClick={onCopyAllResults}
          className="px-3 py-1 bg-gray-800 hover:bg-gray-700 text-gray-300 text-sm rounded border border-gray-600 transition-colors"
        >
          {copiedStates['all-results'] ? '📋 Copied' : '📋 Copy All'}
        </button>
        <button
          onClick={onRunAllTests}
          disabled={isRunning}
          className="px-4 py-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white text-sm rounded transition-colors flex items-center space-x-2"
        >
          {isRunning ? (
            <>
              <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>Running {totalTests} tests...</span>
            </>
          ) : (
            <>
              <span>⚡</span>
              <span>Run All Tests (Parallel)</span>
            </>
          )}
        </button>
        {process.env.NODE_ENV === 'development' && onResetStores && onExportState && (
          <>
            <button
              onClick={onResetStores}
              title="Reset All Zustand Stores & Clear Relevant LocalStorage"
              className="px-3 py-1 bg-red-700 hover:bg-red-800 text-white text-sm rounded transition-colors"
            >
              🔄 Reset Stores
            </button>
            <button
              onClick={onExportState}
              title="Export Current Zustand Store States to JSON"
              className="px-3 py-1 bg-green-700 hover:bg-green-800 text-white text-sm rounded transition-colors"
            >
              💾 Export State
            </button>
          </>
        )}
      </div>
    </div>
    {isRunning && (
      <div className="mt-2 px-4">
        <div className="flex items-center space-x-2 text-sm text-gray-400">
          <span>Progress:</span>
          <span className="text-green-400 font-mono">
            {completedTests}/{totalTests} completed
          </span>
          <span className="text-gray-500">•</span>
          <span className="text-blue-400">
            Running {totalTests - completedTests} tests in parallel
          </span>
          <div className="flex-1 bg-gray-800 rounded-full h-1.5">
            <div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <span className="font-mono text-gray-300 min-w-[3rem]">{progress.toFixed(0)}%</span>
        </div>
      </div>
    )}
  </div>
);

interface TestListProps {
  tests: TestResult[];
  selectedTestId: string | null;
  isRunning: boolean;
  onRunTest: (testId: string) => void;
  onCopyTest: (test: TestResult) => void;
  onSelectTest: (test: TestResult) => void;
  copiedStates: Record<string, boolean>;
}

const TestList: React.FC<TestListProps> = ({
  tests,
  selectedTestId,
  isRunning,
  onRunTest,
  onCopyTest,
  onSelectTest,
  copiedStates
}) => (
  <div className="flex-1 border-r border-gray-700">
    <div className="overflow-auto">
      <table className="w-full text-sm">
        <thead className="bg-gray-800 border-b border-gray-700">
          <tr>
            <th className="text-left px-4 py-2 text-gray-300 w-8">Status</th>
            <th className="text-left px-4 py-2 text-gray-300">Test Name</th>
            <th className="text-left px-4 py-2 text-gray-300 w-48">Error</th>
            <th className="text-right px-4 py-2 text-gray-300 w-20">Duration</th>
            <th className="text-center px-4 py-2 text-gray-300 w-24">Actions</th>
          </tr>
        </thead>
        <tbody>
          {tests.map((test) => (
            <tr
              key={test.id}
              className={`border-b border-gray-800 hover:bg-gray-900 cursor-pointer transition-colors ${
                selectedTestId === test.id ? 'bg-gray-900' : ''
              }`}
              onClick={() => onSelectTest(test)}
            >
              <td className="px-4 py-2">
                <span className="text-lg">{getStatusIcon(test.status)}</span>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col">
                  <span className="text-gray-100">{test.name}</span>
                </div>
              </td>
              <td className="px-4 py-2 text-xs text-red-400 truncate" title={test.status === 'error' && test.result?.errorMessage ? test.result.errorMessage : ''}>
                {test.status === 'error' && test.result?.errorMessage ? (
                  <div className="flex items-center">
                    <span className="font-semibold">{test.result.errorType || 'ERR'}: </span>
                    <span className="ml-1">{test.result.errorMessage.substring(0, 30) + (test.result.errorMessage.length > 30 ? '...' : '')}</span>
                    {test.result.errorSource && <span className="ml-1 text-gray-500 text-[10px] whitespace-nowrap">({test.result.errorSource.replace(/_/g, ' ')})</span>}
                  </div>
                ) : (
                  '-'
                )}
              </td>
              <td className="px-4 py-2 text-right text-gray-400">
                {test.duration ? `${test.duration}ms` : '-'}
              </td>
              <td className="px-4 py-2 text-center">
                <div className="flex justify-center space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRunTest(test.id);
                    }}
                    disabled={isRunning}
                    className="p-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded transition-colors"
                    title="Run this test"
                  >
                    {test.status === 'running' ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <span>▶</span>
                    )}
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onCopyTest(test);
                    }}
                    className="p-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
                    title="Copy test result as JSON"
                  >
                    {copiedStates[test.id] ? '✓' : '📋'}
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
);

interface TestDetailsPanelProps {
  selectedTest: TestResult | null;
  onCopyText: (text: string, key: string) => void;
  copiedStates: Record<string, boolean>;
}

const TestDetailsPanel: React.FC<TestDetailsPanelProps> = ({
  selectedTest,
  onCopyText,
  copiedStates
}) => (
  <div className="w-2/5 p-4 overflow-auto">
    <div className="bg-gray-900 border-b border-gray-700 px-4 py-2 mb-4">
      <h2 className="text-sm font-medium text-gray-300">Test Details</h2>
    </div>
    {selectedTest ? (
      <div className="space-y-4">
        <div>
          <h3 className="text-sm font-semibold text-gray-300 mb-2">Test Info</h3>
          <div className="bg-gray-800 p-3 rounded">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-gray-400">Name:</div>
              <div className="text-gray-100">{selectedTest.name}</div>
              <div className="text-gray-400">Status:</div>
              <div className={getStatusColor(selectedTest.status)}>
                {selectedTest.status.charAt(0).toUpperCase() + selectedTest.status.slice(1)}
              </div>
              <div className="text-gray-400">Duration:</div>
              <div className="text-gray-100">{selectedTest.duration ? `${selectedTest.duration}ms` : '-'}</div>
              <div className="text-gray-400">Timestamp:</div>
              <div className="text-gray-100">{selectedTest.timestamp ? new Date(selectedTest.timestamp).toLocaleString() : '-'}</div>
            </div>
          </div>
        </div>

        {selectedTest.params && Object.keys(selectedTest.params).length > 0 && (
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-semibold text-gray-300">Parameters</h3>
              <button
                onClick={() => onCopyText(JSON.stringify(selectedTest.params, null, 2), `${selectedTest.id}-params`)}
                className="text-xs px-1.5 py-0.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
              >
                {copiedStates[`${selectedTest.id}-params`] ? '✓ Copied' : '📋 Copy'}
              </button>
            </div>
            <div className="bg-gray-800 rounded">
              <pre className="text-xs text-gray-100 p-3 overflow-auto max-h-40">
                {JSON.stringify(selectedTest.params, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {selectedTest.result && (
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-semibold text-gray-300">Result</h3>
              <button
                onClick={() => onCopyText(JSON.stringify(selectedTest.result, null, 2), `${selectedTest.id}-result`)}
                className="text-xs px-1.5 py-0.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
              >
                {copiedStates[`${selectedTest.id}-result`] ? '✓ Copied' : '📋 Copy'}
              </button>
            </div>
            <div className="bg-gray-800 rounded">
              <pre className="text-xs text-gray-100 p-3 overflow-auto max-h-80">
                {JSON.stringify(selectedTest.result, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    ) : (
      <div className="text-center py-10 text-gray-500">
        <p>Select a test to view details</p>
      </div>
    )}
  </div>
);

function useApiTestConsole() {
  // Состояние тестов
  const [testResults, setTestResults] = useState<TestResult[]>(() => {
    return apiTests.map(test => ({
      id: test.id,
      name: test.name,
      status: 'pending' as TestStatus,
      result: null,
      duration: undefined,
      timestamp: undefined,
      params: test.params || {}
    }));
  });
  
  const [selectedTest, setSelectedTest] = useState<TestResult | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});
  
  // Состояние WebSocket
  const wsConnectionHook = useWebSocketConnection();
  const [wsStats, setWsStats] = useState<any>({});
  const [wsLastError, setWsLastError] = useState<string>('');
  const [wsConnectionAttempts, setWsConnectionAttempts] = useState(0);

  // Constants for test execution
  const TEST_TIMEOUT_MS = 10000; // 10 seconds

  // Helper to run a API test with timeout
  const runApiTest = useCallback(async (testId: string): Promise<ApiTestResult> => {
    const test = apiTests.find(t => t.id === testId);
    if (!test) throw new Error(`Test with ID ${testId} not found`);
    
    try {
      const url = new URL(test.endpoint, window.location.origin);
      
      // Add query parameters if any
      if (test.params) {
        Object.entries(test.params).forEach(([key, value]) => {
          url.searchParams.append(key, String(value));
        });
      }
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), TEST_TIMEOUT_MS);
      
      const options: RequestInit = {
        method: test.method,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      };
      
      // Add body if needed
      if (test.body && (test.method === 'POST' || test.method === 'PUT')) {
        options.body = JSON.stringify(test.body);
      }
      
      const response = await fetch(url.toString(), options);
      clearTimeout(timeoutId);
      
      const data = await response.json();
      
      if (!response.ok) {
        return {
          status: 'error',
          errorMessage: data.message || response.statusText,
          errorType: `HTTP ${response.status}`,
          errorSource: 'server',
          data
        };
      }
      
      return {
        status: 'success',
        data
      };
    } catch (error: any) {
      // Абортирован из-за таймаута
      if (error.name === 'AbortError') {
        return {
          status: 'error',
          errorMessage: 'Request timed out',
          errorType: 'TIMEOUT',
          errorSource: 'client'
        };
      }
      
      return {
        status: 'error',
        errorMessage: error.message || 'Unknown error',
        errorType: error.name || 'ERROR',
        errorSource: 'client'
      };
    }
  }, []);

  // Обработчик ошибок WebSocket
  useEffect(() => {
    if (!wsClient) return;
    
    const handleError = (error: any) => {
      setWsLastError(error instanceof Error ? error.message : String(error));
      setWsConnectionAttempts(prev => prev + 1);
    };
    
    wsClient.on('error', handleError);
    
    return () => {
      if (wsClient) {
        wsClient.off('error', handleError);
      }
    };
  }, []);

  // Сбросить ошибку, когда соединение открыто
  useEffect(() => {
    if (wsConnectionHook.status === 'OPEN' && wsLastError) {
      setWsLastError('');
    }
  }, [wsConnectionHook.status, wsLastError]);

  // Запуск одного теста
  const runSingleTest = useCallback(async (testId: string) => {
    setTestResults(prev => prev.map(test =>
      test.id === testId
        ? { ...test, status: 'running', result: null, error: undefined }
        : test
    ));

    const startTime = Date.now();
    try {
      const result = await runApiTest(testId);
      const duration = Date.now() - startTime;
      
      setTestResults(prev => prev.map(test =>
        test.id === testId
          ? {
              ...test,
              status: result.status,
              result,
              duration,
              timestamp: new Date().toISOString()
            }
          : test
      ));
    } catch (error) {
      const duration = Date.now() - startTime;
      setTestResults(prev => prev.map(test =>
        test.id === testId
          ? {
              ...test,
              status: 'error',
              result: {
                status: 'error',
                errorMessage: error instanceof Error ? error.message : String(error),
                errorType: error instanceof Error ? error.name : 'Unknown Error',
                errorSource: 'execution'
              },
              duration,
              timestamp: new Date().toISOString()
            }
          : test
      ));
    }
  }, [runApiTest]);

  // Запуск всех тестов
  const runAllTests = useCallback(async () => {
    setIsRunning(true);
    setProgress(0);
    
    // Проверяем соединение перед запуском тестов
    if (wsClient && wsConnectionHook.status !== 'OPEN') {
      connectWs();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Сбрасываем статусы тестов
    setTestResults(prev => prev.map(test => ({
      ...test,
      status: 'running' as TestStatus,
      result: null,
      error: undefined,
      duration: undefined,
      timestamp: undefined
    })));

    // Запускаем тесты параллельно
    const testPromises = testResults.map(async (test) => {
      const startTime = Date.now();
      
      try {
        const result = await runApiTest(test.id);
        const duration = Date.now() - startTime;
        
        setTestResults(prev => prev.map(t =>
          t.id === test.id
            ? {
                ...t,
                status: result.status,
                result,
                duration,
                timestamp: new Date().toISOString()
              }
            : t
        ));
      } catch (error) {
        const duration = Date.now() - startTime;
        setTestResults(prev => prev.map(t =>
          t.id === test.id
            ? {
                ...t,
                status: 'error',
                result: {
                  status: 'error',
                  errorMessage: error instanceof Error ? error.message : String(error),
                  errorType: error instanceof Error ? error.name : 'Unknown Error',
                  errorSource: 'execution'
                },
                duration,
                timestamp: new Date().toISOString()
              }
            : t
        ));
      }
      setProgress(prev => Math.min(prev + (100 / testResults.length), 100));
    });

    await Promise.allSettled(testPromises);
    setIsRunning(false);
    setProgress(100);
  }, [runApiTest, testResults.length, wsConnectionHook.status]);

  // Копирование в буфер обмена
  const copyToClipboard = useCallback(async (text: string, key: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates(prev => ({ ...prev, [key]: true }));
      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [key]: false }));
      }, 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  }, []);

  // Статистика тестов
  const completedTests = testResults.filter(t => t.status === 'success' || t.status === 'error').length;
  const successCount = testResults.filter(t => t.status === 'success').length;
  const errorCount = testResults.filter(t => t.status === 'error').length;

  // Копирование всех результатов
  const copyAllResults = useCallback(() => {
    const allData = testResults
      .filter(t => t.status !== 'pending')
      .map(test => ({
        name: test.name,
        status: test.status,
        params: test.params,
        result: test.result,
        duration: test.duration,
        timestamp: test.timestamp
      }));
    copyToClipboard(JSON.stringify(allData, null, 2), 'all-results');
  }, [testResults, copyToClipboard]);

  // Копирование данных одного теста
  const copyTestData = useCallback((test: TestResult) => {
    const data = {
      name: test.name,
      status: test.status,
      params: test.params,
      result: test.result,
      duration: test.duration,
      timestamp: test.timestamp
    };
    copyToClipboard(JSON.stringify(data, null, 2), test.id);
  }, [copyToClipboard]);

  // Сброс WebSocket соединения
  const handleResetWsConnection = useCallback(() => {
    disconnectWs();
    setTimeout(() => {
      connectWs();
      setWsLastError('');
      setWsConnectionAttempts(0);
    }, 500);
  }, []);
  
  // Вызов глобальных функций для управления хранилищами
  const callGlobalFunction = useCallback((name: string) => {
    if (typeof window !== 'undefined' && (window as any)[name]) {
      (window as any)[name]();
      return true;
    }
    return false;
  }, []);

  // Сброс хранилищ
  const handleResetStores = useCallback(() => {
    const success = callGlobalFunction('__MCT_RESET_STORES__');
    if (!success) {
      alert('Store reset function not found');
    }
  }, [callGlobalFunction]);

  // Экспорт состояния хранилищ
  const handleExportState = useCallback(() => {
    const success = callGlobalFunction('__MCT_EXPORT_STATE__');
    if (!success) {
      alert('Store export function not found');
    }
  }, [callGlobalFunction]);

  // Auto-run tests on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      runAllTests();
    }, 500);
    return () => clearTimeout(timer);
  }, [runAllTests]);

  return {
    testResults,
    isRunning,
    progress,
    selectedTest,
    copiedStates,
    wsConnectionHook,
    wsStatsHook: [wsStats, setWsStats],
    wsLastError,
    wsConnectionAttempts,
    completedTests,
    successCount,
    errorCount,
    totalTests: apiTests.length,
    runSingleTest,
    runAllTests,
    copyAllResults,
    copyTestData,
    copyToClipboard,
    setSelectedTest,
    handleResetWsConnection,
    handleResetStores,
    handleExportState
  };
}

export default function TestConsole() {
  const {
    testResults,
    isRunning,
    progress,
    selectedTest,
    copiedStates,
    wsConnectionHook,
    wsStatsHook: [wsStats],
    wsLastError,
    wsConnectionAttempts,
    completedTests,
    successCount,
    errorCount,
    totalTests,
    runSingleTest,
    runAllTests,
    copyAllResults,
    copyTestData,
    copyToClipboard,
    setSelectedTest,
    handleResetWsConnection,
    handleResetStores,
    handleExportState
  } = useApiTestConsole();

  return (
    <div className="flex flex-col h-screen bg-gray-800 text-white">
      <TestConsoleHeader
        successCount={successCount}
        errorCount={errorCount}
        totalTests={totalTests}
        completedTests={completedTests}
        wsConnectionStatus={wsConnectionHook.status}
        wsLastError={wsLastError}
        wsConnectionAttempts={wsConnectionAttempts}
        wsStats={wsStats}
        copiedStates={copiedStates}
        isRunning={isRunning}
        progress={progress}
        onCopyAllResults={copyAllResults}
        onRunAllTests={runAllTests}
        onResetWsConnection={handleResetWsConnection}
        onResetStores={handleResetStores}
        onExportState={handleExportState}
      />
      <div className="flex flex-1 overflow-hidden">
        <TestList
          tests={testResults}
          selectedTestId={selectedTest?.id || null}
          isRunning={isRunning}
          onRunTest={runSingleTest}
          onCopyTest={copyTestData}
          onSelectTest={setSelectedTest}
          copiedStates={copiedStates}
        />
        <TestDetailsPanel
          selectedTest={selectedTest}
          onCopyText={copyToClipboard}
          copiedStates={copiedStates}
        />
      </div>
    </div>
  );
}
