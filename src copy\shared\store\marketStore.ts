import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { Ticker, Kline, MarketType, FullTicker } from '@/shared/types';

// Определение статуса соединения WebSocket
export enum WSConnectionStatus {
  CONNECTING = 'CONNECTING',
  OPEN = 'OPEN',
  CLOSING = 'CLOSING',
  CLOSED = 'CLOSED',
  ERROR = 'ERROR'
}

// Определение структуры обработанного тикера
export interface ProcessedTicker extends Ticker {
  // Дополнительные поля для обработанного тикера
  priceChangeColor: string;
  percentChangeColor: string;
  displaySymbol?: string;
  favorite?: boolean;
  baseAsset?: string;
  quoteAsset?: string;
}

// Интерфейс для символа TradingView
export interface TradingViewSymbol {
  symbol: string;
  full_name: string;
  description: string;
  exchange: string;
  ticker: string;
  type: string;
}

// Интерфейс состояния хранилища рынка
interface MarketState {
  // Данные тикеров
  spotTickers: Ticker[];
  futuresTickers: Ticker[];
  processedSpotTickers: ProcessedTicker[];
  processedFuturesTickers: ProcessedTicker[];
  
  // Избранные тикеры
  favoriteTickers: Set<string>;
  
  // Выбранный символ и рынок
  selectedSymbol: string;
  selectedMarketType: MarketType;
  selectedInterval: string;
  
  // Данные исторических свечей
  klines: Record<string, Kline[]>;
  
  // Состояние WebSocket
  wsStatus: WSConnectionStatus;
  
  // Действия (actions)
  setTickers: (marketType: MarketType, tickers: Ticker[] | FullTicker[]) => void;
  updateTicker: (marketType: MarketType, ticker: Ticker | FullTicker) => void;
  setKlines: (key: string, klines: Kline[]) => void;
  addKline: (key: string, kline: Kline) => void;
  setSelectedSymbol: (symbol: string) => void;
  setSelectedMarketType: (marketType: MarketType) => void;
  setSelectedInterval: (interval: string) => void;
  toggleFavorite: (symbol: string) => void;
  setWSStatus: (status: WSConnectionStatus) => void;
  getProcessedTickers: (marketType: MarketType) => ProcessedTicker[];
  reset: () => void;
}

// Helper function for klines cache key - moved outside the store definition
export const getKlinesCacheKey = (symbol: string, interval: string, marketType: MarketType): string => 
  `${marketType}_${symbol}_${interval}`;

// Helper function to map FullTicker to Ticker
const mapFullTickerToTicker = (fullTicker: FullTicker): Ticker => ({
  symbol: fullTicker.symbol,
  marketType: fullTicker.marketType,
  price: fullTicker.lastPrice, // from lastPrice in FullTicker
  priceChangePercent: fullTicker.priceChangePercent,
  volume: fullTicker.volume,
  quoteVolume: fullTicker.quoteVolume,
  count: fullTicker.count,
  lastUpdated: fullTicker.lastUpdated,
});

// Вспомогательная функция для обработки тикеров
const processTicker = (ticker: Ticker, state: any): ProcessedTicker => {
  const priceChangeColor = ticker.priceChangePercent >= 0 ? 'green' : 'red';
  const percentChangeColor = ticker.priceChangePercent >= 0 ? 'green' : 'red';
  
  return {
    ...ticker,
    priceChangeColor,
    percentChangeColor,
    displaySymbol: ticker.symbol,
    favorite: state.favoriteTickers.has(ticker.symbol)
  };
};

const initialState = {
  spotTickers: [],
  futuresTickers: [],
  processedSpotTickers: [],
  processedFuturesTickers: [],
  favoriteTickers: new Set<string>(),
  selectedSymbol: 'BTCUSDT',
  selectedMarketType: 'spot' as MarketType,
  selectedInterval: '1h',
  klines: {},
  wsStatus: WSConnectionStatus.CLOSED,
};

// Создание хранилища
export const useMarketStore = create<MarketState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // Действия
      setTickers: (marketType, incomingTickers) => 
        set((state) => {
          const tickersKey = marketType === 'spot' ? 'spotTickers' : 'futuresTickers';
          const processedKey = marketType === 'spot' ? 'processedSpotTickers' : 'processedFuturesTickers';
          const now = Date.now();
          
          // Быстрая проверка типа тикеров и преобразование при необходимости
          let baseTickers: Ticker[];
          
          // Проверка только первого элемента для определения типа всего массива
          if (incomingTickers.length > 0 && 'lastPrice' in incomingTickers[0] && !('price' in incomingTickers[0])) {
            // Преобразуем FullTicker в Ticker более эффективно
            baseTickers = (incomingTickers as FullTicker[]).map(ft => ({
              symbol: ft.symbol,
              marketType: ft.marketType,
              price: ft.lastPrice,
              priceChangePercent: ft.priceChangePercent,
              volume: ft.volume,
              quoteVolume: ft.quoteVolume,
              count: ft.count || 0,
              lastUpdated: ft.lastUpdated || now
            }));
          } else {
            baseTickers = incomingTickers as Ticker[];
          }
          
          // Определяем полное обновление по сравнению размера массивов
          const existingTickers = state[tickersKey] as Ticker[];
          const incomingCount = baseTickers.length;
          
          // Оптимизированное определение полного обновления:
          // 1. Если пришло больше 300 записей - всегда считаем полным обновлением
          // 2. Если существующих тикеров мало или массив пустой - считаем полным
          // 3. Если пришло более 80% от текущего количества - считаем полным
          const isFullUpdate = incomingCount > 300 || 
                              existingTickers.length === 0 ||
                              incomingCount > (existingTickers.length * 0.8);
          
          let updatedTickers: Ticker[];
          
          if (isFullUpdate) {
            // Для полного обновления просто используем новые данные с обновленным timestamp
            updatedTickers = baseTickers.map(ticker => ({
              ...ticker,
              lastUpdated: now
            }));
            
            if (process.env.NODE_ENV !== 'production') {
              console.debug(`[MarketStore] Full update: ${updatedTickers.length} ${marketType} tickers`);
            }
          } else {
            // Для частичного обновления используем Map для эффективного слияния
            const tickerMap = new Map<string, Ticker>();
            
            // Сначала добавляем все существующие тикеры
            existingTickers.forEach(ticker => tickerMap.set(ticker.symbol, ticker));
            
            // Затем обновляем/добавляем новые тикеры
            baseTickers.forEach(ticker => {
              tickerMap.set(ticker.symbol, {
                ...ticker,
                lastUpdated: now
              });
            });
            
            // Преобразуем Map обратно в массив
            updatedTickers = Array.from(tickerMap.values());
          }
          
          // Обрабатываем тикеры с добавлением производных полей
          const processedTickers = updatedTickers.map(ticker => processTicker(ticker, state));
          
          return {
            [tickersKey]: updatedTickers,
            [processedKey]: processedTickers
          };
        }),
      
      updateTicker: (marketType, incomingTicker) =>
        set((state) => {
          const tickersKey = marketType === 'spot' ? 'spotTickers' : 'futuresTickers';
          const processedKey = marketType === 'spot' ? 'processedSpotTickers' : 'processedFuturesTickers';
          
          // Map to Ticker if FullTicker is received
          const baseTicker: Ticker = ('lastPrice' in incomingTicker) && !('price' in incomingTicker)
            ? mapFullTickerToTicker(incomingTicker as FullTicker)
            : incomingTicker as Ticker;

          const currentTickers = [...(state[tickersKey] as Ticker[])];
          const index = currentTickers.findIndex(t => t.symbol === baseTicker.symbol);
          
          if (index !== -1) {
            currentTickers[index] = baseTicker;
          } else {
            currentTickers.push(baseTicker);
          }
          
          return {
            ...state,
            [tickersKey]: currentTickers,
            [processedKey]: currentTickers.map(ticker => processTicker(ticker, state))
          };
        }),
      
      setKlines: (key, klines) =>
        set((state) => ({
          klines: {
            ...state.klines,
            [key]: klines
          }
        })),
      
      addKline: (key, kline) =>
        set((state) => {
          const existingKlines = state.klines[key] || [];
          const updatedKlines = [...existingKlines];
          
          // Найти индекс свечи с тем же временем открытия или добавить новую
          const index = updatedKlines.findIndex(k => k.openTime === kline.openTime);
          
          if (index !== -1) {
            updatedKlines[index] = kline;
          } else {
            updatedKlines.push(kline);
            // Сортировка по времени открытия
            updatedKlines.sort((a, b) => a.openTime - b.openTime);
          }
          
          return {
            klines: {
              ...state.klines,
              [key]: updatedKlines
            }
          };
        }),
      
      setSelectedSymbol: (symbol) =>
        set((state) => {
          return { selectedSymbol: symbol };
        }),
      
      setSelectedMarketType: (marketType) =>
        set((state) => {
          return { selectedMarketType: marketType };
        }),
      
      setSelectedInterval: (interval) =>
        set((state) => {
          return { selectedInterval: interval };
        }),
      
      toggleFavorite: (symbol) =>
        set((state) => {
          const newFavorites = new Set(state.favoriteTickers);
          
          if (newFavorites.has(symbol)) {
            newFavorites.delete(symbol);
          } else {
            newFavorites.add(symbol);
          }
          
          return { favoriteTickers: newFavorites };
        }),
      
      setWSStatus: (status) =>
        set((state) => {
          return { wsStatus: status };
        }),
      
      getProcessedTickers: (marketType) => {
        const state = get();
        const tickers = marketType === 'spot' 
          ? state.processedSpotTickers 
          : state.processedFuturesTickers;
        
        return tickers.map(ticker => ({
          ...ticker,
          favorite: state.favoriteTickers.has(ticker.symbol)
        }));
      },

      reset: () => set((state) => {
        return initialState;
      }),
    }),
    { name: 'market-storage' }
  )
);

// An empty array for stable reference
const EMPTY_ARRAY: Kline[] = [];

// Селекторы для удобного доступа к данным
export const useSpotTickers = () => useMarketStore(state => state.getProcessedTickers('spot'));
export const useFuturesTickers = () => useMarketStore(state => state.getProcessedTickers('futures'));
export const useSelectedSymbol = () => useMarketStore(state => state.selectedSymbol);
export const useSelectedMarketType = () => useMarketStore(state => state.selectedMarketType);
export const useSelectedInterval = () => useMarketStore(state => state.selectedInterval);
export const useWSStatus = () => useMarketStore(state => state.wsStatus);

// Хук для получения свечей по выбранному символу, интервалу и типу рынка
export const useKlines = (symbol?: string, interval?: string, marketType?: MarketType) => {
  return useMarketStore(state => {
    const s = symbol || state.selectedSymbol;
    const i = interval || state.selectedInterval;
    const mt = marketType || state.selectedMarketType;
    const key = getKlinesCacheKey(s, i, mt);
    return state.klines[key] || EMPTY_ARRAY;
  });
};
