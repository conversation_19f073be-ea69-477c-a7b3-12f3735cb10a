@import "tailwindcss";
/* @import "tw-animate-css"; */

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --card: rgb(255 255 255);                       /* White */
  --card-foreground: rgb(20 20 22);              /* Near black/dark gray */
  --popover: rgb(255 255 255);                       /* White */
  --popover-foreground: rgb(20 20 22);              /* Near black/dark gray */
  --primary: rgb(30 30 33);                    /* Very dark gray */
  --primary-foreground: rgb(249 249 249);           /* Near white */
  --secondary: rgb(244 244 245);                   /* Light gray (zinc-100) */
  --secondary-foreground: rgb(30 30 33);          /* Very dark gray */
  --muted: rgb(244 244 245);                       /* Light gray (zinc-100) */
  --muted-foreground: rgb(113 113 122);             /* Gray (zinc-500) */
  --accent: rgb(244 244 245);                      /* Light gray (zinc-100) */
  --accent-foreground: rgb(30 30 33);             /* Very dark gray */
  --destructive: rgb(220 38 38);                   /* Red (red-600) */
  --border: rgb(228 228 231);                      /* Gray (zinc-200) */
  --input: rgb(228 228 231);                       /* Gray (zinc-200) */
  --ring: rgb(161 161 170);                      /* Gray (zinc-400) */
  --chart-1: rgb(234 179 8);                     /* Yellow (yellow-500) */
  --chart-2: rgb(6 182 212);                     /* Cyan (cyan-500) */
  --chart-3: rgb(99 102 241);                    /* Indigo (indigo-500) */
  --chart-4: rgb(163 230 53);                    /* Lime (lime-500) */
  --chart-5: rgb(245 158 11);                    /* Amber (amber-500) */
  --sidebar: rgb(249 249 249);                   /* Near white */
  --sidebar-foreground: rgb(20 20 22);              /* Near black/dark gray */
  --sidebar-primary: rgb(30 30 33);                /* Very dark gray */
  --sidebar-primary-foreground: rgb(249 249 249);   /* Near white */
  --sidebar-accent: rgb(244 244 245);              /* Light gray (zinc-100) */
  --sidebar-accent-foreground: rgb(30 30 33);     /* Very dark gray */
  --sidebar-border: rgb(228 228 231);              /* Gray (zinc-200) */
  --sidebar-ring: rgb(161 161 170);              /* Gray (zinc-400) */
  --background: rgb(255 255 255);                   /* White */
  --foreground: rgb(20 20 22);                  /* Near black/dark gray */
  --positive: rgb(34 197 94);                    /* Green (green-500) */
  --negative: rgb(220 38 38);                    /* Red (red-600) */
  --icon-default: rgb(113 113 122);             /* Gray (zinc-500) - Same as muted-foreground */
}

.dark {
  --background: rgb(11 14 17);                   /* Очень темный фон (из SCSS $dark-bg) */
  --foreground: rgb(195 195 195);                /* Возвращенный цвет текста (менее яркий) */
  --card: rgb(23 28 36);                         /* Темно-серый фон карточек (из SCSS $dark-card) */
  --card-foreground: rgb(195 195 195);            /* Возвращенный цвет текста */
  --popover: rgb(23 28 36);                      /* Темно-серый фон всплыв. окон (из SCSS $dark-card) */
  --popover-foreground: rgb(195 195 195);         /* Возвращенный цвет текста */
  --primary: rgb(138 43 226);                    /* Фиолетовый (BlueViolet) (из SCSS $primary-color) */
  --primary-foreground: rgb(248 250 252);        /* Чистый белый для контраста */
  --secondary: rgb(36 42 51);                    /* Темный фон при наведении (из SCSS $dark-hover) */
  --secondary-foreground: rgb(195 195 195);       /* Возвращенный цвет текста */
  --muted: rgb(36 42 51);                        /* Темный фон при наведении (из SCSS $dark-hover) */
  --muted-foreground: rgb(77 79 81);             /* Вторичный текст (из SCSS mix) */
  --accent: rgb(157 91 232);                     /* Светло-фиолетовый (из SCSS $info-color) */
  --accent-foreground: rgb(195 195 195);          /* Возвращенный цвет текста */
  --destructive: rgb(239 68 68);                 /* Красный (из SCSS $danger-color) */
  --border: rgb(24 30 37);                       /* Темная граница (из SCSS $dark-border) */
  --input: rgb(23 28 36);                        /* Фон инпутов (из SCSS $dark-card) */
  --ring: rgb(138 43 226);                       /* Фиолетовый (BlueViolet) - совпадает с primary */

  /* Цвета для настроек (индиго) */
  --settings-primary: 99 102 241;                /* Основной цвет настроек (индиго) */
  --settings-primary-foreground: 248 250 252;   /* Текст на основном цвете настроек (белый) */
  --settings-accent: 80 80 200;                 /* Акцентный цвет настроек (темный индиго) */

  --chart-1: rgb(129 140 248);                   /* Индиго 400 (оставлен) */
  --chart-2: rgb(56 189 248);                    /* Светло-голубой (оставлен) */
  --chart-3: rgb(251 191 36);                    /* Янтарный (оставлен) */
  --chart-4: rgb(232 121 249);                   /* Пурпурный (оставлен) */
  --chart-5: rgb(249 115 22);                    /* Оранжевый (оставлен) */
  --sidebar: rgb(23 28 36);                      /* Фон сайдбара (как карточки) */
  --sidebar-foreground: rgb(195 195 195);         /* Возвращенный цвет текста */
  --sidebar-primary: rgb(138 43 226);             /* Фиолетовый (BlueViolet) - совпадает с primary */
  --sidebar-primary-foreground: rgb(248 250 252); /* Чистый белый */
  --sidebar-accent: rgb(157 91 232);              /* Светло-фиолетовый - совпадает с accent */
  --sidebar-accent-foreground: rgb(195 195 195);  /* Возвращенный цвет текста */
  --sidebar-border: rgb(24 30 37);                /* Граница сайдбара (как основная) */
  --sidebar-ring: rgb(138 43 226);               /* Фиолетовый (BlueViolet) - совпадает с primary */
  --positive: rgb(16 185 129);                   /* Зеленый (из SCSS $secondary-color) */
  --negative: rgb(239 68 68);                    /* Красный (из SCSS $danger-color) */
  --icon-default: rgb(160 170 185);              /* Осветленные иконки (предыдущий muted-foreground) */
}


@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Optional: Smooth scrolling for the whole page */
    /* scroll-behavior: smooth; */
  }
  /* Добавляем классы для positive/negative */
  .text-positive {
    color: rgb(var(--positive)); /* Use rgb instead of oklch */
  }
  .text-negative {
    color: rgb(var(--negative)); /* Use rgb instead of oklch */
  }
}

@layer utilities {
  /* Utility to hide native browser scrollbars */
  /* Use this class on elements where a custom scrollbar solution is implemented */
  .hide-native-scrollbar {
    /* Webkit (Chrome, Safari, Edge) */
    &::-webkit-scrollbar {
      width: 0 !important;
      height: 0 !important;
      background: transparent !important;
      display: none !important;
    }
    
    /* Firefox */
    scrollbar-width: none !important;
    
    /* IE/Edge */
    -ms-overflow-style: none !important;
    
    /* Полностью отключаем привязку скроллинга */
    overflow-anchor: none !important;
    
    /* Отключаем инерцию скроллинга, которая может влиять на обновление скроллбара */
    -webkit-overflow-scrolling: auto !important;
    
    /* Дополнительные свойства для повышения производительности */
    will-change: scroll-position !important;
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
    
    /* Защита от будущих версий браузеров */
    -webkit-overflow-scrolling: touch !important;
    
    /* Explicit hack для iOS */
    .scroller-fix {
      height: 101%;
      overflow-y: scroll;
    }
  }
}
