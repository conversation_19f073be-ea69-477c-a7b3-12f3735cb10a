// src/indicators/definitions/rsi.ts
import type {
  IndicatorDefinition,
  IndicatorParam,
  IndicatorOutput,
  IndicatorCalculationResult,
  KlineData,
} from '@/shared/index';
import { LineStyle } from 'lightweight-charts';
// Import source helpers
import { getIndicatorSourceValue, IndicatorSource } from '../sources';

// --- Calculation Functions (Moved from calculations.ts) ---

/**
 * Calculates changes, average gains, and average losses needed for RSI.
 * Uses <PERSON>'s Smoothing Method (similar to EMA but slightly different).
 * @param changes - Array of price changes (close[i] - close[i-1]).
 * @param period - The period for RSI calculation.
 * @returns Object containing arrays of gains, losses, avgGain, and avgLoss.
 */
// Make local (not exported)
const calculateAverageGainLoss = (
    changes: number[],
    period: number
): {
    gains: number[];
    losses: number[];
    avgGain: (number | undefined)[];
    avgLoss: (number | undefined)[];
} => {
    const gains: number[] = changes.map(change => Math.max(0, change));
    const losses: number[] = changes.map(change => Math.max(0, -change));
    const avgGain: (number | undefined)[] = Array(changes.length).fill(undefined);
    const avgLoss: (number | undefined)[] = Array(changes.length).fill(undefined);

    if (changes.length < period) {
        return { gains, losses, avgGain, avgLoss }; // Not enough data
    }

    // Calculate initial average gain/loss (simple average)
    let initialGainSum = 0;
    let initialLossSum = 0;
    for (let i = 0; i < period; i++) {
        initialGainSum += gains[i];
        initialLossSum += losses[i];
    }
    // Ensure index exists before assignment
    if (avgGain.length > period - 1) avgGain[period - 1] = initialGainSum / period;
    if (avgLoss.length > period - 1) avgLoss[period - 1] = initialLossSum / period;

    // Calculate subsequent averages using Wilder's smoothing
    for (let i = period; i < changes.length; i++) {
        const prevAvgGain = avgGain[i - 1] ?? 0; // Use 0 if undefined
        const prevAvgLoss = avgLoss[i - 1] ?? 0;

        avgGain[i] = (prevAvgGain * (period - 1) + gains[i]) / period;
        avgLoss[i] = (prevAvgLoss * (period - 1) + losses[i]) / period;
    }

    return { gains, losses, avgGain, avgLoss };
};

/**
 * Calculates Relative Strength Index (RSI).
 * @param sourceData - Array of price data.
 * @param period - The period for RSI calculation.
 * @returns Array of RSI values or undefined where calculation isn't possible.
 */
// Make local (not exported)
const calculateRSI = (
  sourceData: number[],
  period: number
): (number | undefined)[] => {
  if (period <= 0 || sourceData.length <= period) {
    return Array(sourceData.length).fill(undefined);
  }

  // Calculate price changes
  const changes: number[] = [];
  for (let i = 1; i < sourceData.length; i++) {
      if (sourceData[i] === undefined || sourceData[i-1] === undefined) continue;
      changes.push(sourceData[i] - sourceData[i - 1]);
  }

  // Calculate average gains and losses (uses local function)
  const { avgGain, avgLoss } = calculateAverageGainLoss(changes, period);

  // RSI calculation needs to align with the original sourceData length
  const rsi: (number | undefined)[] = Array(sourceData.length).fill(undefined);

  // Start calculation from the index where avgGain/avgLoss are first defined
  const startIndex = period; // RSI value is defined at index `period` corresponding to `changes[period-1]`

  for (let i = period -1 ; i < changes.length; i++) {
      const currentAvgGain = avgGain[i];
      const currentAvgLoss = avgLoss[i];
      const targetIndex = i + 1; // Index in the original sourceData array

      if (targetIndex >= sourceData.length) break;

      if (currentAvgGain === undefined || currentAvgLoss === undefined) {
          rsi[targetIndex] = undefined;
          continue;
      }

      if (currentAvgLoss === 0) {
          rsi[targetIndex] = 100; // Prevent division by zero, RSI is 100
      } else {
          const rs = currentAvgGain / currentAvgLoss; // Relative Strength
          const calculatedRsi = 100 - (100 / (1 + rs));
          rsi[targetIndex] = calculatedRsi;
      }
  }

  return rsi;
};


// --- Indicator Definition --- (Existing code below)

// Define specific parameter types for RSI
interface RsiParams {
  source: IndicatorSource; // Use the standard type
  length: number;
  [key: string]: unknown;
}

const rsiParameters: ReadonlyArray<IndicatorParam> = [
  {
    id: 'source',
    name: 'Source',
    type: 'source', // Change type to 'source'
    defaultValue: 'close',
    // Remove options, they are handled by the 'source' type
  },
  {
    id: 'length',
    name: 'Length',
    type: 'number',
    defaultValue: 14,
    min: 2,
    step: 1,
  },
];

const rsiOutputs: ReadonlyArray<IndicatorOutput> = [
  {
    id: 'rsi',
    name: 'RSI',
    type: 'line',
    color: '#800080',
    lineWidth: 1,
    lineStyle: LineStyle.Solid,
  },
];

// Wrapper function using getIndicatorSourceValue
const calculateRsiWrapper = (
  ohlcv: ReadonlyArray<KlineData>,
  params: RsiParams
): IndicatorCalculationResult[] => {
  // Default to 'close' if params.source is missing, undefined, null, or an empty string
  const source: IndicatorSource = params.source || 'close';
  // Extract source data using the helper, handle potential NaN
  const sourceData = ohlcv.map((kline) => getIndicatorSourceValue(kline, source));
  // Filter out initial NaNs if necessary, or handle within calculateRSI if it supports undefined/NaN
  // Assuming calculateRSI handles undefined/NaN gracefully as implemented before

  const rsiValues = calculateRSI(sourceData, params.length);

  return ohlcv.map((_, index) => ({
    rsi: rsiValues[index],
  }));
};

export const RSI: IndicatorDefinition<RsiParams> = {
  id: 'RSI',
  name: 'Relative Strength Index',
  shortName: 'RSI',
  description:
    'Measures the magnitude of recent price changes to evaluate overbought or oversold conditions.',
  parameters: rsiParameters,
  outputs: rsiOutputs,
  calculate: calculateRsiWrapper,
  group: 'oscillator',
  pane: 'separate',
}; 