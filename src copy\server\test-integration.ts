// Integration test for refactored components
import { Elysia } from 'elysia';
import { logger } from './lib/logger';
import { initializeQuestDB } from './services/questdb.service';
import { initializeDragonfly } from './services/dragonfly.service';
import { BinanceService } from './exchange/binance.service';
import { initializeIngestService, shutdownIngestService } from './services/ingest.service';
import broadcastService from './services/broadcast.service';
import { apiHandlers } from './handlers/api.handlers';
import { wsHandlers } from './handlers/ws.handler';
import { WebSocket } from 'ws';

// Configuration
const TEST_DURATION = 30000; // Run test for 30 seconds
const TEST_SYMBOLS = ['BTCUSDT', 'ETHUSDT']; // Test with these symbols
const TEST_INTERVALS = ['1m']; // Test with this interval

/**
 * Run integration test for the refactored API and WebSocket components
 */
async function runIntegrationTest() {
  try {
    // Step 1: Initialize services
    logger.info('Step 1: Initializing services');
    
    // Initialize database connections
    const questDBClient = await initializeQuestDB();
    const dragonflyClient = await initializeDragonfly();
    
    // Setup broadcast service
    const publishFunction = (topic: string, data: any) => {
      logger.info(`Publishing to ${topic}: ${JSON.stringify(data)}`);
    };
    broadcastService.initialize(publishFunction);
    
    // Initialize ingest service
    await initializeIngestService();
    
    // Step 2: Create test API server
    logger.info('Step 2: Creating test API server');
    const app = new Elysia({ name: 'integration-test' })
      .decorate('logger', logger)
      .decorate('questDBPool', questDBClient)
      .decorate('dragonfly', dragonflyClient)
      .decorate('broadcast', broadcastService)
      .use(apiHandlers)
      .use(wsHandlers)
      .listen(3001);
    
    logger.info('Test server running at http://localhost:3001');
    
    // Step 4: Connect test client
    logger.info('Step 4: Connecting test WebSocket client');
    const ws = new WebSocket('ws://localhost:3001/ws');
    
    ws.on('open', () => {
      logger.info('Test client connected');
      
      // Subscribe to symbols
      const topics = [
        'tickers_spot',
        ...TEST_SYMBOLS.flatMap(symbol => 
          TEST_INTERVALS.map(interval => `kline_spot_${symbol.toLowerCase()}_${interval}`)
        )
      ];
      
      ws.send(JSON.stringify({ 
        type: 'subscribe', 
        topics 
      }));
      
      logger.info(`Subscribed to topics: ${topics.join(', ')}`);
    });
    
    // Listen for messages
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        // Skip logging for kline messages to reduce noise
        if (message.type === 'data' && message.topic && message.topic.startsWith('kline_')) {
          // Just count them instead of logging every message
          return;
        }
        
        // Log non-kline messages
        logger.info(`Received message: ${data}`);
      } catch (err) {
        logger.error('Failed to parse message:', err);
      }
    });
    
    // Handle connection errors  
    ws.on('error', (error) => {
      logger.error('WebSocket client error:', error);
    });
    
    ws.on('close', (code, reason) => {
      logger.warn(`WebSocket client disconnected: ${code} - ${reason}`);
    });
    
    // Step 5: Run test requests to verify REST APIs
    logger.info('Step 5: Testing REST API endpoints');
    setTimeout(async () => {
      try {
        // Test API endpoints
        const symbolsResponse = await fetch('http://localhost:3001/api/symbols/spot');
        const symbols = await symbolsResponse.json();
        logger.info(`Symbols endpoint returned ${symbols.symbols ? symbols.symbols.length : 0} symbols`);
        
        const tickersResponse = await fetch('http://localhost:3001/api/tickers/spot');
        const tickers = await tickersResponse.json();
        logger.info(`Tickers endpoint returned ${tickers.tickers ? tickers.tickers.length : 0} tickers`);
        
        const candlesResponse = await fetch(`http://localhost:3001/api/candles/spot/${TEST_SYMBOLS[0]}/1m?limit=10`);
        const candles = await candlesResponse.json();
        logger.info(`Candles endpoint returned ${candles.candles ? candles.candles.length : 0} candles for ${TEST_SYMBOLS[0]}/1m`);
      } catch (error) {
        logger.error('REST API test failed:', error);
      }
    }, 5000); // Wait 5 seconds for server to be fully ready
    
    // Step 6: Shutdown after test duration
    logger.info(`Test will run for ${TEST_DURATION/1000} seconds...`);
    
    setTimeout(async () => {
      logger.info('Test duration complete. Shutting down...');
      
      // Close WebSocket client
      ws.close();
      
      // Shutdown services
      await shutdownIngestService();
      broadcastService.shutdown();
      
      // Close server
      app.stop();
      
      logger.info('Integration test completed successfully');
      process.exit(0);
    }, TEST_DURATION);
    
  } catch (error) {
    logger.error('Integration test failed:', error);
    process.exit(1);
  }
}

// Run the test
runIntegrationTest().catch(err => {
  logger.error('Fatal error:', err);
  process.exit(1);
}); 