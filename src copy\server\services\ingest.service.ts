import { EventEmitter } from 'events';
import { Sender } from '@questdb/nodejs-client';
import { logger } from '../lib/logger';
import { MarketType } from '../../shared/types';
import { getDragonflyClient } from './dragonfly.service';
import { binanceService } from '../exchange/binance.service';
import { 
  Kline, 
  FullTicker, 
  SymbolInfo,
  TickerEvent,
  KlineEvent,
  SymbolEvent
} from '../exchange/exchange.interface';

// Create a logger instance for the IngestService
const log = logger.child({ service: 'IngestService' });

// Redis/Dragonfly key patterns
export const REDIS_KEYS = {
  KLINE_KEY: (marketType: MarketType, symbol: string, interval: string) => 
    `klines:${marketType}:${symbol.toUpperCase()}:${interval}`,
  
  TICKERS_KEY: (marketType: MarketType) => 
    `tickers:${marketType}`,
  
  SYMBOLS_KEY: (marketType: MarketType) => 
    `symbols:${marketType}`
};

// TTL settings for different data types in cache
const CACHE_TTL = {
  KLINES: {
    '1m': 60 * 60,        // 1 hour for 1m candles
    '5m': 4 * 60 * 60,    // 4 hours for 5m candles
    '15m': 12 * 60 * 60,  // 12 hours for 15m candles
    '1h': 24 * 60 * 60,   // 24 hours for 1h candles
    '4h': 7 * 24 * 60 * 60, // 7 days for 4h candles
    '1d': 30 * 24 * 60 * 60, // 30 days for daily candles
    DEFAULT: 24 * 60 * 60  // 24 hours default
  },
  TICKERS: 300,  // 5 minutes
  SYMBOLS: 86400 // 24 hours
};

// Popular pairs for preloading
const POPULAR_PAIRS = {
  spot: [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT',
    'DOGEUSDT', 'SOLUSDT', 'MATICUSDT', 'LTCUSDT', 'AVAXUSDT'
  ],
  futures: [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT',
    'DOGEUSDT', 'SOLUSDT', 'MATICUSDT', 'LTCUSDT', 'AVAXUSDT'
  ]
};

// Popular intervals for preloading
const POPULAR_INTERVALS = ['1m', '5m', '15m', '1h', '4h', '1d'];

// App event types
export type AppEventType = 'kline_update' | 'tickers_update' | 'symbols_update';

// App event interface
export interface AppEvent<T = any> {
  type: AppEventType;
  topic: string;
  data: T;
}

/**
 * IngestService is responsible for:
 * 1. Collecting data from exchanges (via ExchangeService)
 * 2. Storing data in persistent storage (QuestDB)
 * 3. Caching data for quick access (Dragonfly/Redis)
 * 4. Generating application-level events for real-time updates
 */
export class IngestService extends EventEmitter {
  private isRunning = false;
  private dragonflyClient: any = null;
  private questDbSender: Sender | null = null;
  private intervalTimers: Record<string, NodeJS.Timeout> = {};
  
  constructor() {
    super();
    this.setMaxListeners(100);
  }
  
  /**
   * Initialize the IngestService
   */
  public async initialize(): Promise<void> {
    if (this.isRunning) {
      log.warn('IngestService already running');
      return;
    }
    
    log.info('Initializing IngestService...');
    
    try {
      // Initialize Dragonfly client
      this.dragonflyClient = await getDragonflyClient();
      
      // Initialize QuestDB Sender
      this.questDbSender = Sender.fromConfig(
        'http::addr=localhost:9000;auto_flush_rows=1000;auto_flush_interval=500;request_timeout=5000;retry_timeout=30000;'
      );
      
      // Initialize the exchange service
      await binanceService.initialize();
      
      // Setup event listeners for exchange data
      this.setupExchangeEventListeners();
      
      // Initial data loading
      await this.loadInitialData();
      
      // Setup periodic data refreshes
      this.setupPeriodicRefreshes();
      
      this.isRunning = true;
      log.info('IngestService initialized successfully');
    } catch (error) {
      log.error('Failed to initialize IngestService:', error);
      throw error;
    }
  }
  
  /**
   * Shutdown the IngestService
   */
  public async shutdown(): Promise<void> {
    if (!this.isRunning) {
      log.warn('IngestService not running');
      return;
    }
    
    log.info('Shutting down IngestService...');
    
    // Clear all interval timers
    Object.keys(this.intervalTimers).forEach(key => {
      clearInterval(this.intervalTimers[key]);
      delete this.intervalTimers[key];
    });
    
    // Shutdown exchange service
    await binanceService.shutdown();
    
    // Flush and close QuestDB Sender
    if (this.questDbSender) {
      try {
        await this.questDbSender.flush();
        await this.questDbSender.close();
      } catch (error) {
        log.error('Error closing QuestDB Sender:', error);
      }
      this.questDbSender = null;
    }
    
    this.isRunning = false;
    log.info('IngestService shutdown completed');
  }
  
  /**
   * Setup event listeners for exchange data
   */
  private setupExchangeEventListeners(): void {
    // Listen for ticker updates
    binanceService.on('ticker', (event: TickerEvent) => {
      this.processTickerEvent(event);
    });
    
    // Listen for kline updates
    binanceService.on('kline', (event: KlineEvent) => {
      this.processKlineEvent(event);
    });
    
    // Listen for symbol updates
    binanceService.on('symbol', (event: SymbolEvent) => {
      this.processSymbolEvent(event);
    });
    
    log.info('Exchange event listeners set up');
  }
  
  /**
   * Process ticker events from the exchange
   */
  private processTickerEvent(event: TickerEvent): void {
    const { marketType, data: tickers } = event;
    
    if (!tickers || tickers.length === 0) {
      return;
    }
    
    // Cache tickers in Redis
    this.cacheTickers(marketType, tickers);
    
    // Save tickers to QuestDB
    this.saveTickersToQuestDB(tickers);
    
    // Emit app event for tickers update
    const topic = `tickers_${marketType}`;
    this.emit('app_event', {
      type: 'tickers_update',
      topic,
      data: tickers
    });
  }
  
  /**
   * Process kline events from the exchange
   */
  private processKlineEvent(event: KlineEvent): void {
    const { marketType, data: kline, symbol, interval } = event;
    
    if (!kline) {
      return;
    }
    
    // Cache kline in Redis
    this.cacheKlines(marketType, [kline]);
    
    // Save kline to QuestDB
    this.saveKlinesToQuestDB([kline]);
    
    // Emit app event for kline update
    const topic = `kline_${marketType}_${symbol.toLowerCase()}_${interval}`;
    this.emit('app_event', {
      type: 'kline_update',
      topic,
      data: kline
    });
  }
  
  /**
   * Process symbol events from the exchange
   */
  private processSymbolEvent(event: SymbolEvent): void {
    const { marketType, data: symbols } = event;
    
    if (!symbols || symbols.length === 0) {
      return;
    }
    
    // Cache symbols in Redis
    this.cacheSymbols(marketType, symbols);
    
    // Emit app event for symbols update
    const topic = `symbols_${marketType}`;
    this.emit('app_event', {
      type: 'symbols_update',
      topic,
      data: symbols
    });
  }
  
  /**
   * Cache tickers in Redis
   */
  private async cacheTickers(marketType: MarketType, tickers: FullTicker[]): Promise<void> {
    try {
      const key = REDIS_KEYS.TICKERS_KEY(marketType);
      await this.dragonflyClient.set(
        key,
        JSON.stringify(tickers),
        'EX',
        CACHE_TTL.TICKERS
      );
      log.debug(`Cached ${tickers.length} ${marketType} tickers`);
    } catch (error) {
      log.error(`Error caching ${marketType} tickers:`, error);
    }
  }
  
  /**
   * Cache klines in Redis
   */
  private async cacheKlines(marketType: MarketType, klines: Kline[]): Promise<void> {
    if (!klines || klines.length === 0) {
      return;
    }
    
    try {
      // Group klines by symbol and interval
      const groupedKlines: Record<string, Kline[]> = {};
      
      klines.forEach(kline => {
        const key = REDIS_KEYS.KLINE_KEY(marketType, kline.symbol, kline.interval);
        if (!groupedKlines[key]) {
          groupedKlines[key] = [];
        }
        groupedKlines[key].push(kline);
      });
      
      // Save each group to Redis
      const promises = Object.entries(groupedKlines).map(([key, klinesGroup]) => {
        // Sort by openTime
        const sortedKlines = [...klinesGroup].sort((a, b) => a.openTime - b.openTime);
        
        // Get TTL based on interval
        const interval = sortedKlines[0].interval;
        const ttl = CACHE_TTL.KLINES[interval as keyof typeof CACHE_TTL.KLINES] || CACHE_TTL.KLINES.DEFAULT;
        
        return this.dragonflyClient.set(
          key,
          JSON.stringify(sortedKlines),
          'EX',
          ttl
        );
      });
      
      await Promise.all(promises);
    } catch (error) {
      log.error('Error caching klines:', error);
    }
  }
  
  /**
   * Cache symbols in Redis
   */
  private async cacheSymbols(marketType: MarketType, symbols: SymbolInfo[]): Promise<void> {
    try {
      const key = REDIS_KEYS.SYMBOLS_KEY(marketType);
      await this.dragonflyClient.set(
        key,
        JSON.stringify(symbols),
        'EX',
        CACHE_TTL.SYMBOLS
      );
      log.debug(`Cached ${symbols.length} ${marketType} symbols`);
    } catch (error) {
      log.error(`Error caching ${marketType} symbols:`, error);
    }
  }
  
  /**
   * Save tickers to QuestDB
   */
  private saveTickersToQuestDB(tickers: FullTicker[]): void {
    if (!this.questDbSender) {
      log.error('QuestDB Sender not initialized');
      return;
    }
    
    try {
      for (const ticker of tickers) {
        const timestamp = ticker.lastUpdated || Date.now();
        this.questDbSender
          .table('tickers')
          .symbol('symbol', ticker.symbol)
          .symbol('market_type', ticker.marketType)
          .floatColumn('price', ticker.price)
          .floatColumn('price_change_percent', ticker.priceChangePercent)
          .floatColumn('volume', ticker.volume)
          .floatColumn('quote_volume', ticker.quoteVolume)
          .intColumn('count', ticker.count)
          .at(timestamp, 'ms');
      }
    } catch (error) {
      log.error('Error saving tickers to QuestDB:', error);
    }
  }
  
  /**
   * Save klines to QuestDB
   */
  private saveKlinesToQuestDB(klines: Kline[]): void {
    if (!this.questDbSender) {
      log.error('QuestDB Sender not initialized');
      return;
    }
    
    try {
      for (const kline of klines) {
        const timestamp = kline.closeTime || kline.openTime || Date.now();
        this.questDbSender
          .table('klines')
          .symbol('symbol', kline.symbol)
          .symbol('market_type', kline.marketType)
          .symbol('interval', kline.interval)
          .timestampColumn('open_time', kline.openTime, 'ms')
          .floatColumn('open', kline.open)
          .floatColumn('high', kline.high)
          .floatColumn('low', kline.low)
          .floatColumn('close', kline.close)
          .floatColumn('volume', kline.volume)
          .timestampColumn('close_time', kline.closeTime, 'ms')
          .floatColumn('quote_volume', kline.quoteVolume)
          .intColumn('trades', kline.trades)
          .booleanColumn('is_closed', kline.isClosed)
          .at(timestamp, 'ms');
      }
    } catch (error) {
      log.error('Error saving klines to QuestDB:', error);
    }
  }
  
  /**
   * Load initial data from exchanges
   */
  private async loadInitialData(): Promise<void> {
    log.info('Loading initial data...');
    
    try {
      // Load symbols for both market types
      await this.refreshSymbols();
      
      // Load tickers for both market types
      await this.refreshTickers();
      
      // Preload popular pairs
      await this.preloadPopularPairs();
      
      log.info('Initial data loading completed');
    } catch (error) {
      log.error('Error loading initial data:', error);
    }
  }
  
  /**
   * Setup periodic data refreshes
   */
  private setupPeriodicRefreshes(): void {
    // Refresh symbols every hour
    this.intervalTimers.symbols = setInterval(
      () => this.refreshSymbols(),
      60 * 60 * 1000 // 1 hour
    );
    
    // Refresh tickers every minute
    this.intervalTimers.tickers = setInterval(
      () => this.refreshTickers(),
      60 * 1000 // 1 minute
    );
    
    // Subscribe to real-time updates for both market types
    this.setupRealtimeSubscriptions();
    
    log.info('Periodic refreshes set up');
  }
  
  /**
   * Setup real-time subscriptions to exchange data
   */
  private setupRealtimeSubscriptions(): void {
    // Subscribe to tickers for both market types
    binanceService.subscribeTickers('spot');
    binanceService.subscribeTickers('futures');
    
    // Subscribe to klines for popular pairs
    this.setupPopularPairsSubscriptions();
    
    log.info('Real-time subscriptions set up');
  }
  
  /**
   * Setup subscriptions for popular pairs
   */
  private setupPopularPairsSubscriptions(): void {
    for (const marketType of ['spot', 'futures'] as MarketType[]) {
      for (const symbol of POPULAR_PAIRS[marketType]) {
        for (const interval of POPULAR_INTERVALS) {
          binanceService.subscribeKline(marketType, symbol, interval);
        }
      }
    }
    
    log.info('Popular pairs subscriptions set up');
  }
  
  /**
   * Refresh symbols for both market types
   */
  private async refreshSymbols(): Promise<void> {
    log.info('Refreshing symbols...');
    
    try {
      const spotSymbols = await binanceService.fetchSymbols('spot');
      const futuresSymbols = await binanceService.fetchSymbols('futures');
      
      // Cache symbols
      await this.cacheSymbols('spot', spotSymbols);
      await this.cacheSymbols('futures', futuresSymbols);
      
      // Emit app events
      this.emit('app_event', {
        type: 'symbols_update',
        topic: 'symbols_spot',
        data: spotSymbols
      });
      
      this.emit('app_event', {
        type: 'symbols_update',
        topic: 'symbols_futures',
        data: futuresSymbols
      });
      
      log.info(`Refreshed ${spotSymbols.length} spot symbols and ${futuresSymbols.length} futures symbols`);
    } catch (error) {
      log.error('Error refreshing symbols:', error);
    }
  }
  
  /**
   * Refresh tickers for both market types
   */
  private async refreshTickers(): Promise<void> {
    log.info('Refreshing tickers...');
    
    try {
      const spotTickers = await binanceService.fetchTickers('spot');
      const futuresTickers = await binanceService.fetchTickers('futures');
      
      // Cache tickers
      await this.cacheTickers('spot', spotTickers);
      await this.cacheTickers('futures', futuresTickers);
      
      // Save to QuestDB
      this.saveTickersToQuestDB(spotTickers);
      this.saveTickersToQuestDB(futuresTickers);
      
      // Emit app events
      this.emit('app_event', {
        type: 'tickers_update',
        topic: 'tickers_spot',
        data: spotTickers
      });
      
      this.emit('app_event', {
        type: 'tickers_update',
        topic: 'tickers_futures',
        data: futuresTickers
      });
      
      log.info(`Refreshed ${spotTickers.length} spot tickers and ${futuresTickers.length} futures tickers`);
    } catch (error) {
      log.error('Error refreshing tickers:', error);
    }
  }
  
  /**
   * Preload historical data for popular pairs
   */
  private async preloadPopularPairs(): Promise<void> {
    log.info('Preloading data for popular pairs...');
    
    try {
      const tasks: Promise<any>[] = [];
      
      // Create tasks for loading historical klines for popular pairs
      for (const marketType of ['spot', 'futures'] as MarketType[]) {
        for (const symbol of POPULAR_PAIRS[marketType]) {
          for (const interval of POPULAR_INTERVALS) {
            tasks.push(this.fetchAndProcessHistoricalKlines(marketType, symbol, interval));
          }
        }
      }
      
      // Execute tasks with limited concurrency
      const batchSize = 5;
      for (let i = 0; i < tasks.length; i += batchSize) {
        const batch = tasks.slice(i, i + batchSize);
        await Promise.allSettled(batch);
        
        // Small delay between batches to avoid rate limiting
        if (i + batchSize < tasks.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      log.info('Preloading of popular pairs completed');
    } catch (error) {
      log.error('Error preloading popular pairs:', error);
    }
  }
  
  /**
   * Fetch and process historical klines
   */
  private async fetchAndProcessHistoricalKlines(
    marketType: MarketType,
    symbol: string,
    interval: string,
    limit: number = 1000
  ): Promise<void> {
    try {
      const klines = await binanceService.fetchKlines(marketType, symbol, interval, { limit });
      
      if (klines.length > 0) {
        // Cache klines
        await this.cacheKlines(marketType, klines);
        
        // Save to QuestDB
        this.saveKlinesToQuestDB(klines);
        
        // log.debug(`Processed ${klines.length} historical klines for ${marketType}:${symbol}:${interval}`); // Commented out to reduce log spam
      }
    } catch (error) {
      log.error(`Error fetching historical klines for ${marketType}:${symbol}:${interval}:`, error);
    }
  }
}

// Create singleton instance
const ingestService = new IngestService();

/**
 * Initialize the IngestService
 */
export async function initializeIngestService(): Promise<void> {
  await ingestService.initialize();
}

/**
 * Shutdown the IngestService
 */
export async function shutdownIngestService(): Promise<void> {
  await ingestService.shutdown();
}

/**
 * Get the IngestService instance
 */
export function getIngestService(): IngestService {
  return ingestService;
}

export default ingestService;