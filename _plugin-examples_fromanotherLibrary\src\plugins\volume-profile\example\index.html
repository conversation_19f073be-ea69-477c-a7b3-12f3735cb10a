<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - Volume Profile Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Volume Profile</h1>
			<p>
				A Volume Profile anchored to a specified point (defined by price and
				time values) on the chart.<br /><strong>Note:</strong> that the example
				is randomly generated so be sure to refresh the chart a few times.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>
