import '@/shared/lib/wdyr'; // Import WDYR initialization as early as possible
import type { Metadata } from "next";
import localFont from 'next/font/local';
import Providers from './providers'; // Используем default импорт
// import { DevTools } from '@/components/DevTools'; // Импортируем DevTools --- Удаляем импорт
import "./globals.css";
// import "@/styles/global.scss"; // Оставляем закомментированным

// const queryClient = new QueryClient({ ... }); // Убираем создание клиента

// Используем локальные шрифты вместо Google Fonts


export const metadata: Metadata = {
  title: "Metacharts",
  description: "Crypto Dashboard",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <body
      >
        {/* Оборачиваем children в Providers */}
        <Providers>
          {children}
          {/* {process.env.NODE_ENV === 'development' && <DevTools />} --- Удаляем DevTools отсюда */}
        </Providers>
      </body>
    </html>
  );
}
