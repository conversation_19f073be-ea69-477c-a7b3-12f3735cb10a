-- Schema definition for MetaCharts QuestDB tables

-- Table for Klines (Candlesticks)
CREATE TABLE IF NOT EXISTS klines (
    symbol SYMBOL CAPACITY 2000,
    market_type SYMBOL CAPACITY 10,
    interval SY<PERSON><PERSON> CAPACITY 50,
    open_time TIMESTAMP,
    open DOUBLE,
    high DOUBLE,
    low DOUBLE,
    close DOUBLE,
    volume DOUBLE,
    close_time TIMESTAMP,
    quote_volume DOUBLE,
    trades LONG,
    is_closed BOOLEAN,
    ts TIMESTAMP -- Designated timestamp for QuestDB
) timestamp(ts) PARTITION BY MONTH WAL DEDUP UPSERT KEYS(ts, symbol, market_type, interval);

