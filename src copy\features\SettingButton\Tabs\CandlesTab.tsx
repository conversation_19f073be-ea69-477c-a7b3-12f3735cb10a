import React, { memo, useMemo, useCallback } from 'react';
import { AppSettings } from '@/shared/index';
import { Slider, SimpleColorPicker } from './Controls'; 
import { cn, throttle, debounce } from '@/shared/lib/utils';
import {
    useAppSettingsStore,
    selectChartCandleBodyUpColor,
    selectChartCandleBodyDownColor,
    selectChartCandleBorderUpColor,
    selectChartCandleBorderDownColor,
    selectChartCandleWickUpColor,
    selectChartCandleWickDownColor,
    selectChartCandleBodyEnabled,
    selectChartCandleBorderEnabled,
    selectChartCandleWickEnabled,
    selectChartRightOffset,
    selectChartBarSpacing,
    selectSetSetting
} from '@/shared/store/settingsStore';

// --- Убираем закомментированные импорты Ant Design ---
// import { ColorPicker } from 'antd';
// import type { Color } from 'antd/es/color-picker'; 

// --- Интерфейс CandlesTabProps ---
interface CandlesTabProps {}

// --- Интерфейс для настроек цветов свечей ---
interface CandleColors {
  upBody: string;
  downBody: string;
  upBorder: string;
  downBorder: string;
  upWick: string;
  downWick: string;
}

// --- Маппинг между внутренним интерфейсом CandleColors и AppSettings ---
const candleColorKeysMap = {
  upBody: 'chartCandleBodyUpColor',
  downBody: 'chartCandleBodyDownColor',
  upBorder: 'chartCandleBorderUpColor',
  downBorder: 'chartCandleBorderDownColor',
  upWick: 'chartCandleWickUpColor',
  downWick: 'chartCandleWickDownColor'
} as const;

// --- Тип для ГРУППЫ настроек свечи (строки) ---
interface CandleGroup {
  id: 'body' | 'borders' | 'wick';
  label: string;
  upColorKey: keyof CandleColors;
  downColorKey: keyof CandleColors;
  enabledKey: 'chartCandleBodyEnabled' | 'chartCandleBorderEnabled' | 'chartCandleWickEnabled';
}

// --- CandlesTab ---
const CandlesTab: React.FC<CandlesTabProps> = memo(() => {
  // --- Get data from Zustand store ---
  const chartCandleBodyUpColor = useAppSettingsStore(selectChartCandleBodyUpColor) || '#26a69a';
  const chartCandleBodyDownColor = useAppSettingsStore(selectChartCandleBodyDownColor) || '#ef5350';
  const chartCandleBorderUpColor = useAppSettingsStore(selectChartCandleBorderUpColor) || '#26a69a';
  const chartCandleBorderDownColor = useAppSettingsStore(selectChartCandleBorderDownColor) || '#ef5350';
  const chartCandleWickUpColor = useAppSettingsStore(selectChartCandleWickUpColor) || '#26a69a';
  const chartCandleWickDownColor = useAppSettingsStore(selectChartCandleWickDownColor) || '#ef5350';
  const chartCandleBodyEnabled = useAppSettingsStore(selectChartCandleBodyEnabled) ?? true;
  const chartCandleBorderEnabled = useAppSettingsStore(selectChartCandleBorderEnabled) ?? true;
  const chartCandleWickEnabled = useAppSettingsStore(selectChartCandleWickEnabled) ?? true;
  const chartRightOffset = useAppSettingsStore(selectChartRightOffset) ?? 5;
  const chartBarSpacing = useAppSettingsStore(selectChartBarSpacing) ?? 6;
  const setSetting = useAppSettingsStore(selectSetSetting);

  // --- Local handler for updating multiple settings (similar to what was in Setting.tsx) ---
  const handleUpdateSettings = useCallback((settingsUpdate: Partial<AppSettings>) => {
    for (const key in settingsUpdate) {
        if (Object.prototype.hasOwnProperty.call(settingsUpdate, key)) {
            const value = settingsUpdate[key as keyof AppSettings];
            if (value !== undefined) {
              setSetting(key as keyof AppSettings, value);
            }
        }
    }
  }, [setSetting]);

  const candleGroups: CandleGroup[] = useMemo(() => [
    { id: 'body', label: 'Body', upColorKey: 'upBody', downColorKey: 'downBody', enabledKey: 'chartCandleBodyEnabled' },
    { id: 'borders', label: 'Borders', upColorKey: 'upBorder', downColorKey: 'downBorder', enabledKey: 'chartCandleBorderEnabled' },
    { id: 'wick', label: 'Wick', upColorKey: 'upWick', downColorKey: 'downWick', enabledKey: 'chartCandleWickEnabled' },
  ], []);

  const currentColors: CandleColors = useMemo(() => ({
    upBody: chartCandleBodyUpColor,
    downBody: chartCandleBodyDownColor,
    upBorder: chartCandleBorderUpColor,
    downBorder: chartCandleBorderDownColor,
    upWick: chartCandleWickUpColor,
    downWick: chartCandleWickDownColor,
  }), [
    chartCandleBodyUpColor, chartCandleBodyDownColor, chartCandleBorderUpColor,
    chartCandleBorderDownColor, chartCandleWickUpColor, chartCandleWickDownColor,
  ]);

  const currentEnabledStates = useMemo(() => ({
    chartCandleBodyEnabled,
    chartCandleBorderEnabled,
    chartCandleWickEnabled,
  }), [chartCandleBodyEnabled, chartCandleBorderEnabled, chartCandleWickEnabled]);

  const createThrottledHandler = useCallback((colorKey: keyof CandleColors) => {
      return throttle((colorString: string) => {
          const settingKey = candleColorKeysMap[colorKey] as keyof AppSettings;
          handleUpdateSettings({ [settingKey]: colorString });
      }, 100);
  }, [handleUpdateSettings]);

  const throttledHandlers = useMemo(() => {
      const handlers: Partial<Record<keyof CandleColors, (colorString: string) => void>> = {};
      candleGroups.forEach(group => {
          handlers[group.upColorKey] = createThrottledHandler(group.upColorKey);
          handlers[group.downColorKey] = createThrottledHandler(group.downColorKey);
      });
      return handlers;
  }, [candleGroups, createThrottledHandler]);

  const debouncedRightOffsetChange = useMemo(
    () => debounce((value: number) => handleUpdateSettings({ chartRightOffset: value }), 150),
    [handleUpdateSettings]
  );

  const debouncedBarSpacingChange = useMemo(
    () => debounce((value: number) => handleUpdateSettings({ chartBarSpacing: value }), 150),
    [handleUpdateSettings]
  );

  return (
    <div className="space-y-6">
      {/* Оборачиваем все в .setting-group для консистентности */}
      <div className="setting-group">
        <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-4 pb-2 border-b border-border/10">Candle Colors</h4>
        <div className="flex flex-col gap-1">
            {candleGroups.map((group) => {
                const handleUpColorChange = throttledHandlers[group.upColorKey];
                const handleDownColorChange = throttledHandlers[group.downColorKey];
                const isEnabled = currentEnabledStates[group.enabledKey];

                return (
                    <div key={group.id} className="flex items-center justify-between py-1">
                        <div className="flex items-center gap-2 cursor-pointer group" onClick={() => handleUpdateSettings({ [group.enabledKey]: !isEnabled })}>
                           <div 
                              className={cn(
                                  "relative w-[34px] h-[18px] bg-muted rounded-full transition-colors duration-150 ease-in-out flex-shrink-0 cursor-pointer",
                                  "group-hover:bg-border",
                                  "before:content-[\'] before:absolute before:left-0.5 before:top-0.5 before:w-3.5 before:h-3.5 before:bg-gray-400 before:rounded-full before:transition-all before:duration-150 before:ease-in-out before:shadow", 
                                  isEnabled && "bg-settings-primary/50 before:translate-x-[16px] before:bg-settings-primary"
                              )}
                              aria-label={`${group.label} toggle`}
                           ></div>
                           <span id={`candle-row-label-${group.id}`} className="text-xs text-muted-foreground font-medium select-none transition-colors duration-150 group-hover:text-foreground">{group.label}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <SimpleColorPicker 
                                value={currentColors[group.upColorKey]}
                                onChange={(color) => handleUpColorChange?.(color)}
                                aria-label={`${group.label} Up color picker`}
                            />
                            <SimpleColorPicker
                                value={currentColors[group.downColorKey]} 
                                onChange={(color) => handleDownColorChange?.(color)}
                                aria-label={`${group.label} Down color picker`}
                            />
                        </div>
                    </div>
                );
            })}
        </div>
      </div>
      
      {/* Новый блок настроек Spacing */}
      <div className="setting-group">
          <h4 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-4 pb-2 border-b border-border/10">Spacing</h4>
          <Slider 
              label="Right Offset (bars)" 
              min={0} 
              max={20} 
              step={1} 
              value={chartRightOffset}
              onChange={debouncedRightOffsetChange}
          />
          <Slider 
              label="Bar Spacing" 
              min={1} 
              max={20} 
              step={1} 
              value={chartBarSpacing}
              onChange={debouncedBarSpacingChange}
          />
      </div>
      
    </div>
  );
});

export default CandlesTab;
