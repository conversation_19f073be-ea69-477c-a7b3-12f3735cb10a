# Metacharts

Metacharts – это веб-приложение для анализа финансовых рынков, предоставляющее интерактивные графики свечей, данные по тикерам, и технические индикаторы. Цель проекта – создать высокопроизводительную и отзывчивую платформу для трейдеров, сравнимую с TradingView.

## Требования

- Node.js (версия 18+)
- PostgreSQL (версия 14+)
- Redis (версия 6+)

## Установка

1. Клонируйте репозиторий:
```
git clone https://github.com/yourusername/metacharts.git
cd metacharts
```

2. Установите зависимости:
```
npm install
```

3. Создайте файл `.env` на основе `.env.example`:
```
cp .env.example .env
```

4. Отредактируйте `.env`, установив правильные значения для подключения к базе данных и Redis.

5. Создайте базу данных в PostgreSQL:
```
createdb metacharts
```

6. Примените миграции к базе данных:
```
npm run db:migrate
```

## Запуск

### Режим разработки

Для запуска в режиме разработки:

```
# Запуск бэкенда
npm run dev:server

# В отдельном терминале запустите фронтенд
npm run dev
```

### Продакшн

Для сборки и запуска проекта в продакшене:

```
# Сборка проекта
npm run build

# Запуск сервера
npm start
```

## Архитектура

Metacharts использует монорепозиторий с разделением на:

- **Фронтенд**: Next.js (App Router, React), Tailwind CSS, Zustand для управления состоянием
- **Бэкенд**: Node.js с Fastify, WebSocket для real-time данных, PostgreSQL и Redis

Основные функции:

1. **Исторические данные**: Загрузка и отображение исторических свечей для различных символов и таймфреймов
2. **Real-time обновления**: WebSocket для получения обновлений рынка в режиме реального времени
3. **Индикаторы**: Технические индикаторы с настраиваемыми параметрами
4. **Рыночный скринер**: Фильтрация и сортировка активов по различным критериям

## API

Основные API эндпоинты:

- **GET /api/v1/klines**: Получение исторических свечей
- **GET /api/v1/tickers**: Получение данных тикеров с фильтрацией
- **WebSocket /ws/market**: Real-time обновления рыночных данных

## Документация

Детальная документация доступна в папке `docs/`.

## Последние изменения

### Внедрены основные компоненты для запуска сервера

1. Реализован метод `saveKlineToDB` для сохранения данных свечей в базу данных
2. Создан сервис `tickerService.ts` для работы с тикерами (сохранение, получение с фильтрацией)
3. Созданы API маршруты для получения данных тикеров
4. Добавлена интеграция `exchangeDataCollectorService` с обработкой тикеров
5. Создан модуль `dataCollector.ts` для инициализации и управления сбором данных с биржи
6. Обновлен главный файл для регистрации новых маршрутов и инициализации всех компонентов

### Известные проблемы

В проекте имеются некоторые известные проблемы, описанные в файле [KNOWN_ISSUES.md](KNOWN_ISSUES.md). Ознакомьтесь с ним перед началом работы.

## Инструкция по запуску проекта

1. Убедитесь, что PostgreSQL и Redis запущены и доступны (или используйте наш скрипт для запуска через Docker)
2. Скопируйте `.env.example` в `.env` и настройте переменные окружения:
```bash
cp .env.example .env
```

3. Установите зависимости:
```bash
npm install
```

4. Запустите миграции базы данных:
```bash
npm run db:migrate
```

5. Запустите проект одной командой через PowerShell скрипт (рекомендуется):
```bash
./dev.mct.ps1
```

Скрипт автоматически:
- Запустит необходимую инфраструктуру через Docker (PostgreSQL, Redis)
- Запустит серверную часть и фронтенд приложения
- Сервер будет доступен по адресу: [http://localhost:3005](http://localhost:3005)
- Фронтенд будет доступен по адресу: [http://localhost:3001](http://localhost:3001)

Альтернативно, можно запустить проект вручную:
```bash
# Запуск всего проекта (сервер + фронтенд)
npm run dev

# Запуск только сервера
npm run dev:server-only

# Запуск только фронтенда
npm run dev:client-win
``` 