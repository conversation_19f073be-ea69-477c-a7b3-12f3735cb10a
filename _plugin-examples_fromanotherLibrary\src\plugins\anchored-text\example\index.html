<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - Anchored Text Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Anchored Text</h1>
			<p>
				Draws text anchored to the center of the chart pane. The text will change after 2 seconds.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>
