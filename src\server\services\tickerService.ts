import { db } from '@/db';
import { Ticker, MarketType } from '@/shared/index';
import { tickers24h } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';
import pino from 'pino';
import { publishMessage, getFromCache, setInCache } from '../lib/redis.js';

// Configure logger
const logger = pino({ name: 'ticker-service', level: 'info' });

// Type for ticker query parameters
export interface TickerQueryParams {
  marketTypes?: MarketType[];
  quoteAssets?: string[];
  minVolume?: number;
  minTrades?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  searchQuery?: string;
}

// Cache key builder
const getTickersCacheKey = (params: TickerQueryParams): string => {
  return `tickers:${JSON.stringify(params)}`;
};

/**
 * Service for handling ticker data persistence
 */
export class TickerService {
  /**
   * Fetches all tickers for a specific market type from the database
   * @param marketType - The market type ('spot' or 'futures')
   * @returns A promise that resolves to an array of tickers
   */
  async getTickersByMarket(marketType: MarketType): Promise<Ticker[]> {
    try {
      const results = await db
        .select()
        .from(tickers24h)
        .where(eq(tickers24h.marketType, marketType));
      
      return results.map(this.mapDbRowToTicker);
    } catch (error) {
      logger.error(`Error fetching ${marketType} tickers from DB:`, error);
      return [];
    }
  }

  /**
   * Upserts multiple tickers into the database
   * @param tickers - An array of ticker data to upsert
   * @returns A promise that resolves when the operation is complete
   */
  async upsertTickers(tickers: Ticker[]): Promise<void> {
    if (tickers.length === 0) {
      return;
    }

    const values = tickers.map(ticker => ({
      symbol: ticker.symbol,
      marketType: ticker.marketType,
      lastPrice: ticker.lastPrice.toString(),
      priceChange: ticker.priceChange.toString(),
      priceChangePercent: ticker.priceChangePercent.toString(),
      highPrice: ticker.highPrice.toString(),
      lowPrice: ticker.lowPrice.toString(),
      volume: ticker.volume.toString(),
      quoteVolume: ticker.quoteVolume.toString(),
      openTime: new Date(ticker.openTime),
      closeTime: new Date(ticker.closeTime),
      count: ticker.count,
      lastUpdated: new Date(ticker.lastUpdated),
      // Optional fields
      weightedAvgPrice: ticker.weightedAvgPrice?.toString(),
      prevClosePrice: ticker.prevClosePrice?.toString(),
      lastQty: ticker.lastQty?.toString(),
      bidPrice: ticker.bidPrice?.toString(),
      bidQty: ticker.bidQty?.toString(),
      askPrice: ticker.askPrice?.toString(),
      askQty: ticker.askQty?.toString(),
      openPrice: ticker.openPrice?.toString(),
      firstId: ticker.firstId,
      lastId: ticker.lastId,
    }));

    try {
      await db.insert(tickers24h)
        .values(values)
        .onConflictDoUpdate({
          target: [tickers24h.symbol, tickers24h.marketType],
          set: {
            lastPrice: sql.raw('excluded.last_price'),
            priceChange: sql.raw('excluded.price_change'),
            priceChangePercent: sql.raw('excluded.price_change_percent'),
            highPrice: sql.raw('excluded.high_price'),
            lowPrice: sql.raw('excluded.low_price'),
            volume: sql.raw('excluded.volume'),
            quoteVolume: sql.raw('excluded.quote_volume'),
            openTime: sql.raw('excluded.open_time'),
            closeTime: sql.raw('excluded.close_time'),
            count: sql.raw('excluded.count'),
            lastUpdated: sql.raw('excluded.last_updated'),
          }
        });
    } catch (error) {
      logger.error('Error upserting tickers:', error);
    }
  }

  /**
   * Maps a database row to a Ticker object
   * @param dbRow - The database row from the tickers24h table
   * @returns A Ticker object
   */
  private mapDbRowToTicker(dbRow: any): Ticker {
    return {
      symbol: dbRow.symbol,
      marketType: dbRow.marketType as MarketType,
      lastPrice: parseFloat(dbRow.lastPrice || '0'),
      priceChange: parseFloat(dbRow.priceChange || '0'),
      priceChangePercent: parseFloat(dbRow.priceChangePercent || '0'),
      highPrice: parseFloat(dbRow.highPrice || '0'),
      lowPrice: parseFloat(dbRow.lowPrice || '0'),
      volume: parseFloat(dbRow.volume || '0'),
      quoteVolume: parseFloat(dbRow.quoteVolume || '0'),
      openTime: dbRow.openTime.getTime(),
      closeTime: dbRow.closeTime.getTime(),
      count: dbRow.count || 0,
      lastUpdated: dbRow.lastUpdated.getTime(),
      weightedAvgPrice: dbRow.weightedAvgPrice ? parseFloat(dbRow.weightedAvgPrice) : 0,
      prevClosePrice: dbRow.prevClosePrice ? parseFloat(dbRow.prevClosePrice) : 0,
      lastQty: dbRow.lastQty ? parseFloat(dbRow.lastQty) : 0,
      bidPrice: dbRow.bidPrice ? parseFloat(dbRow.bidPrice) : 0,
      bidQty: dbRow.bidQty ? parseFloat(dbRow.bidQty) : 0,
      askPrice: dbRow.askPrice ? parseFloat(dbRow.askPrice) : 0,
      askQty: dbRow.askQty ? parseFloat(dbRow.askQty) : 0,
      openPrice: dbRow.openPrice ? parseFloat(dbRow.openPrice) : 0,
      firstId: dbRow.firstId || 0,
      lastId: dbRow.lastId || 0,
    };
  }
}

// Export a singleton instance of the service
export const tickerService = new TickerService();

/**
 * Save a ticker to the database
 * @param ticker The ticker data to save
 */
export async function saveTicker(ticker: Ticker): Promise<void> {
  try {
    // Validate required fields
    if (!ticker.symbol || !ticker.marketType) {
      throw new Error(`Invalid ticker data: missing symbol or marketType for ticker ${JSON.stringify(ticker)}`);
    }

    await db.insert(tickers24h)
      .values({
        symbol: ticker.symbol,
        marketType: ticker.marketType,
        lastPrice: ticker.lastPrice?.toString() || '0',
        priceChange: ticker.priceChange?.toString() || '0',
        priceChangePercent: ticker.priceChangePercent?.toString() || '0',
        highPrice: ticker.highPrice?.toString() || '0',
        lowPrice: ticker.lowPrice?.toString() || '0',
        volume: ticker.volume?.toString() || '0',
        quoteVolume: ticker.quoteVolume?.toString() || '0',
        openTime: ticker.openTime ? new Date(ticker.openTime) : null,
        closeTime: ticker.closeTime ? new Date(ticker.closeTime) : null,
        count: ticker.count || 0,
        lastUpdated: new Date(ticker.lastUpdated || Date.now()),
      })
      .onConflictDoUpdate({
        target: [tickers24h.symbol, tickers24h.marketType],
        set: {
          lastPrice: ticker.lastPrice?.toString() || '0',
          priceChange: ticker.priceChange?.toString() || '0',
          priceChangePercent: ticker.priceChangePercent?.toString() || '0',
          highPrice: ticker.highPrice?.toString() || '0',
          lowPrice: ticker.lowPrice?.toString() || '0',
          volume: ticker.volume?.toString() || '0',
          quoteVolume: ticker.quoteVolume?.toString() || '0',
          openTime: ticker.openTime ? new Date(ticker.openTime) : undefined,
          closeTime: ticker.closeTime ? new Date(ticker.closeTime) : undefined,
          count: ticker.count || 0,
          lastUpdated: new Date(ticker.lastUpdated || Date.now()),
        }
      });

    // Publish update to Redis for real-time subscribers
    try {
      const pubSubChannel = `pubsub:ticker:${ticker.symbol}:${ticker.marketType}`;
      await publishMessage(pubSubChannel, ticker);
    } catch (pubError) {
      logger.warn({ error: pubError, symbol: ticker.symbol, marketType: ticker.marketType }, 'Failed to publish ticker update');
    }

    logger.debug({ symbol: ticker.symbol, marketType: ticker.marketType }, 'Ticker saved to database');
  } catch (error) {
    // Enhanced error logging with full details
    logger.error({
      error: error,
      errorMessage: error instanceof Error ? error.message : 'Unknown error type',
      errorStack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.constructor.name : typeof error,
      symbol: ticker.symbol,
      marketType: ticker.marketType,
      tickerData: ticker,
    }, 'Failed to save ticker to database');

    // Re-throw the error so calling code can handle it appropriately
    throw error;
  }
}

/**
 * Save multiple tickers to the database in a single batch
 * @param tickers Array of ticker data to save
 */
export async function saveTickers(tickers: Ticker[]): Promise<void> {
  const startTime = Date.now();

  try {
    if (!tickers.length) {
      logger.debug('No tickers to save, skipping operation');
      return;
    }

    logger.info(`Starting to save ${tickers.length} tickers to database`);

    // Process in batches of 100 to avoid overwhelming the database and reduce transaction time
    const batchSize = 100;
    let savedCount = 0;

    for (let i = 0; i < tickers.length; i += batchSize) {
      const batch = tickers.slice(i, i + batchSize);

      try {
        // Convert each ticker to the format expected by the database
        const values = batch.map(ticker => {
          // Validate required fields before processing
          if (!ticker.symbol || !ticker.marketType) {
            throw new Error(`Invalid ticker data: missing symbol or marketType for ticker ${JSON.stringify(ticker)}`);
          }

          return {
            symbol: ticker.symbol,
            marketType: ticker.marketType,
            lastPrice: ticker.lastPrice?.toString() || '0',
            priceChange: ticker.priceChange?.toString() || '0',
            priceChangePercent: ticker.priceChangePercent?.toString() || '0',
            highPrice: ticker.highPrice?.toString() || '0',
            lowPrice: ticker.lowPrice?.toString() || '0',
            volume: ticker.volume?.toString() || '0',
            quoteVolume: ticker.quoteVolume?.toString() || '0',
            openTime: ticker.openTime ? new Date(ticker.openTime) : null,
            closeTime: ticker.closeTime ? new Date(ticker.closeTime) : null,
            count: ticker.count || 0,
            lastUpdated: new Date(ticker.lastUpdated || Date.now()),
          };
        });

        // Use optimized batch insert with single upsert operation
        await db.insert(tickers24h)
          .values(values)
          .onConflictDoUpdate({
            target: [tickers24h.symbol, tickers24h.marketType],
            set: {
              lastPrice: sql.raw('excluded.last_price'),
              priceChange: sql.raw('excluded.price_change'),
              priceChangePercent: sql.raw('excluded.price_change_percent'),
              highPrice: sql.raw('excluded.high_price'),
              lowPrice: sql.raw('excluded.low_price'),
              volume: sql.raw('excluded.volume'),
              quoteVolume: sql.raw('excluded.quote_volume'),
              openTime: sql.raw('excluded.open_time'),
              closeTime: sql.raw('excluded.close_time'),
              count: sql.raw('excluded.count'),
              lastUpdated: sql.raw('excluded.last_updated'),
            }
          });

        savedCount += batch.length;

        // Log progress for large batches
        if (tickers.length > 100) {
          logger.debug(`Saved batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(tickers.length / batchSize)}: ${savedCount}/${tickers.length} tickers`);
        }

      } catch (batchError) {
        // Log detailed error for this specific batch
        logger.error({
          error: batchError,
          message: batchError instanceof Error ? batchError.message : 'Unknown batch error',
          stack: batchError instanceof Error ? batchError.stack : undefined,
          batchIndex: Math.floor(i / batchSize),
          batchSize: batch.length,
          batchSymbols: batch.slice(0, 5).map(t => t.symbol), // Log first 5 symbols for debugging
        }, `Failed to save batch ${Math.floor(i / batchSize) + 1} of tickers`);

        // Continue with next batch instead of failing completely
        continue;
      }
    }

    // Publish batch update notification only if we saved some tickers
    if (savedCount > 0) {
      try {
        await publishMessage('pubsub:tickers:batch_update', {
          count: savedCount,
          marketTypes: [...new Set(tickers.map(t => t.marketType))],
          timestamp: Date.now(),
        });
      } catch (pubError) {
        logger.warn({ error: pubError }, 'Failed to publish batch update notification');
      }
    }

    const duration = Date.now() - startTime;
    logger.info(`Successfully saved ${savedCount}/${tickers.length} tickers to database in ${duration}ms`);

  } catch (error) {
    const duration = Date.now() - startTime;

    // Enhanced error logging with full details
    logger.error({
      error: error,
      errorMessage: error instanceof Error ? error.message : 'Unknown error type',
      errorStack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.constructor.name : typeof error,
      tickersCount: tickers.length,
      duration: duration,
      sampleTickers: tickers.slice(0, 3).map(t => ({ symbol: t.symbol, marketType: t.marketType })),
    }, `Failed to save ${tickers.length} tickers to database`);

    // Re-throw the error so calling code can handle it appropriately
    throw error;
  }
}

/**
 * Extract quote asset from symbol (e.g., BTCUSDT -> USDT)
 * Used for filtering by quote asset
 */
const extractQuoteAsset = (symbol: string): string => {
  // Common quote assets to check for
  const quoteAssets = ['USDT', 'BTC', 'ETH', 'BNB', 'BUSD', 'USD', 'TUSD', 'USDC', 'DAI', 'EUR', 'GBP'];
  
  for (const asset of quoteAssets) {
    if (symbol.endsWith(asset)) {
      return asset;
    }
  }
  
  // Default fallback: try to extract the last 3-4 characters
  const matches = symbol.match(/^(.+?)([A-Z]{3,}|[1-9A-Z]{4,})$/);
  return matches ? matches[2] : '';
};

/**
 * Get tickers with filtering, sorting and pagination
 * @param params Query parameters for filtering, sorting and pagination
 * @returns List of tickers matching the filter criteria
 * 
 * @todo Optimize for large datasets by moving filtering and sorting to database queries
 */
export async function getTickers(params: TickerQueryParams): Promise<Ticker[]> {
  try {
    const startTime = Date.now();
    const cacheKey = getTickersCacheKey(params);
    
    // Try to get from cache first
    const cachedData = await getFromCache<Ticker[]>(cacheKey, 30); // 30 seconds TTL
    if (cachedData) {
      logger.debug({ cacheKey, responseTime: Date.now() - startTime }, 'Returning tickers from cache');
      return cachedData;
    }
    
    // For now, get all tickers and filter in-memory to avoid Drizzle typing issues
    const allTickers = await db.select().from(tickers24h);
    
    // Filter results in memory
    let filteredTickers = allTickers;
    
    // Apply market type filter
    if (params.marketTypes && params.marketTypes.length > 0) {
      filteredTickers = filteredTickers.filter(ticker => 
        params.marketTypes!.includes(ticker.marketType as MarketType)
      );
    }
    
    // Apply quote asset filter
    if (params.quoteAssets && params.quoteAssets.length > 0) {
      filteredTickers = filteredTickers.filter(ticker => {
        return params.quoteAssets!.some(asset => ticker.symbol.endsWith(asset));
      });
    }
    
    // Apply volume filter
    if (params.minVolume !== undefined && params.minVolume > 0) {
      filteredTickers = filteredTickers.filter(ticker => {
        const volume = parseFloat(ticker.quoteVolume?.toString() || '0');
        return volume >= params.minVolume!;
      });
    }
    
    // Apply trade count filter
    if (params.minTrades !== undefined && params.minTrades > 0) {
      filteredTickers = filteredTickers.filter(ticker => 
        (ticker.count || 0) >= params.minTrades!
      );
    }
    
    // Apply text search
    if (params.searchQuery) {
      const query = params.searchQuery.toLowerCase();
      filteredTickers = filteredTickers.filter(ticker => 
        ticker.symbol.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    if (params.sortBy) {
      // Используем безопасный доступ к свойствам
      filteredTickers.sort((a, b) => {
        let aValue = 0;
        let bValue = 0;
        
        // Явно проверяем каждое возможное свойство сортировки
        switch (params.sortBy) {
          case 'lastPrice':
            aValue = parseFloat(a.lastPrice?.toString() || '0');
            bValue = parseFloat(b.lastPrice?.toString() || '0');
            break;
          case 'priceChange':
            aValue = parseFloat(a.priceChange?.toString() || '0');
            bValue = parseFloat(b.priceChange?.toString() || '0');
            break;
          case 'priceChangePercent':
            aValue = parseFloat(a.priceChangePercent?.toString() || '0');
            bValue = parseFloat(b.priceChangePercent?.toString() || '0');
            break;
          case 'highPrice':
            aValue = parseFloat(a.highPrice?.toString() || '0');
            bValue = parseFloat(b.highPrice?.toString() || '0');
            break;
          case 'lowPrice':
            aValue = parseFloat(a.lowPrice?.toString() || '0');
            bValue = parseFloat(b.lowPrice?.toString() || '0');
            break;
          case 'volume':
            aValue = parseFloat(a.volume?.toString() || '0');
            bValue = parseFloat(b.volume?.toString() || '0');
            break;
          case 'quoteVolume':
            aValue = parseFloat(a.quoteVolume?.toString() || '0');
            bValue = parseFloat(b.quoteVolume?.toString() || '0');
            break;
          case 'count':
            aValue = a.count || 0;
            bValue = b.count || 0;
            break;
          default:
            // По умолчанию сортировка по символу
            return params.sortOrder === 'asc' 
              ? a.symbol.localeCompare(b.symbol)
              : b.symbol.localeCompare(a.symbol);
        }
        
        return params.sortOrder === 'asc' 
          ? aValue - bValue 
          : bValue - aValue;
      });
    } else {
      // Default sort by quoteVolume desc
      filteredTickers.sort((a, b) => {
        const aVolume = parseFloat(a.quoteVolume?.toString() || '0');
        const bVolume = parseFloat(b.quoteVolume?.toString() || '0');
        
        return bVolume - aVolume; // Descending
      });
    }
    
    // Apply pagination
    if (params.offset !== undefined) {
      filteredTickers = filteredTickers.slice(params.offset);
    }
    
    if (params.limit !== undefined) {
      filteredTickers = filteredTickers.slice(0, params.limit);
    }
    
    // Convert database model to Ticker
    const tickers: Ticker[] = filteredTickers.map(row => ({
      symbol: row.symbol,
      marketType: row.marketType as MarketType,
      lastPrice: parseFloat(row.lastPrice?.toString() || '0'),
      priceChange: parseFloat(row.priceChange?.toString() || '0'),
      priceChangePercent: parseFloat(row.priceChangePercent?.toString() || '0'),
      openPrice: parseFloat(row.lastPrice?.toString() || '0'), // Usually same as last if not stored
      highPrice: parseFloat(row.highPrice?.toString() || '0'),
      lowPrice: parseFloat(row.lowPrice?.toString() || '0'),
      volume: parseFloat(row.volume?.toString() || '0'),
      quoteVolume: parseFloat(row.quoteVolume?.toString() || '0'),
      openTime: row.openTime?.getTime() || 0,
      closeTime: row.closeTime?.getTime() || 0,
      count: row.count || 0,
      lastUpdated: row.lastUpdated.getTime(),
      
      // Additional required Ticker fields
      weightedAvgPrice: parseFloat(row.lastPrice?.toString() || '0'),
      prevClosePrice: parseFloat(row.lastPrice?.toString() || '0'),
      lastQty: 0,
      bidPrice: 0,
      bidQty: 0,
      askPrice: 0,
      askQty: 0,
      firstId: 0,
      lastId: 0,
    }));
    
    // Store in cache
    await setInCache(cacheKey, tickers, 30); // 30 seconds TTL
    
    const responseTime = Date.now() - startTime;
    logger.info({ 
      count: tickers.length, 
      totalCount: allTickers.length, 
      responseTime,
      filters: {
        marketTypes: params.marketTypes,
        quoteAssets: params.quoteAssets,
        minVolume: params.minVolume,
        minTrades: params.minTrades,
        searchQuery: params.searchQuery
      }
    }, 'Retrieved tickers from database');
    
    return tickers;
  } catch (error) {
    logger.error({ error, params }, 'Failed to get tickers from database');
    throw error;
  }
}

/**
 * Process ticker update event and save to database
 */
export async function processTickerUpdate(ticker: Ticker): Promise<void> {
  await saveTicker(ticker);

  // Publish ticker update to WebSocket clients
  try {
    const channel = `tickers_${ticker.marketType}`;
    await publishMessage(channel, {
      type: 'ticker_data',
      data: [ticker] // Send as array to match expected format
    });
  } catch (error) {
    logger.warn({ error, symbol: ticker.symbol, marketType: ticker.marketType }, 'Failed to publish ticker update to WebSocket');
  }
}

/**
 * Process batch ticker update event and save to database
 */
export async function processTickersBatchUpdate(tickers: Ticker[]): Promise<void> {
  await saveTickers(tickers);

  // Group tickers by market type and publish to WebSocket clients
  const spotTickers = tickers.filter(t => t.marketType === 'spot');
  const futuresTickers = tickers.filter(t => t.marketType === 'futures');

  try {
    if (spotTickers.length > 0) {
      await publishMessage('tickers_spot', {
        type: 'ticker_data',
        data: spotTickers
      });
    }

    if (futuresTickers.length > 0) {
      await publishMessage('tickers_futures', {
        type: 'ticker_data',
        data: futuresTickers
      });
    }
  } catch (error) {
    logger.warn({ error, tickersCount: tickers.length }, 'Failed to publish batch ticker updates to WebSocket');
  }
}