import { FastifyInstance } from 'fastify';
import { getKlines, KlineParams } from '@/server/services/klineService.js';
import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { MarketTypeSchema } from '@/shared/schemas/market.schema';

// Zod schema for kline query parameters validation
const GetKlinesSchema = z.object({
  symbol: z.string({ description: 'The trading symbol (e.g., BTCUSDT)' }),
  interval: z.string({ description: 'The interval of klines (e.g., 1m, 1h, 1d)' }),
  marketType: MarketTypeSchema,
  limit: z.coerce.number().int().positive().optional().default(1000).describe('Number of klines to retrieve'),
  endTime: z.coerce.number().int().positive().optional().describe('End time for historical data'),
});

type GetKlinesQuery = z.infer<typeof GetKlinesSchema>;

// Convert Zod schema to JSON schema for Fastify
const GetKlinesJsonSchema = zodToJsonSchema(GetKlinesSchema, 'GetKlinesSchema');

/**
 * @description Registers the kline routes.
 * @param {FastifyInstance} fastify - The Fastify instance.
 */
export async function klineRoutes(fastify: FastifyInstance) {
  fastify.route<{ Querystring: GetKlinesQuery }>({
    method: 'GET',
    url: '/klines',
    schema: {
      querystring: GetKlinesJsonSchema,
      response: {
        200: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              symbol: { type: 'string' },
              interval: { type: 'string' },
              marketType: { type: 'string' },
              openTime: { type: 'number' },
              open: { type: 'string' },
              high: { type: 'string' },
              low: { type: 'string' },
              close: { type: 'string' },
              volume: { type: 'string' },
            },
            required: ['openTime', 'open', 'high', 'low', 'close', 'volume'],
          },
        },
      },
    },
    handler: async (request, reply) => {
      try {
        const klineParams: KlineParams = request.query;
        const klines = await getKlines(klineParams);
        
        // Serialize numbers to strings for the API response, matching the schema.
        const serializedKlines = klines.map(kline => ({
          ...kline,
          open: kline.open?.toString(),
          high: kline.high?.toString(),
          low: kline.low?.toString(),
          close: kline.close?.toString(),
          volume: kline.volume?.toString(),
        }));

        return reply.send(serializedKlines);
      } catch (error) {
        request.log.error(error, 'Failed to get klines');
        reply.status(500).send({ error: 'Internal Server Error' });
      }
    },
  });
} 