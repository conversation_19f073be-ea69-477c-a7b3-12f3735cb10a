import { z } from 'zod';
import { ChartType } from '@/shared/types';
import { MarketTypeSchema } from './market.schema';

/**
 * Перечисление типов компоновки
 */
export const LayoutTypeSchema = z.enum([
  '1x1', '1x2', '1x3', '1x4', '1x5', '1x6', '1x7',
  '2x1', '2x2', '2x3', '2x4', '2x5', '2x6', '2x7',
  '3x1', '3x2', '3x3', '3x4', '3x5', '3x6', '3x7',
  '4x1', '4x2', '4x3', '4x4', '4x5', '4x6', '4x7',
  '5x1', '5x2', '5x3', '5x4', '5x5', '5x6', '5x7',
  '6x1', '6x2', '6x3', '6x4', '6x5', '6x6', '6x7',
  '7x1', '7x2', '7x3', '7x4', '7x5', '7x6', '7x7'
]);

export type LayoutTypeZod = z.infer<typeof LayoutTypeSchema>;

/**
 * Схема для видимых колонок
 */
export const VisibleColumnsSchema = z.object({
  symbol: z.boolean().default(true),
  price: z.boolean().default(true),
  change: z.boolean().default(true),
  volume: z.boolean().default(true),
  trade: z.boolean().default(true),
  spread: z.boolean().optional()
}).catchall(z.boolean().optional());

export type VisibleColumnsZod = z.infer<typeof VisibleColumnsSchema>;

/**
 * Схема для конфигурации сортировки
 */
export const SortConfigSchema = z.object({
  key: z.string(),
  direction: z.enum(['asc', 'desc'])
});

export type SortConfigZod = z.infer<typeof SortConfigSchema>;

/**
 * Схема для ширины колонок
 */
export const ColumnWidthsSchema = z.object({
  symbol: z.number().min(20),
  price: z.number().min(20),
  change: z.number().min(20),
  volume: z.number().min(20),
  trade: z.number().min(20)
}).catchall(z.number().min(20).optional());

export type ColumnWidthsZod = z.infer<typeof ColumnWidthsSchema>;

/**
 * Схема для фильтров колонок
 */
export const ColumnFiltersSchema = z.record(z.string(), z.union([
  z.string().nullable(),
  z.number().nullable()
]));

export type ColumnFiltersZod = z.infer<typeof ColumnFiltersSchema>;

/**
 * Схема для конфигурации индикаторной колонки
 */
export const IndicatorColumnConfigSchema = z.object({
  instanceId: z.string(),
  indicatorId: z.string(),
  name: z.string(),
  timeframe: z.string(),
  parameters: z.record(z.string(), z.union([z.string(), z.number(), z.boolean()])),
  outputId: z.string(),
  columnLabel: z.string().optional()
});

export type IndicatorColumnConfigZod = z.infer<typeof IndicatorColumnConfigSchema>;

/**
 * Schema for Screener Sort By
 */
export const ScreenerSortBySchema = z.enum(['volume', 'trades', 'price_change', 'volume_change']);
export type ScreenerSortByZod = z.infer<typeof ScreenerSortBySchema>;

/**
 * Schema for View Mode
 */
export const ViewModeSchema = z.enum(['focus', 'screener']);
export type ViewModeZod = z.infer<typeof ViewModeSchema>;

/**
 * Schema for Screener Settings
 */
export const ScreenerSettingsSchema = z.object({
  sortBy: ScreenerSortBySchema.default('volume'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  timeframe: z.string().default('1h'),
  autoUpdate: z.boolean().default(false),
  updateInterval: z.number().min(0).default(5), // Changed min to 0 to allow "Off" state represented by 0
  markets: z.array(MarketTypeSchema).default(['futures']), // MarketTypeSchema from market.schema.ts
  pairs: z.array(z.string()).default(['USDT']),
  minVolume: z.number().optional(),
  minTrades: z.number().optional(),
  currentPage: z.number().int().positive().default(1), // Added currentPage
});
export type ScreenerSettingsZod = z.infer<typeof ScreenerSettingsSchema>;

/**
 * Schema for Mode Control Settings
 */
export const ModeControlSettingsSchema = z.object({
  currentMode: ViewModeSchema.default('focus'), // Use ViewModeSchema
  screenerSettings: ScreenerSettingsSchema,
  layoutTypes: z.object({
    focus: LayoutTypeSchema.default('2x2'), 
    screener: LayoutTypeSchema.default('3x2'),
  }),
});
export type ModeControlSettingsZod = z.infer<typeof ModeControlSettingsSchema>;

/**
 * Схема настроек приложения
 */
export const AppSettingsSchema = z.object({
  // Основные настройки
  isDarkMode: z.boolean().default(true),
  isFiltersShown: z.boolean().default(false),
  isGridShown: z.boolean().default(true),
  isDebugMode: z.boolean().default(false),
  
  // Настройки графика
  chartType: z.nativeEnum(ChartType).default(ChartType.Candles),
  volumeEnabled: z.boolean().default(true),
  autoSaveEnabled: z.boolean().default(true),
  syncCrosshair: z.boolean().default(true),
  layoutType: LayoutTypeSchema.default('1x1'),
  chartVolumeHeightRatio: z.number().min(0.1).max(0.5).default(0.2),
  chartRightOffset: z.number().min(0).default(10),
  chartBarSpacing: z.number().min(0).default(6),
  chartFontSize: z.number().min(8).max(18).default(11),
  chartFontFamily: z.string().default('Roboto, sans-serif'),
  uiFontFamily: z.string().default('Roboto, sans-serif'),
  uiFontSize: z.number().min(8).max(18).default(14),
  tableCompactness: z.number().min(0).max(3).default(1),
  chartGridVertLinesVisible: z.boolean().default(true),
  chartGridHorzLinesVisible: z.boolean().default(true),
  chartGridVertLinesColor: z.string().optional(),
  chartGridHorzLinesColor: z.string().optional(),
  chartCandleBodyEnabled: z.boolean().default(true),
  chartCandleBorderEnabled: z.boolean().default(true),
  chartCandleWickEnabled: z.boolean().default(true),
  chartCandleBodyUpColor: z.string().optional(),
  chartCandleBodyDownColor: z.string().optional(),
  chartCandleBorderUpColor: z.string().optional(),
  chartCandleBorderDownColor: z.string().optional(),
  chartCandleWickUpColor: z.string().optional(),
  chartCandleWickDownColor: z.string().optional(),
  chartVolumeUpColor: z.string().optional(),
  chartVolumeDownColor: z.string().optional(),
  chartPriceLineVisible: z.boolean().default(true),
  chartLastValueVisible: z.boolean().default(true),
  chartPriceLineWidth: z.number().min(1).max(4).default(1),
  chartPriceLineStyle: z.number().min(0).max(4).default(2),
  chartPriceLineColor: z.string().default('#2196F3'),
  isSyncEnabled: z.boolean().default(false),
  layoutMode: z.string().default('grid'),
  activeTimeframe: z.string().default('1h'),
  
  // Настройки данных
  showVolumeInUSD: z.boolean().default(true),
  visibleColumns: VisibleColumnsSchema.default({ symbol: true, price: true, change: true, volume: true, trade: true }),
  sortConfigs: z.array(SortConfigSchema).default([{ key: 'volume', direction: 'desc' }]),
  columnWidths: ColumnWidthsSchema.default({ symbol: 120, price: 90, change: 80, volume: 100, trade: 80, spread: 70 }),
  columnOrder: z.array(z.string()).default(['symbol', 'price', 'change', 'volume', 'trade', 'spread']),
  minVolume: z.number().default(0),
  minTrades: z.number().default(0),
  selectedPairs: z.array(z.string()).default(['USDT']),
  selectedMarketTypes: z.array(MarketTypeSchema).default(['futures']),
  columnFilters: ColumnFiltersSchema.default({}),
  globalSearchTerm: z.string().default(''),
  isTableCollapsed: z.boolean().default(false),
  selectedTickerSymbol: z.string().nullable().default(null),
  aggregateVolumeAndTrades: z.boolean().default(false),
  availablePairs: z.array(z.string()).default(['USDT', 'USDC', 'FDUSD', 'DAI', 'USDE', 'OTHR']),
  
  // Настройки внешнего вида
  chartBackgroundColor: z.string().optional(),
  chartLayoutLineColor: z.string().optional(),
  chartCrosshairColor: z.string().optional(),
  chartLineColor: z.string().optional(),
  chartLineWidth: z.number().min(1).max(4).default(2),
  chartAreaLineColor: z.string().optional(),
  chartAreaTopColor: z.string().optional(),
  chartAreaBottomColor: z.string().optional(),
  chartAreaLineWidth: z.number().min(1).max(4).default(2),
  chartCrosshairLabelBackgroundColor: z.string().optional(),
  chartCrosshairLabelTextColor: z.string().optional(),
  chartTextColor: z.string().optional(),
  chartBorderColor: z.string().optional(),
  chartTimeScaleBorderColor: z.string().optional(),
  chartTimeScaleTextColor: z.string().optional(),
  chartPriceScaleBorderColor: z.string().optional(),
  chartPriceScaleTextColor: z.string().optional(),
  chartScaleLabelBackgroundColor: z.string().optional(),
  chartScaleLabelTextColor: z.string().optional(),
  autoHideScalesEnabled: z.boolean().default(false),
  
  // Конфигурация колонок индикаторов
  indicatorColumns: z.array(IndicatorColumnConfigSchema).default([]),
  
  // Add ModeControlSettingsSchema here
  modeControl: ModeControlSettingsSchema.default({
    currentMode: 'focus',
    screenerSettings: {
      sortBy: 'volume',
      sortOrder: 'desc',
      timeframe: '1h',
      autoUpdate: false,
      updateInterval: 5,
      markets: ['futures'],
      pairs: ['USDT'],
      minVolume: 0, 
      minTrades: 0,
      currentPage: 1, // Added currentPage default
    },
    layoutTypes: {
      focus: '2x2',
      screener: '3x2',
    },
  }),
});

export type AppSettingsZod = z.infer<typeof AppSettingsSchema>; 