export type IndicatorParamValue = string | number | boolean;

export type TimeFrame = string; // Added TimeFrame type

export interface IndicatorParams {
  [key: string]: IndicatorParamValue;
}

export interface IndicatorStyle {
  color?: string;
  lineWidth?: number; // Assuming lightweight-charts LineWidth (1, 2, 3, 4)
  lineStyle?: number; // Assuming lightweight-charts LineStyle
  opacity?: number; // Add opacity
  // Add other style properties as needed
}

export interface IndicatorOutputInfo {
  id: string; // e.g., 'smaValue', 'rsiLine'
  name: string; // e.g., 'SMA', 'RSI'
  type: 'line' | 'histogram' | 'area' | 'point'; // Corresponds to series type
  color?: string; // Default color
  lineWidth?: number;
  lineStyle?: number;
  opacity?: number; // Add opacity
  // Optional: specify which pane it should be rendered on (e.g., 'main', 'separate')
  pane?: string; 
}

export interface IndicatorDefinition<TParams extends IndicatorParams = IndicatorParams> {
  id: string; // Unique ID, e.g., "SMA", "RSI"
  name: string; // Display name, e.g., "Simple Moving Average"
  description?: string;
  shortName?: string; // e.g. "SMA(14)"
  inputCount: number; // Number of input series (usually 1 for price data)
  params: Array<{
    id: keyof TParams;
    name: string;
    type: 'number' | 'string' | 'boolean' | 'select';
    defaultValue: IndicatorParamValue;
    options?: Array<{ value: IndicatorParamValue; label: string }>; // For 'select' type
    min?: number; // For 'number' type
    max?: number; // For 'number' type
    step?: number; // For 'number' type
  }>;
  outputs: IndicatorOutputInfo[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  calculate: (data: any[][], params: TParams) => Array<Record<string, number | null>>;
  // Optional: function to format output values for display
  formatValue?: (value: number, outputId: string) => string;
  // Optional: function to generate a short display name with parameters
  getShortName?: (params: TParams) => string;
  // Optional: specifies if the indicator should be plotted on the main price pane or a separate pane
  plotOnMainPane?: boolean; 
  // Optional: default style overrides
  defaultStyle?: Record<string, IndicatorStyle>; 
}

export interface IndicatorInstance<TParams extends IndicatorParams = IndicatorParams> {
  instanceId: string; // Unique ID for this specific instance of the indicator
  indicatorId: string; // ID of the IndicatorDefinition
  chartId: string | 'global'; // Which chart this instance belongs to, or 'global'
  name: string; // Display name, possibly with parameters, e.g., "SMA(14)"
  params: TParams;
  visible: boolean;
  // Style overrides for specific outputs, e.g., { "smaValue": { color: "blue" } }
  styleOverrides?: Record<string, Partial<IndicatorStyle>>;
  // For indicators displayed in a separate pane, this could define the pane's height or other properties
  paneConfig?: {
    height?: number; // e.g., percentage or fixed pixels
  };
  // New fields for table integration
  tableOutputIds?: string[]; // Which outputs to show in the screener table
  tableTimeframe?: string; // Timeframe for the table calculation if different from chart
}

// This type represents the actual calculated values for an indicator instance.
// It's often a combination of the raw calculation result and the instance metadata.
export interface CalculatedIndicatorResult extends IndicatorInstance {
    // The 'results' array from IndicatorResult, but here it's part of the instance context
    // Each element in `data` corresponds to a data point (e.g., a kline)
    // Each Record maps outputId to its calculated value for that data point
    symbol: string; // Added symbol field
    data: Array<Record<string, number | null>>; 
    timestamps: number[]; // Timestamps for each data point in `data`
}


// For indicatorStore.ts, it seems 'IndicatorOutput' and 'IndicatorParam' were used.
// 'IndicatorParam' seems to be a typo for 'IndicatorParams' or a singular version.
// 'IndicatorOutput' might refer to IndicatorOutputInfo or the actual calculated data.

// Let's provide IndicatorOutput as IndicatorOutputInfo for now
export type IndicatorOutput = IndicatorOutputInfo;

// If a singular IndicatorParam is needed:
export interface IndicatorParam {
    id: string;
    name: string;
    type: 'number' | 'string' | 'boolean' | 'select';
    defaultValue: IndicatorParamValue;
    options?: Array<{ value: IndicatorParamValue; label: string }>;
    min?: number;
    max?: number;
    step?: number;
} 