import React, { useState, useEffect, useCallback, useMemo, memo, forwardRef, ForwardedRef } from 'react';
import { LayoutType, WSConnectionStatus, MarketType, ChartType } from '@/shared/index';
import LayoutSelector from '../GridSelector/Grid';
import StatusBar, { StatusBarProps } from '../StatusBar/StatusBar';
import { Icon, type IconName } from '@/shared/ui/icons/all_Icon';
// Correct the import name
import { IndicatorsPanelTrigger } from '../Indicators/Indicators';
import SettingsButton from '../SettingButton/Setting';
import TickerSearch from '../TickerSearch/TickerSearch';
import { Button } from '@/shared/ui/button';
import { TickerTable } from '../TickerManagement/Table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/shared/ui/tooltip";
import { ModeControl } from '../ModeControl';

// --- Interfaces ---
export interface ControlPanelProps {
  className?: string;
  selectedSymbol: string | null;
  loading?: boolean;
  error?: string | null;
  lastUpdate?: number | null;
  onRefresh?: () => void;
  isWsConnected?: boolean;
  connectionDetails?: {
    name: string;
    status: WSConnectionStatus;
    details?: string;
  }[];
  totalTickers?: number;
  filteredTickers?: number;
  isTableCollapsed?: boolean;
  onToggleTableCollapse?: () => void;
  isFiltering?: boolean;
  activeChartId?: string;
  layoutType: LayoutType;
  selectedMarketTypes: MarketType[];
  aggregateVolumeAndTrades?: boolean;
  setLayoutType: (layout: LayoutType) => void;
  dragConstraintsRef: React.RefObject<HTMLDivElement | null>;
  onSearchChange: (query: string) => void;
}

// --- Component ---
const ControlPanelComponentInternal = forwardRef<HTMLDivElement, ControlPanelProps>(({
    className,
    selectedSymbol,
    loading,
    error,
    lastUpdate,
    onRefresh,
    isWsConnected,
    connectionDetails,
    totalTickers,
    filteredTickers,
    isTableCollapsed,
    onToggleTableCollapse,
    isFiltering,
    activeChartId = 'chart-0',
    layoutType,
    selectedMarketTypes,
    aggregateVolumeAndTrades,
    setLayoutType,
    dragConstraintsRef,
    onSearchChange,
}, ref: ForwardedRef<HTMLDivElement>) => {
  // Remove local state for chart type toggle
  // const [localChartType, setLocalChartType] = useState<ChartType>(ChartType.Candles);

  // Determine StatusBar props (remains the same)
  const statusBarProps: StatusBarProps = {
      loading,
      error,
      lastUpdate,
      onRefresh,
      connectionDetails: connectionDetails as any,
      totalTickers,
      filteredTickers,
  };

  // Determine current market type display (remains the same)
  const currentMarketType = useMemo(() => {
    const types = selectedMarketTypes ?? [];
    if (types.length === 0) return 'N/A';
    if (types.length === 1) return types[0];
    return 'ALL';
  }, [selectedMarketTypes]);

  return (
    <div ref={ref} className={`relative flex items-center justify-between border-b border-l border-r border-border rounded-bl-lg rounded-br-lg h-9 flex-shrink-0 px-2 w-full ${className || ''}`}>
      {/* Left Section */}
      <div className="flex items-center gap-1">
         {/* Collapse Button */}
         {onToggleTableCollapse && (
           <div className="collapse-button-container" title={isTableCollapsed ? "Развернуть таблицу" : "Свернуть таблицу"}>
             <Button
               variant="ghost"
               size="icon"
               className="absolute -top-4 right-4 h-7 w-7 rounded-full border"
               onClick={onToggleTableCollapse}
             >
               <Icon
                 name={isTableCollapsed ? "PanelBottomClose" : "PanelTopClose" }
                 className="rotate-90"
               />
             </Button>
           </div>
         )}

         {/* Settings Button */}
         <SettingsButton dragConstraintsRef={dragConstraintsRef} />

         {/* Ticker Search Component */}
         <div className="search-container w-[180px] h-[28px]">
           <TickerSearch
             selectedSymbol={selectedSymbol}
             isFiltering={isFiltering}
             className="h-full"
             onSearchChange={onSearchChange}
           />
         </div>

         {/* Layout Selector */}
         <div className="grid-selector-wrapper ml-1 flex items-center h-full min-w-[32px] relative z-10">
           <LayoutSelector
              currentLayout={layoutType}
              onLayoutChange={setLayoutType}
           />
         </div>

         {/* Indicators Button - Correct the component usage */}
         <div className="indicator-button-wrapper ml-1 flex items-center h-full">
           <IndicatorsPanelTrigger chartId={activeChartId} />
         </div>
      </div>

      {/* Center Section - Mode Control */}
      <div className="flex items-center h-full">
        <TooltipProvider>
          <ModeControl className="h-[28px]" />
        </TooltipProvider>
      </div>

      {/* Right Section - Status Bar */}
      <StatusBar {...statusBarProps} />
    </div>
  );
});

ControlPanelComponentInternal.displayName = 'ControlPanelComponentInternal';

// --- Export memoized component ---
const ControlPanel = memo(ControlPanelComponentInternal);
export default ControlPanel;
