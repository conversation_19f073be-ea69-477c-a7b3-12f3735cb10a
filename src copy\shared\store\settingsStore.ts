import { create, StateCreator } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { AppSettings, /* SortConfig, */ VisibleColumns, ColumnWidths, ChartAppearanceSettings, ChartType, IndicatorColumnConfig, ViewMode, ScreenerSettings, ModeControlSettings, LayoutType, MarketType } from '@/shared/index'; // Changed ApiMarketType to MarketType
import { LineStyle, LineWidth } from 'lightweight-charts'; // Import LineStyle and LineWidth
import { SortingState } from '@tanstack/react-table'; // Import SortingState

// Define the default timeframe constant here
const DEFAULT_TIMEFRAME = '1m'; // Example: 1 hour

// Ключ для localStorage
const LS_SETTINGS_KEY = 'cryptoDashboardSettings';

// Начальные значения по умолчанию
// ЭКСПОРТИРУЕМ ДЛЯ СБРОСА
export const defaultSettings: AppSettings = {
  isFiltersShown: true,
  isGridShown: true,
  isDebugMode: false,
  isDarkMode: true,

  // === ОСНОВНЫЕ НАСТРОЙКИ ГРАФИКА === 
  chartType: ChartType.Candles,
  chartFontFamily: 'Inter, sans-serif',
  chartFontSize: 11,
  
  // --- Основные цвета графика ---
  chartTextColor: 'rgb(210, 210, 210)',                 // Единый цвет текста для всех шкал и меток
  chartBackgroundColor: '#0b0e11',                      // Фоновый цвет всего графика
  chartLayoutLineColor: '#181e25',                      // Цвет разделительных линий графика
  chartBorderColor: 'rgba(255, 255, 255, 0.1)',         // Единый цвет границ для всех элементов графика
  
  // --- Настройки меток на шкалах ---
  chartScaleLabelBackgroundColor: 'rgb(230, 230, 230)', // Единый фон для всех меток на шкале
  chartScaleLabelTextColor: '#0b0e11',                  // Единый цвет текста для всех меток на шкале
  
  // --- Цвета сетки графика ---
  chartGridVertLinesVisible: false,                     // Видимость вертикальных линий сетки
  chartGridHorzLinesVisible: false,                     // Видимость горизонтальных линий сетки
  chartGridVertLinesColor: '#2e2e2e',                   // Цвет вертикальных линий сетки
  chartGridHorzLinesColor: '#2e2e2e',                   // Цвет горизонтальных линий сетки
  
  // --- Цвета перекрестия (Crosshair) ---
  chartCrosshairColor: 'rgba(255, 255, 255, 0.3)',      // Цвет линий перекрестия
  chartCrosshairLabelBackgroundColor: 'rgb(54,58,69)',  // Фон подписей перекрестия
  chartCrosshairLabelTextColor: '#ffffff',              // Цвет текста подписей перекрестия
  
  // --- Настройки линии цены ---
  chartPriceLineVisible: true,                          // Видимость линии текущей цены
  chartLastValueVisible: true,                          // Видимость подписи текущей цены
  chartPriceLineWidth: 1,                               // Толщина линии текущей цены
  chartPriceLineStyle: 1,                               // Стиль линии текущей цены
  chartPriceLineColor: 'rgb(200, 200, 200)',            // Цвет линии последней цены
  
  // --- Цвета линейного графика ---
  chartLineColor: '#9B7DFF',                            // Цвет линии
  chartLineWidth: 1,                                    // Толщина линии
  
  // --- Цвета графика-области ---
  chartAreaLineColor: '#9B7DFF',                        // Цвет линии
  chartAreaTopColor: 'rgba(155, 125, 255, 0.2)',        // Цвет верхней области заливки
  chartAreaBottomColor: 'rgba(155, 125, 255, 0.05)',    // Цвет нижней области заливки
  chartAreaLineWidth: 1,                                // Толщина линии
  

  // --- Настройки свечей ---
  chartCandleBodyEnabled: true,                         // Видимость тела свечи
  chartCandleBodyUpColor: 'rgb(255, 255, 255, 0)',      // Цвет тела растущей свечи
  chartCandleBodyDownColor: 'rgb(220, 220, 220)',       // Цвет тела падающей свечи
  
  chartCandleBorderEnabled: true,                       // Видимость границы свечи
  chartCandleBorderUpColor: 'rgb(220, 220, 220)',       // Цвет границы растущей свечи
  chartCandleBorderDownColor: 'rgb(220, 220, 220)',     // Цвет границы падающей свечи
  
  chartCandleWickEnabled: true,                         // Видимость теней свечи
  chartCandleWickUpColor: 'rgb(220, 220, 220)',         // Цвет теней растущей свечи
  chartCandleWickDownColor: 'rgb(220, 220, 220)',       // Цвет теней падающей свечи
  
  // Для обратной совместимости 
  chartTimeScaleBorderColor: 'rgba(255, 255, 255, 0.1)', // То же, что и chartBorderColor
  chartPriceScaleBorderColor: 'rgba(255, 255, 255, 0.1)', // То же, что и chartBorderColor
  
  // Значения для единых цветов шкал
  chartTimeScaleTextColor: 'rgb(230, 230, 230)',         // То же, что и chartTextColor
  chartPriceScaleTextColor: 'rgb(230, 230, 230)',         // То же, что и chartTextColor
  
  // --- Настройки объема ---
  volumeEnabled: true,                                  // Включен ли объем на графике
  chartVolumeHeightRatio: 0.15,                         // Соотношение высоты области объема
  chartVolumeUpColor: 'rgba(0, 150, 136, 0.8)',         // Цвет объема при росте цены
  chartVolumeDownColor: 'rgba(255, 82, 82, 0.8)',       // Цвет объема при падении цены
  
  // --- Размещение и масштабирование ---
  chartRightOffset: 10,                                  // Отступ справа на графике (Увеличено с 8 до 20)
  chartBarSpacing: 3,                                    // Расстояние между свечами/барами
  autoHideScalesEnabled: false,                         // НОВОЕ: Включено ли автоскрытие шкал
  
  // === ПРОЧИЕ НАСТРОЙКИ ===
  autoSaveEnabled: true,
  syncCrosshair: true,
  sortConfigs: [{ id: 'volume', desc: true }],
  visibleColumns: { symbol: true, price: true, change: true, volume: true, trade: true, spread: false },
  columnWidths: {
    symbol: 80,
    price: 70,
    change: 50,
    volume: 45,
    trade: 45,
    spread: 45,
  },
  columnOrder: ['symbol', 'price', 'change', 'volume', 'trade', 'spread'],
  columnFilters: {},
  globalSearchTerm: '',
  showVolumeInUSD: true,
  aggregateVolumeAndTrades: false,
  minVolume: 0,
  minTrades: 0,
  selectedPairs: ['USDT'],
  selectedMarketTypes: ['futures' as MarketType], // Changed ApiMarketType to MarketType
  tableCompactness: 14,
  uiFontFamily: 'Verdana, sans-serif',
  uiFontSize: 12,
  isSyncEnabled: true,
  layoutMode: 'grid',
  layoutType: '3x2',
  selectedInterval: DEFAULT_TIMEFRAME, // This was already changed in the previous step
  isTableCollapsed: false,
  selectedTickerSymbol: null,
  availablePairs: ['USDT', 'USDC', 'FDUSD', 'DAI', 'USDE', 'OTHR'],
  indicatorColumns: [],
  
  // === MODE CONTROL SETTINGS ===
  modeControl: {
    currentMode: 'focus',
    screenerSettings: {
      sortBy: 'volume',
      sortOrder: 'desc',
      timeframe: '1h',
      autoUpdate: false,
      updateInterval: 5,
      markets: ['futures'],
      pairs: ['USDT'],
      minVolume: 0,
      minTrades: 0,
      currentPage: 1,
    },
    layoutTypes: {
      focus: '2x2',
      screener: '3x2',
    },
  },
};

// --- Интерфейс для состояния хранилища, включая действия ---
// AppSettingsState now correctly expects selectedInterval from AppSettings
interface AppSettingsState extends AppSettings {
  globalSearchTerm: string;
  // Действия для работы с настройками
  setSetting: <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => void;
  toggleColumnVisibility: (key: keyof VisibleColumns | string) => void; // Allow string for dynamic columns
  updateSortConfigs: (newSortingState: SortingState) => void;
  setColumnOrder: (newOrder: string[]) => void;
  setColumnWidth: (key: keyof ColumnWidths | string, width: number) => void; // Allow string for dynamic columns
  setColumnWidths: (newWidths: ColumnWidths) => void;
  setColumnFilter: (key: keyof AppSettings['columnFilters'] | string, value: string | number | null) => void; // Allow string for dynamic columns
  setTableCollapsed: (isCollapsed: boolean) => void;
  resetSettings: () => void;
  toggleAutoHideScales: () => void;
  setGlobalSearchTerm: (term: string) => void;

  // NEW: Actions for managing indicator columns
  addIndicatorColumn: (config: IndicatorColumnConfig) => void;
  removeIndicatorColumn: (instanceId: string) => void;
  updateIndicatorColumn: (instanceId: string, updates: Partial<IndicatorColumnConfig>) => void;
  
  // Mode control actions
  setViewMode: (mode: ViewMode) => void;
  updateScreenerSettings: (updates: Partial<ScreenerSettings>) => void;
  setLayoutTypeForMode: (mode: ViewMode, layoutType: LayoutType) => void;
}

// --- Определяем действия отдельно ---
const createAppSettingsActions = (
  set: (partial: Partial<AppSettingsState> | ((state: AppSettingsState) => Partial<AppSettingsState>)) => void,
  get: () => AppSettingsState
): Omit<AppSettingsState, keyof AppSettings> => ({
  // Действие для обновления любого поля настроек
  setSetting: <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => set({ [key]: value } as Pick<AppSettingsState, K>),

  // Allow string keys for dynamic columns
  toggleColumnVisibility: (key: keyof VisibleColumns | string) => {
    const currentCols = { ...get().visibleColumns }; // Clone to avoid direct mutation issues
    // Запрещаем скрытие ключевой колонки (например, 'symbol')
    if (key === 'symbol' && currentCols[key]) {
      console.warn("Cannot hide the primary 'symbol' column.");
      return;
    }
    // При скрытии колонки также сбрасываем ее фильтр и ширину
    if (currentCols[key]) {
        get().setColumnFilter(key as string, null);
        // Optionally reset width (consider if this is desired)
        // const currentWidths = { ...get().columnWidths };
        // delete currentWidths[key as string];
        // set({ columnWidths: currentWidths });
    }
    set((state) => ({
      visibleColumns: {
        ...state.visibleColumns,
        [key as string]: !state.visibleColumns[key as string] // Toggle visibility
      }
    }));
  },

  // Действие для установки нового порядка колонок
  setColumnOrder: (newOrder: string[]) => set({ columnOrder: newOrder }),

  // Allow string keys for dynamic columns
  setColumnWidth: (key: keyof ColumnWidths | string, width: number) => {
    const currentWidths = get().columnWidths;
    const minWidth = 40;
    set({
      columnWidths: {
        ...currentWidths,
        [key as string]: Math.max(width, minWidth),
      },
    });
  },

  // NEW: Действие для установки всех ширин колонок
  setColumnWidths: (newWidths: ColumnWidths) => {
    const validatedWidths: ColumnWidths = { ...newWidths };
    const minWidth = 40;
    // Ensure minimum width for all columns
    for (const key in validatedWidths) {
        if (Object.prototype.hasOwnProperty.call(validatedWidths, key)) {
             validatedWidths[key as keyof ColumnWidths] = Math.max(validatedWidths[key as keyof ColumnWidths]!, minWidth);
        }
    }
    set({ columnWidths: validatedWidths });
  },

  // Allow string keys for dynamic columns
  setColumnFilter: (key: keyof AppSettings['columnFilters'] | string, value: string | number | null) => {
      const currentFilters = get().columnFilters;
      let processedValue = value;
      if (typeof value === 'string' && value.trim() === '') {
          processedValue = null;
      }
      if (currentFilters[key as string] === processedValue || (!currentFilters[key as string] && !processedValue)) return;

      set({
          columnFilters: {
              ...currentFilters,
              [key as string]: processedValue,
          },
      });
  },

  setTableCollapsed: (isCollapsed: boolean) => set({ isTableCollapsed: isCollapsed }),

  // Действие для сброса всех настроек к значениям по умолчанию
  resetSettings: () => {
    console.log("Resetting settings to default values.");
    set(defaultSettings);
  },
  
  // Действие для переключения автоскрытия шкал
  toggleAutoHideScales: () => set((state) => ({ autoHideScalesEnabled: !state.autoHideScalesEnabled })),
  
  // NEW: Действие для установки глобального поискового запроса
  setGlobalSearchTerm: (term: string) => set({ globalSearchTerm: term }),

  // NEW: Actions for managing indicator columns
  addIndicatorColumn: (config: IndicatorColumnConfig) => set((state) => ({
    indicatorColumns: [...state.indicatorColumns, config]
  })),
  removeIndicatorColumn: (instanceId: string) => set((state) => ({
    indicatorColumns: state.indicatorColumns.filter(col => col.instanceId !== instanceId)
  })),
  updateIndicatorColumn: (instanceId: string, updates: Partial<IndicatorColumnConfig>) => set((state) => ({
    indicatorColumns: state.indicatorColumns.map(col => 
      col.instanceId === instanceId ? { ...col, ...updates } : col
    )
  })),
  
  // Mode control actions
  setViewMode: (mode: ViewMode) => set(state => ({
    modeControl: {
      ...state.modeControl,
      currentMode: mode,
    }
  })),
  updateScreenerSettings: (updates: Partial<ScreenerSettings>) => set(state => ({
    modeControl: {
      ...state.modeControl,
      screenerSettings: { ...state.modeControl.screenerSettings, ...updates }
    }
  })),
  setLayoutTypeForMode: (mode: ViewMode, layoutType: LayoutType) => set(state => ({
    modeControl: {
      ...state.modeControl,
      layoutTypes: {
        ...state.modeControl.layoutTypes,
        [mode]: layoutType,
      }
    }
  })),

  updateSortConfigs: (newSortingState: SortingState) => {
    set({ sortConfigs: newSortingState }); // SortingState already has { id: string, desc: boolean }[] type
  },
});

// --- Определяем тип StateCreator ---
type AppSettingsStateCreator = StateCreator<
  AppSettingsState,
  [],
  [],
  AppSettingsState
>;

const settingsCreator: AppSettingsStateCreator = (set, get) => ({
  ...defaultSettings,
  ...createAppSettingsActions(set, get),
});

// --- Создаем хранилище с persist middleware ---
const settingsStore = create<AppSettingsState>()(
  persist(
    settingsCreator,
    {
      name: LS_SETTINGS_KEY,
      storage: createJSONStorage(() => {
        // Проверяем, что код выполняется в браузере
        const isClient = typeof window !== 'undefined';
        if (!isClient) return {
          getItem: () => Promise.resolve(null),
          setItem: () => Promise.resolve(),
          removeItem: () => Promise.resolve()
        };
        
        // ВАЖНО: НЕ удаляем настройки пользователя при инициализации!
        // Сохраняем возможность отловить ошибки при работе с localStorage
        try {
          // Просто проверяем доступность localStorage
          if (localStorage) {
            // Можно добавить миграцию настроек здесь, если нужно
          }
        } catch (e) {
          console.warn('[AppSettingsStore] Error accessing localStorage:', e);
        }
        return localStorage;
      }),
      partialize: (state) => {
        // Отфильтровываем методы и функции
        const { 
          setSetting, toggleColumnVisibility, updateSortConfigs, setColumnOrder, 
          setColumnWidth, setColumnWidths, setColumnFilter, setTableCollapsed,
          resetSettings, toggleAutoHideScales, setGlobalSearchTerm,
          addIndicatorColumn, removeIndicatorColumn, updateIndicatorColumn,
          setViewMode, updateScreenerSettings, setLayoutTypeForMode,
          ...persistedState 
        } = state;
        return persistedState;
      },
      merge: (persisted, current) => {
        if (!persisted || typeof persisted !== 'object') {
          console.log('[AppSettingsStore] Invalid persisted state during merge, returning current state.');
          return current;
        }
        
        try {
          // Функции и методы
          const newState = {
            ...current,
            // Безопасно копируем только примитивные и непустые объектные значения
          };
          
          // Типизированный персистед
          const typedPersisted = persisted as Partial<AppSettings>;
          
          // Перенос данных из сохраненного состояния
          for (const key in typedPersisted) {
            if (Object.prototype.hasOwnProperty.call(typedPersisted, key)) {
              const value = typedPersisted[key as keyof typeof typedPersisted];
              
              // Проверяем корректность типа данных
              if (value === null || value === undefined) continue;
              
              // Обрабатываем массивы
              if (Array.isArray(value)) {
                (newState as any)[key] = [...value];
              }
              // Обрабатываем объекты (не массивы)
              else if (typeof value === 'object') {
                (newState as any)[key] = {
                  ...((current as any)[key] || {}),
                  ...value
                };
              }
              // Примитивные типы
              else {
                (newState as any)[key] = value;
              }
            }
          }
          
          // Проверяем обязательные поля
          if (!newState.visibleColumns) newState.visibleColumns = defaultSettings.visibleColumns;
          if (!newState.columnWidths) newState.columnWidths = defaultSettings.columnWidths;
          if (!newState.columnOrder) newState.columnOrder = defaultSettings.columnOrder;
          
          // Проверяем типы важных полей
          if (!Array.isArray(newState.indicatorColumns)) {
            newState.indicatorColumns = defaultSettings.indicatorColumns;
          }
          
          return newState;
        } catch (error) {
          console.error('[AppSettingsStore] Error during state merge:', error);
          return current;
        }
      },
    }
  )
);

// Селекторы для доступа к частям состояния
export const selectIsDarkMode = (state: AppSettingsState) => state.isDarkMode;
export const selectIsFiltersShown = (state: AppSettingsState) => state.isFiltersShown;
export const selectIsGridShown = (state: AppSettingsState) => state.isGridShown;
export const selectIsDebugMode = (state: AppSettingsState) => state.isDebugMode;

// === СЕЛЕКТОРЫ ДЛЯ НАСТРОЕК ГРАФИКА ===
export const selectChartType = (state: AppSettingsState) => state.chartType;
export const selectChartBackgroundColor = (state: AppSettingsState) => state.chartBackgroundColor;
export const selectChartLayoutLineColor = (state: AppSettingsState) => state.chartLayoutLineColor;
export const selectChartTextColor = (state: AppSettingsState) => state.chartTextColor;
export const selectChartFontFamily = (state: AppSettingsState) => state.chartFontFamily;
export const selectChartFontSize = (state: AppSettingsState) => state.chartFontSize;
export const selectChartBorderColor = (state: AppSettingsState) => state.chartBorderColor || state.chartTimeScaleBorderColor; // Новый селектор с обратной совместимостью

// --- Селекторы для сетки ---
export const selectChartGridVertLinesVisible = (state: AppSettingsState) => state.chartGridVertLinesVisible;
export const selectChartGridHorzLinesVisible = (state: AppSettingsState) => state.chartGridHorzLinesVisible;
export const selectChartGridVertLinesColor = (state: AppSettingsState) => state.chartGridVertLinesColor;
export const selectChartGridHorzLinesColor = (state: AppSettingsState) => state.chartGridHorzLinesColor;

// --- Селекторы для свечей ---
export const selectChartCandleBodyEnabled = (state: AppSettingsState) => state.chartCandleBodyEnabled;
export const selectChartCandleBorderEnabled = (state: AppSettingsState) => state.chartCandleBorderEnabled;
export const selectChartCandleWickEnabled = (state: AppSettingsState) => state.chartCandleWickEnabled;

export const selectChartCandleBodyUpColor = (state: AppSettingsState) => state.chartCandleBodyUpColor;
export const selectChartCandleBodyDownColor = (state: AppSettingsState) => state.chartCandleBodyDownColor;

export const selectChartCandleBorderUpColor = (state: AppSettingsState) => state.chartCandleBorderUpColor;
export const selectChartCandleBorderDownColor = (state: AppSettingsState) => state.chartCandleBorderDownColor;

export const selectChartCandleWickUpColor = (state: AppSettingsState) => state.chartCandleWickUpColor;
export const selectChartCandleWickDownColor = (state: AppSettingsState) => state.chartCandleWickDownColor;

// --- Селекторы для линейного графика ---
export const selectChartLineColor = (state: AppSettingsState) => state.chartLineColor;
export const selectChartLineWidth = (state: AppSettingsState): LineWidth => state.chartLineWidth as LineWidth;

// --- Селекторы для графика-области ---
export const selectChartAreaLineColor = (state: AppSettingsState) => state.chartAreaLineColor;
export const selectChartAreaTopColor = (state: AppSettingsState) => state.chartAreaTopColor;
export const selectChartAreaBottomColor = (state: AppSettingsState) => state.chartAreaBottomColor;
export const selectChartAreaLineWidth = (state: AppSettingsState): LineWidth => state.chartAreaLineWidth as LineWidth;

// --- Селекторы для перекрестия ---
export const selectChartCrosshairColor = (state: AppSettingsState) => state.chartCrosshairColor;
export const selectChartCrosshairLabelBackgroundColor = (state: AppSettingsState) => state.chartCrosshairLabelBackgroundColor;
export const selectChartCrosshairLabelTextColor = (state: AppSettingsState) => state.chartCrosshairLabelTextColor;

// --- Селекторы для шкал (с обратной совместимостью) ---
export const selectChartTimeScaleBorderColor = (state: AppSettingsState) => state.chartBorderColor || state.chartTimeScaleBorderColor; // Для обратной совместимости
export const selectChartTimeScaleTextColor = (state: AppSettingsState) => state.chartTextColor || state.chartTimeScaleTextColor; // Для обратной совместимости
export const selectChartPriceScaleBorderColor = (state: AppSettingsState) => state.chartBorderColor || state.chartPriceScaleBorderColor; // Для обратной совместимости
export const selectChartPriceScaleTextColor = (state: AppSettingsState) => state.chartTextColor || state.chartPriceScaleTextColor; // Для обратной совместимости

// --- Селекторы для линии цены ---
export const selectChartPriceLineVisible = (state: AppSettingsState) => state.chartPriceLineVisible;
export const selectChartLastValueVisible = (state: AppSettingsState) => state.chartLastValueVisible;
export const selectChartPriceLineWidth = (state: AppSettingsState) => state.chartPriceLineWidth;
export const selectChartPriceLineStyle = (state: AppSettingsState) => state.chartPriceLineStyle;
export const selectChartPriceLineColor = (state: AppSettingsState) => state.chartPriceLineColor;

// --- Селекторы для объема ---
export const selectVolumeEnabled = (state: AppSettingsState) => state.volumeEnabled;
export const selectChartVolumeHeightRatio = (state: AppSettingsState) => state.chartVolumeHeightRatio;
export const selectChartVolumeUpColor = (state: AppSettingsState) => state.chartVolumeUpColor;
export const selectChartVolumeDownColor = (state: AppSettingsState) => state.chartVolumeDownColor;

// --- Селекторы для размещения и масштабирования ---
export const selectChartRightOffset = (state: AppSettingsState) => state.chartRightOffset;
export const selectChartBarSpacing = (state: AppSettingsState) => state.chartBarSpacing;

// === ПРОЧИЕ СЕЛЕКТОРЫ ===
export const selectLayoutMode = (state: AppSettingsState) => state.layoutMode;
export const selectLayoutType = (state: AppSettingsState) => state.layoutType;
export const selectSelectedPairs = (state: AppSettingsState) => state.selectedPairs;
export const selectMarketTypes = (state: AppSettingsState) => state.selectedMarketTypes;
export const selectMinVolume = (state: AppSettingsState) => state.minVolume;
export const selectMinTrades = (state: AppSettingsState) => state.minTrades;
export const selectShowVolumeInUSD = (state: AppSettingsState) => state.showVolumeInUSD;
export const selectAggregateVolumeAndTrades = (state: AppSettingsState) => state.aggregateVolumeAndTrades;
export const selectTableCompactness = (state: AppSettingsState) => state.tableCompactness;
export const selectIsSyncEnabled = (state: AppSettingsState) => state.isSyncEnabled;
export const selectUiFontFamily = (state: AppSettingsState) => state.uiFontFamily;
export const selectUiFontSize = (state: AppSettingsState) => state.uiFontSize;
export const selectSortConfigs = (state: AppSettingsState) => state.sortConfigs;
export const selectVisibleColumns = (state: AppSettingsState) => state.visibleColumns;
export const selectColumnWidths = (state: AppSettingsState) => state.columnWidths;
export const selectColumnOrder = (state: AppSettingsState) => state.columnOrder;
export const selectColumnFilters = (state: AppSettingsState) => state.columnFilters;
export const selectIsTableCollapsed = (state: AppSettingsState) => state.isTableCollapsed;
export const selectSelectedTickerSymbol = (state: AppSettingsState) => state.selectedTickerSymbol;
export const selectGlobalSearchTerm = (state: AppSettingsState) => state.globalSearchTerm;

// Селектор для всех настроек отображения графика
export const selectChartAppearance = (state: AppSettingsState): ChartAppearanceSettings => ({
  type: state.chartType,
  volumeEnabled: state.volumeEnabled,
  candles: {
    bodyVisible: state.chartCandleBodyEnabled,
    borderVisible: state.chartCandleBorderEnabled,
    wickVisible: state.chartCandleWickEnabled,
    upColor: state.chartCandleBodyUpColor,
    downColor: state.chartCandleBodyDownColor,
    borderUpColor: state.chartCandleBorderUpColor,
    borderDownColor: state.chartCandleBorderDownColor,
    wickUpColor: state.chartCandleWickUpColor,
    wickDownColor: state.chartCandleWickDownColor,
  },
  grid: {
    vertLinesVisible: state.chartGridVertLinesVisible,
    horzLinesVisible: state.chartGridHorzLinesVisible,
    vertLinesColor: state.chartGridVertLinesColor,
    horzLinesColor: state.chartGridHorzLinesColor,
  },
  layout: {
    fontFamily: state.chartFontFamily,
    fontSize: state.chartFontSize,
    textColor: state.chartTextColor,
    backgroundColor: state.chartBackgroundColor,
    lineColor: state.chartLayoutLineColor,
    crosshairColor: state.chartCrosshairColor,
    crosshairLabelBackgroundColor: state.chartCrosshairLabelBackgroundColor,
    crosshairLabelTextColor: state.chartCrosshairLabelTextColor,
  },
  volume: {
    heightRatio: state.chartVolumeHeightRatio,
    upColor: state.chartVolumeUpColor,
    downColor: state.chartVolumeDownColor,
  },
  spacing: {
    rightOffset: state.chartRightOffset,
    barSpacing: state.chartBarSpacing,
  },
  priceLine: {
    visible: state.chartPriceLineVisible,
    lastValueVisible: state.chartLastValueVisible,
    width: state.chartPriceLineWidth,
    style: state.chartPriceLineStyle,
    color: state.chartPriceLineColor,
  },
  line: {
    color: state.chartLineColor,
    width: state.chartLineWidth,
  },
  area: {
    lineColor: state.chartAreaLineColor,
    topColor: state.chartAreaTopColor,
    bottomColor: state.chartAreaBottomColor,
    lineWidth: state.chartAreaLineWidth,
  },
  // Используем единые цвета для шкал из корневого уровня
  timeScaleBorderColor: state.chartBorderColor || state.chartTimeScaleBorderColor, 
  timeScaleTextColor: state.chartTextColor || state.chartTimeScaleTextColor,
  priceScaleBorderColor: state.chartBorderColor || state.chartPriceScaleBorderColor,
  priceScaleTextColor: state.chartTextColor || state.chartPriceScaleTextColor,
  // Используем единые цвета для меток на шкалах
  countdownLabelBackgroundColor: state.chartScaleLabelBackgroundColor,
  countdownLabelTextColor: state.chartScaleLabelTextColor,
  lastPriceLabelBackgroundColor: state.chartScaleLabelBackgroundColor, 
  lastPriceLabelTextColor: state.chartScaleLabelTextColor,
  autoHideScalesEnabled: state.autoHideScalesEnabled,
});

// Селекторы для действий
export const selectSetSetting = (state: AppSettingsState) => state.setSetting;
export const selectToggleColumnVisibility = (state: AppSettingsState) => state.toggleColumnVisibility;
export const selectUpdateSortConfigs = (state: AppSettingsState) => state.updateSortConfigs;
export const selectSetColumnOrder = (state: AppSettingsState) => state.setColumnOrder;
export const selectSetColumnWidth = (state: AppSettingsState) => state.setColumnWidth;
export const selectSetColumnFilter = (state: AppSettingsState) => state.setColumnFilter;
export const selectSetTableCollapsed = (state: AppSettingsState) => state.setTableCollapsed;
export const selectResetSettings = (state: AppSettingsState) => state.resetSettings;
export const selectSetGlobalSearchTerm = (state: AppSettingsState) => state.setGlobalSearchTerm;

// --- Селекторы для меток на шкалах ---
export const selectChartScaleLabelBackgroundColor = (state: AppSettingsState) => state.chartScaleLabelBackgroundColor;
export const selectChartScaleLabelTextColor = (state: AppSettingsState) => state.chartScaleLabelTextColor;
// --- Селектор для автоскрытия шкал ---
export const selectAutoHideScalesEnabled = (state: AppSettingsState) => state.autoHideScalesEnabled;
export const selectToggleAutoHideScales = (state: AppSettingsState) => state.toggleAutoHideScales; 
export const selectSetColumnWidths = (state: AppSettingsState) => state.setColumnWidths; 
export const selectSelectedInterval = (state: AppSettingsState) => state.selectedInterval; // Renamed from selectActiveTimeframe
export const selectAvailablePairs = (state: AppSettingsState) => state.availablePairs;

// --- Селекторы для индикаторных колонок ---
export const selectIndicatorColumns = (state: AppSettingsState) => state.indicatorColumns;
export const selectAddIndicatorColumn = (state: AppSettingsState) => state.addIndicatorColumn;
export const selectRemoveIndicatorColumn = (state: AppSettingsState) => state.removeIndicatorColumn;
export const selectUpdateIndicatorColumn = (state: AppSettingsState) => state.updateIndicatorColumn;

// --- Селекторы для управления режимами ---
export const selectModeControl = (state: AppSettingsState) => state.modeControl;
export const selectViewMode = (state: AppSettingsState) => state.modeControl.currentMode;
export const selectScreenerSettings = (state: AppSettingsState) => state.modeControl.screenerSettings;
export const selectSetViewMode = (state: AppSettingsState) => state.setViewMode;
export const selectUpdateScreenerSettings = (state: AppSettingsState) => state.updateScreenerSettings;
export const selectLayoutTypesForModes = (state: AppSettingsState) => state.modeControl.layoutTypes;
export const selectLayoutTypeForCurrentMode = (state: AppSettingsState) => {
  const mode = state.modeControl.currentMode;
  return state.modeControl.layoutTypes[mode] || defaultSettings.modeControl.layoutTypes[mode] || defaultSettings.layoutType; // Fallback to global layoutType
};
export const selectSetLayoutTypeForMode = (state: AppSettingsState) => state.setLayoutTypeForMode;

export const useAppSettingsStore = settingsStore;
export default useAppSettingsStore; 