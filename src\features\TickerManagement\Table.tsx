"use client"

import React, { useRef, useEffect, KeyboardEvent, useCallback, useMemo, useTransition, CSSProperties, memo, useState } from 'react';
import {
  ColumnDef, ColumnSizingState, Header, Cell, OnChangeFn,
  SortingState, flexRender, getCoreRowModel, getSortedRowModel,
  getFilteredRowModel, useReactTable, Row, RowData, TableMeta
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { clsx } from 'clsx';
import { throttle } from 'throttle-debounce';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Identifier } from 'dnd-core';
import { Skeleton } from '@/shared/ui/skeleton';
import { Ticker, MarketType, ProcessedTicker, ColumnWidths, IndicatorColumnConfig, CalculatedIndicatorResult, WSConnectionStatus as ConnectionStatus, KlineInterval, TradingViewSymbol } from '@/shared/index';
import { 
  useAppSettingsStore, 
  selectTableCompactness,
  selectUiFontSize, 
  selectUiFontFamily, 
  selectShowVolumeInUSD, 
  selectIndicatorColumns, 
  selectMarketTypes 
} from '@/shared/store/settingsStore';
import { useMarketStore, DEFAULT_MARKET_TYPE, useWebSocketConnection } from '@/shared/index';
import { useTableStateConfig } from './useTableUIstate';
import { useProcessedTableData, useIndicatorDataForTable } from './useTableData';

// --- Constants ---
const DND_ITEM_TYPE = 'column';
const BASE_BODY_ROW_HEIGHT = 34;
const MIN_BODY_ROW_HEIGHT = 20;
const BASE_BODY_CELL_PADDING_X = 8;
const MIN_BODY_CELL_PADDING_X = 4;
const BASE_HEADER_CELL_PADDING_X = 6;
const MIN_HEADER_CELL_PADDING_X = 4;
const SKELETON_ROW_COUNT = 40;

// --- Helper Functions ---
const formatNumber = (value: number | null | undefined, fractionDigits = 2): string =>
  (value === null || value === undefined || isNaN(value)) ? '-' : value.toFixed(fractionDigits);

const formatPercent = (value: number | null | undefined): string =>
  (value === null || value === undefined || isNaN(value) || typeof value !== 'number') ? '-' : `${value.toFixed(2)}%`;

const formatAbbreviatedInteger = (value: number | null | undefined): string => {
  if (value === null || value === undefined || isNaN(value)) return '-';
  if (value === 0) return '0';

  const absValue = Math.abs(value);
  const sign = value < 0 ? '-' : '';
  const roundedValue = Math.round(absValue);

  if (roundedValue >= 1e12) return `${sign}${Math.round(roundedValue / 1e12)}T`;
  if (roundedValue >= 1e9)  return `${sign}${Math.round(roundedValue / 1e9)}B`;
  if (roundedValue >= 1e6)  return `${sign}${Math.round(roundedValue / 1e6)}M`;
  if (roundedValue >= 1e3)  return `${sign}${Math.round(roundedValue / 1e3)}K`;

  return `${sign}${roundedValue}`;
};

const formatDynamicPrecisionPrice = (value: number | null | undefined): string => {
  if (value === null || value === undefined || isNaN(value)) return '-';

  const maxPrecision = 8;
  const minPrecision = 2;
  let numStr = value.toFixed(maxPrecision);

  if (!numStr.includes('.')) return value.toFixed(0);

  while (numStr.endsWith('0') && numStr.length - numStr.indexOf('.') - 1 > minPrecision) {
    numStr = numStr.slice(0, -1);
  }
  return numStr.endsWith('.') ? numStr.slice(0, -1) : numStr;
};

// --- Table Row Cell Rendering ---
const renderCellContent = (
  cell: Cell<ProcessedTicker, unknown>,
  selectedPairs: string[],
  indicatorColumns: IndicatorColumnConfig[],
  indicatorDataMap: Map<string, Map<string, any>>,
  indicatorQueryStatusMap: Map<string, 'pending' | 'success' | 'error'>
): React.ReactNode => {
  const { column, row, getContext } = cell;
  const columnId = column.id;
  const cellValue = cell.getValue();
  const originalData = row.original as ProcessedTicker;

  const indicatorConfig = indicatorColumns.find(col => col.instanceId === columnId);

  if (indicatorConfig) {
    const indicatorDataKey = `${indicatorConfig.indicatorId}_${indicatorConfig.timeframe || '1h'}_${originalData.marketType}`;
    const queryStatus = indicatorQueryStatusMap.get(indicatorDataKey);

    if (queryStatus === undefined || queryStatus === 'pending') {
      return <Skeleton className="h-4 w-10" />;
    } else if (queryStatus === 'error') {
      return <span className="text-destructive text-xs" title="Error loading data">Err</span>;
    }

    const dataForSymbol = indicatorDataMap.get(indicatorDataKey)?.get(originalData.symbol);
    
    let value = null;
    
    if (dataForSymbol) {
      if (dataForSymbol.values && indicatorConfig.outputId in dataForSymbol.values) {
        value = dataForSymbol.values[indicatorConfig.outputId];
      } else if (dataForSymbol.data && dataForSymbol.data.length > 0) {
        const lastDataPoint = dataForSymbol.data[dataForSymbol.data.length - 1];
        if (lastDataPoint && indicatorConfig.outputId in lastDataPoint) {
          value = lastDataPoint[indicatorConfig.outputId];
        }
      }
    }
    
    if (value !== null && value !== undefined) {
      if (typeof value === 'number') return formatNumber(value, 2);
      return String(value);
    } else {
      return '-';
    }
  }

  switch (columnId) {
    case 'symbol': {
      const originalSymbol = cellValue as string;
      if (selectedPairs.length === 1 && selectedPairs[0] !== 'OTHR' && originalSymbol.endsWith(selectedPairs[0])) {
        return originalSymbol.slice(0, -selectedPairs[0].length);
      }
      return originalSymbol;
    }
    case 'price':
      return formatDynamicPrecisionPrice(cellValue as number | null);
    case 'volume':
      return formatAbbreviatedInteger(cellValue as number | null);
    case 'trade':
      return formatAbbreviatedInteger(originalData.count); 
    case 'spread':
      return formatPercent(cellValue as number | null);
    case 'change': {
      const value = cellValue as number | null;
      if (value === null || isNaN(value)) return '-';
      const colorClass = value > 0 ? 'text-positive' : value < 0 ? 'text-negative' : '';
      return <span className={colorClass}>{formatPercent(value)}</span>;
    }
    default:
      return flexRender(column.columnDef.cell, getContext());
  }
};

// --- Table Row Component ---
interface TableRowComponentProps {
  virtualRow: ReturnType<ReturnType<typeof useVirtualizer<HTMLDivElement, Element>>['getVirtualItems']>[number];
  row: Row<ProcessedTicker>;
  isSelected: boolean;
  bodyCellPaddingX: number;
  handleRowClick: (symbol: string) => void;
  selectedPairs: string[];
  indicatorColumns: IndicatorColumnConfig[];
  indicatorDataMap: Map<string, Map<string, any>>;
  indicatorQueryStatusMap: Map<string, 'pending' | 'success' | 'error'>;
  renderCell: typeof renderCellContent;
}

const TableRowComponent = memo(({
  virtualRow,
  row,
  isSelected,
  bodyCellPaddingX,
  handleRowClick,
  selectedPairs,
  indicatorColumns,
  indicatorDataMap,
  indicatorQueryStatusMap,
  renderCell
}: TableRowComponentProps) => {
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    handleRowClick(row.original.symbol);
  }, [handleRowClick, row.original.symbol]);

  const rowStyle = useMemo<CSSProperties>(() => ({
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: `${virtualRow.size}px`,
    transform: `translateY(${virtualRow.start}px)`,
    contain: 'layout style',
  }), [virtualRow.size, virtualRow.start]);

  return (
    <div
      style={rowStyle}
      className={clsx(
        'flex w-full items-center group transition-none',
        'hover:bg-primary/5',
        isSelected && 'bg-primary/10',
        'cursor-pointer'
      )}
      onClick={handleClick}
      data-index={virtualRow.index}
      role="row"
    >
      {isSelected && <div className="absolute left-0 top-[5%] bottom-[5%] w-0.5 bg-primary rounded-r-sm" />}
      {row.getVisibleCells().map((cell) => {
        const meta = cell.column.columnDef.meta as { textAlignment?: string } | undefined;
        const isCentered = meta?.textAlignment === 'text-center';
        const cellContent = renderCell(
          cell,
          selectedPairs,
          indicatorColumns,
          indicatorDataMap,
          indicatorQueryStatusMap
        );

        return (
          <div
            key={cell.id}
            className={clsx(
              'overflow-hidden whitespace-nowrap align-middle h-full flex',
              isCentered ? 'justify-center items-center' : 'items-center',
              meta?.textAlignment ?? 'text-left'
            )}
            style={{
              width: cell.column.getSize(),
              paddingLeft: `${bodyCellPaddingX}px`,
              paddingRight: `${bodyCellPaddingX}px`,
            }}
            title={typeof cellContent === 'string' ? cellContent : String(cell.getValue() ?? '')}
            role="gridcell"
          >
            {cellContent}
          </div>
        );
      })}
    </div>
  );
}, (prevProps, nextProps) => {
  if (prevProps.isSelected !== nextProps.isSelected) return false;
  if (prevProps.bodyCellPaddingX !== nextProps.bodyCellPaddingX) return false;
  
  if (prevProps.virtualRow.index !== nextProps.virtualRow.index ||
      prevProps.virtualRow.size !== nextProps.virtualRow.size ||
      prevProps.virtualRow.start !== nextProps.virtualRow.start) {
    return false;
  }

  if (prevProps.row.original !== nextProps.row.original) {
    return false;
  }

  if (prevProps.selectedPairs.length !== nextProps.selectedPairs.length ||
      !prevProps.selectedPairs.every((val, idx) => val === nextProps.selectedPairs[idx])) {
    return false;
  }
  
  if (prevProps.indicatorColumns.length !== nextProps.indicatorColumns.length ||
      !prevProps.indicatorColumns.every((prevCol, idx) => {
        const nextCol = nextProps.indicatorColumns[idx];
        return prevCol.instanceId === nextCol.instanceId && 
               prevCol.outputId === nextCol.outputId &&
               prevCol.timeframe === nextCol.timeframe &&
               prevCol.name === nextCol.name;
      })
  ) {
    return false;
  }

  const symbol = nextProps.row.original.symbol; 
  for (const col of nextProps.indicatorColumns) {
    const key = `${col.indicatorId}_${col.timeframe || '1h'}_${nextProps.row.original.marketType}`;
    
    const prevIndicatorMapForKey = prevProps.indicatorDataMap.get(key);
    const nextIndicatorMapForKey = nextProps.indicatorDataMap.get(key);
    const prevStatus = prevProps.indicatorQueryStatusMap.get(key);
    const nextStatus = nextProps.indicatorQueryStatusMap.get(key);

    if (prevStatus !== nextStatus) return false;

    if (nextStatus === 'pending' || nextStatus === 'error') {
        continue;
    }

    if (prevIndicatorMapForKey !== nextIndicatorMapForKey) {
      const prevData = prevIndicatorMapForKey?.get(symbol);
      const nextData = nextIndicatorMapForKey?.get(symbol);
      if (prevData !== nextData) return false; 
    } else if (prevIndicatorMapForKey && nextIndicatorMapForKey) { 
       const prevData = prevIndicatorMapForKey.get(symbol);
       const nextData = nextIndicatorMapForKey.get(symbol);
       if (prevData !== nextData) return false;
    }
  }
  
  return true;
});

TableRowComponent.displayName = 'TableRowComponent';

// --- Table Header Cell with DND ---
interface DragItem { id: string; type: string; }
interface TableHeaderCellProps {
  header: Header<ProcessedTicker, unknown>;
  columnOrder: string[];
  reorderColumn: (draggedId: string, targetId: string) => void;
}

const TableHeaderCellComponent: React.FC<TableHeaderCellProps> = ({ header, columnOrder, reorderColumn }) => {
  const ref = useRef<HTMLDivElement>(null);
  const dragRef = useRef<HTMLDivElement>(null);
  const { column } = header;
  const { id } = column;
  const meta = column.columnDef.meta as { textAlignment?: string } | undefined;
  const compactnessFactor = header.getContext().table.options.meta?.compactnessFactor ?? 0;

  const [{ handlerId }, drop] = useDrop<DragItem, void, { handlerId: Identifier | null }>({
    accept: DND_ITEM_TYPE,
    hover: (item: DragItem, monitor) => {
      if (!ref.current || item.id === id) return;
      const dragId = item.id;
      const hoverId = id;
      const hoverBoundingRect = ref.current.getBoundingClientRect();
      const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;
      const hoverClientX = clientOffset.x - hoverBoundingRect.left;
      const dragIndex = columnOrder.indexOf(dragId);
      const hoverIndex = columnOrder.indexOf(hoverId);
      if ((dragIndex < hoverIndex && hoverClientX < hoverMiddleX) ||
          (dragIndex > hoverIndex && hoverClientX > hoverMiddleX)) {
        return;
      }
      reorderColumn(dragId, hoverId);
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: DND_ITEM_TYPE,
    item: () => ({ id, type: DND_ITEM_TYPE }),
    collect: (monitor) => ({ isDragging: monitor.isDragging() }),
  });

  drop(ref);
  drag(dragRef);

  const isResizing = column.getIsResizing();
  const throttledResizeHandler = useMemo(() => throttle(125, header.getResizeHandler()), [header]);

  const delayedSortHandler = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (column.getCanSort()) {
      header.column.getToggleSortingHandler()?.(e);
    }
  }, [header, column]);

  const headerCellPaddingX = useMemo(() =>
    Math.round(BASE_HEADER_CELL_PADDING_X - (BASE_HEADER_CELL_PADDING_X - MIN_HEADER_CELL_PADDING_X) * compactnessFactor)
  , [compactnessFactor]);

  const getSortTooltip = () => {
    if (!column.getCanSort()) return undefined;
    switch (column.getNextSortingOrder()) {
      case 'asc': return 'Sort ascending';
      case 'desc': return 'Sort descending';
      default: return 'Clear sort';
    }
  };

  return (
    <div
      ref={ref}
      key={id}
      className="relative flex items-center select-none group"
      style={{ width: header.getSize(), opacity: isDragging ? 0.5 : 1 }}
      data-column-id={id}
      data-handler-id={handlerId}
      role="columnheader"
      aria-sort={column.getIsSorted() === 'asc' ? 'ascending' : column.getIsSorted() === 'desc' ? 'descending' : undefined}
    >
      <div className="flex items-center w-full h-full overflow-hidden">
        <div
          ref={dragRef}
          className={clsx(
            'flex items-center flex-grow h-full overflow-hidden whitespace-nowrap',
            column.getCanSort() ? 'cursor-pointer' : 'cursor-move',
            meta?.textAlignment === 'text-center' && 'justify-center'
          )}
          style={{
            paddingLeft: `${headerCellPaddingX}px`,
            paddingRight: `${headerCellPaddingX}px`
          }}
          onClick={delayedSortHandler}
          title={getSortTooltip()}
        >
          {flexRender(column.columnDef.header, header.getContext())}
          {column.getIsSorted() && (
            <div className="absolute bottom-0 left-0 right-0 h-px bg-primary"></div>
          )}
        </div>

        {column.getCanResize() && (
          <div
            onMouseDown={(e) => { e.stopPropagation(); throttledResizeHandler(e); }}
            onTouchStart={(e) => { e.stopPropagation(); throttledResizeHandler(e); }}
            className={clsx(
              'absolute top-0 right-0 h-full w-2 cursor-ew-resize select-none touch-none z-10',
              'bg-transparent group-hover:bg-primary/20 active:bg-primary/40 transition-colors duration-150',
              isResizing && 'bg-primary/40'
            )}
            onClick={(e) => e.stopPropagation()}
            role="separator"
            aria-orientation="vertical"
          >
            <div className={clsx(
              "absolute top-[15%] bottom-[15%] right-[3px] w-px transition-colors duration-150 ease-in-out",
              "bg-transparent group-hover:bg-primary/50",
              isResizing && "bg-primary/50"
            )}></div>
          </div>
        )}
      </div>
    </div>
  );
};

const TableHeaderCell = memo(TableHeaderCellComponent);
TableHeaderCell.displayName = 'TableHeaderCell';

// --- Main TickerTable Component ---
interface TickerTableProps {
  searchQuery: string;
  aggregateVolumeAndTrades: boolean;
  onCountsChange: (counts: { total: number; filtered: number }) => void;
  onSymbolSelect: (symbol: string) => void;
  selectedSymbol: string | null;
}

export const TickerTable = memo<TickerTableProps>(({
  searchQuery,
  aggregateVolumeAndTrades,
  onCountsChange,
  onSymbolSelect,
  selectedSymbol: externalSelectedSymbol,
}) => {
  const parentRef = useRef<HTMLDivElement>(null);
  const [isProcessingInteraction, startTransition] = useTransition();
  const [hasDataEverLoaded, setHasDataEverLoaded] = useState(false);

  const indicatorColumns = useAppSettingsStore(selectIndicatorColumns);
  const tableCompactness = useAppSettingsStore(selectTableCompactness);
  const uiFontSize = useAppSettingsStore(selectUiFontSize);
  const uiFontFamily = useAppSettingsStore(selectUiFontFamily);
  const showVolumeInUSD = useAppSettingsStore(selectShowVolumeInUSD);
  const selectedPairsFromStore = useAppSettingsStore(state => state.selectedPairs);
  const selectedMarketTypesFromStore = useAppSettingsStore(selectMarketTypes);

  const spotTickers = useMarketStore(state => state.processedSpotTickers);
  const futuresTickers = useMarketStore(state => state.processedFuturesTickers);

  const {
    tableSortingState,
    columnOrder, 
    tableVisibilityState,
    tableSizingState,
    updateZustandSortConfigs,
    setZustandColumnOrder,
    setZustandColumnSizing
  } = useTableStateConfig();

  const { combinedTickers, finalFilteredData } = useProcessedTableData({
    searchQuery,
    aggregateVolumeAndTrades,
    selectedPairs: selectedPairsFromStore,
  });

  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);
  const { indicatorDataMap, indicatorQueryStatusMap } = useIndicatorDataForTable();
  const { status: wsStatus } = useWebSocketConnection();
  
  useEffect(() => {
    const marketsToSubscribe = selectedMarketTypesFromStore || [];
    if (marketsToSubscribe.length > 0) {
        useMarketStore.getState().setSelectedMarketType(marketsToSubscribe[0]);
    } else {
        useMarketStore.getState().setSelectedMarketType(DEFAULT_MARKET_TYPE);
    }
    // No explicit cleanup for setSelectedMarketType, it just sets the state.
    // If WebSocket subscriptions were managed here, cleanup would be needed.
  }, [selectedMarketTypesFromStore]); 
  
  useEffect(() => {
    console.log(`[TickerTable] WebSocket status: ${wsStatus}`);
  }, [wsStatus]);
  
  useEffect(() => {
    if (hasDataEverLoaded) {
      setLastUpdateTime(new Date());
    }
  }, [spotTickers, futuresTickers, hasDataEverLoaded]); 

  const compactnessFactor = useMemo(() =>
    Math.max(0, Math.min(1, (tableCompactness ?? 0) / 20)),
  [tableCompactness]);

  const bodyRowHeight = useMemo(() =>
    Math.round(BASE_BODY_ROW_HEIGHT - (BASE_BODY_ROW_HEIGHT - MIN_BODY_ROW_HEIGHT) * compactnessFactor),
  [compactnessFactor]);

  const bodyCellPaddingX = useMemo(() =>
    Math.round(BASE_BODY_CELL_PADDING_X - (BASE_BODY_CELL_PADDING_X - MIN_BODY_CELL_PADDING_X) * compactnessFactor),
  [compactnessFactor]);
  
  const headerRowHeight = useMemo(() => 36, []); 
  
  const fontSizeClass = useMemo(() => {
    if (uiFontSize <= 12) return 'text-xs';
    if (uiFontSize <= 14) return 'text-sm';
    return 'text-base';
  }, [uiFontSize]);
  
  const fontFamilyClass = useMemo(() => {
    switch (uiFontFamily) {
      case 'mono': return 'font-mono';
      case 'serif': return 'font-serif';
      default: return 'font-sans';
    }
  }, [uiFontFamily]);
  
  const compactClasses = useMemo(() => {
    if (compactnessFactor > 0.7) return 'compact-extreme';
    if (compactnessFactor > 0.3) return 'compact-medium';
    return 'compact-normal';
  }, [compactnessFactor]);

  const tableStyles = useMemo<CSSProperties>(() => ({
    '--table-body-row-height': `${bodyRowHeight}px`,
    '--table-body-cell-padding-x': `${bodyCellPaddingX}px`,
    '--table-header-cell-padding-x': `${BASE_HEADER_CELL_PADDING_X}px`,
    fontSize: `${uiFontSize}px`,
    fontFamily: uiFontFamily,
  }), [bodyRowHeight, bodyCellPaddingX, uiFontSize, uiFontFamily]);

  const columns = useMemo<ColumnDef<ProcessedTicker>[]>(() => {
    const standardColumns: ColumnDef<ProcessedTicker>[] = [
      {
        accessorKey: 'symbol',
        header: ({ table }) => (
          <>
            Ticker
            <span className="text-muted-foreground text-[10px] ml-1">
              ({table.getFilteredRowModel().rows.length})
            </span>
          </>
        ),
        cell: info => info.getValue<string>(),
        size: 150,
        id: 'symbol',
        enableResizing: true,
        enableSorting: true,
      },
      {
        accessorKey: 'price',
        header: 'Price',
        cell: info => info.getValue<number>(),
        size: 100,
        id: 'price',
        meta: { textAlignment: 'text-right' },
        enableResizing: true,
        enableSorting: true,
      },
      {
        accessorKey: 'priceChangePercent',
        header: 'Cng %',
        cell: info => info.getValue<number>(),
        size: 90,
        id: 'change',
        meta: { textAlignment: 'text-center' },
        enableResizing: true,
        enableSorting: true,
      },
      {
        accessorKey: showVolumeInUSD ? 'quoteVolume' : 'volume',
        header: showVolumeInUSD ? 'Vol $' : 'Vol',
        cell: info => info.getValue<number>(),
        size: 130,
        id: 'volume',
        meta: { textAlignment: 'text-center' },
        enableResizing: true,
        enableSorting: true,
      },
      {
        accessorKey: 'count', // Corrected from trades
        header: 'Trades',
        cell: info => info.getValue<number>(),
        size: 100,
        id: 'trade',
        meta: { textAlignment: 'text-center' },
        enableResizing: true,
        enableSorting: true,
      },
      {
        accessorKey: 'spread',
        header: 'Spread %',
        cell: info => info.getValue<number>(),
        size: 90,
        id: 'spread',
        meta: { textAlignment: 'text-right' },
        enableResizing: true,
        enableSorting: true,
      },
    ];

    const dynamicIndicatorColumns: ColumnDef<ProcessedTicker>[] = indicatorColumns.map(config => ({
      id: config.instanceId, 
      header: `${config.name} (${config.timeframe})`,
      cell: info => null, 
      size: 100, 
      meta: { textAlignment: 'text-right' }, 
      enableResizing: true,
      enableSorting: false, 
    }));
    return [...standardColumns, ...dynamicIndicatorColumns];
  }, [showVolumeInUSD, indicatorColumns]); 

  const tableMeta = useMemo<TableMeta<ProcessedTicker>>(() => ({
    selectedTickerSymbol: externalSelectedSymbol,
    setSelectedTickerSymbol: onSymbolSelect,
    selectedPairs: selectedPairsFromStore,
    compactnessFactor: compactnessFactor,
    indicatorColumns: indicatorColumns, 
    indicatorDataMap: indicatorDataMap, 
    indicatorQueryStatusMap: indicatorQueryStatusMap, 
  }), [
    externalSelectedSymbol, onSymbolSelect, selectedPairsFromStore,
    compactnessFactor, indicatorColumns, 
    indicatorDataMap, indicatorQueryStatusMap, 
  ]);

  const table = useReactTable({
    data: finalFilteredData,
    columns, 
    state: {
      sorting: tableSortingState,
      columnOrder,
      columnVisibility: tableVisibilityState,
      columnSizing: tableSizingState,
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    columnResizeMode: 'onChange',
    enableSorting: true,
    enableColumnResizing: true,
    enableSortingRemoval: false,
    meta: tableMeta, 
  });

  const { rows } = table.getRowModel();
  const totalSize = table.getTotalSize(); 

  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => bodyRowHeight, 
    overscan: 10, 
  });

  useEffect(() => {
    rowVirtualizer.measure();
  }, [bodyRowHeight, rowVirtualizer]);

  const virtualItems = rowVirtualizer.getVirtualItems();
  const totalVirtualHeight = rowVirtualizer.getTotalSize();

  const handleRowClick = useCallback((symbol: string) => {
    onSymbolSelect(symbol);
  }, [onSymbolSelect]);

  const selectedRowIndex = useMemo(() => {
    if (!externalSelectedSymbol) return -1;
    return rows.findIndex(row => row.original.symbol === externalSelectedSymbol);
  }, [rows, externalSelectedSymbol]);

  const handleKeyDown = useCallback((event: KeyboardEvent<HTMLDivElement>) => {
    if (!rows.length || !parentRef.current) return;
    const currentIndex = selectedRowIndex;
    let nextIndex = currentIndex;
    if (event.key === 'ArrowUp') {
      event.preventDefault();
      nextIndex = Math.max(0, currentIndex - 1);
    } else if (event.key === 'ArrowDown') {
      event.preventDefault();
      nextIndex = currentIndex === -1 ? 0 : Math.min(rows.length - 1, currentIndex + 1);
    } else {
      return; 
    }
    if (nextIndex !== currentIndex && nextIndex >= 0 && nextIndex < rows.length) {
      const nextSymbol = rows[nextIndex]?.original?.symbol;
      if (nextSymbol) {
        onSymbolSelect(nextSymbol);
        rowVirtualizer.scrollToIndex?.(nextIndex, { align: 'auto', behavior: 'smooth' });
      }
    }
  }, [rows, selectedRowIndex, onSymbolSelect, rowVirtualizer]);

  const reorderColumn = useCallback((draggedId: string, targetId: string) => {
    if (draggedId === targetId) return;
    startTransition(() => {
      const currentOrder = table.getState().columnOrder;
      const dragIndex = currentOrder.indexOf(draggedId);
      const targetIndex = currentOrder.indexOf(targetId);
      if (dragIndex !== -1 && targetIndex !== -1) {
        const newOrder = [...currentOrder];
        newOrder.splice(dragIndex, 1);
        newOrder.splice(targetIndex, 0, draggedId);
        setZustandColumnOrder(newOrder);
      }
    });
  }, [table, setZustandColumnOrder, startTransition]);

  const handleSortingChange: OnChangeFn<SortingState> = useCallback((updaterOrValue) => {
    startTransition(() => {
      const newSortingState = typeof updaterOrValue === 'function'
        ? updaterOrValue(table.getState().sorting)
        : updaterOrValue;
      updateZustandSortConfigs(newSortingState);
      if (newSortingState.length > 0) {
        rowVirtualizer.scrollToIndex?.(0, { align: 'start', behavior: 'auto' });
      } else {
        console.warn("handleSortingChange called with empty state despite enableSortingRemoval=false");
      }
    });
  }, [table, updateZustandSortConfigs, startTransition, rowVirtualizer]);

  const handleColumnSizingChange: OnChangeFn<ColumnSizingState> = useCallback((updater) => {
    startTransition(() => {
        const newSizingState = typeof updater === 'function' ? updater(table.getState().columnSizing) : updater;
        const currentZustandSizing = useAppSettingsStore.getState().columnWidths;
        const newFullSizing: ColumnWidths = { ...currentZustandSizing };
        for (const key in newSizingState) {
            if (Object.prototype.hasOwnProperty.call(newSizingState, key) && key !== 'isResizing') {
                const value = newSizingState[key];
                if (typeof value === 'number') {
                    (newFullSizing as any)[key] = value;
                }
            }
        }
        setZustandColumnSizing(newFullSizing);
    });
  }, [table, setZustandColumnSizing, startTransition]);

  useEffect(() => {
    table.setOptions(prev => ({
      ...prev,
      onSortingChange: handleSortingChange,
      onColumnSizingChange: handleColumnSizingChange,
      meta: {
        selectedTickerSymbol: externalSelectedSymbol,
        setSelectedTickerSymbol: onSymbolSelect,
        selectedPairs: selectedPairsFromStore,
        compactnessFactor: compactnessFactor,
        indicatorColumns: indicatorColumns,
        indicatorDataMap: indicatorDataMap,
        indicatorQueryStatusMap: indicatorQueryStatusMap,
      }
    }));
  }, [
    table, handleSortingChange, handleColumnSizingChange,
    externalSelectedSymbol, onSymbolSelect, selectedPairsFromStore,
    compactnessFactor, indicatorColumns,
    indicatorDataMap, indicatorQueryStatusMap,
  ]);

  const renderLoadingSkeletons = () => (
    <div className="w-full h-full flex flex-col justify-start p-1 overflow-hidden">
      {Array.from({ length: SKELETON_ROW_COUNT }).map((_, i) => (
          <div
            key={`skeleton-row-${i}`}
            className="flex items-center shrink-0"
            style={{
            height: `${bodyRowHeight}px`,
            paddingLeft: `${bodyCellPaddingX}px`,
            paddingRight: `${bodyCellPaddingX}px`,
           }}
        >
          <Skeleton className={clsx(
              "h-[60%] w-full",
              isProcessingInteraction && "opacity-50 transition-opacity duration-300"
          )} />
          </div>
        ))}
      </div>
    );

  const renderEmptyState = () => {
    const hasOriginalData = combinedTickers.length > 0;
    let message = "No results found.";
    if (!hasOriginalData && !hasDataEverLoaded) {
        message = "Loading data...";
    } else if (!hasOriginalData) {
      message = "No data available.";
    } else if (searchQuery && finalFilteredData.length === 0) {
      message = `No tickers match '${searchQuery}'.`;
    } else if (finalFilteredData.length === 0) {
      message = "No tickers match current filters.";
    }
    return (
      <div className="absolute inset-0 flex items-center justify-center h-full text-muted-foreground">
        {message}
      </div>
    );
  };

  const renderTableBody = () => {
    const showSkeleton = !hasDataEverLoaded && combinedTickers.length === 0;
    const showEmptyState = !showSkeleton && rows.length === 0;
    const containerHeight = showSkeleton ? '100%' : (rows.length > 0 ? `${totalVirtualHeight}px` : '100%');

    return (
      <div
        ref={parentRef}
        className={clsx(
          "flex-grow relative outline-none h-full overflow-y-auto overflow-x-hidden hide-native-scrollbar",
        )}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="grid"
        aria-rowcount={rows.length}
        style={{ willChange: 'transform' }}
      >
        <div
          className="relative w-full"
          style={{
            minHeight: showSkeleton || showEmptyState ? '300px' : `${totalVirtualHeight}px`,
            height: containerHeight,
          }}
        >
          {showSkeleton ? renderLoadingSkeletons() :
           showEmptyState ? renderEmptyState() :
            virtualItems.map((virtualRow) => {
              const row = rows[virtualRow.index];
              if (!row) {
                console.warn(`TableRowComponent requested for non-existent row at index ${virtualRow.index}`);
                return null;
              }
              const isSelected = row.original.symbol === externalSelectedSymbol;
              return (
                <TableRowComponent
                  key={row.original.symbol}
                  virtualRow={virtualRow}
                  row={row}
                  isSelected={isSelected}
                  bodyCellPaddingX={bodyCellPaddingX}
                  handleRowClick={handleRowClick}
                  selectedPairs={selectedPairsFromStore}
                  indicatorColumns={indicatorColumns}
                  indicatorDataMap={indicatorDataMap}
                  indicatorQueryStatusMap={indicatorQueryStatusMap}
                  renderCell={renderCellContent}
                />
              );
            })
          }
        </div>
      </div>
    );
  };

  useEffect(() => {

    // Update counts for parent component
    onCountsChange({
      total: combinedTickers.length,
      filtered: finalFilteredData.length
    });
    
    // Set hasDataEverLoaded flag when we receive data
    if (combinedTickers.length > 0 && !hasDataEverLoaded) {
      setHasDataEverLoaded(true);
    }
  }, [combinedTickers, finalFilteredData, hasDataEverLoaded]);

  return (
    <div
      className={clsx(
        'h-full w-full flex flex-col overflow-hidden',
        compactClasses,
        'text-sm',
        fontSizeClass,
        fontFamilyClass,
        isProcessingInteraction && 'opacity-80'
      )}
      style={{ minHeight: '100px' }}
    >
      <DndProvider backend={HTML5Backend}>
        <div
          className="shrink-0 flex overflow-hidden"
          style={{
            height: `${headerRowHeight}px`,
            width: `${table.getTotalSize()}px`,
          }}
        >
          {table.getHeaderGroups().map((headerGroup) => (
            <div
              key={headerGroup.id}
              className="flex h-9 border-b border-border"
              style={{ width: `${totalSize}px` }}
            >
              {headerGroup.headers.map((header) => (
                <TableHeaderCell
                  key={header.id}
                  header={header}
                  columnOrder={columnOrder}
                  reorderColumn={reorderColumn}
                />
              ))}
            </div>
          ))}
        </div>
      </DndProvider>
      {renderTableBody()}
    </div>
  );
});

TickerTable.displayName = 'TickerTable';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    selectedTickerSymbol: string | null;
    setSelectedTickerSymbol: (symbol: string) => void;
    selectedPairs: string[];
    compactnessFactor?: number;
    indicatorColumns?: IndicatorColumnConfig[];
    indicatorDataMap?: Map<string, Map<string, any>>;
    indicatorQueryStatusMap?: Map<string, 'pending' | 'success' | 'error'>;
  }
}
