import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Utility function to merge Tailwind CSS classes with clsx.
 * @param inputs - Class values to merge.
 * @returns Merged class string.
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Throttles a function, ensuring it's called at most once per limit milliseconds.
 * @param func - The function to throttle.
 * @param limit - The throttle limit in milliseconds.
 * @returns A throttled version of the function.
 */
export function throttle<F extends (...args: any[]) => any>(func: F, limit: number) {
    let inThrottle: boolean;
    let lastResult: ReturnType<F>;
  
    return function (this: ThisParameterType<F>, ...args: Parameters<F>): ReturnType<F> {
      if (!inThrottle) {
        inThrottle = true;
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const context = this;
        setTimeout(() => (inThrottle = false), limit);
        lastResult = func.apply(context, args);
      }
      return lastResult;
    };
  }
  
  /**
   * Debounces a function, delaying its execution until after wait milliseconds have elapsed
   * since the last time it was invoked.
   * @param func - The function to debounce.
   * @param wait - The debounce wait time in milliseconds.
   * @returns A debounced version of the function.
   */
  export function debounce<F extends (...args: any[]) => any>(func: F, wait: number) {
    let timeout: NodeJS.Timeout | undefined;
    let lastResult: ReturnType<F>;
  
    return function (this: ThisParameterType<F>, ...args: Parameters<F>): ReturnType<F> {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const context = this;
      const later = () => {
        timeout = undefined;
        lastResult = func.apply(context, args);
      };
  
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      return lastResult; // This will return undefined if the function has not been called yet
    };
  } 


  export const hexFromRgba = (rgbaString: string): string => {
    const match = rgbaString.match(
      /^rgba?\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})(?:,\s*(\d(?:\.\d+)?))?\)$/
    );
    if (!match) {
      // Return a default color or throw an error if the format is unexpected
      console.warn(`Invalid RGBA string format: ${rgbaString}. Defaulting to #000000.`);
      return '#000000';
    }
  
    const r = parseInt(match[1], 10);
    const g = parseInt(match[2], 10);
    const b = parseInt(match[3], 10);
    // Alpha is optional, defaults to 1 if not present or invalid
    const a = match[4] ? parseFloat(match[4]) : 1;
  
    // Clamp alpha to [0, 1]
    const clampedAlpha = Math.max(0, Math.min(1, a));
  
    const toHex = (c: number) => {
      const hex = c.toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };
  
    const hexR = toHex(r);
    const hexG = toHex(g);
    const hexB = toHex(b);
  
    if (clampedAlpha === 1) {
      return `#${hexR}${hexG}${hexB}`;
    }
    // For alpha, convert to a 0-255 range and then to hex
    const hexA = toHex(Math.round(clampedAlpha * 255));
    return `#${hexR}${hexG}${hexB}${hexA}`;
  };
  
  export const rgbaFromHex = (hex: string, alpha = 1): string => {
    if (!hex || typeof hex !== 'string') {
      console.warn(`Invalid HEX string: ${hex}. Defaulting to rgba(0,0,0,${alpha}).`);
      return `rgba(0,0,0,${alpha})`;
    }
    let hexValue = hex.replace('#', '');
  
    if (hexValue.length === 3) {
      hexValue = hexValue
        .split('')
        .map((char) => char + char)
        .join('');
    }
    
    // Handle 8-digit hex (with alpha)
    let parsedAlpha = alpha;
    if (hexValue.length === 8) {
      const alphaHex = hexValue.substring(6, 8);
      parsedAlpha = parseInt(alphaHex, 16) / 255;
      hexValue = hexValue.substring(0, 6);
    } else if (hexValue.length !== 6) {
       console.warn(`Invalid HEX string length: ${hex}. Defaulting to rgba(0,0,0,${alpha}).`);
      return `rgba(0,0,0,${alpha})`;
    }
  
    const bigint = parseInt(hexValue, 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
  
    // Clamp provided alpha to [0, 1]
    const clampedAlphaInput = Math.max(0, Math.min(1, alpha));
    // Use parsedAlpha if hex had alpha, otherwise use clamped input alpha
    const finalAlpha = hexValue.length === 8 && hex.length > 7 ? parsedAlpha : clampedAlphaInput;
  
    return `rgba(${r},${g},${b},${Number(finalAlpha.toFixed(2))})`;
  };
  
  // Placeholder for other formatting functions, assuming they exist or will be added
  export const formatPrice = (price: string | number | null | undefined): string => {
    if (price === null || price === undefined) return '-';
    const num = Number(price);
    if (isNaN(num)) return '-';
    // Basic formatting, can be expanded
    return num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 8 });
  };
  
  export const formatPercent = (percent: string | number | null | undefined): string => {
    if (percent === null || percent === undefined) return '-';
    const num = Number(percent);
    if (isNaN(num)) return '-';
    return `${num.toFixed(2)}%`;
  };
  
  export const formatVolume = (volume: string | number | null | undefined): string => {
    if (volume === null || volume === undefined) return '-';
    const num = Number(volume);
    if (isNaN(num)) return '-';
    // Basic formatting, can be expanded (e.g., K, M, B for large numbers)
    return num.toLocaleString(undefined, { maximumFractionDigits: 2 });
  };
  
  export const formatCount = (count: string | number | null | undefined): string => {
    if (count === null || count === undefined) return '-';
    const num = Number(count);
    if (isNaN(num)) return '-';
    return num.toLocaleString();
  };
  
  export const formatSpread = (spread: string | number | null | undefined): string => {
    if (spread === null || spread === undefined) return '-';
    const num = Number(spread);
    if (isNaN(num)) return '-';
    return num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 8 });
  }; 

// --- Helper function to compare map keys (moved from page.tsx) ---
export function haveSameKeys(mapA: Map<string, any>, mapB: Map<string, any>): boolean {
    if (mapA.size !== mapB.size) {
        return false;
    }
    for (const key of mapA.keys()) {
        if (!mapB.has(key)) {
            return false;
        }
    }
    return true;
}

/**
 * Type guard to check if a value is an Error object
 * @param value - The value to check
 * @returns True if the value is an Error object
 */
export function isError(value: unknown): value is Error {
    return value instanceof Error;
}

