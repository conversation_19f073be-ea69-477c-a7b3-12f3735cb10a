import { EventEmitter } from 'events';
import { logger } from '../lib/logger';
import { MarketType } from '../../shared/types';
import { 
  ExchangeService, 
  SymbolInfo, 
  FullTicker, 
  Kline, 
  KlineOptions,
  ExchangeEvent,
  TickerEvent,
  KlineEvent,
  SymbolEvent
} from './exchange.interface';

// Supported kline intervals
export const SUPPORTED_INTERVALS = [
  '1m', '3m', '5m', '15m', '30m', 
  '1h', '2h', '4h', '6h', '8h', '12h', 
  '1d', '3d', '1w', '1M'
];

// API Configuration
const API_CONFIG = {
  spot: {
    apiBaseUrl: 'https://api.binance.com/api/v3',
    wsBaseUrl: 'wss://stream.binance.com:9443/ws',
  },
  futures: {
    apiBaseUrl: 'https://fapi.binance.com/fapi/v1',
    wsBaseUrl: 'wss://fstream.binance.com/ws',
  }
};

// WebSocket configuration
const WS_CONFIG = {
  RECONNECT_DELAY_MS: 1000,
  MAX_RECONNECT_ATTEMPTS: 10,
  REQUEST_TIMEOUT_MS: 15000,
  TICKER_ARR_STREAM_NAME: '!ticker@arr',
};

// REST API configuration
const REST_CONFIG = {
  MAX_RETRIES: 3,
  INITIAL_RETRY_DELAY_MS: 1000,
};

/**
 * Helper function to get the kline stream name in Binance format
 */
function getBinanceKlineStreamName(symbol: string, interval: string): string {
  return `${symbol.toLowerCase()}@kline_${interval}`;
}

/**
 * BinanceService implementation of the ExchangeService interface
 */
export class BinanceService extends EventEmitter implements ExchangeService {
  public readonly exchangeName = 'binance';
  
  // WebSocket connections for each market type
  private wsConnections: Map<MarketType, WebSocket | null> = new Map();
  
  // Connection state tracking
  private connecting: Map<MarketType, boolean> = new Map();
  private reconnectTimers: Map<MarketType, NodeJS.Timeout | null> = new Map();
  private reconnectAttempts: Map<MarketType, number> = new Map();
  
  // Subscription tracking
  private activeSubscriptions: Map<MarketType, Set<string>> = new Map();
  
  constructor() {
    super();
    
    // Set higher limit for event listeners as we might have many subscriptions
    this.setMaxListeners(100);
    
    // Initialize maps for each market type
    const marketTypes: MarketType[] = ['spot', 'futures'];
    marketTypes.forEach(marketType => {
      this.wsConnections.set(marketType, null);
      this.connecting.set(marketType, false);
      this.reconnectTimers.set(marketType, null);
      this.reconnectAttempts.set(marketType, 0);
      this.activeSubscriptions.set(marketType, new Set());
    });
  }
  
  /**
   * Initialize the service
   */
  public async initialize(): Promise<void> {
    logger.info('[BinanceService] Initializing');
    // Nothing specific to initialize here as connections are established on-demand
    return Promise.resolve();
  }
  
  /**
   * Shutdown the service and close all connections
   */
  public async shutdown(): Promise<void> {
    logger.info('[BinanceService] Shutting down');
    
    // Close all WebSocket connections
    const marketTypes: MarketType[] = ['spot', 'futures'];
    marketTypes.forEach(marketType => {
      this.disconnect(marketType);
      
      // Clear any reconnect timers
      const timer = this.reconnectTimers.get(marketType);
      if (timer) {
        clearTimeout(timer);
        this.reconnectTimers.set(marketType, null);
      }
    });
    
    return Promise.resolve();
  }
  
  /**
   * Connect to Binance WebSocket for the specified market type
   */
  private connect(marketType: MarketType): void {
    // Skip if already connected or connecting
    if (
      this.wsConnections.get(marketType) || 
      this.connecting.get(marketType)
    ) {
      return;
    }
    
    const wsUrl = API_CONFIG[marketType].wsBaseUrl;
    logger.info(`[BinanceService] Connecting to ${marketType} WebSocket: ${wsUrl}`);
    
    this.connecting.set(marketType, true);
    
    try {
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => this.handleOpen(marketType, ws);
      ws.onmessage = (event) => this.handleMessage(marketType, event);
      ws.onclose = (event) => this.handleClose(marketType, event);
      ws.onerror = (event) => this.handleError(marketType, event);
      
      this.wsConnections.set(marketType, ws);
    } catch (error) {
      logger.error(`[BinanceService] Error creating WebSocket for ${marketType}:`, error);
      this.connecting.set(marketType, false);
      this.scheduleReconnect(marketType);
    }
  }
  
  /**
   * Disconnect from Binance WebSocket for the specified market type
   */
  private disconnect(marketType: MarketType): void {
    const ws = this.wsConnections.get(marketType);
    if (ws) {
      try {
        ws.close();
      } catch (error) {
        logger.error(`[BinanceService] Error closing ${marketType} WebSocket:`, error);
      }
      this.wsConnections.set(marketType, null);
    }
    this.connecting.set(marketType, false);
  }
  
  /**
   * Handle WebSocket open event
   */
  private handleOpen(marketType: MarketType, ws: WebSocket): void {
    logger.info(`[BinanceService] ${marketType} WebSocket connected`);
    this.connecting.set(marketType, false);
    this.reconnectAttempts.set(marketType, 0);
    
    // Resubscribe to active streams
    this.resubscribe(marketType);
  }
  
  /**
   * Handle WebSocket message event
   */
  private handleMessage(marketType: MarketType, event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data as string);
      
      // Handle different message types from Binance
      if (data.e === 'kline' && data.k) {
        // Kline update
        const kline = this.mapBinanceWsKlineToKline(data.k, marketType);
        if (kline) {
          const klineEvent: KlineEvent = {
            type: 'kline',
            marketType,
            data: kline,
            symbol: kline.symbol,
            interval: kline.interval,
            timestamp: Date.now(),
            exchange: this.exchangeName
          };
          this.emit('kline', klineEvent);
        }
      } else if (data.stream === WS_CONFIG.TICKER_ARR_STREAM_NAME && Array.isArray(data.data)) {
        // Ticker array update
        const tickers = data.data.map((ticker: any) => 
          this.mapBinanceWsTickerToFullTicker(ticker, marketType)
        );
        
        const tickerEvent: TickerEvent = {
          type: 'ticker',
          marketType,
          data: tickers,
          timestamp: Date.now(),
          exchange: this.exchangeName
        };
        this.emit('ticker', tickerEvent);
      } else if (data.e === '24hrTicker') {
        // Single ticker update
        const ticker = this.mapBinanceWsTickerToFullTicker(data, marketType);
        
        const tickerEvent: TickerEvent = {
          type: 'ticker',
          marketType,
          data: [ticker],
          timestamp: Date.now(),
          exchange: this.exchangeName
        };
        this.emit('ticker', tickerEvent);
      }
    } catch (error) {
      logger.error(`[BinanceService] Error processing ${marketType} WebSocket message:`, error);
    }
  }
  
  /**
   * Handle WebSocket close event
   */
  private handleClose(marketType: MarketType, event: CloseEvent): void {
    logger.info(`[BinanceService] ${marketType} WebSocket closed. Code: ${event.code}, Reason: ${event.reason || 'No reason'}`);
    this.wsConnections.set(marketType, null);
    this.connecting.set(marketType, false);
    
    if (this.activeSubscriptions.get(marketType)?.size) {
      this.scheduleReconnect(marketType);
    }
  }
  
  /**
   * Handle WebSocket error event
   */
  private handleError(marketType: MarketType, event: Event): void {
    logger.error(`[BinanceService] ${marketType} WebSocket error:`, event);
    
    // The connection will likely close after an error, which will trigger handleClose
  }
  
  /**
   * Schedule a reconnect attempt for the specified market type
   */
  private scheduleReconnect(marketType: MarketType): void {
    // Skip if already reconnecting
    if (this.reconnectTimers.get(marketType) || this.connecting.get(marketType)) {
      return;
    }
    
    const attempts = (this.reconnectAttempts.get(marketType) || 0) + 1;
    this.reconnectAttempts.set(marketType, attempts);
    
    if (attempts > WS_CONFIG.MAX_RECONNECT_ATTEMPTS) {
      logger.warn(`[BinanceService] Max reconnect attempts (${WS_CONFIG.MAX_RECONNECT_ATTEMPTS}) reached for ${marketType}. Giving up.`);
      return;
    }
    
    // Exponential backoff with jitter
    const jitter = Math.random() * 1000;
    const delay = Math.min(
      WS_CONFIG.RECONNECT_DELAY_MS * Math.pow(1.5, attempts - 1) + jitter,
      60000 // Max 60 seconds
    );
    
    logger.info(`[BinanceService] Scheduling reconnect attempt ${attempts}/${WS_CONFIG.MAX_RECONNECT_ATTEMPTS} for ${marketType} in ${Math.round(delay / 1000)}s`);
    
    const timer = setTimeout(() => {
      this.reconnectTimers.set(marketType, null);
      if (this.activeSubscriptions.get(marketType)?.size) {
        this.connect(marketType);
      }
    }, delay);
    
    this.reconnectTimers.set(marketType, timer);
  }
  
  /**
   * Subscribe to a stream on Binance WebSocket
   */
  private subscribe(marketType: MarketType, streamName: string): void {
    // Add to active subscriptions
    const subscriptions = this.activeSubscriptions.get(marketType) || new Set();
    subscriptions.add(streamName);
    this.activeSubscriptions.set(marketType, subscriptions);
    
    // Connect if not connected
    if (!this.wsConnections.get(marketType)) {
      this.connect(marketType);
      return; // The subscription will be sent after connection is established
    }
    
    // Send subscription message
    const ws = this.wsConnections.get(marketType);
    if (ws && ws.readyState === WebSocket.OPEN) {
      const subscribeMsg = {
        method: 'SUBSCRIBE',
        params: [streamName],
        id: Date.now()
      };
      
      try {
        ws.send(JSON.stringify(subscribeMsg));
        logger.info(`[BinanceService] Subscribed to ${marketType} stream: ${streamName}`);
      } catch (error) {
        logger.error(`[BinanceService] Error subscribing to ${marketType} stream ${streamName}:`, error);
      }
    }
  }
  
  /**
   * Unsubscribe from a stream on Binance WebSocket
   */
  private unsubscribe(marketType: MarketType, streamName: string): void {
    // Remove from active subscriptions
    const subscriptions = this.activeSubscriptions.get(marketType) || new Set();
    subscriptions.delete(streamName);
    this.activeSubscriptions.set(marketType, subscriptions);
    
    // Send unsubscribe message if connected
    const ws = this.wsConnections.get(marketType);
    if (ws && ws.readyState === WebSocket.OPEN) {
      const unsubscribeMsg = {
        method: 'UNSUBSCRIBE',
        params: [streamName],
        id: Date.now()
      };
      
      try {
        ws.send(JSON.stringify(unsubscribeMsg));
        logger.info(`[BinanceService] Unsubscribed from ${marketType} stream: ${streamName}`);
      } catch (error) {
        logger.error(`[BinanceService] Error unsubscribing from ${marketType} stream ${streamName}:`, error);
      }
    }
    
    // Disconnect if no more subscriptions
    if (subscriptions.size === 0) {
      this.disconnect(marketType);
    }
  }
  
  /**
   * Resubscribe to all active streams after reconnection
   */
  private resubscribe(marketType: MarketType): void {
    const subscriptions = this.activeSubscriptions.get(marketType);
    if (!subscriptions || subscriptions.size === 0) {
      return;
    }
    
    const ws = this.wsConnections.get(marketType);
    if (!ws || ws.readyState !== WebSocket.OPEN) {
      return;
    }
    
    const subscribeMsg = {
      method: 'SUBSCRIBE',
      params: Array.from(subscriptions),
      id: Date.now()
    };
    
    try {
      ws.send(JSON.stringify(subscribeMsg));
      logger.info(`[BinanceService] Resubscribed to ${subscriptions.size} ${marketType} streams`);
    } catch (error) {
      logger.error(`[BinanceService] Error resubscribing to ${marketType} streams:`, error);
    }
  }
  
  /**
   * Make a request to Binance REST API
   */
  private async fetchApi(
    marketType: MarketType,
    endpoint: string,
    queryParams?: Record<string, string | number>,
    retries = REST_CONFIG.MAX_RETRIES
  ): Promise<any> {
    const baseUrl = API_CONFIG[marketType].apiBaseUrl;
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const url = new URL(baseUrl + normalizedEndpoint);
    
    if (queryParams) {
      Object.entries(queryParams).forEach(([key, value]) => {
        url.searchParams.append(key, String(value));
      });
    }
    
    const logPrefix = `[BinanceService:REST:${marketType}:${endpoint}]`;
    logger.info(`${logPrefix} Fetching ${url.toString()}`);
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), WS_CONFIG.REQUEST_TIMEOUT_MS);
    
    try {
      const response = await fetch(url.toString(), {
        signal: controller.signal,
        headers: { 'User-Agent': 'MetaCharts/1.0', 'Accept': 'application/json' }
      });
      clearTimeout(timeoutId);
      
      const responseText = await response.text();
      const responseData = responseText ? JSON.parse(responseText) : null;
      
      if (!response.ok) {
        const errorMessage = `HTTP error ${response.status}: ${response.statusText}`;
        logger.error(`${logPrefix} ${errorMessage}`);
        
        // Handle rate limiting
        if ((response.status === 429 || response.status === 418) && retries > 0) {
          const retryAfterHeader = response.headers.get('Retry-After');
          let retryDelaySeconds = REST_CONFIG.INITIAL_RETRY_DELAY_MS / 1000;
          
          if (retryAfterHeader) {
            const parsedRetryAfter = parseInt(retryAfterHeader, 10);
            if (!isNaN(parsedRetryAfter)) {
              retryDelaySeconds = parsedRetryAfter;
            }
          }
          
          const waitMs = Math.max(retryDelaySeconds * 1000, REST_CONFIG.INITIAL_RETRY_DELAY_MS);
          logger.warn(`${logPrefix} Rate limit hit. Retrying in ${Math.round(waitMs / 1000)}s... (${retries - 1} retries left)`);
          
          await new Promise(resolve => setTimeout(resolve, waitMs));
          return this.fetchApi(marketType, endpoint, queryParams, retries - 1);
        }
        
        throw new Error(errorMessage);
      }
      
      return responseData;
    } catch (e: any) {
      clearTimeout(timeoutId);
      
      if (e.name === 'AbortError') {
        logger.error(`${logPrefix} Request timed out after ${WS_CONFIG.REQUEST_TIMEOUT_MS / 1000}s`);
        
        if (retries > 0) {
          logger.warn(`${logPrefix} Retrying due to timeout... (${retries - 1} retries left)`);
          await new Promise(resolve => setTimeout(resolve, REST_CONFIG.INITIAL_RETRY_DELAY_MS));
          return this.fetchApi(marketType, endpoint, queryParams, retries - 1);
        }
        
        throw new Error(`Request to ${endpoint} timed out`);
      }
      
      throw e;
    }
  }
  
  // --- ExchangeService Interface Implementation ---
  
  /**
   * Fetch available symbols for a market type
   */
  public async fetchSymbols(marketType: MarketType): Promise<SymbolInfo[]> {
    const exchangeInfo = await this.fetchApi(marketType, '/exchangeInfo');
    
    return exchangeInfo.symbols
      .filter((s: any) => s.status !== 'BREAK') // Filter out symbols with BREAK status
      .map((symbol: any) => this.mapBinanceSymbolToSymbolInfo(symbol, marketType));
  }
  
  /**
   * Fetch current tickers for a market type
   */
  public async fetchTickers(marketType: MarketType): Promise<FullTicker[]> {
    const rawTickers = await this.fetchApi(marketType, '/ticker/24hr');
    
    if (!Array.isArray(rawTickers)) {
      throw new Error(`Expected array response for tickers, got ${typeof rawTickers}`);
    }
    
    return rawTickers
      .filter(t => 
        typeof t.symbol === 'string' && 
        typeof t.lastPrice === 'string' &&
        typeof t.priceChangePercent === 'string'
      )
      .map(ticker => this.mapBinanceRestTickerToFullTicker(ticker, marketType));
  }
  
  /**
   * Fetch historical klines for a specific symbol and interval
   */
  public async fetchKlines(
    marketType: MarketType,
    symbol: string,
    interval: string,
    options: KlineOptions = {}
  ): Promise<Kline[]> {
    const queryParams: Record<string, string | number> = {
      symbol: symbol.toUpperCase(),
      interval,
      limit: options.limit || 1000,
    };
    
    if (options.startTime) queryParams.startTime = options.startTime;
    if (options.endTime) queryParams.endTime = options.endTime;
    
    const rawKlines = await this.fetchApi(marketType, '/klines', queryParams);
    
    if (!Array.isArray(rawKlines)) {
      throw new Error(`Expected array response for klines, got ${typeof rawKlines}`);
    }
    
    return rawKlines
      .map(kline => this.mapBinanceRestKlineToKline(kline, symbol, interval, marketType))
      .filter(Boolean) as Kline[];
  }
  
  /**
   * Subscribe to real-time ticker updates for a market type
   * Returns an unsubscribe function
   */
  public subscribeTickers(marketType: MarketType): () => void {
    const streamName = WS_CONFIG.TICKER_ARR_STREAM_NAME;
    this.subscribe(marketType, streamName);
    
    return () => this.unsubscribe(marketType, streamName);
  }
  
  /**
   * Subscribe to real-time kline updates for a symbol and interval
   * Returns an unsubscribe function
   */
  public subscribeKline(marketType: MarketType, symbol: string, interval: string): () => void {
    const streamName = getBinanceKlineStreamName(symbol, interval);
    this.subscribe(marketType, streamName);
    
    return () => this.unsubscribe(marketType, streamName);
  }
  
  // --- Data Mapping Functions ---
  
  /**
   * Map a Binance REST API symbol to our SymbolInfo interface
   */
  private mapBinanceSymbolToSymbolInfo(rawSymbol: any, marketType: MarketType): SymbolInfo {
    return {
      symbol: rawSymbol.symbol,
      baseAsset: rawSymbol.baseAsset,
      quoteAsset: rawSymbol.quoteAsset,
      marketType,
      status: rawSymbol.status,
    };
  }
  
  /**
   * Map a Binance REST API ticker to our FullTicker interface
   */
  private mapBinanceRestTickerToFullTicker(restTicker: any, marketType: MarketType): FullTicker {
    return {
      symbol: restTicker.symbol,
      marketType,
      price: parseFloat(restTicker.lastPrice),
      priceChange: parseFloat(restTicker.priceChange),
      priceChangePercent: parseFloat(restTicker.priceChangePercent),
      weightedAvgPrice: parseFloat(restTicker.weightedAvgPrice),
      prevClosePrice: parseFloat(restTicker.prevClosePrice),
      lastQty: parseFloat(restTicker.lastQty),
      bidPrice: parseFloat(restTicker.bidPrice),
      bidQty: parseFloat(restTicker.bidQty),
      askPrice: parseFloat(restTicker.askPrice),
      askQty: parseFloat(restTicker.askQty),
      openPrice: parseFloat(restTicker.openPrice),
      highPrice: parseFloat(restTicker.highPrice),
      lowPrice: parseFloat(restTicker.lowPrice),
      volume: parseFloat(restTicker.volume),
      quoteVolume: parseFloat(restTicker.quoteVolume),
      openTime: restTicker.openTime,
      closeTime: restTicker.closeTime,
      firstId: restTicker.firstId,
      lastId: restTicker.lastId,
      count: restTicker.count,
      lastUpdated: Date.now(),
    };
  }
  
  /**
   * Map a Binance WebSocket ticker to our FullTicker interface
   */
  private mapBinanceWsTickerToFullTicker(wsTicker: any, marketType: MarketType): FullTicker {
    if (!wsTicker?.s) {
      throw new Error(`Missing symbol in ticker data`);
    }
    
    // Helper function for safe parsing
    const safeParseFloat = (value: any): number => {
      if (typeof value === 'number') return value;
      if (typeof value === 'string') return parseFloat(value);
      return 0;
    };
    
    // Destructure fields from the WebSocket message
    const {
      s: symbol,
      c: lastPriceStr,
      p: priceChangeStr,
      P: priceChangePercentStr,
      v: volumeStr,
      q: quoteVolumeStr,
      w: weightedAvgPriceStr,
      x: prevClosePriceStr,
      Q: lastQtyStr,
      b: bidPriceStr,
      B: bidQtyStr,
      a: askPriceStr,
      A: askQtyStr,
      o: openPriceStr,
      h: highPriceStr,
      l: lowPriceStr,
      O: openTime = 0,
      C: closeTime = 0,
      F: firstId = 0,
      L: lastId = 0,
      n: count = 0,
      E: eventTime = Date.now(),
    } = wsTicker;
    
    return {
      symbol,
      marketType,
      price: safeParseFloat(lastPriceStr),
      priceChange: safeParseFloat(priceChangeStr),
      priceChangePercent: safeParseFloat(priceChangePercentStr),
      weightedAvgPrice: safeParseFloat(weightedAvgPriceStr),
      prevClosePrice: safeParseFloat(prevClosePriceStr),
      lastQty: safeParseFloat(lastQtyStr),
      bidPrice: safeParseFloat(bidPriceStr),
      bidQty: safeParseFloat(bidQtyStr),
      askPrice: safeParseFloat(askPriceStr),
      askQty: safeParseFloat(askQtyStr),
      openPrice: safeParseFloat(openPriceStr),
      highPrice: safeParseFloat(highPriceStr),
      lowPrice: safeParseFloat(lowPriceStr),
      volume: safeParseFloat(volumeStr),
      quoteVolume: safeParseFloat(quoteVolumeStr),
      openTime,
      closeTime,
      firstId,
      lastId,
      count,
      lastUpdated: eventTime
    };
  }
  
  /**
   * Map a Binance REST API kline to our Kline interface
   */
  private mapBinanceRestKlineToKline(klineData: any[], symbol: string, interval: string, marketType: MarketType): Kline | null {
    const openTime = Number(klineData[0]);
    const closeTime = Number(klineData[6]);
    
    if (isNaN(openTime) || isNaN(closeTime)) {
      logger.warn(`[BinanceService] Invalid timestamp in REST kline data for ${symbol} ${interval} ${marketType}`);
      return null;
    }
    
    return {
      symbol,
      marketType,
      interval,
      openTime,
      open: parseFloat(klineData[1]),
      high: parseFloat(klineData[2]),
      low: parseFloat(klineData[3]),
      close: parseFloat(klineData[4]),
      volume: parseFloat(klineData[5]),
      closeTime,
      quoteVolume: parseFloat(klineData[7]),
      trades: Number(klineData[8]),
      isClosed: true, // REST klines are always closed
    };
  }
  
  /**
   * Map a Binance WebSocket kline to our Kline interface
   */
  private mapBinanceWsKlineToKline(wsKlinePayload: any, marketType: MarketType): Kline | null {
    const openTime = Number(wsKlinePayload.t);
    const closeTime = Number(wsKlinePayload.T);
    
    if (isNaN(openTime) || isNaN(closeTime)) {
      logger.warn(`[BinanceService] Invalid timestamp in WebSocket kline data for ${wsKlinePayload.s} ${wsKlinePayload.i} ${marketType}`);
      return null;
    }
    
    return {
      symbol: wsKlinePayload.s,
      marketType,
      interval: wsKlinePayload.i,
      openTime,
      open: parseFloat(wsKlinePayload.o),
      high: parseFloat(wsKlinePayload.h),
      low: parseFloat(wsKlinePayload.l),
      close: parseFloat(wsKlinePayload.c),
      volume: parseFloat(wsKlinePayload.v),
      closeTime,
      quoteVolume: parseFloat(wsKlinePayload.q),
      trades: Number(wsKlinePayload.n),
      isClosed: wsKlinePayload.x,
    };
  }
}

// Create singleton instance
export const binanceService = new BinanceService(); 