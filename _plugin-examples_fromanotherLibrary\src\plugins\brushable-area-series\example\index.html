<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - Brushable Area Series Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Brushable Area Series</h1>
			<p class="hint-message">
				HINT: Click and drag across the chart. Click once to clear.
			</p>
			<p>
				An area series plot where the styling of individual points can be
				changed dynamically without needing to set new data.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>
