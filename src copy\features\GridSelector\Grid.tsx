import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
// Удаляем импорт Framer Motion
// import { motion, AnimatePresence } from 'framer-motion';
import { LayoutType } from '@/shared/index'; // Импортируем тип из централизованной папки типов
// import { ReactComponent as GridIcon } from './GridIcon.svg';
import { Icon } from '@/shared/ui/icons/all_Icon'; // NEW IMPORT
// import './Grid.scss'; // Удаляем импорт SCSS
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/ui/popover"
import { Button } from "@/shared/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/shared/ui/tooltip"
import { 
  useAppSettingsStore, 
  selectLayoutType, 
  selectViewMode,
  selectLayoutTypeForCurrentMode,
  selectSetLayoutTypeForMode 
} from '@/shared/store/settingsStore';
import { DropdownMenuTrigger } from "@/shared/ui/dropdown-menu"

// Определяем типы для пропсов
interface LayoutSelectorProps {
  currentLayout: LayoutType;
  onLayoutChange: (layout: LayoutType) => void;
}

/**
 * Компонент для выбора типа компоновки графиков.
 */
const LayoutSelector: React.FC<LayoutSelectorProps> = ({ currentLayout, onLayoutChange }) => {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [hoveredCell, setHoveredCell] = useState<{ row: number, col: number } | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [popupPosition, setPopupPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });
  const buttonRef = useRef<HTMLButtonElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);

  // Get current mode from store
  const currentMode = useAppSettingsStore(selectViewMode);
  const currentModeLayout = useAppSettingsStore(selectLayoutTypeForCurrentMode);
  const setLayoutTypeForMode = useAppSettingsStore(selectSetLayoutTypeForMode);

  // Client-side effect
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // Парсим текущий макет для текущего режима для определения выбранных строк и столбцов
  const [currentRows, currentCols] = (currentModeLayout && typeof currentModeLayout === 'string') 
    ? currentModeLayout.split('x').map(Number)
    : [1, 1]; // Дефолтные значения

  // Функция для открытия всплывающего окна
  const openPopup = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setPopupPosition({
        top: rect.bottom + 4,
        left: rect.left
      });
    }
    setIsPopupOpen(true);
  };

  // Функция для закрытия всплывающего окна
  const closePopup = () => {
    setIsPopupOpen(false);
    setHoveredCell(null);
  };

  // Обработчик клика вне компонента и ESC для закрытия попапа (обновлен для Portal)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isPopupOpen &&
        popupRef.current && 
        !popupRef.current.contains(event.target as Node) &&
        buttonRef.current && 
        !buttonRef.current.contains(event.target as Node)
      ) {
        closePopup();
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isPopupOpen) {
        closePopup();
      }
    };

    if (isPopupOpen && isClient) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isPopupOpen, isClient]);

  // Функция для выбора размеров сетки
  const handleCellSelect = (row: number, col: number) => {
    // Проверяем, что выбранные размеры допустимы
    if (row >= 1 && row <= 7 && col >= 1 && col <= 7) {
      const newLayout = `${row}x${col}` as LayoutType;
      
      // Сохраняем выбор для текущего режима
      setLayoutTypeForMode(currentMode, newLayout);
      
      // Также вызываем переданный обработчик для обратной совместимости
      onLayoutChange(newLayout);
      closePopup();
    }
  };

  // Функция обработки наведения на ячейку
  const handleCellHover = (row: number, col: number) => {
    setHoveredCell({ row, col });
  };

  // Функция для определения, должна ли ячейка быть активной
  const isCellActive = (row: number, col: number) => {
    if (hoveredCell) {
      return row <= hoveredCell.row && col <= hoveredCell.col;
    }
    return row <= currentRows && col <= currentCols;
  };

  // Позиционирование попапа через Portal для правильного z-index

  return (
    // layoutSelector -> relative flex items-center h-full z-50
    <div className="relative flex items-center h-full z-50">
      <Button 
        ref={buttonRef}
        variant="ghost"
        size="icon"
        className="group"
        onClick={() => isPopupOpen ? closePopup() : openPopup()}
        title="Выбрать сетку"
        data-state={isPopupOpen ? 'open' : 'closed'}
        aria-label="Select grid layout"
      >
        <Icon 
          name="GridLayout" 
          className="text-muted-foreground group-hover:text-foreground"
        />
      </Button>
      
      {/* Render popup using Portal */}
      {isClient && isPopupOpen && typeof document !== 'undefined' && createPortal(
        <div 
          ref={popupRef} 
          className="fixed z-[100] w-[280px] bg-gradient-to-b from-card to-background border border-border rounded-md shadow-lg overflow-hidden p-3"
          style={{ 
            top: popupPosition.top,
            left: popupPosition.left,
            animation: 'popupFadeIn 0.2s ease-out' 
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* popupHeader -> Увеличиваем нижний отступ */}
          <div className="flex items-center justify-between mb-3">
            {/* popupTitle -> Увеличиваем шрифт */}
            <span className="text-sm font-medium text-muted-foreground">
              Grid {currentMode === 'focus' ? 'Focus' : 'Screener'}
            </span>
            {/* gridSizeInfo -> Увеличиваем шрифт */}
            <div className="text-base font-semibold text-primary px-1.5 py-0.5 rounded-sm bg-primary/10">
              {hoveredCell 
                ? `${hoveredCell.row} × ${hoveredCell.col} = ${hoveredCell.row * hoveredCell.col}` 
                : `${currentRows} × ${currentCols} = ${currentRows * currentCols}`}
            </div>
          </div>
          
          {/* interactiveGrid -> Смягчаем фон */}
          <div className="flex flex-col w-full bg-muted/30 rounded-sm overflow-hidden border border-border/10">
            {/* Верхняя нумерация столбцов */}
            {/* columnNumbers -> Увеличиваем высоту и отступ */}
            <div className="flex h-[24px] ml-[24px] pr-[3px]"> {/* Увеличены h, ml, pr */} 
              {Array.from({ length: 7 }, (_, i) => (
                // number -> Увеличиваем шрифт
                <div key={`col-${i}`} className="flex-1 flex items-center justify-center text-xs font-medium text-muted-foreground select-none">{i + 1}</div>
              ))}
            </div>
            
            {/* gridContent -> */}
            <div className="flex flex-1">
              {/* Левая нумерация строк */}
              {/* rowNumbers -> Увеличиваем ширину и отступ */}
              <div className="flex flex-col w-[24px] pt-[3px]"> {/* Увеличены w, pt */} 
                {Array.from({ length: 7 }, (_, i) => (
                  // number -> Увеличиваем высоту и шрифт (h = cell height + gap)
                  <div key={`row-${i}`} className="h-[31px] flex items-center justify-center text-xs font-medium text-muted-foreground select-none">{i + 1}</div>
                ))}
              </div>
              
              {/* Сетка ячеек */}
              {/* cellsGrid -> Увеличиваем gap и padding */}
              <div className="flex-1 flex flex-col gap-[3px] p-[3px]">
                {Array.from({ length: 7 }, (_, rowIndex) => (
                  // gridRow -> Увеличиваем высоту и gap
                  <div key={`row-${rowIndex}`} className="flex h-[28px] gap-[3px]">
                    {Array.from({ length: 7 }, (_, colIndex) => {
                      const row = rowIndex + 1;
                      const col = colIndex + 1;
                      const isActive = isCellActive(row, col);
                      
                      return (
                        // cell -> Убираем scale, оставляем только закрашивание
                        <div 
                          key={`cell-${row}-${col}`} 
                          className={[
                            "flex-1 rounded-sm transition-colors duration-150 ease-in-out cursor-pointer",
                            "bg-background hover:bg-accent/60", 
                            isActive && "bg-primary" // Убрали scale, оставили только цвет
                          ].filter(Boolean).join(' ')}
                          onMouseEnter={() => handleCellHover(row, col)}
                          onClick={() => handleCellSelect(row, col)}
                          title={`${row} × ${col}`}
                        />
                      );
                    })}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default LayoutSelector; 