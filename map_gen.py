import os
import re
import ast
import time
import logging
import datetime
import fnmatch
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any


# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('map_generator')

# Конфигурация скрипта
CONFIG = {
    'interval_seconds': 60,  # Интервал между запусками в секундах (по умолчанию 1 час)
    'output_file': 'MAP_PROJECT.py',  # Выходной файл в формате Python
    'exclude_dirs': ['.git', '__pycache__', '.cursor', 'venv', 'env', 'venv310'],  # Директории для исключения
    'exclude_dir_patterns': ['*__pycache__*', 'test_*'],  # Паттерны игнорируемых директорий
    'ignore_files': [  # Список файлов, которые нужно игнорировать
        'map_gen.py',  # Сам скрипт генерации карты
        'map_gen2.py',  # Сам скрипт генерации карты    
        'MAP_PROJECT.py',  # Результат генерации
        'MAP_PROJECT_referens.py',  # Референсный файл
    ],
    'ignore_file_patterns': ['test_*.py', '*.test.py', '*.min.js'],  # Паттерны игнорируемых файлов
}


def extract_file_header_comment(file_path: str) -> str:
    """Извлекает комментарии из начала файла до первого импорта."""
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        if not lines:
            return ""
        
        comments = []
        
        # Читаем строки до первого импорта
        for line in lines:
            stripped = line.strip()
            
            # Пропускаем пустые строки
            if not stripped:
                continue
                
            # Если нашли строку импорта, останавливаемся
            if stripped.startswith('import ') or stripped.startswith('from '):
                break
                
            # Собираем комментарии
            if stripped.startswith('#') and not stripped.startswith('#!'):  # Исключаем шебанг
                # Удаляем символ комментария и пробелы
                comments.append(stripped[1:].strip())
        
        # Объединяем все комментарии в одну строку
        if comments:
            return "; ".join(comments)
        
        return ""


def parse_docstring(node: ast.AST) -> str:
    """Извлекает первую строку из docstring функции или класса."""
    docstring = ast.get_docstring(node)
    if docstring:
        # Берем только первую строку из docstring
        return docstring.strip().split('\n')[0].strip()
    return ""


def parse_function(node: ast.FunctionDef) -> Tuple[str, str, List[str], int]:
    """
    Парсит объявление функции и извлекает имя, описание, параметры и размер.
    
    Args:
        node: AST-узел функции
    
    Returns:
        Кортеж из имени, описания, списка параметров и размера функции (в строках кода)
    """
    name = node.name
    description = parse_docstring(node)
    
    # Получаем параметры функции
    params = []
    for arg in node.args.args:
        if arg.arg != 'self':  # Исключаем self из параметров метода
            params.append(arg.arg)
    
    # Оцениваем размер функции по количеству строк
    try:
        # end_lineno доступно только в Python 3.8+
        size = node.end_lineno - node.lineno + 1 if hasattr(node, 'end_lineno') else 0
    except AttributeError:
        size = 0
    
    return name, description, params, size


def parse_class(node: ast.ClassDef) -> Tuple[str, str, Dict[str, Any]]:
    """
    Парсит объявление класса и извлекает имя, описание и методы/атрибуты.
    
    Args:
        node: AST-узел класса
        
    Returns:
        Кортеж из имени, описания и словаря с методами и атрибутами
    """
    name = node.name
    description = parse_docstring(node)
    
    methods = {}
    attributes = {}
    
    for item in node.body:
        if isinstance(item, ast.FunctionDef):
            method_name, method_desc, method_params, method_size = parse_function(item)
            methods[method_name] = {
                'description': method_desc,
                'params': method_params,
                'size': method_size
            }
        elif isinstance(item, ast.Assign):
            for target in item.targets:
                if isinstance(target, ast.Name):
                    attr_name = target.id
                    attributes[attr_name] = {'description': ''}
    
    return name, description, {'methods': methods, 'attributes': attributes}


def parse_python_file(file_path: str) -> Dict[str, Any]:
    """Парсит Python файл и извлекает классы и функции."""
    with open(file_path, 'r', encoding='utf-8') as file:
        try:
            content = file.read()
            tree = ast.parse(content)
            
            result = {
                'classes': {},
                'functions': {},
                'description': '',
                'header_comment': extract_file_header_comment(file_path)
            }
            
            # Ищем модульный docstring
            if (len(tree.body) > 0 and isinstance(tree.body[0], ast.Expr) and 
                    (isinstance(tree.body[0].value, ast.Constant) or 
                     (hasattr(ast, 'Str') and isinstance(tree.body[0].value, ast.Str)))):
                # Совместимое с разными версиями Python получение значения
                if hasattr(tree.body[0].value, 'value'):
                    result['description'] = str(tree.body[0].value.value).strip().split('\n')[0].strip()
                elif hasattr(tree.body[0].value, 's'):
                    result['description'] = tree.body[0].value.s.strip().split('\n')[0].strip()
            
            for node in tree.body:
                if isinstance(node, ast.ClassDef):
                    class_name, class_desc, class_content = parse_class(node)
                    result['classes'][class_name] = {
                        'description': class_desc,
                        'methods': class_content['methods'],
                        'attributes': class_content['attributes']
                    }
                elif isinstance(node, ast.FunctionDef) or isinstance(node, ast.AsyncFunctionDef):
                    func_name, func_desc, func_params, func_size = parse_function(node)
                    # Добавляем префикс "async" для асинхронных функций
                    prefix = "async " if isinstance(node, ast.AsyncFunctionDef) else ""
                    result['functions'][func_name] = {
                        'description': func_desc,
                        'params': func_params,
                        'is_async': isinstance(node, ast.AsyncFunctionDef),
                        'prefix': prefix,
                        'size': func_size
                    }
            
            return result
        except Exception as e:
            print(f"Ошибка при парсинге файла {file_path}: {e}")
            return {
                'classes': {},
                'functions': {},
                'description': f'Ошибка парсинга: {str(e)}'
            }


def scan_directory(root_dir: str) -> Dict[str, Dict[str, Any]]:
    """Сканирует директорию и собирает информацию о Python файлах."""
    project_structure = {}
    
    for root, dirs, files in os.walk(root_dir):
        # Пропускаем скрытые директории и директории из CONFIG['exclude_dirs']
        # и соответствующие паттернам из CONFIG['exclude_dir_patterns']
        filtered_dirs = []
        for d in dirs:
            if d.startswith('.') or d in CONFIG['exclude_dirs']:
                continue
                
            # Проверяем соответствие паттернам
            skip = False
            for pattern in CONFIG['exclude_dir_patterns']:
                if fnmatch.fnmatch(d, pattern):
                    skip = True
                    break
                    
            if not skip:
                filtered_dirs.append(d)
                
        dirs[:] = filtered_dirs
        
        for file in files:
            # Пропускаем игнорируемые файлы напрямую или по паттерну
            if file in CONFIG['ignore_files']:
                continue
                
            # Проверяем соответствие паттернам игнорируемых файлов
            skip = False
            for pattern in CONFIG['ignore_file_patterns']:
                if fnmatch.fnmatch(file, pattern):
                    skip = True
                    break
                    
            if skip:
                continue
                
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, root_dir)
                
                # Парсим файл
                file_info = parse_python_file(file_path)
                
                # Добавляем информацию в структуру проекта
                add_to_structure(project_structure, rel_path, file_info)
    
    return project_structure


def add_to_structure(structure: Dict[str, Any], path: str, file_info: Dict[str, Any]) -> None:
    """Добавляет информацию о файле в структуру проекта."""
    parts = path.replace('\\', '/').split('/')
    current = structure
    
    # Создаем структуру директорий
    for i, part in enumerate(parts[:-1]):
        if part not in current:
            current[part] = {}
        current = current[part]
    
    # Добавляем файл
    file_name = parts[-1]
    current[file_name] = {
        'type': 'file',
        'description': file_info.get('description', ''),
        'header_comment': file_info.get('header_comment', ''),
        'classes': file_info.get('classes', {}),
        'functions': file_info.get('functions', {})
    }


def format_params(params: List[str]) -> str:
    """Форматирует список параметров функции."""
    return ', '.join(params)


def generate_python_map(project_structure: Dict[str, Any], root_dir: str) -> str:
    """
    Генерирует карту проекта в формате Python-файла с импортами.
    
    Args:
        project_structure: Структура проекта
        root_dir: Корневая директория проекта
        
    Returns:
        Строка с кодом Python
    """
    lines = []
    
    # Собираем все импорты
    imports = []
    python_files = []
    
    def collect_imports(structure, path=""):
        for name, content in sorted(structure.items()):
            if name == "__pycache__" or name.startswith("."):
                continue
                
            # Пропускаем игнорируемые файлы и файлы по паттерну
            if name in CONFIG['ignore_files']:
                continue
                
            # Проверяем файл на соответствие паттернам игнорирования
            skip = False
            for pattern in CONFIG['ignore_file_patterns']:
                if fnmatch.fnmatch(name, pattern):
                    skip = True
                    break
                    
            if skip:
                continue
                
            if isinstance(content, dict) and 'type' not in content:
                # Проверяем директорию на соответствие паттернам игнорирования
                skip = False
                for pattern in CONFIG['exclude_dir_patterns']:
                    if fnmatch.fnmatch(name, pattern):
                        skip = True
                        break
                        
                if skip:
                    continue
                
                # Это директория
                new_path = f"{path}/{name}" if path else name
                collect_imports(content, new_path)
            elif isinstance(content, dict) and content['type'] == 'file' and name.endswith('.py'):
                # Это Python файл
                if name != "__init__.py":
                    module_path = f"{path}/{name[:-3]}" if path else name[:-3]
                    module_path = module_path.replace("/", ".")
                    
                    # Сохраняем путь для дальнейшего использования при импорте функций
                    file_info = {
                        'module_path': module_path,
                        'file_name': name,
                        'functions': content.get('functions', {}),
                        'classes': content.get('classes', {})
                    }
                    python_files.append(file_info)
    
    collect_imports(project_structure)
    
    # Добавляем импорты функций из модулей в одну строку
    # Группируем импорты по модулю
    module_imports = {}
    for file_info in python_files:
        module = file_info['module_path']
        
        # Импортируем все функции из модуля
        for func_name in file_info['functions']:
            if module not in module_imports:
                module_imports[module] = []
            module_imports[module].append(func_name)
        
        # Импортируем все классы из модуля
        for class_name in file_info['classes']:
            if module not in module_imports:
                module_imports[module] = []
            module_imports[module].append(class_name)
    
    # Выводим импорты в одну строку для каждого модуля
    for module, names in module_imports.items():
        if names:
            names_str = ", ".join(names)
            lines.append(f"from {module} import {names_str}  # type: ignore # noqa")
    
    lines.append("")
    lines.append("")
    
    # Генерируем структуру проекта в виде переменных Python
    comment_position = 50  # Позиция для выравнивания комментариев
    
    def process_directory(directory: Dict[str, Any], prefix: str = "", level: int = 0):
        # Определяем индентацию на основе уровня вложенности только для директорий
        # Для папок и файлов используем индентацию на основе вложенности
        indent = "__" * level
        
        # Для функций и классов всегда используем фиксированный отступ
        # Независимо от уровня вложенности директорий
        function_indent = "____"
        
        # Сортируем содержимое по имени
        items = sorted(directory.items())
        
        # Сначала собираем директории и файлы отдельно
        dirs = []
        files = []
        
        for name, content in items:
            if name == "__pycache__" or name.startswith("."):
                continue
                
            # Пропускаем игнорируемые файлы напрямую или по паттерну
            if name in CONFIG['ignore_files']:
                continue
                
            # Проверяем файл на соответствие паттернам игнорирования
            skip = False
            for pattern in CONFIG['ignore_file_patterns']:
                if fnmatch.fnmatch(name, pattern):
                    skip = True
                    break
                    
            if skip:
                continue
                
            if isinstance(content, dict) and 'type' not in content:
                # Проверяем директорию на соответствие паттернам игнорирования
                skip = False
                for pattern in CONFIG['exclude_dir_patterns']:
                    if fnmatch.fnmatch(name, pattern):
                        skip = True
                        break
                        
                if skip:
                    continue
                
                # Это директория
                dirs.append((name, content))
            elif isinstance(content, dict) and content['type'] == 'file' and name.endswith('.py'):
                # Это Python файл
                files.append((name, content))
        
        # Обрабатываем директории и их содержимое
        for dir_name, content in dirs:
            # Собираем все файлы в этой директории
            py_files = []
            for file_name, file_content in sorted(content.items()):
                # Пропускаем игнорируемые файлы
                if file_name in CONFIG['ignore_files']:
                    continue
                    
                # Проверяем файл на соответствие паттернам игнорирования
                skip = False
                for pattern in CONFIG['ignore_file_patterns']:
                    if fnmatch.fnmatch(file_name, pattern):
                        skip = True
                        break
                        
                if skip:
                    continue
                
                if (isinstance(file_content, dict) and file_content.get('type') == 'file' 
                        and file_name.endswith('.py') and not file_name.startswith('.')):
                    py_files.append((file_name, file_content))
            
            # Обрабатываем каждый файл в директории отдельно
            for file_name, file_content in py_files:
                # Создаем строку с директорией и файлом
                # Используем отступ на основе уровня вложенности для заголовка файла
                header_comment = file_content.get('header_comment', '')
                file_line = f"{indent}#📁 {prefix}{dir_name}/ 📄 {file_name}"
                
                # Добавляем комментарий в ту же строку с выравниванием
                if header_comment:
                    # Рассчитываем отступ для выравнивания комментария
                    padding = max(40 - len(file_line) + len(indent), 2)
                    file_line = f"{file_line}{' ' * padding}# {header_comment}"
                
                lines.append(file_line)
                
                # Флаг для отслеживания, были ли добавлены функции или классы
                has_content = False
                
                # Обрабатываем функции в файле
                # Используем фиксированный отступ для функций и классов
                functions = file_content.get('functions', {})
                for func_name, func_info in sorted(functions.items()):
                    description = func_info.get('description', '')
                    params = func_info.get('params', [])
                    params_str = format_params(params)
                    lines.append(f"{function_indent}= {func_name}(){' ' * (40 - len(func_name) - 2)} # {description}   ({params_str})")
                    has_content = True
                
                # Обрабатываем классы в файле
                classes = file_content.get('classes', {})
                for class_name, class_info in sorted(classes.items()):
                    description = class_info.get('description', '')
                    lines.append(f"{function_indent}= {class_name}(){' ' * (40 - len(class_name) - 2)} # {description}   ()")
                    
                    # Добавляем методы класса
                    methods = class_info.get('methods', {})
                    for method_name, method_info in sorted(methods.items()):
                        method_description = method_info.get('description', '')
                        method_params = method_info.get('params', [])
                        method_params_str = format_params(method_params)
                        full_method_name = f"{class_name}.{method_name}"
                        lines.append(f"{function_indent}= {full_method_name}(){' ' * (40 - len(full_method_name) - 2)} # {method_description}   ({method_params_str})")
                    has_content = True
                
                # Добавляем пустую строку после содержимого файла, если в нем были функции или классы
                if has_content:
                    lines.append("")
            
            # Рекурсивно обрабатываем поддиректории
            process_subdir = {k: v for k, v in content.items() 
                               if isinstance(v, dict) and 'type' not in v 
                               and not k.startswith('.')}
            
            # Фильтруем поддиректории по паттернам игнорирования
            filtered_subdir = {}
            for name, content in process_subdir.items():
                # Пропускаем директории из exclude_dirs
                if name in CONFIG['exclude_dirs']:
                    continue
                    
                # Проверяем директорию на соответствие паттернам игнорирования
                skip = False
                for pattern in CONFIG['exclude_dir_patterns']:
                    if fnmatch.fnmatch(name, pattern):
                        skip = True
                        break
                        
                if not skip:
                    filtered_subdir[name] = content
            
            if filtered_subdir:
                new_prefix = f"{prefix}{dir_name}/"
                process_directory(filtered_subdir, new_prefix, level + 1)
        
        # Обрабатываем файлы в корневой директории
        for name, content in files:
            # Добавляем файл как комментарий
            header_comment = content.get('header_comment', '')
            file_line = f"{indent}#📄 {prefix}{name}"
            
            # Добавляем комментарий в ту же строку с выравниванием
            if header_comment:
                # Рассчитываем отступ для выравнивания комментария
                padding = max(40 - len(file_line) + len(indent), 2)
                file_line = f"{file_line}{' ' * padding}# {header_comment}"
            
            lines.append(file_line)
            
            # Выводим функции и классы для этого файла
            # Для функций и классов в корневых файлах тоже используем фиксированный отступ
            
            # Флаг для отслеживания, были ли добавлены функции или классы
            has_content = False
            
            functions = content.get('functions', {})
            for func_name, func_info in sorted(functions.items()):
                description = func_info.get('description', '')
                params = func_info.get('params', [])
                params_str = format_params(params)
                lines.append(f"{function_indent}= {func_name}(){' ' * (40 - len(func_name) - 2)} # {description}   ({params_str})")
                has_content = True
            
            # Обрабатываем классы в файле
            classes = content.get('classes', {})
            for class_name, class_info in sorted(classes.items()):
                description = class_info.get('description', '')
                lines.append(f"{function_indent}= {class_name}(){' ' * (40 - len(class_name) - 2)} # {description}   ()")
                
                # Добавляем методы класса
                methods = class_info.get('methods', {})
                for method_name, method_info in sorted(methods.items()):
                    method_description = method_info.get('description', '')
                    method_params = method_info.get('params', [])
                    method_params_str = format_params(method_params)
                    full_method_name = f"{class_name}.{method_name}"
                    lines.append(f"{function_indent}= {full_method_name}(){' ' * (40 - len(full_method_name) - 2)} # {method_description}   ({method_params_str})")
                has_content = True
            
            # Добавляем пустую строку после содержимого файла, если в нем были функции или классы
            if has_content:
                lines.append("")
    
    process_directory(project_structure)
    return "\n".join(lines)


def collect_stats(project_structure: Dict[str, Any]) -> Dict[str, Any]:
    """
    Собирает статистику по обработанным файлам, функциям и классам.
    
    Args:
        project_structure: Структура проекта
        
    Returns:
        Словарь со статистикой
    """
    stats = {
        'total_files': 0,
        'total_functions': 0,
        'total_classes': 0,
        'total_methods': 0,
        'files_size': 0,
        'largest_file': {'name': '', 'size': 0},
        'largest_class': {'name': '', 'file': '', 'methods_count': 0},
        'largest_function': {'name': '', 'file': '', 'size': 0},
    }
    
    def process_structure(structure, path=""):
        for key, value in structure.items():
            if isinstance(value, dict):
                if value.get('type') == 'file':
                    # Это файл
                    file_path = os.path.join(path, key)
                    stats['total_files'] += 1
                    
                    # Размер файла
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        stats['files_size'] += file_size
                        
                        if file_size > stats['largest_file']['size']:
                            stats['largest_file'] = {'name': file_path, 'size': file_size}
                    
                    # Классы
                    classes = value.get('classes', {})
                    stats['total_classes'] += len(classes)
                    
                    for class_name, class_info in classes.items():
                        methods_count = len(class_info.get('methods', {}))
                        stats['total_methods'] += methods_count
                        
                        if methods_count > stats['largest_class'].get('methods_count', 0):
                            stats['largest_class'] = {
                                'name': class_name,
                                'file': file_path,
                                'methods_count': methods_count
                            }
                    
                    # Функции
                    functions = value.get('functions', {})
                    stats['total_functions'] += len(functions)
                    
                    # Здесь мы не можем точно определить размер функции, так как не сохраняем эту информацию
                    # В будущем можно улучшить парсинг для получения этой информации
                else:
                    # Это директория
                    process_structure(value, os.path.join(path, key))
    
    process_structure(project_structure)
    return stats


def log_stats(stats: Dict[str, Any]) -> None:
    """
    Выводит статистику в лог.
    
    Args:
        stats: Словарь со статистикой
    """
    logger.info("Статистика по проекту:")
    logger.info(f"Обработано файлов: {stats['total_files']}")
    logger.info(f"Общий размер файлов: {stats['files_size'] / 1024:.2f} KB")
    logger.info(f"Найдено функций: {stats['total_functions']}")
    logger.info(f"Найдено классов: {stats['total_classes']}")
    logger.info(f"Найдено методов: {stats['total_methods']}")
    
    if stats['largest_file']['name']:
        logger.info(f"Самый большой файл: {stats['largest_file']['name']} "
                   f"({stats['largest_file']['size'] / 1024:.2f} KB)")
    
    if stats['largest_class']['name']:
        logger.info(f"Самый большой класс: {stats['largest_class']['name']} "
                   f"в файле {stats['largest_class']['file']} "
                   f"({stats['largest_class']['methods_count']} методов)")


def generate_project_map(root_dir: str) -> Tuple[str, Dict[str, Any]]:
    """
    Генерирует карту проекта и собирает статистику.
    
    Args:
        root_dir: Корневая директория проекта
        
    Returns:
        Кортеж из строки с картой проекта и словаря со статистикой
    """
    # Сканируем директорию
    project_structure = scan_directory(root_dir)
    
    # Собираем статистику
    stats = collect_stats(project_structure)
    
    # Генерируем структуру проекта в формате Python
    content = generate_python_map(project_structure, root_dir)
    
    return content, stats


if __name__ == "__main__":
    # Текущая директория проекта
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    logger.info(f"Запуск генератора карты проекта. Интервал обновления: {CONFIG['interval_seconds']} секунд")
    logger.info(f"Игнорируемые директории: {CONFIG['exclude_dirs']}")
    logger.info(f"Паттерны игнорируемых директорий: {CONFIG['exclude_dir_patterns']}")
    logger.info(f"Игнорируемые файлы: {CONFIG['ignore_files']}")
    logger.info(f"Паттерны игнорируемых файлов: {CONFIG['ignore_file_patterns']}")
    
    try:
        while True:
            start_time = time.time()
            now = datetime.datetime.now()
            
            logger.info(f"Начало генерации карты проекта: {now.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Генерируем карту проекта и получаем статистику
            project_map, stats = generate_project_map(current_dir)
            
            # Выводим статистику
            log_stats(stats)
            
            # Сохраняем карту проекта в файл
            with open(CONFIG['output_file'], 'w', encoding='utf-8') as f:
                f.write(project_map)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.info(f"Карта проекта успешно сохранена в файл {CONFIG['output_file']}")
            
            # Выводим информацию в терминал
            print(f"[{now.strftime('%Y-%m-%d %H:%M:%S')}] Карта проекта сгенерирована за {execution_time:.2f} секунд.")
            print(f"Обработано {stats['total_files']} файлов, {stats['total_functions']} функций, {stats['total_classes']} классов.")
            print(f"Следующее обновление через {CONFIG['interval_seconds']} секунд...")
            
            # Пауза перед следующей итерацией
            sleep_time = CONFIG['interval_seconds']
            if execution_time < sleep_time:
                time.sleep(sleep_time - execution_time)
    
    except KeyboardInterrupt:
        logger.info("Генератор карты проекта остановлен пользователем")
        print("\nГенератор карты проекта остановлен. До свидания!")
    except Exception as e:
        logger.error(f"Произошла ошибка: {str(e)}", exc_info=True) 