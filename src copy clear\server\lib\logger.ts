// Basic logger utility using console
// This can be replaced with a more sophisticated logger like <PERSON><PERSON> or <PERSON> later.

type LogLevel = 'info' | 'warn' | 'error' | 'debug';
type LogFunction = (...args: any[]) => void;

export const logger = (() => {
  // Helper function to format log prefix
  const formatLogPrefix = (level: LogLevel): string => 
    `[${level.toUpperCase()}] [${new Date().toISOString()}]`;

  // Create a log method for a specific level
  const createLogMethod = (level: LogLevel, consoleFn: LogFunction, shouldLog = true): LogFunction => 
    (...args: any[]) => {
      if (shouldLog) {
        consoleFn(formatLogPrefix(level), ...args);
      }
    };
  
  // Check if debug logs should be enabled
  const isDebugEnabled = (): boolean => 
    process.env.LOG_LEVEL === 'debug';
  
  // Base logger methods
  const baseLogger = {
    info: createLogMethod('info', console.log),
    warn: createLogMethod('warn', console.warn),
    error: createLogMethod('error', console.error),
    debug: createLogMethod('debug', console.debug, isDebugEnabled()),
    
    // Method to create a child logger with a specific context
    child: (context: Record<string, any>) => ({
      info: (...args: any[]) => createLogMethod('info', console.log)(context, ...args),
      warn: (...args: any[]) => createLogMethod('warn', console.warn)(context, ...args),
      error: (...args: any[]) => createLogMethod('error', console.error)(context, ...args),
      debug: (...args: any[]) => createLogMethod('debug', console.debug, isDebugEnabled())(context, ...args),
    }),
  };
  
  return baseLogger;
})();

export default logger; 