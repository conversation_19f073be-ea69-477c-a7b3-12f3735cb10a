{"name": "metacharts", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "concurrently \"npm run dev:server-only\" \"npm run dev:client-win\"", "dev:server": "nodemon -r esbuild-register --watch 'src/server/**/*.ts' src/server/main.ts | pino-pretty", "dev:server-win": "concurrently \"npm run dev:server-only\" \"npm run dev:client-win\"", "dev:server-only": "nodemon --exec \"npx tsx src/server/main.ts\" --watch \"src/server/**/*.ts\" | npx pino-pretty", "dev:client": "WATCHPACK_POLLING=true next dev --turbo -p 3001", "dev:client-win": "next dev --turbo -p 3001", "dev:docker": "docker compose up --build", "dev:docker:watch": "docker compose watch", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "tsx src/db/migrate.ts", "db:studio": "drizzle-kit studio"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/helmet": "^13.0.1", "@fastify/websocket": "^11.1.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@sinclair/typebox": "^0.32.34", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.9", "@types/chokidar": "^2.1.7", "@types/glob": "^8.1.0", "@types/ioredis": "^5.0.0", "axios": "^1.9.0", "chalk": "^5.4.1", "chokidar": "^4.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "fastify": "^5.4.0", "framer-motion": "^12.18.1", "glob": "^11.0.3", "immer": "^10.1.1", "ioredis": "^5.6.1", "lightweight-charts": "^5.0.7", "lodash-es": "^4.17.21", "lucide-react": "^0.515.0", "minimatch": "^10.0.3", "next": "15.3.3", "next-themes": "^0.4.6", "node-cron": "^4.1.0", "pg": "^8.16.0", "pino": "^9.7.0", "postgres": "^3.4.7", "prom-client": "^15.1.3", "react": "^19.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-use": "^17.6.0", "redis": "^5.5.6", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "throttle-debounce": "^5.0.2", "uuid": "^11.1.0", "zod": "^3.25.64", "zod-to-json-schema": "^3.24.5", "zustand": "^5.0.5"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.1.10", "@types/minimatch": "^5.1.2", "@types/node": "^24.0.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/throttle-debounce": "^5.0.2", "@types/webpack": "^5.28.5", "@types/ws": "^8.18.1", "@welldone-software/why-did-you-render": "^10.0.1", "concurrently": "^9.1.2", "drizzle-kit": "^0.31.1", "esbuild": "^0.25.5", "esbuild-register": "^3.6.0", "nodemon": "^3.1.10", "pino-pretty": "^13.0.0", "sass": "^1.89.2", "svgo": "^3.3.2", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.3", "typescript": "^5.8.3"}}