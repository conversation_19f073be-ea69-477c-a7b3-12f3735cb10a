Общая стратегия: Инкрементальная разработка в рамках монорепозитория

Проект уже организован как монорепозиторий. Фронтенд (Next.js) находится в `src/`, а бэкенд (Node.js) будет жить в существующей папке `src/server/`.

Вместо того чтобы сначала полностью сделать весь бэкенд, а потом весь фронтенд (или наоборот), мы будем реализовывать функциональность по частям, затрагивая и бэк, и фронт для каждой части. Это позволит быстрее видеть результаты и корректировать курс.

Предварительный шаг: Подготовка общего (shared)

Прежде чем начать, убедись, что у тебя есть место для общих типов и схем Zod, которые будут использоваться и на бэкенде, и на фронтенде.

    Создай директорию для общих артефактов: Например, packages/shared (если используешь npm/yarn/pnpm workspaces) или просто shared на верхнем уровне, доступная и для src (фронтенд), и для server (бэкенд).

    Перенеси/скопируй туда:

        Из src/shared/types/index.ts и src/shared/schemas/market.schema.ts: определения Kline, Ticker (или ProcessedTicker, если он будет общим), MarketType, KlineIntervalSchema и т.д.

        Убедись, что пути импорта на фронтенде обновлены.

        Бэкенд будет импортировать эти типы отсюда же.

Детальный План (Бэкенд и Фронтенд)

Этап 1: Базовый API для исторических свечей и его интеграция

Бэкенд (в папке `src/server/`):

    Настройка проекта:

        Убедись, что все зависимости установлены (`npm install`).
        У нас уже есть `drizzle.config.ts`, `src/db/index.ts`, `src/db/migrate.ts` и `src/db/schema.ts`.

    Drizzle & PostgreSQL:

        Создай схему для таблицы `candles` в `src/db/schema.ts`:

        ```typescript
        import { pgTable, serial, text, varchar, timestamp, decimal, index, unique } from 'drizzle-orm/pg-core';
        import { sql } from 'drizzle-orm';

        export const candles = pgTable('candles', {
          id: serial('id').primaryKey(),
          symbol: varchar('symbol', { length: 32 }).notNull(),
          interval: varchar('interval', { length: 16 }).notNull(),
          marketType: varchar('market_type', { length: 32 }).notNull(),
          openTime: timestamp('open_time', { withTimezone: true }).notNull(),
          open: decimal('open', { precision: 18, scale: 8 }).notNull(),
          high: decimal('high', { precision: 18, scale: 8 }).notNull(),
          low: decimal('low', { precision: 18, scale: 8 }).notNull(),
          close: decimal('close', { precision: 18, scale: 8 }).notNull(),
          volume: decimal('volume', { precision: 18, scale: 8 }).notNull(),
        }, (table) => {
          return {
            symbolIntervalMarketTimeIdx: index('symbol_interval_market_time_idx').on(table.symbol, table.interval, table.marketType, table.openTime),
            unq: unique('candles_unq').on(table.symbol, table.interval, table.marketType, table.openTime),
          };
        });
        ```

        Выполни `npm run db:generate`, чтобы создать SQL-файл миграции в папке `drizzle/`.

        Выполни `npm run db:migrate`, чтобы применить миграцию и создать таблицу в базе данных.

    Redis:

        Настрой подключение к Redis в server/config/redis.ts (используя ioredis).

    API Endpoint для Klines:

        Создай server/api/v1/klineRoutes.ts.

        Роут: GET /klines

        Схема запроса (Zod) для параметров: symbol: string, interval: string, marketType: string, limit?: number, endTime?: number (timestamp ms).

        Сервис server/services/klineService.ts:

            Функция getKlines(params):

                Формирует ключ для Redis-кэша.

                Проверяет Redis. Если есть – отдает.

                Если нет в кэше:

                    Делает запрос в PostgreSQL с помощью Drizzle:

                    ```typescript
                    // Примерный запрос в Drizzle
                    import { db } from '@/db';
                    import { candles } from '@/db/schema';
                    import { and, eq, lt, desc, asc } from 'drizzle-orm';

                    // ...
                    const query = db.select().from(candles).where(
                        and(
                            eq(candles.symbol, params.symbol),
                            eq(candles.interval, params.interval),
                            eq(candles.marketType, params.marketType),
                            params.endTime ? lt(candles.openTime, new Date(params.endTime)) : undefined
                        )
                    ).orderBy(desc(candles.openTime)).limit(params.limit || 1000);

                    const result = await query;
                    // Для lightweight-charts данные должны быть отсортированы по возрастанию времени.
                    // Поэтому разворачиваем массив.
                    const sortedResult = result.reverse();
                    ```

                    Сохраняет результат в Redis с TTL.

                    Возвращает массив Kline[] (сортировка по openTime ASC).

        В роутере Fastify вызывай этот сервис и отдавай JSON. Не забудь про CORS.

Фронтенд (в папке src):

    Адаптация useHistoricalKlinesQuery:

        Этот хук (вероятно, использующий TanStack Query) должен теперь делать запрос на http://localhost:БЭКЕНД_ПОРТ/api/v1/klines.

        Убедись, что он передает symbol, interval, marketType.

        Для начальной загрузки он может не передавать endTime.

        Важно: Данные от бэкенда придут в формате Kline[] (с openTime как DateTime или строкой ISO, а open/high/low/close/volume как Decimal или строкой). Их нужно будет преобразовать в числа, если Lightweight Charts этого требует, и openTime в UTCTimestamp (секунды).

    Интеграция в features/Chart/useChartData.ts:

        Убедись, что данные, полученные из useHistoricalKlinesQuery, корректно обрабатываются и передаются в series.setData().

        Логика пагинации (дозагрузки):

            Lightweight Charts предоставляет событие или способ определить, когда пользователь доскроллил до левого края.

            При этом событии useChartData должен:

                Взять openTime самой старой загруженной свечи (rawKlines[0].openTime).

                Вызвать fetchNextPage (если useHistoricalKlinesQuery использует useInfiniteQuery от TanStack Query) или сделать новый запрос к /api/v1/klines, передав этот openTime как endTime и limit.

                Полученные новые (более старые) свечи добавить в начало rawKlines и processedKlineData, и обновить серию в Lightweight Charts (например, series.setData() с объединенным массивом или series.applyOptions({ data: ... }) если это поддерживается для полной замены, или найти метод для добавления данных в начало).

        Убери любую логику прямого обращения к BinanceService или другим внешним API из этого хука. Вся загрузка данных должна идти через useHistoricalKlinesQuery.

Тестирование Этапа 1:
Запусти бэкенд и фронтенд. Открой график. Должны загрузиться исторические свечи. Проверь пагинацию при скролле влево.

Этап 2: WebSocket для обновлений свечей в реальном времени

Бэкенд:

    Настройка WebSocket:

        Установи fastify-websocket (@fastify/websocket).

        Зарегистрируй плагин в Fastify.

        Создай WebSocket-эндпоинт (например, /ws/market).

    Сервис server/services/webSocketService.ts:

        Функция handleConnection(socket, request):

            Обработка сообщений от клиента:

                { "action": "subscribe", "type": "kline", "params": { "symbol", "interval", "marketType" } }:

                    Сохранить подписку этого socket на канал (например, в Map<string, Set<WebSocket>>, где ключ – это kline:${symbol}:${interval}:${marketType}).

                    Подписаться на соответствующий Redis Pub/Sub канал (например, pubsub:kline:${symbol}:${interval}:${marketType}), если это первая подписка на этот канал для всего сервера.

                { "action": "unsubscribe", ... }: Удалить подписку. Если на Redis Pub/Sub канал больше никто не подписан, отписаться от него.

            Обработка закрытия соединения: удалить все подписки этого socket.

        Функция для обработки сообщений из Redis Pub/Sub:

            Когда приходит сообщение (новая свеча Kline) из Redis Pub/Sub, найти все socket-ы, подписанные на этот канал, и отправить им эту свечу в формате { "type": "kline_update", "data": Kline }.

    Интеграция с Redis Pub/Sub:

        При старте бэкенда создать Redis-клиента для подписок (отдельного от того, что для команд GET/SET, т.к. подписчик блокирует соединение).

        Этот подписчик слушает каналы и вызывает функцию из webSocketService.ts.

Фронтенд:

    Централизованный WebSocket-менеджер (src/shared/lib/websocketManager.ts или похожее):

        Отвечает за установку и поддержание одного WebSocket-соединения с бэкендом (/ws/market).

        Методы: connect(), disconnect(), subscribeKline(symbol, interval, marketType, callback), unsubscribeKline(...).

        При получении сообщения от сервера (kline_update), вызывает соответствующий callback, зарегистрированный при подписке.

        Используй reconnecting-websocket или реализуй логику переподключения.

    Интеграция в features/Chart/useChartData.ts:

        В useEffect при монтировании и при смене symbol/interval/marketType:

            Вызывать websocketManager.subscribeKline(symbol, interval, marketType, handleWsKlineUpdate).

            В функции handleWsKlineUpdate(kline: Kline): вызывать твой safeUpdateSeries(kline).

            В return из useEffect вызывать websocketManager.unsubscribeKline(...).

        Убери старую логику подписки на WebSocket (если она была через BinanceService или useWebSocketSubscription напрямую к бирже).

        Логика буферизации (wsBufferRef) все еще может быть полезна, если сообщения приходят очень быстро или во время других операций.

Тестирование Этапа 2:
Нужен механизм на бэкенде, который будет публиковать "новые" свечи в Redis Pub/Sub (пока нет реального сбора данных с биржи). Это может быть простой скрипт или тестовый эндпоинт на бэкенде. Проверь, что график обновляется в реальном времени.

Этап 3: Сбор данных с биржи (на бэкенде)

Бэкенд:

    Сервис server/services/exchangeDataCollectorService.ts:

        При получении новой свечи от биржи:

            Преобразовать в твой формат Kline.

            Сохранить в PostgreSQL (таблица `candles`) с помощью Drizzle.

            Опубликовать эту свечу в Redis Pub/Sub.

Фронтенд:
Изменений не требуется, если Этап 2 реализован правильно. Фронтенд просто получает обновления от бэкенда.

Тестирование Этапа 3:
Теперь данные должны приходить с реальной биржи, сохраняться в БД и транслироваться клиентам.

Этап 4: API для данных таблицы/скринера и его интеграция

Бэкенд:

    Добавь схему для `ticker24h` в `src/db/schema.ts`:

        ```typescript
        export const tickers24h = pgTable('tickers_24h', {
          id: serial('id').primaryKey(),
          symbol: varchar('symbol', { length: 32 }).notNull(),
          marketType: varchar('market_type', { length: 32 }).notNull(),
          lastPrice: decimal('last_price', { precision: 18, scale: 8 }),
          priceChange: decimal('price_change', { precision: 18, scale: 8 }),
          priceChangePercent: decimal('price_change_percent', { precision: 18, scale: 4 }),
          highPrice: decimal('high_price', { precision: 18, scale: 8 }),
          lowPrice: decimal('low_price', { precision: 18, scale: 8 }),
          volume: decimal('volume', { precision: 18, scale: 8 }),
          quoteVolume: decimal('quote_volume', { precision: 18, scale: 2 }),
          openTime: timestamp('open_time', { withTimezone: true }),
          closeTime: timestamp('close_time', { withTimezone: true }),
          count: integer('count'),
          lastUpdated: timestamp('last_updated', { withTimezone: true }).defaultNow().notNull(),
        }, (table) => {
          return {
            marketTypeIdx: index('market_type_idx').on(table.marketType),
            quoteVolumeIdx: index('quote_volume_idx').on(table.quoteVolume),
            priceChangePercentIdx: index('price_change_percent_idx').on(table.priceChangePercent),
            unq: unique('tickers_24h_unq').on(table.symbol, table.marketType),
          };
        });
        ```

        Выполни `npm run db:generate` и `npm run db:migrate`.

    Обновление ExchangeDataCollectorService:

        При получении обновления тикера:

            Преобразовать в формат Ticker24h.

            Сохранить/обновить (upsert) в PostgreSQL с помощью Drizzle.
            ```typescript
            // Пример upsert в Drizzle
            import { db } from '@/db';
            import { tickers24h } from '@/db/schema';
            import { eq } from 'drizzle-orm';

            // ...
            await db.insert(tickers24h)
              .values(newTickerData)
              .onConflictDoUpdate({
                target: [tickers24h.symbol, tickers24h.marketType],
                set: { ...newTickerData, lastUpdated: new Date() }
              });
            ```

            Опубликовать обновление в Redis Pub/Sub.

    API Endpoint для тикеров:

        Создай server/api/v1/marketDataRoutes.ts.

        Роут: GET /tickers

        Схема запроса (Zod) для параметров: marketTypes?: string[], quoteAssets?: string[], minVolume?: number, minTrades?: number, sortBy?: string, sortOrder?: 'asc'|'desc', limit?: number, offset?: number, searchQuery?: string.

        Сервис server/services/marketDataService.ts:

            Функция getTickers(params):

                Формирует ключ для Redis-кэша.

                Проверяет Redis.

                Если нет: Запрос к PostgreSQL (таблица Ticker24h) с учетом всех фильтров, сортировки и пагинации. Вся логика фильтрации и сортировки из useScreenerTickers и useTableData переносится сюда.

                Сохраняет в Redis.

                Возвращает массив Ticker24h[] (или твой ProcessedTicker[]).

    WebSocket для обновлений тикеров (опционально, для real-time таблицы):

        WebSocketService обрабатывает подписки типа subscribe_tickers_overview (возможно, с параметрами фильтров).

        При получении сообщения из pubsub:ticker_update:..., проверяет, соответствует ли обновленный тикер фильтрам активных подписок, и рассылает.

Фронтенд:

    Рефакторинг features/TickerManagement/useTableData.ts и features/ModeControl/useScreenerTickers.ts:

        Создать хуки с TanStack Query (например, useMarketTickersQuery) для запроса данных к /api/v1/market_data/tickers, передавая все параметры фильтрации и сортировки.

        Удалить всю сложную клиентскую логику фильтрации, сортировки и агрегации из этих хуков. Они должны просто получать готовые данные от бэкенда.

        aggregateTickers – если эта логика нужна, она должна быть на бэкенде. Бэкенд может иметь параметр aggregate=true или отдавать агрегированные данные по умолчанию, если это основной сценарий.

        TanStack Table будет работать с данными, полученными от сервера.

    WebSocket для обновлений таблицы (если реализовано на бэке):

        websocketManager должен поддерживать подписку на обновления тикеров.

        Хуки таблицы/скринера должны подписываться и обновлять данные (например, обновлять кэш TanStack Query или локальное состояние).

Тестирование Этапа 4:
Таблица и скринер должны загружать данные с бэкенда. Фильтры и сортировка должны работать, отправляя соответствующие параметры на бэкенд.

Этап 5: Прочие API и рефакторинг

    API для настроек (если нужно хранить что-то на сервере): Например, сохраненные пользователем "пироги индикаторов" или конфигурации графиков.

    Аутентификация (если планируется).

    Дальнейший рефакторинг фронтенда:

        Проанализировать settingsStore на предмет возможного разделения или упрощения.

        Убедиться, что все серверные данные запрашиваются через TanStack Query.

        Оптимизировать рендеринг, если есть узкие места.

Ответ на вопрос "сначала фронт или бэк?" в этом плане:
Мы движемся вертикальными срезами. Для каждого этапа:

    Определяем API на бэкенде.

    Реализуем этот API на бэкенде.

    Адаптируем/переписываем соответствующую часть фронтенда для использования этого API.

Какие части фронтенда переписывать и как:

    features/Chart/useChartData.ts:

        Как есть: Сложная логика загрузки, WebSocket, буферизация.

        Как будет: Логика загрузки исторических данных упростится до вызова хука TanStack Query (с пагинацией). WebSocket-обработка будет получать данные от центрального менеджера. Основная сложность останется в управлении Lightweight Charts и пагинацией.

    features/TickerManagement/useTableData.ts и features/ModeControl/useScreenerTickers.ts:

        Как есть: Много клиентской логики фильтрации, сортировки.

        Как будет: Значительно упростятся. Будут в основном вызывать хук TanStack Query с параметрами фильтров/сортировки и отображать результат. Вся тяжелая работа по обработке данных уедет на бэк.

    src/server/exchange/binance.service.ts:

        Как есть: Находится во фронтенд-части (в src/server/, что сбивает с толку в Next.js контексте, т.к. это не API route).

        Как будет: Полностью переедет на бэкенд и станет частью ExchangeDataCollectorService.

    WebSocket управление:

        Как есть: Возможно, разрозненно или через BinanceService.

        Как будет: Централизованный websocketManager на фронте, который общается с WebSocket-сервером на бэкенде. Бэкенд управляет подписками на биржу и рассылкой клиентам.

Дополнительные данные из проекта:
На данный момент предоставленной информации достаточно для составления этого детального плана. Если в процессе реализации какого-то этапа возникнут специфические вопросы по конкретному хуку или компоненту, тогда можно будет углубиться.