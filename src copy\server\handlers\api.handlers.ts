import { Elysia, t } from 'elysia';
import { MarketType } from '../../shared/types';
import { REDIS_KEYS } from '../services/ingest.service';
import { binanceService } from '../exchange/binance.service';
import { getDragonflyClient } from '../services/dragonfly.service';
import { queryQuestDB } from '../services/questdb.service';

// Типы контекста для декораторов
type AppContext = {
  dragonfly: any;
  logger: any;
  questDBPool: any;
};

/**
 * API handlers for market data endpoints
 * Exported as a reusable Elysia plugin
 */
export const apiHandlers = new Elysia({ prefix: '/api' })
  // Добавляем типы декораторов
  .derive(() => {
    return {} as AppContext;
  })
  .get('/symbols/:marketType', async ({ params, dragonfly, logger }) => {
    const { marketType } = params;
    const marketTypeVal = marketType as MarketType;
    
    logger.info(`Requesting symbols for ${marketType}`);

    try {
      // Try to get from cache first
      const cacheKey = REDIS_KEYS.SYMBOLS_KEY(marketTypeVal);
      const cachedData = await dragonfly.get(cacheKey);
      
      if (cachedData) {
        try {
          const symbols = JSON.parse(cachedData);
          logger.info(`Returning ${symbols.length} symbols from cache`);
          return { source: 'cache', symbols };
        } catch (error) {
          logger.warn(`Failed to parse cached symbols data: ${error}`);
          // Continue to fetch from exchange on parse error
        }
      }
      
      // Fetch from exchange if not in cache
      const symbols = await binanceService.fetchSymbols(marketTypeVal);
      logger.info(`Fetched ${symbols.length} symbols from exchange`);
      
      // Cache the result
      try {
        await dragonfly.set(
          cacheKey,
          JSON.stringify(symbols),
          'EX',
          86400 // 24 hours
        );
      } catch (error) {
        logger.warn(`Failed to cache symbols data: ${error}`);
      }
      
      return { source: 'exchange', symbols };
    } catch (error) {
      logger.error(`Error in getSymbolsHandler:`, error);
      return { 
        error: 'Failed to fetch symbols',
        message: error instanceof Error ? error.message : String(error),
        symbols: []
      };
    }
  }, {
    params: t.Object({
      marketType: t.String()
    })
  })
  
  .get('/tickers/:marketType', async ({ params, dragonfly, logger }) => {
    const { marketType } = params;
    const marketTypeVal = marketType as MarketType;

    logger.info(`Requesting tickers for ${marketType}`);

    try {
      // Try to get from cache first
      const cacheKey = REDIS_KEYS.TICKERS_KEY(marketTypeVal);
      const cachedData = await dragonfly.get(cacheKey);
      
      if (cachedData) {
        try {
          const tickers = JSON.parse(cachedData);
          logger.info(`Returning ${tickers.length} tickers from cache`);
          return { source: 'cache', tickers };
        } catch (error) {
          logger.warn(`Failed to parse cached tickers data: ${error}`);
          // Continue to fetch from exchange on parse error
        }
      }
      
      // Fetch from exchange if not in cache
      const tickers = await binanceService.fetchTickers(marketTypeVal);
      logger.info(`Fetched ${tickers.length} tickers from exchange`);
      
      // Cache the result
      try {
        await dragonfly.set(
          cacheKey,
          JSON.stringify(tickers),
          'EX',
          300 // 5 minutes
        );
      } catch (error) {
        logger.warn(`Failed to cache tickers data: ${error}`);
      }
      
      return { source: 'exchange', tickers };
    } catch (error) {
      logger.error(`Error in getTickersHandler:`, error);
      return { 
        error: 'Failed to fetch tickers',
        message: error instanceof Error ? error.message : String(error),
        tickers: []
      };
    }
  }, {
    params: t.Object({
      marketType: t.String()
    })
  })
  
  .get('/candles/:marketType/:symbol/:interval', async ({ params, query, dragonfly, questDBPool, logger }) => {
    const { marketType, symbol, interval } = params;
    const { limit = '1000', startTime, endTime } = query;
    
    const marketTypeVal = marketType as MarketType;
    const symbolVal = symbol || '';
    const intervalVal = interval || '';
    
    const limitInt = Math.min(parseInt(limit, 10) || 1000, 1000); // Limit to 1000 max
    const startTimeInt = startTime ? parseInt(startTime, 10) : undefined;
    const endTimeInt = endTime ? parseInt(endTime, 10) : undefined;
    
    logger.info(`Requesting candles for ${marketType}/${symbol}/${interval} (limit: ${limitInt}, startTime: ${startTimeInt}, endTime: ${endTimeInt})`);
    
    try {
      // Skip cache for requests with time range parameters
      if (!startTimeInt && !endTimeInt) {
        try {
          const cacheKey = REDIS_KEYS.KLINE_KEY(marketTypeVal, symbolVal, intervalVal);
          const cachedData = await dragonfly.get(cacheKey);
          
          if (cachedData) {
            const candles = JSON.parse(cachedData);
            logger.info(`Returning ${candles.length} candles from cache (full cached set)`);
            // Return the full set of candles from cache, limit should be applied by DB or exchange query if cache miss
            return { 
              source: 'cache',
              candles: candles 
            };
          }
        } catch (e) {
          logger.error(`Cache error:`, e);
        }
      }
      
      // Try to get from database for historical data
      try {
        if (questDBPool) {
          let params: any[] = [
            symbolVal.toUpperCase(),
            marketTypeVal,
            intervalVal,
            limitInt
          ];
          
          let questQuery = `
            SELECT 
              symbol, market_type as "marketType", interval, 
              open_time as "openTime", open, high, low, close,
              volume, close_time as "closeTime", quote_volume as "quoteVolume", 
              trades, is_closed as "isClosed"
            FROM klines 
            WHERE symbol = $1 
              AND market_type = $2 
              AND interval = $3`;
              
          if (startTimeInt) {
            questQuery += ` AND open_time >= $${params.length + 1}`;
            params.push(new Date(startTimeInt).toISOString());
          }
              
          if (endTimeInt) {
            questQuery += ` AND open_time <= $${params.length + 1}`;
            params.push(new Date(endTimeInt).toISOString());
          }
              
          questQuery += ` ORDER BY open_time DESC LIMIT $4`;
          
          const dbResult = await queryQuestDB(questQuery, params);
          
          if (dbResult.rows && dbResult.rows.length > 0) {
            // Convert timestamps to numbers and ensure correct types
            const candles = dbResult.rows.map((row: any) => {
              if (!row) return null;
              try {
                const openTime = row.openTime instanceof Date 
                  ? row.openTime.getTime() 
                  : new Date(row.openTime).getTime();
                    
                const closeTime = row.closeTime instanceof Date 
                  ? row.closeTime.getTime() 
                  : new Date(row.closeTime).getTime();
                
                // Convert numeric fields to proper types
                return {
                  symbol: row.symbol,
                  marketType: row.marketType,
                  interval: row.interval,
                  openTime,
                  open: typeof row.open === 'number' ? row.open : parseFloat(row.open),
                  high: typeof row.high === 'number' ? row.high : parseFloat(row.high),
                  low: typeof row.low === 'number' ? row.low : parseFloat(row.low),
                  close: typeof row.close === 'number' ? row.close : parseFloat(row.close),
                  volume: typeof row.volume === 'number' ? row.volume : parseFloat(row.volume),
                  closeTime,
                  quoteVolume: typeof row.quoteVolume === 'number' ? row.quoteVolume : parseFloat(row.quoteVolume),
                  trades: typeof row.trades === 'number' ? row.trades : Number(row.trades),
                  isClosed: !!row.isClosed
                };
              } catch (e) {
                logger.error('Row conversion error:', e);
                return null; 
              }
            }).filter(Boolean).reverse(); // Oldest first
            
            logger.info(`Returning ${candles.length} candles from DB`);
            return { source: 'db', candles };
          }
        }
      } catch (e) {
        logger.error(`DB error:`, e);
      }
      
      // Fetch from exchange as last resort
      const options = {
        limit: limitInt,
        startTime: startTimeInt,
        endTime: endTimeInt
      };
      
      const candles = await binanceService.fetchKlines(marketTypeVal, symbolVal, intervalVal, options);
      logger.info(`Fetched ${candles.length} candles from exchange`);
      
      // Cache the result if it's a request for recent data
      if (!startTimeInt && !endTimeInt) {
        try {
          const cacheKey = REDIS_KEYS.KLINE_KEY(marketTypeVal, symbolVal, intervalVal);
          
          // Determine appropriate TTL based on interval
          let ttl = 24 * 60 * 60; // Default 1 day
          if (intervalVal === '1m') ttl = 60 * 60; // 1 hour for 1m candles
          else if (intervalVal === '5m') ttl = 4 * 60 * 60; // 4 hours for 5m candles
          else if (intervalVal === '15m') ttl = 12 * 60 * 60; // 12 hours for 15m candles
          
          await dragonfly.set(
            cacheKey,
            JSON.stringify(candles),
            'EX',
            ttl
          );
        } catch (error) {
          logger.warn(`Failed to cache candles data: ${error}`);
        }
      }
      
      return { source: 'exchange', candles };
      
    } catch (error) {
      logger.error(`Critical error in getCandlesHandler:`, error);
      return { 
        error: 'Failed to fetch candles',
        message: error instanceof Error ? error.message : String(error),
        candles: []
      };
    }
  }, {
    params: t.Object({
      marketType: t.String(),
      symbol: t.String(),
      interval: t.String()
    }),
    query: t.Object({
      limit: t.Optional(t.String()),
      startTime: t.Optional(t.String()),
      endTime: t.Optional(t.String())
    })
  }); 