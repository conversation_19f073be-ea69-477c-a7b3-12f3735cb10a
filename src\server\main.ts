import Fastify, { FastifyRequest, FastifyReply } from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import fastifyWebsocket from '@fastify/websocket';
import { pino } from 'pino';
import dotenv from 'dotenv';
import { klineRoutes } from './api/v1/klineRoutes.js';
import { tickerRoutes } from './api/v1/tickerRoutes.js';
import { websocketRoutes } from './api/v1/websocketRoutes.js';
import { connectRedis, closeRedisConnections } from './lib/redis.js';
import { exchangeDataCollector } from './services/exchangeDataCollectorService.js';
import { initializeDataCollection, shutdownDataCollection } from './services/dataCollector.js';

// Load environment variables from .env file
dotenv.config();

// Initialize Fastify app
const fastify = Fastify({
  logger: {
    level: 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname',
      },
    },
  },
});

// Register essential plugins
fastify.register(helmet);
fastify.register(cors, {
  // Configure CORS options here
  origin: '*', // Be more specific in production
});

// Register WebSocket support
fastify.register(fastifyWebsocket, {
  options: { 
    maxPayload: 1048576, // 1MB
  }
});

// Define a simple health check route
fastify.get('/', async (request: FastifyRequest, reply: FastifyReply) => {
  return { status: 'ok' };
});

// Register API routes
fastify.register(klineRoutes, { prefix: '/api/v1' });
fastify.register(tickerRoutes, { prefix: '/api/v1' });
fastify.register(websocketRoutes, { prefix: '/ws' });

// Function to start the server
const start = async () => {
  try {
    // Initialize Redis connection
    await connectRedis();
    
    // Initialize Exchange Data Collector
    await exchangeDataCollector.initialize();
    
    // Initialize Data Collection from exchanges
    await initializeDataCollection();
    
    // Listen on port 3005 by default (API_PORT), or from environment variable
    const port = process.env.API_PORT ? parseInt(process.env.API_PORT, 10) :
                 process.env.PORT ? parseInt(process.env.PORT, 10) : 3005;
    await fastify.listen({ port, host: '0.0.0.0' });
    fastify.log.info(`Server listening on ${fastify.server.address()}`);
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

// Handle graceful shutdown
const shutdown = async (signal: string) => {
  fastify.log.info(`Received ${signal}, shutting down gracefully...`);
  
  try {
    // Stop Data Collection
    await shutdownDataCollection();
    
    // Shutdown Exchange Data Collector
    await exchangeDataCollector.shutdown();
    
    // Close Fastify server
    await fastify.close();
    
    // Close Redis connections
    await closeRedisConnections();
    
    fastify.log.info('Server shutdown complete');
    process.exit(0);
  } catch (err) {
    fastify.log.error(err, 'Error during shutdown');
    process.exit(1);
  }
};

// Register shutdown handlers
process.on('SIGTERM', () => shutdown('SIGTERM'));
process.on('SIGINT', () => shutdown('SIGINT'));

start();
