import React, { useMemo, memo } from 'react';
import SimpleChart from '@/features/Chart/Chart'; // Assuming SimpleChart is here
import { Ticker, MarketType } from '@/shared/index';

const MemoizedSimpleChart = memo(SimpleChart);
const DEFAULT_TIMEFRAME_CONST = '1h'; // Match default from page.tsx

interface ChartDisplayManagerProps {
  isScreenerMode: boolean;
  screenerTickers: Ticker[];
  screenerTimeframe: string;
  screenerCurrentPage: number;
  selectedTickerSymbol: string | null;
  fullscreenChartIndex: number | null;
  chartIndices: number[];
  chartIntervals: Record<number, string>;
  getMarketTypeForSymbol: (symbol: string) => MarketType;
  intervalHandlers: Record<number, (newInterval: string) => void>;
  fullscreenHandlers: Record<number, () => void>;
  emptyChartMessage: React.ReactNode;
  totalCharts: number; // Needed for screenerTickers.slice
}

const ChartDisplayManager: React.FC<ChartDisplayManagerProps> = ({
  isScreenerMode,
  screenerTickers,
  screenerTimeframe,
  screenerCurrentPage,
  selectedTickerSymbol,
  fullscreenChartIndex,
  chartIndices,
  chartIntervals,
  getMarketTypeForSymbol,
  intervalHandlers,
  fullscreenHandlers,
  emptyChartMessage,
  totalCharts,
}) => {
  const renderCharts = useMemo(() => {
    // В режиме скринера
    if (isScreenerMode) {
      if (!screenerTickers.length) {
        return (
          <div className="flex items-center justify-center h-full text-muted-foreground col-span-full row-span-full">
            <div className="text-center">
              <div>Нет данных для скринера</div>
              <div className="text-xs mt-1">Проверьте настройки фильтрации</div>
            </div>
          </div>
        );
      }

      // Handle fullscreen display for screener mode
      // Note: fullscreenChartIndex is the index within the *currently visible page* of tickers.
      // We need to calculate the actual index in the screenerTickers array.
      const startIndexForPage = (screenerCurrentPage - 1) * totalCharts;

      if (fullscreenChartIndex !== null) {
        const actualTickerIndex = startIndexForPage + fullscreenChartIndex;
        if (screenerTickers[actualTickerIndex]) {
          const ticker = screenerTickers[actualTickerIndex];
          const currentMarketType = ticker.marketType;
          const currentInterval = screenerTimeframe; 
          
          return (
            <div key={`screener-chart-fullscreen-${actualTickerIndex}`} className="absolute inset-0 z-50 bg-background">
              <MemoizedSimpleChart
                symbol={ticker.symbol}
                marketType={currentMarketType}
                interval={currentInterval}
                onIntervalChange={intervalHandlers[fullscreenChartIndex]} 
                onFullscreenToggle={fullscreenHandlers[fullscreenChartIndex]}
              />
            </div>
          );
        }
      }
      
      // Calculate the slice of tickers for the current page
      const endIndexForPage = startIndexForPage + totalCharts;
      const visibleTickers = screenerTickers.slice(startIndexForPage, endIndexForPage);
      
      return visibleTickers.map((ticker, pageIndex) => {
        // pageIndex is the index within the visibleTickers array (0 to totalCharts-1)
        // This is what should be used for accessing intervalHandlers and fullscreenHandlers
        const currentMarketType = ticker.marketType;
        const currentInterval = screenerTimeframe; // Все графики используют один таймфрейм

        return (
          <div key={`screener-chart-${ticker.symbol}-${pageIndex}`} className="bg-background relative h-full w-full">
            <MemoizedSimpleChart
              symbol={ticker.symbol}
              marketType={currentMarketType}
              interval={currentInterval}
              onIntervalChange={intervalHandlers[pageIndex]} // Use pageIndex
              onFullscreenToggle={fullscreenHandlers[pageIndex]} // Use pageIndex
            />
          </div>
        );
      });
    }

    // В режиме фокуса (обычная логика)
    if (!selectedTickerSymbol) return emptyChartMessage;
  
    if (fullscreenChartIndex !== null) {
      const index = fullscreenChartIndex;
      const currentMarketType = getMarketTypeForSymbol(selectedTickerSymbol);
      const currentInterval = chartIntervals[index] || DEFAULT_TIMEFRAME_CONST;
      
      return (
        <div key={`focus-chart-${index}`} className="absolute inset-0 z-50 bg-background">
          <MemoizedSimpleChart
            symbol={selectedTickerSymbol}
            marketType={currentMarketType}
            interval={currentInterval}
            onIntervalChange={intervalHandlers[index]}
            onFullscreenToggle={fullscreenHandlers[index]}
          />
        </div>
      );
    }
    
    return chartIndices.map((index) => {
      const currentMarketType = getMarketTypeForSymbol(selectedTickerSymbol);
      const currentInterval = chartIntervals[index] || DEFAULT_TIMEFRAME_CONST;

      return (
        <div key={`focus-chart-${index}`} className="bg-background relative h-full w-full">
          <MemoizedSimpleChart
            symbol={selectedTickerSymbol}
            marketType={currentMarketType}
            interval={currentInterval}
            onIntervalChange={intervalHandlers[index]}
            onFullscreenToggle={fullscreenHandlers[index]}
          />
        </div>
      );
    });
  }, [
    isScreenerMode,
    screenerTickers,
    screenerTimeframe,
    screenerCurrentPage,
    selectedTickerSymbol, 
    fullscreenChartIndex, 
    chartIndices, 
    chartIntervals, 
    getMarketTypeForSymbol, 
    intervalHandlers, 
    fullscreenHandlers, 
    emptyChartMessage,
    totalCharts
  ]);

  return <>{renderCharts}</>;
};

export default ChartDisplayManager; 