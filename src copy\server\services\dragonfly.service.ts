import Redis from 'ioredis';
import { DRAGONFLY_URL } from '../config';
import { logger } from '../lib/logger'; // Assuming a logger is available or will be created

const log = logger.child({ service: 'DragonflyService' });

let redisClient: Redis | null = null;
let connectionPromise: Promise<Redis> | null = null;

// Define a generic Logger type or use 'any' if not strictly typed yet
type LoggerInstance = any; // Replace 'any' with a more specific type

/**
 * Fetches data with caching logic.
 *
 * @param cacheKey The key for the cache.
 * @param fetchFromSource A function to fetch data from the source.
 * @param dragonflyClient The Dragonfly (Redis) client.
 * @param cacheTTL Cache time-to-live in seconds.
 * @param loggerInstance An instance of the logger.
 * @param options Additional options.
 * @returns A result object with the data source and data.
 */
export async function fetchDataWithCache<T>(
  cacheKey: string,
  fetchFromSource: () => Promise<T>,
  dragonflyClient: Redis, // Changed from DragonflyClient to Redis
  cacheTTL: number,
  loggerInstance: LoggerInstance = log, // Default to service logger
  options: { sourceNameForLogs: string; dataItemName: string; sourceOrigin: 'exchange' | 'db' }
): Promise<{ source: 'cache' | 'exchange' | 'db'; data: T | null; error?: string; message?: string }> {
  const { sourceNameForLogs, dataItemName, sourceOrigin } = options;

  // Check cache for data
  try {
    const cachedData = await dragonflyClient.get(cacheKey);
    if (cachedData) {
      const data = JSON.parse(cachedData) as T;
      loggerInstance.info(`Returning ${Array.isArray(data) ? data.length : 1} ${dataItemName} from cache for ${sourceNameForLogs}`);
      return { source: 'cache', data };
    }
  } catch (cacheError) {
    loggerInstance.error(`Cache retrieval error for ${cacheKey}:`, cacheError);
  }

  // Fetch data from source
  try {
    const data = await fetchFromSource();

    // Cache data if it's not empty
    if (data && (!Array.isArray(data) || data.length > 0) && cacheTTL > 0) {
      try {
        await dragonflyClient.set(cacheKey, JSON.stringify(data), 'EX', cacheTTL);
        loggerInstance.info(`Cached ${Array.isArray(data) ? data.length : 1} ${dataItemName} for ${cacheTTL}s from ${sourceNameForLogs}`);
      } catch (cacheSetError) {
        loggerInstance.error(`Caching error for ${cacheKey}:`, cacheSetError);
      }
    }

    loggerInstance.info(`Returning ${Array.isArray(data) ? data.length : 1} ${dataItemName} from ${sourceOrigin} for ${sourceNameForLogs}`);
    return { source: sourceOrigin, data };
  } catch (sourceError) {
    loggerInstance.error(`Source fetch error for ${sourceNameForLogs}:`, sourceError);
    return {
      source: sourceOrigin,
      data: null,
      error: `Failed to fetch ${dataItemName}`,
      message: sourceError instanceof Error ? sourceError.message : String(sourceError)
    };
  }
}

/**
 * Get or create a Redis client
 */
async function getClient(): Promise<Redis> {
    if (redisClient && redisClient.status === 'ready') {
        return redisClient;
    }

    if (connectionPromise) {
        return connectionPromise;
    }

    connectionPromise = new Promise((resolve, reject) => {
        log.info(`Connecting to Dragonfly/Redis at ${DRAGONFLY_URL}`);
        
        const client = new Redis(DRAGONFLY_URL, {
            maxRetriesPerRequest: 3,
            retryStrategy: (times) => {
                const delay = Math.min(times * 100, 2000); // Exponential backoff up to 2s
                log.warn(`Retrying connection (attempt ${times}), delay ${delay}ms`);
                return delay;
            },
            enableOfflineQueue: true, // Buffer commands while reconnecting
        });

        // Setup event handlers
        const events = {
            connect: () => log.info('Client connecting...'),
            ready: () => {
                log.info('Client ready and connected');
                redisClient = client;
                resolve(client);
            },
            error: (err: Error) => {
                log.error('Connection error:', err);
                if (!redisClient) {
                    connectionPromise = null; // Reset promise for next attempt
                    reject(err);
                }
            },
            close: () => log.warn('Connection closed'),
            reconnecting: () => {
                log.info('Client reconnecting...');
                redisClient = null; // Mark as not ready during reconnection
            },
            end: () => {
                log.warn('Connection ended (max retries exhausted)');
                redisClient = null;
                connectionPromise = null; // Allow new connection attempts
            }
        };

        // Register all event handlers
        Object.entries(events).forEach(([event, handler]) => {
            client.on(event, handler);
        });
    });

    return connectionPromise;
}

/**
 * Initialize the Dragonfly/Redis client
 */
export async function initializeDragonfly(): Promise<Redis> {
    try {
        const client = await getClient();
        log.info('Client initialized successfully');
        return client;
    } catch (error) {
        log.error('Failed to initialize client:', error);
        throw error; // Re-throw to be handled by the application startup logic
    }
}

/**
 * Set a cache key with optional expiration
 */
export async function setCache(key: string, value: string, ttlSeconds?: number): Promise<'OK' | null> {
    try {
        const client = await getClient();
        return ttlSeconds 
            ? await client.set(key, value, 'EX', ttlSeconds) 
            : await client.set(key, value);
    } catch (error) {
        log.error(`Error setting cache key "${key}":`, error);
        return null;
    }
}

/**
 * Set multiple cache keys in a single operation
 */
export async function setBulkCache(items: Array<{ key: string; value: string; ttl?: number }>): Promise<number> {
    if (!items.length) return 0;
    
    try {
        const client = await getClient();
        const pipeline = client.pipeline();
        
        items.forEach(({ key, value, ttl }) => {
            ttl 
                ? pipeline.set(key, value, 'EX', ttl)
                : pipeline.set(key, value);
        });
        
        const results = await pipeline.exec();
        return results ? results.length : 0;
    } catch (error) {
        log.error('Error setting bulk cache:', error);
        return 0;
    }
}

/**
 * Get a cached value by key
 */
export async function getCache(key: string): Promise<string | null> {
    try {
        const client = await getClient();
        return await client.get(key);
    } catch (error) {
        log.error(`Error getting cache key "${key}":`, error);
        return null;
    }
}

/**
 * Delete a cache key
 */
export async function deleteCache(key: string): Promise<number> {
    try {
        const client = await getClient();
        return await client.del(key);
    } catch (error) {
        log.error(`Error deleting cache key "${key}":`, error);
        return 0;
    }
}

/**
 * Get the Dragonfly/Redis client instance
 */
export async function getDragonflyClient(): Promise<Redis> {
    return getClient();
}

/**
 * Gracefully disconnect the Dragonfly/Redis client
 */
export async function disconnectDragonfly(): Promise<void> {
    if (redisClient) {
        try {
            await redisClient.quit();
            log.info('Client disconnected successfully');
        } catch (error) {
            log.error('Error disconnecting client:', error);
        }
        redisClient = null;
        connectionPromise = null;
    }
} 