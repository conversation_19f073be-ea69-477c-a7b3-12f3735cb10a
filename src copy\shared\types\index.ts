// export * from './common.types'; // Removed as its contents are moved
export * from './settings.types';
export * from './indicator.types';
// export * from './market.types'; // Removed duplicate export

// Импортируем типы, выведенные из Zod схем
import type { MarketTypeZod, KlineZod, TickerZod, FullTickerZod } from '../schemas/market.schema';

// Реэкспорт типов из существующих файлов
// export * from './settings.types'; // Already exported above
// export * from './indicator.types'; // Already exported above

// Базовые типы, теперь использующие Zod-выведенные типы для большей строгости
export type MarketType = MarketTypeZod;
export type Kline = KlineZod;
export type Ticker = TickerZod;
export type FullTicker = FullTickerZod;

// Типы для информации о символах (остаются как есть, если нет Zod схем)
export interface AppSymbolInfo {
  symbol: string;
  status: string;
  baseAsset: string;
  quoteAsset: string;
  baseAssetPrecision: number;
  quoteAssetPrecision: number;
  marketType: MarketType; // Теперь будет использовать MarketType = MarketTypeZod
}

