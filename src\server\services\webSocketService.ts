import { FastifyRequest } from 'fastify';
import { pino } from 'pino';
import { z } from 'zod';
import { subscribeToChannel, unsubscribeFromChannel, publishMessage } from '../lib/redis.js';

// Определяем базовые типы, которые нам нужны
type MarketType = 'spot' | 'futures';

interface Kline {
  symbol: string;
  marketType: MarketType;
  interval: string;
  openTime: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  closeTime?: number;
  quoteVolume?: number;
  trades?: number;
  isClosed?: boolean;
}

// Определяем тип для WebSocket соединения из Fastify
// Используем более точную типизацию, соответствующую WebSocket API
interface WebSocketLike {
  send: (data: string) => void;
  on(event: 'message', listener: (data: Buffer) => void): void;
  on(event: 'close', listener: () => void): void;
  on(event: 'error', listener: (error: Error) => void): void;
  close: () => void;
}

type SocketStream = {
  socket: WebSocketLike;
} | WebSocketLike;

// Создаем логгер
const logger = pino({ name: 'webSocketService', level: 'info' });

// Define message schemas
const SubscribeKlineMessageSchema = z.object({
  action: z.literal('subscribe'),
  type: z.literal('kline'),
  params: z.object({
    symbol: z.string(),
    interval: z.string(),
    marketType: z.string(),
  }),
});

const UnsubscribeKlineMessageSchema = z.object({
  action: z.literal('unsubscribe'),
  type: z.literal('kline'),
  params: z.object({
    symbol: z.string(),
    interval: z.string(),
    marketType: z.string(),
  }),
});

const MessageSchema = z.union([
  SubscribeKlineMessageSchema,
  UnsubscribeKlineMessageSchema,
]);

type WebSocketMessage = z.infer<typeof MessageSchema>;
type ClientSubscription = {
  symbol: string;
  interval: string;
  marketType: string;
  channel: string;
};

// Helper functions to work with SocketStream union type
function getWebSocket(connection: SocketStream): WebSocketLike {
  // Check if connection has socket property (SocketStream with socket)
  if ('socket' in connection) {
    return connection.socket;
  }
  // Otherwise, connection is direct WebSocket
  return connection;
}

/**
 * WebSocketManager - синглтон для управления WebSocket соединениями
 */
export class WebSocketManager {
  private static instance: WebSocketManager;
  private clients: Map<string, SocketStream> = new Map(); // clientId -> connection
  private subscriptions: Map<string, Set<string>> = new Map(); // channel -> Set of clientIds
  private clientSubscriptions: Map<string, Set<ClientSubscription>> = new Map(); // clientId -> Set of subscriptions
  private channelSubscribers: Map<string, number> = new Map(); // channel -> number of subscribers

  private constructor() {
    // Private constructor to implement singleton pattern
    logger.info('WebSocketManager initialized');
  }

  /**
   * Получить экземпляр WebSocketManager (синглтон)
   */
  public static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  /**
   * Обработать новое WebSocket соединение
   */
  public handleConnection(connection: SocketStream, request: FastifyRequest): void {
    const clientId = request.id as string;

    // Store client connection
    this.clients.set(clientId, connection);
    this.clientSubscriptions.set(clientId, new Set());

    logger.info({ clientId }, 'Client connected');

    // Get WebSocket instance from connection
    const ws = getWebSocket(connection);

    // Handle incoming messages
    ws.on('message', (message: Buffer) => {
      try {
        const parsedMessage = JSON.parse(message.toString());
        this.handleMessage(clientId, parsedMessage);
      } catch (err) {
        logger.error({ clientId, err }, 'Invalid message format');
        this.sendErrorToClient(clientId, 'Invalid message format');
      }
    });

    // Handle client disconnect
    ws.on('close', () => {
      this.handleDisconnect(clientId);
    });
  }

  /**
   * Обработать сообщение от клиента
   */
  private handleMessage(clientId: string, message: unknown): void {
    try {
      // Validate message format
      const validation = MessageSchema.safeParse(message);
      if (!validation.success) {
        logger.warn({ clientId, message, errors: validation.error.format() }, 'Message validation failed');
        this.sendErrorToClient(clientId, 'Invalid message format');
        return;
      }

      const validatedMessage = validation.data;
      logger.debug({ clientId, message: validatedMessage }, 'Received valid message');

      // Process message based on action
      switch (validatedMessage.action) {
        case 'subscribe':
          this.handleSubscription(clientId, validatedMessage);
          break;
        case 'unsubscribe':
          this.handleUnsubscription(clientId, validatedMessage);
          break;
        default:
          // This should not happen due to Zod validation
          this.sendErrorToClient(clientId, 'Unknown action');
      }
    } catch (err) {
      logger.error({ clientId, err, message }, 'Error processing message');
      this.sendErrorToClient(clientId, 'Error processing message');
    }
  }

  /**
   * Обработать подписку клиента
   */
  private handleSubscription(clientId: string, message: WebSocketMessage): void {
    if (message.action !== 'subscribe') return; // Type guard

    const { symbol, interval, marketType } = message.params;
    
    // Создать имя канала на основе параметров
    const channel = this.getChannelName(message.type, symbol, interval, marketType);

    // Получить текущие подписки клиента
    const clientSubs = this.clientSubscriptions.get(clientId) || new Set();
    
    // Проверить, подписан ли уже клиент на этот канал
    const existingSub = Array.from(clientSubs).find(
      (sub) => 
        sub.symbol === symbol && 
        sub.interval === interval && 
        sub.marketType === marketType
    );

    if (existingSub) {
      // Клиент уже подписан, отправляем уведомление
      logger.debug({ clientId, channel }, 'Client already subscribed to channel');
      this.sendMessageToClient(clientId, {
        type: 'subscribed', 
        channel,
        message: 'Already subscribed'
      });
      return;
    }

    // Добавляем подписку для клиента
    const subscription: ClientSubscription = { symbol, interval, marketType, channel };
    clientSubs.add(subscription);
    this.clientSubscriptions.set(clientId, clientSubs);

    // Добавляем клиента в список подписчиков канала
    const channelClients = this.subscriptions.get(channel) || new Set();
    channelClients.add(clientId);
    this.subscriptions.set(channel, channelClients);

    // Обновить счетчик подписчиков
    const subscribeCount = (this.channelSubscribers.get(channel) || 0) + 1;
    this.channelSubscribers.set(channel, subscribeCount);

    // Если это первая подписка на канал, подписаться в Redis
    if (subscribeCount === 1) {
      this.subscribeToRedisChannel(channel);
    }

    logger.info({ clientId, channel }, 'Client subscribed to channel');
    this.sendMessageToClient(clientId, {
      type: 'subscribed', 
      channel,
      message: 'Successfully subscribed'
    });
  }

  /**
   * Обработать отписку клиента
   */
  private handleUnsubscription(clientId: string, message: WebSocketMessage): void {
    if (message.action !== 'unsubscribe') return; // Type guard

    const { symbol, interval, marketType } = message.params;
    const channel = this.getChannelName(message.type, symbol, interval, marketType);

    this.unsubscribeClientFromChannel(clientId, channel);

    logger.info({ clientId, channel }, 'Client unsubscribed from channel');
    this.sendMessageToClient(clientId, {
      type: 'unsubscribed', 
      channel,
      message: 'Successfully unsubscribed'
    });
  }

  /**
   * Отписать клиента от канала
   */
  private unsubscribeClientFromChannel(clientId: string, channel: string): void {
    // Удаляем подписку из списка подписок клиента
    const clientSubs = this.clientSubscriptions.get(clientId);
    if (clientSubs) {
      const subToRemove = Array.from(clientSubs).find(sub => sub.channel === channel);
      if (subToRemove) {
        clientSubs.delete(subToRemove);
      }
    }

    // Удаляем клиента из списка подписчиков канала
    const channelClients = this.subscriptions.get(channel);
    if (channelClients) {
      channelClients.delete(clientId);
      
      // Если не осталось подписчиков, удаляем канал и отписываемся от Redis
      if (channelClients.size === 0) {
        this.subscriptions.delete(channel);
        this.channelSubscribers.set(channel, 0);
        this.unsubscribeFromRedisChannel(channel);
      } else {
        // Обновляем счетчик подписчиков
        const count = Math.max(0, (this.channelSubscribers.get(channel) || 1) - 1);
        this.channelSubscribers.set(channel, count);
      }
    }
  }

  /**
   * Обработать отключение клиента
   */
  private handleDisconnect(clientId: string): void {
    // Получить все подписки клиента
    const clientSubs = this.clientSubscriptions.get(clientId);
    if (clientSubs) {
      // Для каждой подписки удалить клиента из списка подписчиков
      clientSubs.forEach(sub => {
        const channelClients = this.subscriptions.get(sub.channel);
        if (channelClients) {
          channelClients.delete(clientId);
          
          // Если не осталось подписчиков, удаляем канал
          if (channelClients.size === 0) {
            this.subscriptions.delete(sub.channel);
            this.channelSubscribers.set(sub.channel, 0);
            this.unsubscribeFromRedisChannel(sub.channel);
          } else {
            // Обновляем счетчик подписчиков
            const count = Math.max(0, (this.channelSubscribers.get(sub.channel) || 1) - 1);
            this.channelSubscribers.set(sub.channel, count);
          }
        }
      });
    }
    
    // Удаляем клиента из списков
    this.clients.delete(clientId);
    this.clientSubscriptions.delete(clientId);
    
    logger.info({ clientId }, 'Client disconnected');
  }

  /**
   * Подписаться на Redis-канал и настроить обработку сообщений
   */
  private subscribeToRedisChannel(channel: string): void {
    logger.info({ channel }, 'Subscribing to Redis channel');
    
    // Подписываемся на Redis-канал
    subscribeToChannel(channel, (message) => {
      // Получаем всех клиентов, подписанных на канал
      const subscribers = this.subscriptions.get(channel);
      if (!subscribers || subscribers.size === 0) {
        return;
      }
      
      // Отправляем сообщение каждому клиенту
      subscribers.forEach(clientId => {
        this.sendMessageToClient(clientId, {
          type: 'update',
          channel,
          data: message
        });
      });
    });
  }

  /**
   * Отписаться от Redis-канала
   */
  private unsubscribeFromRedisChannel(channel: string): void {
    logger.info({ channel }, 'Unsubscribing from Redis channel');
    unsubscribeFromChannel(channel);
  }

  /**
   * Создать имя канала на основе типа данных и параметров
   */
  public getChannelName(type: string, symbol: string, interval: string, marketType: string): string {
    return `pubsub:${type}:${symbol}:${interval}:${marketType}`;
  }

  /**
   * Отправить сообщение клиенту
   */
  private sendMessageToClient(clientId: string, message: unknown): void {
    try {
      const connection = this.clients.get(clientId);
      if (!connection) {
        logger.warn({ clientId }, 'Attempt to send message to disconnected client');
        return;
      }

      const serializedMessage = JSON.stringify(message);
      const ws = getWebSocket(connection);
      ws.send(serializedMessage);
    } catch (err) {
      logger.error({ clientId, err }, 'Error sending message to client');
      // Если произошла ошибка при отправке, возможно клиент отключился
      // Удаляем клиента из списка подключенных
      this.handleDisconnect(clientId);
    }
  }

  /**
   * Отправить сообщение об ошибке клиенту
   */
  private sendErrorToClient(clientId: string, errorMessage: string): void {
    this.sendMessageToClient(clientId, {
      type: 'error',
      message: errorMessage
    });
  }

  /**
   * Публиковать обновление свечи
   */
  public publishKlineUpdate(kline: Kline): void {
    const { symbol, interval, marketType } = kline;
    const channel = this.getChannelName('kline', symbol, interval, marketType);
    
    // Публикуем обновление в Redis
    publishMessage(channel, kline);
  }
} 