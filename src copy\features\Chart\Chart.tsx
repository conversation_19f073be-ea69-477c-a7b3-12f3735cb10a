"use client";

import React, { useRef, useEffect, useState, useCallback, useMemo, useId, createContext, useContext } from 'react';
import {
  create<PERSON>hart,
  IChartApi,
  UTCTimestamp,
  ISeriesApi,
  PriceLineOptions, 
  IPriceLine, 
  CandlestickSeries, 
  PriceFormatterFn, 
  BarPrice,
  LineWidth,
  CandlestickData,
  ChartOptions,
  DeepPartial,
  SeriesOptionsMap,
  TimeScaleOptions,
  PriceScaleOptions,
  MouseEventParams,
  IPriceScaleApi,
} from 'lightweight-charts';
// Import OhlcvData type
import { 
  Kline, 
  MarketType, 
  useMarketStore, 
  useKlines,
  useWebSocketSubscription,
  useHistoricalKlinesQuery,
  useWebSocketAutoConnect 
} from '@/shared/index';
// Import hooks from their correct market locations
// Import wsEvents for snapshot listener
// Restore useHistoricalKlinesQuery
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/shared/ui/dropdown-menu";
import { Icon } from '@/shared/ui/icons/all_Icon';
import { Skeleton } from "@/shared/ui/skeleton";
import { 
  useAppSettingsStore, 
  selectAutoHideScalesEnabled, 
  selectToggleAutoHideScales,
  selectChartBackgroundColor,
  selectChartTextColor,
  selectChartBorderColor,
  selectChartGridVertLinesVisible,
  selectChartGridHorzLinesVisible,
  selectChartGridVertLinesColor,
  selectChartGridHorzLinesColor,
  selectChartCandleBodyUpColor,
  selectChartCandleBodyDownColor,
  selectChartCandleBorderUpColor,
  selectChartCandleBorderDownColor,
  selectChartCandleWickUpColor,
  selectChartCandleWickDownColor,
  selectChartRightOffset,
  selectChartBarSpacing,
  selectChartScaleLabelBackgroundColor,
  selectChartScaleLabelTextColor,
  selectChartPriceLineColor,
} from '@/shared/store/settingsStore';
import { useInterval, useLatest } from 'react-use';
import { useChartIndicatorsIntegration } from './useChartIndicators';
import { usePriceScaleCountdownLabel } from './plugins/usePriceAndTimer';
import { useChartData, ChartDataHookResult, ChartDataHookOptions } from './useChartData'; // Added import for the new hook

// --- Constants ---
const AVAILABLE_TIMEFRAMES = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '1d', '1w'];
const EMPTY_KLINE_DATA: ReadonlyArray<Kline> = [];
const MINIMUM_MOVE = 0.00000001; // Used for priceFormat minMove
const DEFAULT_HISTORICAL_LIMIT = 1000; // Number of historical candles to fetch
const KLINE_UPDATE_DEBOUNCE_MS = 333; // Debounce time for WS updates callback
const COUNTDOWN_LABEL_PIXEL_OFFSET = 2; // Pixel offset for the countdown label on the price scale
const SCALE_DIMENSION_UPDATE_DELAY_MS = 50; // Delay for initial scale dimension calculation
const SCALE_VISIBILITY_UPDATE_DELAY_MS = 0; // Delay for updating dimensions after visibility change
const HIDDEN_SCALE_BAR_SPACING_ADJUSTMENT = 0.5; // How much to adjust bar spacing when scales are hidden

// --- Helper Functions ---

/** Calculates interval duration in seconds */
function getIntervalDurationSeconds(interval: string): number {
  const match = interval.match(/^(\d+)(\w)$/);
  if (!match) return 60;
  const value = parseInt(match[1], 10);
  const unit = match[2].toLowerCase();
  const unitMultipliers: Record<string, number> = { s: 1, m: 60, h: 3600, d: 86400, w: 604800 };
  return (unitMultipliers[unit] || 60) * value;
}

/** Determines price precision based on magnitude */
const getPrecision = (price: number): number => {
    if (!isFinite(price)) return 2;
    const absPrice = Math.abs(price);
  // If the price is effectively zero (or very close), display as 0.00
  if (absPrice < 1e-7) return 2; // Changed from 8 to 2 for near-zero values

  // Keep the existing logic for other thresholds
  const thresholds = [1e-6, 1e-4, 0.01, 1, 10, 100, 1000];
  const precisions = [8, 7, 6, 5, 4, 3, 2, 1];
    for (let i = 0; i < thresholds.length; i++) {
        if (absPrice < thresholds[i]) return precisions[i];
    }
    return precisions[precisions.length - 1];
};

/** Formats time remaining until the next bar closes */
function formatTimeToBarClose(ms: number, interval: string): string {
  if (!isFinite(ms) || ms < 0) {
    const unit = interval.slice(-1);
    const val = parseInt(interval.slice(0, -1), 10);
    if (unit === 'd' || unit === 'w') return '0d 0h';
    if (unit === 'h' || (unit === 'm' && val >= 60)) return '0h 0m';
    return '00:00';
  }
  const sec = Math.floor(ms / 1000);
  if (sec < 86400) { // Less than a day
    const h = Math.floor(sec / 3600);
    const m = Math.floor((sec % 3600) / 60);
    const s = sec % 60;
    return h > 0
      ? `${h.toString()}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`
      : `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  }
  const d = Math.floor(sec / 86400);
  const h = Math.floor((sec % 86400) / 3600);
  return `${d}d ${h}h`;
}

/** Formats price with dynamic precision */
function formatChartPrice(price: number): string {
  return isFinite(price) ? price.toFixed(getPrecision(price)) : '';
}

/** Creates a global price formatter function */
const createGlobalPriceFormatter = (): PriceFormatterFn => (price: BarPrice): string => {
  return formatChartPrice(Number(price));
};

/** Calculates time until next bar closes based on candle data and interval */
const calculateTimeToBarCloseFromCandle = (candleTime: number, interval: string, closeTime?: number): number => {
  if (typeof candleTime !== 'number' || !isFinite(candleTime)) return 0;
  const nowSec = Math.floor(Date.now() / 1000);
  if (closeTime && isFinite(closeTime)) {
    return Math.max(0, (closeTime - nowSec) * 1000);
  }
  const durationSeconds = getIntervalDurationSeconds(interval);
  if (!durationSeconds) return 0;
  const calculatedCloseTimeSec = candleTime + durationSeconds;
  return Math.max(0, (calculatedCloseTimeSec - nowSec) * 1000);
};

/** Safely removes a price line from a series */
const safeRemovePriceLine = (series: ISeriesApi<keyof SeriesOptionsMap> | null, line: IPriceLine | null): void => {
  if (series && line) {
    try {
      series.removePriceLine(line);
    } catch (e) { /* Ignore errors during cleanup */ }
  }
};

// --- React Context for Chart State ---
type ChartContextType = {
  chartId: string;
  timeToBarClose: number;
  setTimeToBarClose: React.Dispatch<React.SetStateAction<number>>;
};
const ChartContext = createContext<ChartContextType | null>(null);

const ChartContextProvider: React.FC<{children: React.ReactNode; chartId: string}> = ({ children, chartId }) => {
  const [timeToBarClose, setTimeToBarClose] = useState(0);

  // Memoize the context value to prevent unnecessary re-renders of consumers
  const contextValue = useMemo(() => ({
    chartId,
    timeToBarClose,
    setTimeToBarClose,
  }), [chartId, timeToBarClose, setTimeToBarClose]);

  return (
    <ChartContext.Provider value={contextValue}>
      {children}
    </ChartContext.Provider>
  );
};

const useChartContext = () => {
  const context = useContext(ChartContext);
  if (!context) throw new Error('useChartContext must be used within a ChartContextProvider');
  return context;
};

// --- Types ---
interface SimpleChartProps {
  symbol: string;
  marketType: MarketType;
  interval: string;
  onIntervalChange?: (newInterval: string) => void;
  onFullscreenToggle?: () => void;
}

interface ChartSettings {
  chartBackgroundColor: string;
  chartTextColor: string;
  gridVertLinesVisible: boolean;
  gridHorzLinesVisible: boolean;
  gridVertLinesColor: string;
  gridHorzLinesColor: string;
  borderColor: string;
  candleBodyUpColor: string;
  candleBodyDownColor: string;
  candleBorderUpColor: string;
  candleBorderDownColor: string;
  candleWickUpColor: string;
  candleWickDownColor: string;
  rightOffset: number;
  barSpacing: number;
  scaleLabelBackgroundColor: string;
  scaleLabelTextColor: string;
  chartPriceLineColor: string;
  autoHideScalesEnabled: boolean;
}

// --- Custom Hooks ---

/** Hook to manage chart initialization, options, and cleanup */
function useChartCore(
  chartContainerRef: React.RefObject<HTMLDivElement | null>,
  settings: ChartSettings
) {
  const chartRef = useRef<IChartApi | null>(null);
  const seriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const [isReady, setIsReady] = useState(false);

  const {
    chartBackgroundColor, chartTextColor, gridVertLinesColor, gridVertLinesVisible,
    gridHorzLinesColor, gridHorzLinesVisible, borderColor, barSpacing, rightOffset
  } = settings;

  const getBaseChartOptions = useCallback((): DeepPartial<ChartOptions> => ({
    layout: {
      background: { color: chartBackgroundColor },
      textColor: chartTextColor,
      attributionLogo: false,
    },
    grid: {
      vertLines: { color: gridVertLinesColor, visible: gridVertLinesVisible },
      horzLines: { color: gridHorzLinesColor, visible: gridHorzLinesVisible },
    },
    localization: {
      priceFormatter: createGlobalPriceFormatter(),
    },
    timeScale: {
      borderColor: borderColor,
      timeVisible: true,
      visible: true,
      barSpacing: barSpacing,
      rightOffset: rightOffset,
    },
    rightPriceScale: {
      borderColor: borderColor,
      visible: true,
    },
    crosshair: { mode: 0 },
    handleScroll: true,
    handleScale: true,
    autoSize: true,
  }), [
    chartBackgroundColor, chartTextColor, gridVertLinesColor, gridVertLinesVisible,
    gridHorzLinesColor, gridHorzLinesVisible, borderColor, barSpacing, rightOffset
  ]);

  // Destructure settings needed for candlestick options
  const {
    candleBodyUpColor, candleBodyDownColor, candleBorderUpColor, candleBorderDownColor,
    candleWickUpColor, candleWickDownColor, chartPriceLineColor
  } = settings;

  const getCandlestickOptions = useCallback((): DeepPartial<SeriesOptionsMap['Candlestick']> => ({
    upColor: candleBodyUpColor,
    downColor: candleBodyDownColor,
      borderVisible: true,
    borderUpColor: candleBorderUpColor,
    borderDownColor: candleBorderDownColor,
    wickUpColor: candleWickUpColor,
    wickDownColor: candleWickDownColor,
    priceLineVisible: true, // Controlled later maybe, but default to true
    lastValueVisible: false, // We use custom label
      priceLineWidth: 1 as LineWidth,
    priceLineColor: chartPriceLineColor,
      priceFormat: {
      type: 'price',
      minMove: MINIMUM_MOVE,
    },
  }), [
    // Explicitly list dependencies
    candleBodyUpColor, candleBodyDownColor, candleBorderUpColor, candleBorderDownColor,
    candleWickUpColor, candleWickDownColor, chartPriceLineColor
  ]);

  // Initialize chart and series
  useEffect(() => {
    if (!chartContainerRef.current) return;

    const chart = createChart(chartContainerRef.current, getBaseChartOptions());
    const candlestickSeries = chart.addSeries(CandlestickSeries, getCandlestickOptions());

    chartRef.current = chart;
    seriesRef.current = candlestickSeries;
    setIsReady(true);

    // Cleanup function
    return () => {
      const chartToRemove = chartRef.current;
      chartRef.current = null;
      seriesRef.current = null;
      setIsReady(false);
      if (chartToRemove) {
        try {
          chartToRemove.remove();
        } catch (e) { /* Ignore cleanup errors */ }
      }
    };
  }, [chartContainerRef, getBaseChartOptions, getCandlestickOptions]); // Re-create chart if base options change fundamentally

  // Apply general chart options reactively
  useEffect(() => {
    const chart = chartRef.current;
    if (!chart || !isReady) return;
    try {
      // Apply only layout, grid, localization, timescale, rightPriceScale options that might change
      const { layout, grid, localization, timeScale, rightPriceScale, ...rest } = getBaseChartOptions();
      // Don't re-apply formatter constantly if it hasn't changed structure
      const optionsToApply: DeepPartial<ChartOptions> = { layout, grid, timeScale, rightPriceScale };
      if (JSON.stringify(localization) !== JSON.stringify(chart.options().localization)) {
        // Explicitly cast to 'any' to avoid type errors when adding localization dynamically
        (optionsToApply as any).localization = localization;
      }
      // Prevent flicker by not touching visibility here
      // Visibility is handled by useScaleInteraction
      delete (optionsToApply.timeScale as DeepPartial<TimeScaleOptions> | undefined)?.visible;
      delete (optionsToApply.rightPriceScale as DeepPartial<PriceScaleOptions> | undefined)?.visible;
      
      chart.applyOptions(optionsToApply);
    } catch (error) { /* Ignore */ }
  }, [getBaseChartOptions, isReady]);

  // Apply series options reactively
  useEffect(() => {
    const series = seriesRef.current;
    if (!series || !isReady) return;
    try {
      series.applyOptions(getCandlestickOptions());
    } catch (error) { /* Ignore */ }
  }, [getCandlestickOptions, isReady]);

  return { chartRef, seriesRef, isReady, getBaseChartOptions };
}


/** Hook to manage the countdown timer */
function useCountdown(
  lastCandleRef: React.RefObject<Kline | null>,
  interval: string,
  isReady: boolean,
  setTimeToBarClose: React.Dispatch<React.SetStateAction<number>>
) {
  const latestLastCandle = useLatest(lastCandleRef.current);

  const updateCountdown = useCallback(() => {
    const currentCandle = latestLastCandle.current;
    const candleTime = currentCandle?.openTime ? Math.floor(currentCandle.openTime / 1000) : null;
    const candleCloseTime = currentCandle?.closeTime ? Math.floor(currentCandle.closeTime / 1000) : undefined;

    if (currentCandle?.isClosed) {
      setTimeToBarClose(0);
      return;
    }
    
    if (candleTime) {
      const timeToClose = calculateTimeToBarCloseFromCandle(candleTime, interval, candleCloseTime);
      setTimeToBarClose(timeToClose);
    } else {
      setTimeToBarClose(0);
    }
  }, [interval, setTimeToBarClose, latestLastCandle]);

  useInterval(updateCountdown, isReady ? 1000 : null);

  // Trigger initial update when ready or candle changes
  useEffect(() => {
    if (isReady) {
      updateCountdown();
    }
  }, [isReady, lastCandleRef.current, updateCountdown]); // Re-run if candle ref content changes
}

/** Hook to manage scale visibility, dimensions, and interaction */
function useScaleInteraction(
  chartRef: React.RefObject<IChartApi | null>,
  isReady: boolean,
  autoHideScalesEnabled: boolean,
  baseBarSpacing: number // Pass base bar spacing from options
) {
  const [scaleWidth, setScaleWidth] = useState(0);
  const [timeScaleHeight, setTimeScaleHeight] = useState(0);
  const [isHovered, setIsHovered] = useState(false); // Hovering over chart area
  
  const updateScaleDimensions = useCallback(() => {
    if (!chartRef.current || !isReady) return;
    try {
      const chart = chartRef.current;
      const priceScale = chart.priceScale('right');
      const timeScale = chart.timeScale();
      if (priceScale && timeScale) {
        const newWidth = priceScale.width();
        const newHeight = timeScale.height();
        // Update state only if dimensions actually changed
        setScaleWidth(prev => (newWidth !== prev ? newWidth : prev));
        setTimeScaleHeight(prev => (newHeight !== prev ? newHeight : prev));
      } else {
        // Reset if scales become unavailable
        setScaleWidth(0);
        setTimeScaleHeight(0);
      }
    } catch (e) { /* Ignore errors during dimension update */ }
  }, [isReady, chartRef]);

  // Effect for initial dimension calculation and resize listener
  useEffect(() => {
    if (!isReady) return;
    // Initial calculation might need slight delay for layout stabilization
    const initialTimeout = setTimeout(updateScaleDimensions, SCALE_DIMENSION_UPDATE_DELAY_MS); // Use constant
    window.addEventListener('resize', updateScaleDimensions);
    return () => {
      clearTimeout(initialTimeout);
      window.removeEventListener('resize', updateScaleDimensions);
    };
  }, [isReady, updateScaleDimensions]);

  // Effect to control scale visibility and update dimensions after visibility change
  useEffect(() => {
    const chart = chartRef.current;
    if (!chart || !isReady) return;

    const shouldBeVisible = !autoHideScalesEnabled || isHovered;

    try {
      const timeScaleOptions: DeepPartial<TimeScaleOptions> = {
        visible: shouldBeVisible,
        // Slightly adjust bar spacing when scales are hidden to potentially compensate for layout shifts
        barSpacing: shouldBeVisible ? baseBarSpacing : baseBarSpacing + HIDDEN_SCALE_BAR_SPACING_ADJUSTMENT, // Use constant
      };
      const priceScaleOptions: DeepPartial<PriceScaleOptions> = {
        visible: shouldBeVisible,
      };

      chart.applyOptions({
          timeScale: timeScaleOptions,
          rightPriceScale: priceScaleOptions
      });

      // Update dimensions shortly after visibility change to ensure correct measurements
      const visibilityTimeout = setTimeout(updateScaleDimensions, SCALE_VISIBILITY_UPDATE_DELAY_MS); // Use constant
      return () => clearTimeout(visibilityTimeout);

    } catch (error) { /* Ignore applyOptions errors */ }
  }, [isReady, isHovered, autoHideScalesEnabled, baseBarSpacing, chartRef, updateScaleDimensions]);

  // Event Handlers for Hover States
  const handleMouseEnter = useCallback(() => setIsHovered(true), []);
  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  return {
    scaleWidth, timeScaleHeight, isHovered,
    handleMouseEnter, handleMouseLeave,
  };
}


// --- UI Components ---

const EyeIconSvg: React.FC = React.memo(() => (
  <svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0" y="0" viewBox="0 0 512 512" width="14" height="14">
    <g>
      <circle cx="256" cy="256" r="80" fill="none" stroke="currentColor" strokeWidth="40" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10"></circle>
      <path d="M483.24 231.168c11.679 14.438 11.679 35.226 0 49.664C446.883 325.775 355.535 416 256 416S65.117 325.775 28.76 280.832c-11.679-14.438-11.679-35.226 0-49.664C65.117 186.225 156.465 96 256 96s190.883 90.225 227.24 135.168z" fill="none" stroke="currentColor" strokeWidth="40" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10"></path>
    </g>
  </svg>
));
EyeIconSvg.displayName = 'EyeIconSvg';

interface ToggleScalesButtonProps {
  isVisible: boolean;
  scaleWidth: number;
  timeScaleHeight: number;
  isButtonHovered: boolean;
  chartBackgroundColor: string; // Use direct prop
  autoHideScalesEnabled: boolean;
  onToggle: () => void;
  onContainerMouseEnter: () => void;
  onContainerMouseLeave: () => void;
  onButtonMouseEnter: () => void;
  onButtonMouseLeave: () => void;
}

const ToggleScalesVisibilityButton: React.FC<ToggleScalesButtonProps> = React.memo(({
  isVisible, scaleWidth, timeScaleHeight, isButtonHovered, chartBackgroundColor,
  autoHideScalesEnabled, onToggle, onContainerMouseEnter, onContainerMouseLeave,
  onButtonMouseEnter, onButtonMouseLeave,
}) => {
  // Render only if conditions met (visible, scale dimensions valid)
  if (!isVisible || scaleWidth <= 0 || timeScaleHeight <= 0) {
    return null;
  }

  return (
    <div 
      className="absolute z-5"
          style={{
            width: `calc(${scaleWidth}px - 2px)`, 
            height: '24px', // Adjusted height for the button area
            bottom: `${timeScaleHeight}px`,
            right: '0px', // Explicit right position
          }}
          onMouseEnter={onContainerMouseEnter}
          onMouseLeave={onContainerMouseLeave}
    >
      <button
        className="w-full h-full text-neutral-400 hover:text-neutral-100 transition-colors duration-150 flex items-center justify-center border-none focus:outline-none focus-visible:ring-1 focus-visible:ring-ring/50"
        style={{
           // Use the chart's background color directly for seamless integration
           backgroundColor: chartBackgroundColor,
        }}
        onClick={onToggle}
        title="Переключить автоскрытие шкал"
        onMouseEnter={onButtonMouseEnter}
        onMouseLeave={onButtonMouseLeave}
      >
        <EyeIconSvg />
      </button>
    </div>
  );
});
ToggleScalesVisibilityButton.displayName = 'ToggleScalesVisibilityButton';


// --- Main Chart Component Logic ---

const ChartContent: React.FC<SimpleChartProps & { uniqueChartId: string }> = React.memo(({
  symbol,
  marketType,
  interval,
  onIntervalChange,
  onFullscreenToggle,
  uniqueChartId
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const { timeToBarClose, setTimeToBarClose } = useChartContext();
  const [isPointerOverScaleCoords, setIsPointerOverScaleCoords] = useState(false);
  const [isButtonHovered, setIsButtonHovered] = useState(false);
  const handleButtonMouseEnter = useCallback(() => setIsButtonHovered(true), []);
  const handleButtonMouseLeave = useCallback(() => setIsButtonHovered(false), []);
  
  // Ensure WebSocket is always connected
  useWebSocketAutoConnect();

  // <<< --- НОВЫЙ ЛОГ МОНТИРОВАНИЯ/РАЗМОНТИРОВАНИЯ --- >>>
  useEffect(() => {
    return () => {
    };
  }, [uniqueChartId, symbol, marketType, interval]);
  // <<< --- КОНЕЦ НОВОГО ЛОГА --- >>>

  // --- Settings from Store ---
  // Select individual state pieces first
  const chartBackgroundColor = useAppSettingsStore(selectChartBackgroundColor);
  const chartTextColor = useAppSettingsStore(selectChartTextColor);
  const gridVertLinesVisible = useAppSettingsStore(selectChartGridVertLinesVisible);
  const gridHorzLinesVisible = useAppSettingsStore(selectChartGridHorzLinesVisible);
  const gridVertLinesColor = useAppSettingsStore(selectChartGridVertLinesColor);
  const gridHorzLinesColor = useAppSettingsStore(selectChartGridHorzLinesColor);
  const borderColor = useAppSettingsStore(selectChartBorderColor);
  const candleBodyUpColor = useAppSettingsStore(selectChartCandleBodyUpColor);
  const candleBodyDownColor = useAppSettingsStore(selectChartCandleBodyDownColor);
  const candleBorderUpColor = useAppSettingsStore(selectChartCandleBorderUpColor);
  const candleBorderDownColor = useAppSettingsStore(selectChartCandleBorderDownColor);
  const candleWickUpColor = useAppSettingsStore(selectChartCandleWickUpColor);
  const candleWickDownColor = useAppSettingsStore(selectChartCandleWickDownColor);
  const rightOffset = useAppSettingsStore(selectChartRightOffset);
  const barSpacing = useAppSettingsStore(selectChartBarSpacing);
  const scaleLabelBackgroundColor = useAppSettingsStore(selectChartScaleLabelBackgroundColor);
  const scaleLabelTextColor = useAppSettingsStore(selectChartScaleLabelTextColor);
  const chartPriceLineColor = useAppSettingsStore(selectChartPriceLineColor);
  const autoHideScalesEnabled = useAppSettingsStore(selectAutoHideScalesEnabled);
  const toggleAutoHideScales = useAppSettingsStore(selectToggleAutoHideScales);

  // Memoize the derived settings object based on selected values
  const settings = useMemo<ChartSettings>(() => ({
    chartBackgroundColor,
    chartTextColor,
    gridVertLinesVisible,
    gridHorzLinesVisible,
    // Provide defaults here to ensure string type for ChartSettings
    gridVertLinesColor: gridVertLinesColor ?? 'rgba(70, 70, 70, 0.5)',
    gridHorzLinesColor: gridHorzLinesColor ?? 'rgba(70, 70, 70, 0.5)',
    borderColor: borderColor ?? '#cccccc',
    candleBodyUpColor: candleBodyUpColor ?? '#26a69a',
    candleBodyDownColor: candleBodyDownColor ?? '#ef5350',
    candleBorderUpColor: candleBorderUpColor ?? '#26a69a',
    candleBorderDownColor: candleBorderDownColor ?? '#ef5350',
    candleWickUpColor: candleWickUpColor ?? '#26a69a',
    candleWickDownColor: candleWickDownColor ?? '#ef5350',
    rightOffset,
    barSpacing,
    scaleLabelBackgroundColor: scaleLabelBackgroundColor ?? '#1f2937',
    scaleLabelTextColor: scaleLabelTextColor ?? '#d1d5db',
    chartPriceLineColor: chartPriceLineColor ?? '#cccccc',
    autoHideScalesEnabled,
  }), [
      chartBackgroundColor,
      chartTextColor,
      gridVertLinesVisible,
      gridHorzLinesVisible,
      gridVertLinesColor,
      gridHorzLinesColor,
      borderColor,
      candleBodyUpColor,
      candleBodyDownColor,
      candleBorderUpColor,
      candleBorderDownColor,
      candleWickUpColor,
      candleWickDownColor,
      rightOffset,
      barSpacing,
      scaleLabelBackgroundColor,
      scaleLabelTextColor,
      chartPriceLineColor,
      autoHideScalesEnabled,
  ]);

  // --- Core Chart Hook ---
  const { chartRef, seriesRef, isReady, getBaseChartOptions } = useChartCore(chartContainerRef, settings);

  // --- Data Handling Hook ---
  // Callback to update price lines and countdown when data changes
  const handleDataUpdate = useCallback((lastKline: Kline | null, source: 'hist' | 'ws') => {
  }, [/* Dependencies if any */]); // <<<--- Удаляем removeCountdownLabel из зависимостей

  const handleWsError = useCallback((error: Error) => {
  }, [symbol, interval]);

  // Use the new useChartData hook
  const { 
    isInitialLoading, 
    isError, 
    errorMessage, 
    lastCandleRef, 
    lastKnownPriceRef, 
    processedKlineData, 
    rawKlines
  }: ChartDataHookResult = useChartData({
    chartRef,
    marketType,
    symbol,
    interval,
    seriesRef,
    isReady,
    uniqueChartId,
    onDataUpdate: handleDataUpdate,
    onSubscriptionError: handleWsError,
  } as ChartDataHookOptions); // Explicit cast to satisfy type checking if needed, or ensure options match

  // --- Countdown Hook ---
  useCountdown(lastCandleRef, interval, isReady, setTimeToBarClose);

  // --- Price Scale Countdown Label Hook (NEW) ---
    usePriceScaleCountdownLabel(
      chartRef.current,
      seriesRef.current ?? null,
      lastKnownPriceRef.current,
      timeToBarClose,
      interval,
      {
        priceTextColor: settings.scaleLabelTextColor,
        countdownTextColor: settings.scaleLabelTextColor,
        backgroundColor: settings.scaleLabelBackgroundColor,
      }
    );

  // --- Scale Interaction Hook ---
  const baseBarSpacing = useMemo(() => getBaseChartOptions().timeScale?.barSpacing ?? 6, [getBaseChartOptions]);
  const {
    scaleWidth, timeScaleHeight, isHovered,
    handleMouseEnter, handleMouseLeave,
  } = useScaleInteraction(chartRef, isReady, autoHideScalesEnabled, baseBarSpacing);

  // --- Indicator Integration Hook ---
  useChartIndicatorsIntegration(
    chartRef,
    uniqueChartId,
    rawKlines,
    symbol,
    interval as Kline['interval']
  );

  // --- Effect for Container Pointer Events Listener ---
  useEffect(() => {
    const container = chartContainerRef.current;
    if (!isReady || !container || scaleWidth <= 0) {
      // Ensure state is false if conditions aren't met
      if (isPointerOverScaleCoords) setIsPointerOverScaleCoords(false);
      return;
    }

    const handlePointerMove = (e: PointerEvent) => {
      if (!container) return; // Should not happen, but safety check
      const clientWidth = container.clientWidth;
      const rect = container.getBoundingClientRect();
      const localX = e.clientX - rect.left;
      const isOverScale = localX >= clientWidth - scaleWidth;

      // Always attempt to set state (React handles optimization)
      setIsPointerOverScaleCoords(isOverScale);
    };

    const handlePointerLeave = () => {
        // Always set to false when leaving the container
        setIsPointerOverScaleCoords(false);
    };

    // Attach listeners to the main chart container
    container.addEventListener('pointermove', handlePointerMove);
    container.addEventListener('pointerleave', handlePointerLeave);

    // Cleanup function
    return () => {
      container.removeEventListener('pointermove', handlePointerMove);
      container.removeEventListener('pointerleave', handlePointerLeave);
      // Reset state on cleanup
      setIsPointerOverScaleCoords(false);
    };

  // Re-run if readiness, container ref, or scaleWidth changes
  }, [isReady, chartContainerRef, scaleWidth, isPointerOverScaleCoords]); // Restore isPointerOverScaleCoords dependency


  // --- Interval Change Handling ---
  const handleIntervalSelect = useCallback((newInterval: string) => {
    onIntervalChange?.(newInterval);
  }, [onIntervalChange]);

  // --- Double Click for Fullscreen Effect ---
  useEffect(() => {
    const chart = chartRef.current;
    const container = chartContainerRef.current;
    if (!chart || !isReady || !container || !onFullscreenToggle) return; // Exit if chart/toggle not ready

    const handleChartDoubleClick = (param: MouseEventParams) => {
      if (!param.point) return; // Click outside the chart area

      const rect = container.getBoundingClientRect();
      const chartWidth = rect.width;
      const chartHeight = rect.height;

      // Check if the click occurred within the main chart area (excluding scales)
      const isClickInsideChartArea =
        param.point.x > 0 && param.point.x < chartWidth - scaleWidth &&
        param.point.y > 0 && param.point.y < chartHeight - timeScaleHeight;

      if (isClickInsideChartArea) {
        onFullscreenToggle();
      }
    };

    // Subscribe to the double-click event
    chart.subscribeDblClick(handleChartDoubleClick);

    // Cleanup: Unsubscribe when component unmounts or dependencies change
    return () => {
      try {
        chart.unsubscribeDblClick(handleChartDoubleClick);
      } catch (e) { /* Ignore cleanup errors */ }
    };
  // Dependencies: Ensure effect re-runs if any of these change
  }, [chartRef, isReady, onFullscreenToggle, scaleWidth, timeScaleHeight, chartContainerRef]);

  // --- Render Logic ---
  return (
    <div
      ref={chartContainerRef}
      className="chart-container relative h-full w-full border rounded-md overflow-hidden select-none cursor-crosshair"
      style={{ borderColor: settings.borderColor }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Показываем скелетон, если график еще не готов */}
      {!isReady && (
        <Skeleton className="absolute inset-0 z-30" /> // Высокий z-index, чтобы перекрыть все остальные элементы
      )}

      {/* Chart Header - отображаем только если график готов */}
      {isReady && (
        <div className="chart-header absolute top-0 left-0 right-0 z-10 p-2 flex items-center justify-between text-xs pointer-events-none">
          <div className="flex items-center pointer-events-auto bg-black/20 backdrop-blur-sm rounded">
            <span className="font-medium text-xs text-neutral-200 px-1.5 py-0.5">
                {symbol}
            </span>
            <DropdownMenu>
                <DropdownMenuTrigger>
                  <div className="flex items-center justify-center rounded-sm px-1 text-[10px] font-medium leading-none text-muted-foreground hover:bg-accent hover:text-accent-foreground">
                    <span>{interval}</span>
                    <Icon name="ChevronDown" />
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="pointer-events-auto">
                  {AVAILABLE_TIMEFRAMES.map((tf) => (
                    <DropdownMenuItem
                      key={tf}
                      disabled={tf === interval || isInitialLoading} // Use correct loading state variable
                      onSelect={() => handleIntervalSelect(tf)}
                    >
                      {tf}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="status-area flex items-center gap-2 pointer-events-auto">
            {isError && errorMessage && ( // Use correct error state variables
              <div className="text-xs text-red-400 truncate max-w-[150px] px-1.5 py-0.5 rounded bg-red-900/30 backdrop-blur-sm" title={errorMessage}>
                Load Error
              </div>
            )}
          </div>
        </div>
      )}

      {/* Toggle Scales Button - отображаем только если график готов */}
      {isReady && (
        <ToggleScalesVisibilityButton
          isVisible={isReady && (isPointerOverScaleCoords || isButtonHovered) && scaleWidth > 0 && timeScaleHeight > 0}
          scaleWidth={scaleWidth}
          timeScaleHeight={timeScaleHeight}
          isButtonHovered={isButtonHovered}
          chartBackgroundColor={settings.chartBackgroundColor}
          autoHideScalesEnabled={settings.autoHideScalesEnabled}
          onToggle={toggleAutoHideScales}
          onContainerMouseEnter={handleButtonMouseEnter}
          onContainerMouseLeave={handleButtonMouseLeave}
          onButtonMouseEnter={handleButtonMouseEnter}
          onButtonMouseLeave={handleButtonMouseLeave}
        />
      )}
    </div>
  );
});


// --- Entry Point Component ---
const SimpleChart: React.FC<SimpleChartProps> = React.memo(({
  symbol,
  marketType,
  interval,
  onIntervalChange,
  onFullscreenToggle
}) => {
  const uniqueChartId = useId();
  
  // Если нет допустимого символа или market type еще не определен, 
  // показываем один сплошной скелетон с рамкой
  if (!symbol || !marketType) {
    return (
      <div className="h-full w-full border border-border rounded-sm overflow-hidden">
        <Skeleton className="h-full w-full" />
      </div>
    );
  }
  
  return (
    <ChartContextProvider chartId={uniqueChartId}>
      <ChartContent
        key={`${symbol}-${marketType}-${interval}`}
        symbol={symbol}
        marketType={marketType}
        interval={interval}
        onIntervalChange={onIntervalChange}
        onFullscreenToggle={onFullscreenToggle}
        uniqueChartId={uniqueChartId}
      />
    </ChartContextProvider>
  );
});

export default SimpleChart;

// --- WDYR Integration ---
if (process.env.NODE_ENV === 'development') {
  (ChartContent as any).whyDidYouRender = true;
  (SimpleChart as any).whyDidYouRender = true;
}
