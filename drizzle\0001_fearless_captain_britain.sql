CREATE TABLE IF NOT EXISTS "candles" (
	"id" serial PRIMARY KEY NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"interval" varchar(16) NOT NULL,
	"market_type" varchar(32) NOT NULL,
	"open_time" timestamp with time zone NOT NULL,
	"open" numeric(18, 8) NOT NULL,
	"high" numeric(18, 8) NOT NULL,
	"low" numeric(18, 8) NOT NULL,
	"close" numeric(18, 8) NOT NULL,
	"volume" numeric(18, 8) NOT NULL,
	CONSTRAINT "candles_unq" UNIQUE("symbol","interval","market_type","open_time")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "tickers_24h" (
	"id" serial PRIMARY KEY NOT NULL,
	"symbol" varchar(32) NOT NULL,
	"market_type" varchar(32) NOT NULL,
	"last_price" numeric(18, 8),
	"price_change" numeric(18, 8),
	"price_change_percent" numeric(18, 4),
	"high_price" numeric(18, 8),
	"low_price" numeric(18, 8),
	"volume" numeric(18, 8),
	"quote_volume" numeric(18, 2),
	"open_time" timestamp with time zone,
	"close_time" timestamp with time zone,
	"count" integer,
	"last_updated" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "tickers_24h_unq" UNIQUE("symbol","market_type")
);
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "symbol_interval_market_time_idx" ON "candles" USING btree ("symbol","interval","market_type","open_time");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "market_type_idx" ON "tickers_24h" USING btree ("market_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "quote_volume_idx" ON "tickers_24h" USING btree ("quote_volume");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "price_change_percent_idx" ON "tickers_24h" USING btree ("price_change_percent");