---
title: "Ш<PERSON><PERSON><PERSON>он Правил для [Технология/Библиотека]"
description: "Краткий, действенный набор правил и лучших практик для [Технология/Библиотека]. Цель - общность и переиспользуемость. Максимальный размер - 500 строк."
tags: ["шаблон", "правила", "best-practices", "технология", "библиотека"] # Добавьте релевантные теги
---

# [Технология/Библиотека] – Ключевые Правила и Лучшие Практики

## 0. Введение для Пользователя Шаблона

*   **Цель**: Этот шаблон поможет вам создать четкий и полезный набор правил для работы с `[Технология/Библиотека]`.
*   **Фокус**: Правила должны быть **конкретными**, **действенными** и **легко проверяемыми**.
*   **Краткость**: Стремитесь к лаконичности. Если раздел становится слишком большим, рассмотрите возможность вынесения его в отдельный, более специализированный файл правил.
*   **Примеры**: Включайте **конкретные примеры кода** или ссылки на файлы-шаблоны (`@path/to/template.ext`).
*   **Адаптация**: Места, требующие специфичной для проекта информации, отмечены как `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ]`.

## 1. Основные Принципы и Концепции

*   **Назначение `[Технология/Библиотека]`**: `[Кратко: 1-2 предложения о том, для чего используется технология. Например: "Высокопроизводительная база данных временных рядов..."]`
*   **Ключевые Идеи/Парадигмы**:
    *   `[Концепция 1: Краткое описание. Пример: "Designated Timestamp: Основная временная колонка для оптимизации..."]`
    *   `[Концепция 2: Краткое описание. Пример: "Partitions: Автоматическое разделение данных по времени..."]`
    *   `[Добавьте другие ключевые концепции по необходимости]`
*   **Обязательно к Прочтению (Ссылки)**:
    *   Официальная документация: `[URL ОСНОВНОЙ ДОКУМЕНТАЦИИ]`
    *   Ключевые разделы:
        *   `[Название раздела 1]: [URL РАЗДЕЛА 1]`
        *   `[Название раздела 2]: [URL РАЗДЕЛА 2]`
*   **Актуальная Версия**: `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ: Версия, используемая в проекте, например, 3.2.1]`
    *   Ссылка на Changelog: `[URL К CHANGELOG]`

## 2. Конфигурация и Настройка Среды

*   **Основные Файлы Конфигурации**:
    *   `[Файл 1, например, server.conf]: [Краткое описание и ключевые параметры]`
    *   `[Файл 2, например, .env]: [Ключевые переменные окружения]`
*   **Типичные Зависимости (для проекта)**: `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ: например, client-library-vX.Y, other-package]`
*   **Инструменты Сборки/Разработки**: `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ: например, Webpack, Babel, esbuild]`

## 3. Рекомендации по Написанию Кода

### 3.1. Структура Проекта и Именование

*   `[Рекомендация по структуре папок, если есть специфика для технологии]`
*   `[Правила именования файлов/классов/функций, если есть специфика]`

### 3.2. Паттерны и Анти-паттерны (с Примерами)

#### Хорошо: [Описание хорошего, конкретного примера]
*   **Действие**: `[Что конкретно делать]`
*   **Почему**: `[Краткое объяснение преимуществ]`
```[язык]
// Пример хорошего, общего кода
// @path/to/good-example-template.ext (если есть файл-шаблон)
```

#### Плохо: [Описание плохого, конкретного примера и почему он плох]
*   **Проблема**: `[Что конкретно не делать]`
*   **Почему**: `[Краткое объяснение негативных последствий]`
```[язык]
// Пример плохого, общего кода
```

### 3.3. Производительность

*   `[Ключевая рекомендация по производительности 1. Пример: "Всегда используйте designated timestamp для фильтрации..."]`
*   `[Ключевая рекомендация по производительности 2. Пример: "Избегайте SELECT * на больших таблицах..."]`

### 3.4. Безопасность

*   `[Ключевая рекомендация по безопасности 1, если применимо]`

## 4. Особенности Работы с API / Ключевые Функции

*   **Функция/Модуль 1**: `[Название функции/модуля]`
    *   Назначение: `[Кратко]`
    *   Ключевые параметры: `[параметр1, параметр2]`
    *   Пример использования:
    ```[язык]
    // Код-пример для Функция/Модуль 1
    ```
*   **Функция/Модуль 2**: `[Название функции/модуля]`
    *   ... (аналогично)

## 5. НЕ ИСПОЛЬЗОВАТЬ / ИЗБЕГАТЬ

*   **НЕ ИСПОЛЬЗОВАТЬ**:
    *   `[Конкретная устаревшая функция/API]: Причина - [устарело в версии X.Y, есть лучшая альтернатива Z]`
    *   `[Анти-паттерн 1]: Пример - [короткий код-пример анти-паттерна]`
*   **ИЗБЕГАТЬ**:
    *   `[Практика, негативно влияющая на производительность/читаемость. Пример: "Частые операции UPDATE для массовых изменений..."]`

## 6. ВСЕГДА ДЕЛАТЬ

*   `[Ключевое правило 1. Пример: "Определять designated timestamp для всех таблиц временных рядов."]`
*   `[Ключевое правило 2. Пример: "Использовать партиционирование для больших таблиц."]`
*   `[Ключевое правило 3. Пример: "Фильтровать по designated timestamp как можно раньше в запросе."]`

## 7. Инструменты и Рабочий Процесс

*   **Основные Инструменты**:
    *   `[Инструмент 1, например, QuestDB Web Console]: [Кратко, для чего используется]`
    *   `[Инструмент 2, например, psql client]: [Кратко, для чего используется]`
*   **Линтинг и Форматирование**: `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ: Конкретные инструменты и конфигурации, например, "ESLint с плагином X, Prettier"]`
*   **Тестирование**: `[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ: Рекомендуемые фреймворки/подходы для тестирования кода, работающего с этой технологией]`

## 8. Полезные Сниппеты / Шаблоны Кода

*   **Шаблон для [Типичная Задача 1, например, Создание новой таблицы]**:
    ```[язык]
    // @path/to/create_table_template.sql
    CREATE TABLE [table_name] (
        ts TIMESTAMP,
        // ... другие колонки
    ) TIMESTAMP(ts) PARTITION BY [DAY/MONTH/YEAR];
    ```
*   **Шаблон для [Типичная Задача 2, например, Подключение к БД через Python]**:
    ```python
    # @path/to/python_connection_template.py
    # import library
    #
    # connection_string = "[ЗАПОЛНИТЬ ПОЛЬЗОВАТЕЛЮ]"
    # try:
    #     # connect
    #     pass
    # except Exception as e:
    #     print(f"Error: {e}")
    ```

## 9. Самопроверка / Чек-лист для ИИ

Перед предоставлением решения, ИИ должен проверить:
*   [ ] Соответствует ли решение актуальной версии `[Технология/Библиотека]`?
*   [ ] Применены ли ключевые принципы (см. Раздел 1)?
*   [ ] Использованы ли рекомендованные паттерны и избегаются ли анти-паттерны (см. Раздел 3.2)?
*   [ ] Учтены ли ограничения "НЕ ИСПОЛЬЗОВАТЬ / ИЗБЕГАТЬ" (см. Раздел 5)?
*   [ ] Выполнены ли требования "ВСЕГДА ДЕЛАТЬ" (см. Раздел 6)?
*   [ ] Код читаем, эффективен и безопасен?
*   [ ] Язык общения: Русский. Код и комментарии в коде: Английский.

## 10. Завершение Задачи и Отчетность

*   Кратко указать, что было сделано.
*   Привести ссылки на измененные/созданные файлы.
*   **Обязательно прочитать релевантные файлы и зависимости перед внесением изменений (если это разрешено и возможно в текущем контексте).**

---