--- START FILE: src\app\layout.tsx ---
import '@/shared/lib/wdyr'; // Import WDYR initialization as early as possible
import type { Metadata } from "next";
import localFont from 'next/font/local';
import Providers from './providers'; // Используем default импорт
// import { DevTools } from '@/components/DevTools'; // Импортируем DevTools --- Удаляем импорт
import "./globals.css";
// import "@/styles/global.scss"; // Оставляем закомментированным

// const queryClient = new QueryClient({ ... }); // Убираем создание клиента

// Используем локальные шрифты вместо Google Fonts


export const metadata: Metadata = {
  title: "Metacharts",
  description: "Crypto Dashboard",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <body
      >
        {/* Оборачиваем children в Providers */}
        <Providers>
          {children}
          {/* {process.env.NODE_ENV === 'development' && <DevTools />} --- Удаляем DevTools отсюда */}
        </Providers>
      </body>
    </html>
  );
}

--- END FILE: src\app\layout.tsx ---

--- START FILE: src\app\page.tsx ---
'use client';

import React, { useEffect, useMemo, useRef, useCallback, useState, memo, Suspense, useTransition } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { TickerTable } from '@/features/TickerManagement/Table';
import ControlPanel from '@/features/ControlPanel/ControlPanel';
import SimpleChart from '@/features/Chart/Chart';
import { DevTools } from '@/shared/lib/DevTools';
import { useAppSettingsStore, selectSetSetting, selectSelectedTickerSymbol, selectIsTableCollapsed, selectSetTableCollapsed, selectMarketTypes, selectUpdateSortConfigs, selectAggregateVolumeAndTrades, selectChartVolumeUpColor, selectChartVolumeDownColor, selectViewMode, selectSetViewMode, selectLayoutTypeForCurrentMode, selectSelectedInterval, selectSelectedPairs, selectSetLayoutTypeForMode } from '@/shared/store/settingsStore';
import { useScreenerTickers } from '@/features/ModeControl/useScreenerTickers';
import {
  useMarketStore,
  useWebSocketConnection,
  WSConnectionStatus,
  MarketType,
  LayoutType,
  Ticker,
  Kline,
  useMarketDataManager
} from '@/shared/index';

// Import WebSocket auto-connect from websocket module specifically
import { useWebSocketAutoConnect } from '@/shared/market/websocket';
import { clsx } from 'clsx';
import { ErrorBoundary } from '@/shared/lib/ErrorBoundary';
import { haveSameKeys, isError } from '@/shared/lib/utils';
import ChartDisplayManager from '@/features/Chart/ChartDisplay';

// Memoize the SimpleChart component
const MemoizedSimpleChart = memo(SimpleChart);

// --- Constants ---
const DEFAULT_TIMEFRAME = '1h';
const DEFAULT_TICKER = 'BTCUSDT';
const DEFAULT_MARKET_TYPE = MarketType.Futures;
const AUTO_UPDATE_INTERVAL = 3000;
const FOCUS_AUTO_RECONNECT = true;

// Типы для группировки состояний
type ChartState = {
  intervals: Record<number, string>;
  fullscreenIndex: number | null;
};

type TableState = {
  searchQuery: string;
  counts: { total: number; filtered: number };
  filteredData: Ticker[];
};

function HomePageContent() {
  const [isPending, startTransition] = useTransition();
  
  // Селекторы из глобального стора
  const setSetting = useAppSettingsStore(selectSetSetting);
  const setViewMode = useAppSettingsStore(selectSetViewMode);
  const selectedTickerSymbol = useAppSettingsStore(selectSelectedTickerSymbol);
  const selectedInterval = useAppSettingsStore(selectSelectedInterval);
  const isTableCollapsed = useAppSettingsStore(selectIsTableCollapsed);
  const setTableCollapsed = useAppSettingsStore(selectSetTableCollapsed);
  const selectedMarketTypes = useAppSettingsStore(selectMarketTypes);
  const updateSortConfigs = useAppSettingsStore(selectUpdateSortConfigs);
  const aggregateVolumeAndTrades = useAppSettingsStore(selectAggregateVolumeAndTrades);
  const currentModeLayoutType = useAppSettingsStore(selectLayoutTypeForCurrentMode);
  const currentViewMode = useAppSettingsStore(selectViewMode);
  const selectedPairs = useAppSettingsStore(selectSelectedPairs);
  const setLayoutTypeForMode = useAppSettingsStore(selectSetLayoutTypeForMode);

  // Скринер хук для получения топ тикеров
  const { screenerTickers, isScreenerMode, screenerSettings } = useScreenerTickers();

  // Получаем централизованный менеджер данных
  const marketDataManager = useMarketDataManager();
  const { 
    isInitialLoading, 
    isWsConnected, 
    error: marketDataError, 
    connectWebSocket, 
    wsStatus: wsConnectionStatus 
  } = marketDataManager;
  
  // Store данные
  const spotTickers = useMarketStore(state => state.processedSpotTickers);
  const futuresTickers = useMarketStore(state => state.processedFuturesTickers);
  
  // Группировка связанных состояний
  const [chartState, setChartState] = useState<ChartState>({
    intervals: {},
    fullscreenIndex: null
  });
  
  const [tableState, setTableState] = useState<TableState>({
    searchQuery: '',
    counts: { total: 0, filtered: 0 },
    filteredData: []
  });
  
  const [localSymbolMarketMap, setLocalSymbolMarketMap] = useState<Map<string, MarketType>>(new Map());
  
  const mainContainerRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Вспомогательные функции для обновления состояния
  const updateChartState = useCallback((updates: Partial<ChartState>) => {
    setChartState(prev => ({ ...prev, ...updates }));
  }, []);
  
  const updateTableState = useCallback((updates: Partial<TableState>) => {
    setTableState(prev => ({ ...prev, ...updates }));
  }, []);

  // Мемоизированная функция определения типа рынка для тикера
  const getMarketTypeForSymbol = useCallback((symbol: string): MarketType => {
    if (!symbol) return DEFAULT_MARKET_TYPE;
    
    // Проверяем карту соответствий
    const marketType = localSymbolMarketMap.get(symbol);
    if (marketType) return marketType;
    
    // Проверяем доступность тикера в обработанных тикерах
    return (
      spotTickers.find(t => t.symbol === symbol) ? MarketType.Spot :
      futuresTickers.find(t => t.symbol === symbol) ? MarketType.Futures :
      DEFAULT_MARKET_TYPE
    );
  }, [localSymbolMarketMap, spotTickers, futuresTickers]);

  // Используем менеджер для подписки на свечи выбранного символа
  useEffect(() => {
    if (!selectedTickerSymbol || !selectedInterval) return;
    
    const marketType = getMarketTypeForSymbol(selectedTickerSymbol);
    return marketDataManager.subscribeToSymbol(selectedTickerSymbol, selectedInterval, marketType);
  }, [selectedTickerSymbol, selectedInterval, getMarketTypeForSymbol, marketDataManager]);

  // Автоматическое соединение с WebSocket
  useWebSocketAutoConnect();

  // Обработка данных и состояния загрузки
  useEffect(() => {
    // Если есть данные, то выходим из загрузки
    if (spotTickers.length > 0 || futuresTickers.length > 0 || isWsConnected) {
      return;
    }
    
    // Если еще загружаем данные, то ничего не делаем
    if (isInitialLoading) {
      return;
    }
    
    // Если нет данных и не загружаем, попробуем загрузить данные тикеров
    marketDataManager.loadInitialTickers();
  }, [isInitialLoading, isWsConnected, spotTickers.length, futuresTickers.length, marketDataManager]);

  // Обновление карты соответствия символов и типов рынка
  useEffect(() => {
    const newMap = new Map<string, MarketType>();
    spotTickers.forEach(ticker => newMap.set(ticker.symbol, MarketType.Spot));
    futuresTickers.forEach(ticker => newMap.set(ticker.symbol, MarketType.Futures));

    if (!haveSameKeys(localSymbolMarketMap, newMap)) {
      setLocalSymbolMarketMap(newMap);
    }
  }, [spotTickers, futuresTickers, localSymbolMarketMap]);

  // Установка дефолтного тикера если необходимо
  useEffect(() => {
    const shouldSetDefaultTicker = !selectedTickerSymbol && 
                                  !isInitialLoading && 
                                  localSymbolMarketMap.size > 0;
    
    if (shouldSetDefaultTicker) {
      const symbolToSelect = localSymbolMarketMap.has(DEFAULT_TICKER) 
        ? DEFAULT_TICKER 
        : Array.from(localSymbolMarketMap.keys())[0];
      
      setSetting('selectedTickerSymbol', symbolToSelect);
    }
  }, [isInitialLoading, localSymbolMarketMap, selectedTickerSymbol, setSetting]);

  // Логика определения размера сетки для графиков
  const { rows, cols, totalCharts } = useMemo(() => {
    const activeLayoutType = currentModeLayoutType || '1x1';
    
    if (!activeLayoutType || typeof activeLayoutType !== 'string') {
      return { rows: 1, cols: 1, totalCharts: 1 };
    }
    
    const [r, c] = activeLayoutType.split('x').map(Number);
    const layoutRows = isNaN(r) || r < 1 ? 1 : r;
    const layoutCols = isNaN(c) || c < 1 ? 1 : c;
    
    return { 
      rows: layoutRows, 
      cols: layoutCols, 
      totalCharts: layoutRows * layoutCols 
    };
  }, [currentModeLayoutType]);

  // Обновляем интервалы при изменении количества графиков
  useEffect(() => {
    // This effect ensures that the chartState.intervals object is updated
    // correctly when the total number of charts changes, while preserving
    // existing interval settings for charts that remain.
    setChartState(prevChartState => {
      const newIntervals = Array.from({ length: totalCharts }).reduce<Record<number, string>>((acc, _, i) => {
        acc[i] = prevChartState.intervals[i] || DEFAULT_TIMEFRAME;
        return acc;
      }, {});

      // Check if newIntervals is actually different from prevChartState.intervals.
      // For Record<number, string> where keys are effectively 0-indexed, Object.values() maintains order.
      const prevIntervalValues = Object.values(prevChartState.intervals);
      const newIntervalValues = Object.values(newIntervals); // newIntervals has totalCharts entries, keys 0..N-1

      // Compare lengths first, then values if lengths are equal using Array.some().
      if (prevIntervalValues.length !== newIntervalValues.length ||
          newIntervalValues.some((value, index) => value !== prevIntervalValues[index])) {
        return { ...prevChartState, intervals: newIntervals };
      }

      // If lengths are the same and all values are the same, no change needed.
      return prevChartState;
    });
  }, [totalCharts]); // chartState.intervals and updateChartState (via setChartState) removed from deps to break the loop.

  // Сброс полноэкранного режима при переходе в режим Screener
  useEffect(() => {
    if (currentViewMode === 'screener' && chartState.fullscreenIndex !== null) {
      updateChartState({ fullscreenIndex: null });
    }
  }, [currentViewMode, chartState.fullscreenIndex, updateChartState]);

  // Объединяем все обработчики в единый объект
  const handlers = useMemo(() => ({
    symbolSelect: (symbol: string) => {
      startTransition(() => {
        setSetting('selectedTickerSymbol', symbol);
        setViewMode('focus');
        updateChartState({ fullscreenIndex: null });
      });
    },
    
    toggleTableCollapse: () => setTableCollapsed(!isTableCollapsed),
    
    setLayoutType: (layout: LayoutType) => {
      setLayoutTypeForMode(currentViewMode, layout);
      updateChartState({ fullscreenIndex: null });
    },
    
    searchChange: (query: string) => {
      startTransition(() => {
        updateTableState({ searchQuery: query });
      });
    },
    
    countsChange: (counts: { total: number; filtered: number }) => {
      updateTableState({ counts });
    },
    
    intervalChange: (chartIndex: number, newInterval: string) => {
      updateChartState({
        intervals: {
          ...chartState.intervals,
          [chartIndex]: newInterval
        }
      });
    },
    
    fullscreenToggle: (chartIndex: number) => {
      if (isScreenerMode) {
        const tickerToFocus = screenerTickers[chartIndex];
        if (tickerToFocus) {
          startTransition(() => {
            setSetting('selectedTickerSymbol', tickerToFocus.symbol);
            setViewMode('focus');
            updateChartState({ fullscreenIndex: null });
          });
        }
      } else {
        updateChartState({ 
          fullscreenIndex: chartState.fullscreenIndex === chartIndex ? null : chartIndex 
        });
      }
    },
    
    filteredDataChange: (filteredData: Ticker[]) => {
      startTransition(() => {
        updateTableState({ filteredData });
      });
    }
  }), [
    isScreenerMode,
    isTableCollapsed,
    screenerTickers, 
    setSetting,
    setTableCollapsed,
    setViewMode,
    chartState.fullscreenIndex,
    chartState.intervals,
    startTransition,
    updateChartState,
    updateTableState,
    currentViewMode,
    setLayoutTypeForMode
  ]);

  // Детали соединения
  const connectionDetails = useMemo(() => [{
    name: 'WebSocket',
    status: wsConnectionStatus,
    details: marketDataError ?
      (isError(marketDataError) ? marketDataError.message : String(marketDataError)) :
      (isWsConnected ?
        'Connected' :
        wsConnectionStatus === WSConnectionStatus.Connecting ?
          'Connecting...' :
          'Closed/Error'),
  }], [wsConnectionStatus, marketDataError, isWsConnected]);

  // Определяем текущее состояние загрузки
  const isLoading = isInitialLoading && !isWsConnected && !marketDataError;

  // UI-элементы, вычисляемые свойства и состояния
  const uiElements = useMemo(() => {
    // Объединенные тикеры для панели управления
    const combinedTickers = [...spotTickers, ...futuresTickers];
    
    // Стили для сетки графиков
    const gridStyle = chartState.fullscreenIndex === null ? {
      gridTemplateColumns: `repeat(${cols}, minmax(0, 1fr))`,
      gridTemplateRows: `repeat(${rows}, minmax(0, 1fr))`,
    } : {};
    
    // Сообщение для пустого состояния
    const emptyChartMessage = (
      <div className="flex items-center justify-center h-full text-muted-foreground col-span-full row-span-full">
        Select a symbol from the table to display the chart.
      </div>
    );
    
    // Массив индексов для графиков
    const chartIndices = Array.from({ length: totalCharts }, (_, i) => i);
    
    // Обработчики для графиков
    const intervalHandlers = chartIndices.reduce((acc, index) => {
      acc[index] = (newInterval: string) => handlers.intervalChange(index, newInterval);
      return acc;
    }, {} as Record<number, (newInterval: string) => void>);
    
    const fullscreenHandlers = chartIndices.reduce((acc, index) => {
      acc[index] = () => handlers.fullscreenToggle(index);
      return acc;
    }, {} as Record<number, () => void>);
    
    return {
      combinedTickers,
      gridStyle,
      emptyChartMessage,
      chartIndices,
      intervalHandlers,
      fullscreenHandlers
    };
  }, [cols, rows, chartState.fullscreenIndex, totalCharts, spotTickers, futuresTickers, handlers]);

  // Log WebSocket connection status for debugging
  useEffect(() => {
    console.log(`[WebSocket Status]: ${wsConnectionStatus}`);
  }, [wsConnectionStatus]);

  return (
    <DndProvider backend={HTML5Backend}>
      {process.env.NODE_ENV === 'development' && <DevTools />}
      <div ref={mainContainerRef} className="flex h-screen flex-row bg-background text-foreground overflow-hidden gap-[3px]">
        <div
          className={clsx(
            "ticker-table-wrapper h-full transition-all duration-300 ease-in-out overflow-hidden flex-shrink-0 min-w-0",
            !isTableCollapsed 
              ? 'max-w-xl border-r border-border'
              : 'max-w-0 border-r-0'
          )}
        >
          <TickerTable
            searchQuery={tableState.searchQuery}
            aggregateVolumeAndTrades={aggregateVolumeAndTrades}
            onCountsChange={handlers.countsChange}
            onSymbolSelect={handlers.symbolSelect}
            selectedSymbol={selectedTickerSymbol || null}
          />
        </div>
        <div className="flex flex-col flex-grow overflow-hidden">
          <ControlPanel
            ref={scrollContainerRef}
            dragConstraintsRef={mainContainerRef}
            selectedSymbol={selectedTickerSymbol ?? 'N/A'}
            loading={isLoading}
            error={isError(marketDataError) ? marketDataError.message : null}
            lastUpdate={null}
            onRefresh={connectWebSocket}
            isWsConnected={isWsConnected}
            connectionDetails={connectionDetails}
            totalTickers={tableState.counts.total}
            filteredTickers={tableState.counts.filtered}
            isTableCollapsed={isTableCollapsed}
            onToggleTableCollapse={handlers.toggleTableCollapse}
            isFiltering={false}
            layoutType={currentModeLayoutType as LayoutType}
            selectedMarketTypes={selectedMarketTypes || []}
            aggregateVolumeAndTrades={aggregateVolumeAndTrades}
            setLayoutType={handlers.setLayoutType}
            onSearchChange={handlers.searchChange}
          />
          <div
            className={`flex-grow grid gap-[3px] mt-[3px] overflow-hidden relative`}
            style={uiElements.gridStyle}
          >
            <ChartDisplayManager
              isScreenerMode={isScreenerMode}
              screenerTickers={screenerTickers}
              screenerTimeframe={screenerSettings.timeframe}
              screenerCurrentPage={screenerSettings.currentPage}
              selectedTickerSymbol={selectedTickerSymbol || null}
              chartIndices={Array.from({ length: totalCharts }, (_, i) => i)}
              chartIntervals={chartState.intervals}
              getMarketTypeForSymbol={getMarketTypeForSymbol}
              intervalHandlers={uiElements.intervalHandlers}
              fullscreenHandlers={uiElements.fullscreenHandlers}
              fullscreenChartIndex={chartState.fullscreenIndex}
              emptyChartMessage={uiElements.emptyChartMessage}
              totalCharts={totalCharts}
            />
          </div>
        </div>
      </div>
    </DndProvider>
  );
}

export default function HomePage() {
  return (
    <ErrorBoundary>
      <Suspense fallback={
        <div className="flex h-screen items-center justify-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div>
        </div>
      }>
        <HomePageContent />
      </Suspense>
    </ErrorBoundary>
  );
}
--- END FILE: src\app\page.tsx ---

--- START FILE: src\app\providers.tsx ---
'use client';

import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
// Optional: If you want React Query DevTools
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Create a client instance (ensure it's only created once per session)
// Using useState ensures it's stable across renders
function makeQueryClient() {
	return new QueryClient({
		defaultOptions: {
			queries: {
				// Default query options can go here
				staleTime: 60 * 1000, // 1 minute
			},
		},
	});
}

let browserQueryClient: QueryClient | undefined = undefined;

function getQueryClient() {
	if (typeof window === 'undefined') {
		// Server: always make a new query client
		return makeQueryClient();
	} else {
		// Browser: make a new query client if we don't already have one
		// This is very important so we don't re-make a new client if React
		// suspends during the initial render. This may not be needed if using
		// React 18 Concurrent Features, but it's safer to keep.
		if (!browserQueryClient) browserQueryClient = makeQueryClient();
		return browserQueryClient;
	}
}

export default function Providers({ children }: { children: React.ReactNode }) {
	// NOTE: Avoid useState when initializing the query client if you are using
	//       concurrent rendering mode in React 18. Instead, create the client
	//       instance outside the component's render cycle.
	// const [queryClient] = React.useState(() => new QueryClient());
	// Use the getter function instead for better SSR/suspense compatibility
	const queryClient = getQueryClient();

	return (
		<QueryClientProvider client={queryClient}>
			{children}
			{/* Optional: React Query DevTools */}
			{/* <ReactQueryDevtools initialIsOpen={false} /> */}
		</QueryClientProvider>
	);
} 
--- END FILE: src\app\providers.tsx ---

--- START FILE: src\app\test\page.tsx ---
'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useWebSocketConnection, WSConnectionStatus } from '@/shared/index';
import { MarketType } from '@/shared/index';
import { wsClient } from '@/shared/index';

// Убедимся, что wsClient существует, прежде чем использовать его
const getWsStatus = () => {
  return wsClient ? wsClient.status : WSConnectionStatus.Disconnected;
};

const connectWs = () => {
  wsClient.connect();
};

const disconnectWs = () => {
  wsClient.disconnect();
};

// Определение типов для тестов API
type TestStatus = 'success' | 'error' | 'running' | 'pending';
type ApiTestResult = {
  status: TestStatus;
  data?: any;
  errorMessage?: string;
  errorType?: string;
  errorSource?: string;
};

interface TestResult {
  id: string;
  name: string;
  status: TestStatus;
  result: ApiTestResult | null;
  error?: string;
  duration?: number;
  timestamp?: string;
  params: Record<string, any>;
}

// Определение API тестов
const apiTests: Array<{
  id: string;
  name: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  params?: Record<string, any>;
  body?: any;
}> = [
  {
    id: 'get-spot-symbols',
    name: 'Get Spot Symbols',
    endpoint: '/api/market/symbols/spot',
    method: 'GET'
  },
  {
    id: 'get-futures-symbols',
    name: 'Get Futures Symbols',
    endpoint: '/api/market/symbols/futures',
    method: 'GET'
  },
  {
    id: 'get-spot-tickers',
    name: 'Get Spot Tickers',
    endpoint: '/api/market/tickers/spot',
    method: 'GET'
  },
  {
    id: 'get-futures-tickers',
    name: 'Get Futures Tickers',
    endpoint: '/api/market/tickers/futures',
    method: 'GET'
  },
  {
    id: 'get-btc-candles-1h',
    name: 'Get BTCUSDT 1h Candles',
    endpoint: '/api/market/candles/spot/BTCUSDT/1h',
    method: 'GET',
    params: { limit: '100' }
  },
  {
    id: 'get-eth-candles-15m',
    name: 'Get ETHUSDT 15m Candles',
    endpoint: '/api/market/candles/spot/ETHUSDT/15m',
    method: 'GET',
    params: { limit: '50' }
  },
  {
    id: 'get-btc-futures-candles',
    name: 'Get BTCUSDT Futures 4h Candles',
    endpoint: '/api/market/candles/futures/BTCUSDT/4h',
    method: 'GET',
    params: { limit: '30' }
  }
];

interface TestConsoleHeaderProps {
  successCount: number;
  errorCount: number;
  totalTests: number;
  completedTests: number;
  wsConnectionStatus: string;
  wsLastError: string;
  wsConnectionAttempts: number;
  wsStats: any;
  copiedStates: Record<string, boolean>;
  isRunning: boolean;
  progress: number;
  onCopyAllResults: () => void;
  onRunAllTests: () => void;
  onResetWsConnection: () => void;
  onResetStores?: () => void;
  onExportState?: () => void;
}

// Helper functions
const getStatusIcon = (status: TestStatus): string => {
  const icons: Record<TestStatus, string> = {
    success: '✅',
    error: '❌',
    running: '⏳',
    pending: '⏸️'
  };
  return icons[status] || icons.pending;
};

const getStatusColor = (status: TestStatus): string => {
  const colors: Record<TestStatus, string> = {
    success: 'text-green-400',
    error: 'text-red-400',
    running: 'text-yellow-400',
    pending: 'text-gray-500'
  };
  return colors[status] || colors.pending;
};

const TestConsoleHeader: React.FC<TestConsoleHeaderProps> = ({
  successCount,
  errorCount,
  totalTests,
  completedTests,
  wsConnectionStatus,
  wsLastError,
  wsConnectionAttempts,
  wsStats,
  copiedStates,
  isRunning,
  progress,
  onCopyAllResults,
  onRunAllTests,
  onResetWsConnection,
  onResetStores,
  onExportState,
}) => (
  <div className="bg-gray-900 border-b border-gray-700 px-4 py-3">
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <h1 className="text-lg font-semibold">API Test Console</h1>
        <div className="flex items-center space-x-3 text-sm">
          <span className="text-gray-400">Tests:</span>
          <span className="text-green-400">{successCount} passed</span>
          <span className="text-red-400">{errorCount} failed</span>
          <span className="text-gray-400">{totalTests - completedTests} pending</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-gray-400 text-sm">WebSocket:</span>
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${
              wsConnectionStatus === 'OPEN' ? 'bg-green-400 animate-pulse' :
              wsConnectionStatus === 'CONNECTING' ? 'bg-yellow-400 animate-ping' : 'bg-red-400'
            }`} />
            <span className={`text-sm font-medium ${
              wsConnectionStatus === 'OPEN' ? 'text-green-400' :
              wsConnectionStatus === 'CONNECTING' ? 'text-yellow-400' : 'text-red-400'
            }`}>
              {wsConnectionStatus}
            </span>
          </div>
          {wsLastError && (
            <span className="text-xs text-red-400 max-w-40 truncate bg-red-950 px-2 py-1 rounded" title={wsLastError}>
              Error: {wsLastError}
            </span>
          )}
          {wsConnectionAttempts > 0 && (
            <span className="text-xs text-yellow-400 bg-yellow-950 px-1 py-0.5 rounded">
              {wsConnectionAttempts} retries
            </span>
          )}
          {wsStats?.clientsCount && (
            <span className="text-xs text-gray-400">
              {wsStats.clientsCount} clients
            </span>
          )}
          <button
            onClick={onResetWsConnection}
            className="px-2 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 text-xs rounded transition-colors"
            title="Force WebSocket Reconnection"
          >
            🔄 Reconnect
          </button>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <button
          onClick={onCopyAllResults}
          className="px-3 py-1 bg-gray-800 hover:bg-gray-700 text-gray-300 text-sm rounded border border-gray-600 transition-colors"
        >
          {copiedStates['all-results'] ? '📋 Copied' : '📋 Copy All'}
        </button>
        <button
          onClick={onRunAllTests}
          disabled={isRunning}
          className="px-4 py-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white text-sm rounded transition-colors flex items-center space-x-2"
        >
          {isRunning ? (
            <>
              <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>Running {totalTests} tests...</span>
            </>
          ) : (
            <>
              <span>⚡</span>
              <span>Run All Tests (Parallel)</span>
            </>
          )}
        </button>
        {process.env.NODE_ENV === 'development' && onResetStores && onExportState && (
          <>
            <button
              onClick={onResetStores}
              title="Reset All Zustand Stores & Clear Relevant LocalStorage"
              className="px-3 py-1 bg-red-700 hover:bg-red-800 text-white text-sm rounded transition-colors"
            >
              🔄 Reset Stores
            </button>
            <button
              onClick={onExportState}
              title="Export Current Zustand Store States to JSON"
              className="px-3 py-1 bg-green-700 hover:bg-green-800 text-white text-sm rounded transition-colors"
            >
              💾 Export State
            </button>
          </>
        )}
      </div>
    </div>
    {isRunning && (
      <div className="mt-2 px-4">
        <div className="flex items-center space-x-2 text-sm text-gray-400">
          <span>Progress:</span>
          <span className="text-green-400 font-mono">
            {completedTests}/{totalTests} completed
          </span>
          <span className="text-gray-500">•</span>
          <span className="text-blue-400">
            Running {totalTests - completedTests} tests in parallel
          </span>
          <div className="flex-1 bg-gray-800 rounded-full h-1.5">
            <div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-1.5 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <span className="font-mono text-gray-300 min-w-[3rem]">{progress.toFixed(0)}%</span>
        </div>
      </div>
    )}
  </div>
);

interface TestListProps {
  tests: TestResult[];
  selectedTestId: string | null;
  isRunning: boolean;
  onRunTest: (testId: string) => void;
  onCopyTest: (test: TestResult) => void;
  onSelectTest: (test: TestResult) => void;
  copiedStates: Record<string, boolean>;
}

const TestList: React.FC<TestListProps> = ({
  tests,
  selectedTestId,
  isRunning,
  onRunTest,
  onCopyTest,
  onSelectTest,
  copiedStates
}) => (
  <div className="flex-1 border-r border-gray-700">
    <div className="overflow-auto">
      <table className="w-full text-sm">
        <thead className="bg-gray-800 border-b border-gray-700">
          <tr>
            <th className="text-left px-4 py-2 text-gray-300 w-8">Status</th>
            <th className="text-left px-4 py-2 text-gray-300">Test Name</th>
            <th className="text-left px-4 py-2 text-gray-300 w-48">Error</th>
            <th className="text-right px-4 py-2 text-gray-300 w-20">Duration</th>
            <th className="text-center px-4 py-2 text-gray-300 w-24">Actions</th>
          </tr>
        </thead>
        <tbody>
          {tests.map((test) => (
            <tr
              key={test.id}
              className={`border-b border-gray-800 hover:bg-gray-900 cursor-pointer transition-colors ${
                selectedTestId === test.id ? 'bg-gray-900' : ''
              }`}
              onClick={() => onSelectTest(test)}
            >
              <td className="px-4 py-2">
                <span className="text-lg">{getStatusIcon(test.status)}</span>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col">
                  <span className="text-gray-100">{test.name}</span>
                </div>
              </td>
              <td className="px-4 py-2 text-xs text-red-400 truncate" title={test.status === 'error' && test.result?.errorMessage ? test.result.errorMessage : ''}>
                {test.status === 'error' && test.result?.errorMessage ? (
                  <div className="flex items-center">
                    <span className="font-semibold">{test.result.errorType || 'ERR'}: </span>
                    <span className="ml-1">{test.result.errorMessage.substring(0, 30) + (test.result.errorMessage.length > 30 ? '...' : '')}</span>
                    {test.result.errorSource && <span className="ml-1 text-gray-500 text-[10px] whitespace-nowrap">({test.result.errorSource.replace(/_/g, ' ')})</span>}
                  </div>
                ) : (
                  '-'
                )}
              </td>
              <td className="px-4 py-2 text-right text-gray-400">
                {test.duration ? `${test.duration}ms` : '-'}
              </td>
              <td className="px-4 py-2 text-center">
                <div className="flex justify-center space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onRunTest(test.id);
                    }}
                    disabled={isRunning}
                    className="p-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white rounded transition-colors"
                    title="Run this test"
                  >
                    {test.status === 'running' ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <span>▶</span>
                    )}
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onCopyTest(test);
                    }}
                    className="p-1 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
                    title="Copy test result as JSON"
                  >
                    {copiedStates[test.id] ? '✓' : '📋'}
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
);

interface TestDetailsPanelProps {
  selectedTest: TestResult | null;
  onCopyText: (text: string, key: string) => void;
  copiedStates: Record<string, boolean>;
}

const TestDetailsPanel: React.FC<TestDetailsPanelProps> = ({
  selectedTest,
  onCopyText,
  copiedStates
}) => (
  <div className="w-2/5 p-4 overflow-auto">
    <div className="bg-gray-900 border-b border-gray-700 px-4 py-2 mb-4">
      <h2 className="text-sm font-medium text-gray-300">Test Details</h2>
    </div>
    {selectedTest ? (
      <div className="space-y-4">
        <div>
          <h3 className="text-sm font-semibold text-gray-300 mb-2">Test Info</h3>
          <div className="bg-gray-800 p-3 rounded">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-gray-400">Name:</div>
              <div className="text-gray-100">{selectedTest.name}</div>
              <div className="text-gray-400">Status:</div>
              <div className={getStatusColor(selectedTest.status)}>
                {selectedTest.status.charAt(0).toUpperCase() + selectedTest.status.slice(1)}
              </div>
              <div className="text-gray-400">Duration:</div>
              <div className="text-gray-100">{selectedTest.duration ? `${selectedTest.duration}ms` : '-'}</div>
              <div className="text-gray-400">Timestamp:</div>
              <div className="text-gray-100">{selectedTest.timestamp ? new Date(selectedTest.timestamp).toLocaleString() : '-'}</div>
            </div>
          </div>
        </div>

        {selectedTest.params && Object.keys(selectedTest.params).length > 0 && (
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-semibold text-gray-300">Parameters</h3>
              <button
                onClick={() => onCopyText(JSON.stringify(selectedTest.params, null, 2), `${selectedTest.id}-params`)}
                className="text-xs px-1.5 py-0.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
              >
                {copiedStates[`${selectedTest.id}-params`] ? '✓ Copied' : '📋 Copy'}
              </button>
            </div>
            <div className="bg-gray-800 rounded">
              <pre className="text-xs text-gray-100 p-3 overflow-auto max-h-40">
                {JSON.stringify(selectedTest.params, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {selectedTest.result && (
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-semibold text-gray-300">Result</h3>
              <button
                onClick={() => onCopyText(JSON.stringify(selectedTest.result, null, 2), `${selectedTest.id}-result`)}
                className="text-xs px-1.5 py-0.5 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
              >
                {copiedStates[`${selectedTest.id}-result`] ? '✓ Copied' : '📋 Copy'}
              </button>
            </div>
            <div className="bg-gray-800 rounded">
              <pre className="text-xs text-gray-100 p-3 overflow-auto max-h-80">
                {JSON.stringify(selectedTest.result, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    ) : (
      <div className="text-center py-10 text-gray-500">
        <p>Select a test to view details</p>
      </div>
    )}
  </div>
);

function useApiTestConsole() {
  // Состояние тестов
  const [testResults, setTestResults] = useState<TestResult[]>(() => {
    return apiTests.map(test => ({
      id: test.id,
      name: test.name,
      status: 'pending' as TestStatus,
      result: null,
      duration: undefined,
      timestamp: undefined,
      params: test.params || {}
    }));
  });
  
  const [selectedTest, setSelectedTest] = useState<TestResult | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});
  
  // Состояние WebSocket
  const wsConnectionHook = useWebSocketConnection();
  const [wsStats, setWsStats] = useState<any>({});
  const [wsLastError, setWsLastError] = useState<string>('');
  const [wsConnectionAttempts, setWsConnectionAttempts] = useState(0);

  // Constants for test execution
  const TEST_TIMEOUT_MS = 10000; // 10 seconds

  // Helper to run a API test with timeout
  const runApiTest = useCallback(async (testId: string): Promise<ApiTestResult> => {
    const test = apiTests.find(t => t.id === testId);
    if (!test) throw new Error(`Test with ID ${testId} not found`);
    
    try {
      const url = new URL(test.endpoint, window.location.origin);
      
      // Add query parameters if any
      if (test.params) {
        Object.entries(test.params).forEach(([key, value]) => {
          url.searchParams.append(key, String(value));
        });
      }
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), TEST_TIMEOUT_MS);
      
      const options: RequestInit = {
        method: test.method,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      };
      
      // Add body if needed
      if (test.body && (test.method === 'POST' || test.method === 'PUT')) {
        options.body = JSON.stringify(test.body);
      }
      
      const response = await fetch(url.toString(), options);
      clearTimeout(timeoutId);
      
      const data = await response.json();
      
      if (!response.ok) {
        return {
          status: 'error',
          errorMessage: data.message || response.statusText,
          errorType: `HTTP ${response.status}`,
          errorSource: 'server',
          data
        };
      }
      
      return {
        status: 'success',
        data
      };
    } catch (error: any) {
      // Абортирован из-за таймаута
      if (error.name === 'AbortError') {
        return {
          status: 'error',
          errorMessage: 'Request timed out',
          errorType: 'TIMEOUT',
          errorSource: 'client'
        };
      }
      
      return {
        status: 'error',
        errorMessage: error.message || 'Unknown error',
        errorType: error.name || 'ERROR',
        errorSource: 'client'
      };
    }
  }, []);

  // Обработчик ошибок WebSocket
  useEffect(() => {
    // Replace wsClient.on with direct event handler assignment
    const handleMessage = (data: any) => {
      // Handle message
    };
    
    wsClient.onMessage = handleMessage;
    
    return () => {
      // Replace wsClient.off with null assignment
      wsClient.onMessage = null;
    };
  }, []);

  // Сбросить ошибку, когда соединение открыто
  useEffect(() => {
    if (wsConnectionHook.status === WSConnectionStatus.Connected && wsLastError) {
      setWsLastError('');
    }
  }, [wsConnectionHook.status, wsLastError]);

  // Запуск одного теста
  const runSingleTest = useCallback(async (testId: string) => {
    setTestResults(prev => prev.map(test =>
      test.id === testId
        ? { ...test, status: 'running', result: null, error: undefined }
        : test
    ));

    const startTime = Date.now();
    try {
      const result = await runApiTest(testId);
      const duration = Date.now() - startTime;
      
      setTestResults(prev => prev.map(test =>
        test.id === testId
          ? {
              ...test,
              status: result.status,
              result,
              duration,
              timestamp: new Date().toISOString()
            }
          : test
      ));
    } catch (error) {
      const duration = Date.now() - startTime;
      setTestResults(prev => prev.map(test =>
        test.id === testId
          ? {
              ...test,
              status: 'error',
              result: {
                status: 'error',
                errorMessage: error instanceof Error ? error.message : String(error),
                errorType: error instanceof Error ? error.name : 'Unknown Error',
                errorSource: 'execution'
              },
              duration,
              timestamp: new Date().toISOString()
            }
          : test
      ));
    }
  }, [runApiTest]);

  // Запуск всех тестов
  const runAllTests = useCallback(async () => {
    setIsRunning(true);
    setProgress(0);
    
    // Проверяем соединение перед запуском тестов
    if (wsClient && wsConnectionHook.status !== WSConnectionStatus.Connected) {
      connectWs();
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Сбрасываем статусы тестов
    setTestResults(prev => prev.map(test => ({
      ...test,
      status: 'running' as TestStatus,
      result: null,
      error: undefined,
      duration: undefined,
      timestamp: undefined
    })));

    // Запускаем тесты параллельно
    const testPromises = testResults.map(async (test) => {
      const startTime = Date.now();
      
      try {
        const result = await runApiTest(test.id);
        const duration = Date.now() - startTime;
        
        setTestResults(prev => prev.map(t =>
          t.id === test.id
            ? {
                ...t,
                status: result.status,
                result,
                duration,
                timestamp: new Date().toISOString()
              }
            : t
        ));
      } catch (error) {
        const duration = Date.now() - startTime;
        setTestResults(prev => prev.map(t =>
          t.id === test.id
            ? {
                ...t,
                status: 'error',
                result: {
                  status: 'error',
                  errorMessage: error instanceof Error ? error.message : String(error),
                  errorType: error instanceof Error ? error.name : 'Unknown Error',
                  errorSource: 'execution'
                },
                duration,
                timestamp: new Date().toISOString()
              }
            : t
        ));
      }
      setProgress(prev => Math.min(prev + (100 / testResults.length), 100));
    });

    await Promise.allSettled(testPromises);
    setIsRunning(false);
    setProgress(100);
  }, [runApiTest, testResults.length, wsConnectionHook.status]);

  // Копирование в буфер обмена
  const copyToClipboard = useCallback(async (text: string, key: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedStates(prev => ({ ...prev, [key]: true }));
      setTimeout(() => {
        setCopiedStates(prev => ({ ...prev, [key]: false }));
      }, 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  }, []);

  // Статистика тестов
  const completedTests = testResults.filter(t => t.status === 'success' || t.status === 'error').length;
  const successCount = testResults.filter(t => t.status === 'success').length;
  const errorCount = testResults.filter(t => t.status === 'error').length;

  // Копирование всех результатов
  const copyAllResults = useCallback(() => {
    const allData = testResults
      .filter(t => t.status !== 'pending')
      .map(test => ({
        name: test.name,
        status: test.status,
        params: test.params,
        result: test.result,
        duration: test.duration,
        timestamp: test.timestamp
      }));
    copyToClipboard(JSON.stringify(allData, null, 2), 'all-results');
  }, [testResults, copyToClipboard]);

  // Копирование данных одного теста
  const copyTestData = useCallback((test: TestResult) => {
    const data = {
      name: test.name,
      status: test.status,
      params: test.params,
      result: test.result,
      duration: test.duration,
      timestamp: test.timestamp
    };
    copyToClipboard(JSON.stringify(data, null, 2), test.id);
  }, [copyToClipboard]);

  // Сброс WebSocket соединения
  const handleResetWsConnection = useCallback(() => {
    disconnectWs();
    setTimeout(() => {
      connectWs();
      setWsLastError('');
      setWsConnectionAttempts(0);
    }, 500);
  }, []);
  
  // Вызов глобальных функций для управления хранилищами
  const callGlobalFunction = useCallback((name: string) => {
    if (typeof window !== 'undefined' && (window as any)[name]) {
      (window as any)[name]();
      return true;
    }
    return false;
  }, []);

  // Сброс хранилищ
  const handleResetStores = useCallback(() => {
    const success = callGlobalFunction('__MCT_RESET_STORES__');
    if (!success) {
      alert('Store reset function not found');
    }
  }, [callGlobalFunction]);

  // Экспорт состояния хранилищ
  const handleExportState = useCallback(() => {
    const success = callGlobalFunction('__MCT_EXPORT_STATE__');
    if (!success) {
      alert('Store export function not found');
    }
  }, [callGlobalFunction]);

  // Auto-run tests on component mount
  useEffect(() => {
    const timer = setTimeout(() => {
      runAllTests();
    }, 500);
    return () => clearTimeout(timer);
  }, [runAllTests]);

  return {
    testResults,
    isRunning,
    progress,
    selectedTest,
    copiedStates,
    wsConnectionHook,
    wsStatsHook: [wsStats, setWsStats],
    wsLastError,
    wsConnectionAttempts,
    completedTests,
    successCount,
    errorCount,
    totalTests: apiTests.length,
    runSingleTest,
    runAllTests,
    copyAllResults,
    copyTestData,
    copyToClipboard,
    setSelectedTest,
    handleResetWsConnection,
    handleResetStores,
    handleExportState
  };
}

export default function TestConsole() {
  const {
    testResults,
    isRunning,
    progress,
    selectedTest,
    copiedStates,
    wsConnectionHook,
    wsStatsHook: [wsStats],
    wsLastError,
    wsConnectionAttempts,
    completedTests,
    successCount,
    errorCount,
    totalTests,
    runSingleTest,
    runAllTests,
    copyAllResults,
    copyTestData,
    copyToClipboard,
    setSelectedTest,
    handleResetWsConnection,
    handleResetStores,
    handleExportState
  } = useApiTestConsole();

  return (
    <div className="flex flex-col h-screen bg-gray-800 text-white">
      <TestConsoleHeader
        successCount={successCount}
        errorCount={errorCount}
        totalTests={totalTests}
        completedTests={completedTests}
        wsConnectionStatus={wsConnectionHook.status}
        wsLastError={wsLastError}
        wsConnectionAttempts={wsConnectionAttempts}
        wsStats={wsStats}
        copiedStates={copiedStates}
        isRunning={isRunning}
        progress={progress}
        onCopyAllResults={copyAllResults}
        onRunAllTests={runAllTests}
        onResetWsConnection={handleResetWsConnection}
        onResetStores={handleResetStores}
        onExportState={handleExportState}
      />
      <div className="flex flex-1 overflow-hidden">
        <TestList
          tests={testResults}
          selectedTestId={selectedTest?.id || null}
          isRunning={isRunning}
          onRunTest={runSingleTest}
          onCopyTest={copyTestData}
          onSelectTest={setSelectedTest}
          copiedStates={copiedStates}
        />
        <TestDetailsPanel
          selectedTest={selectedTest}
          onCopyText={copyToClipboard}
          copiedStates={copiedStates}
        />
      </div>
    </div>
  );
}

--- END FILE: src\app\test\page.tsx ---

--- START FILE: src\db\index.ts ---
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';
import 'dotenv/config';

// Create a connection to the database
const client = postgres(process.env.DATABASE_URL!);

// Create the Drizzle instance without passing the schema
export const db = drizzle(client);

// Export the schema separately for use in queries
export { schema }; 
--- END FILE: src\db\index.ts ---

--- START FILE: src\db\migrate.ts ---
import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import 'dotenv/config';

async function main() {
  // eslint-disable-next-line no-console
  console.log('Running migrations...');

  const migrationClient = postgres(process.env.DATABASE_URL!, { max: 1 });

  await migrate(drizzle(migrationClient), { migrationsFolder: './drizzle' });

  // eslint-disable-next-line no-console
  console.log('Migrations finished!');
  await migrationClient.end();
  process.exit(0);
}

main().catch((err) => {
  // eslint-disable-next-line no-console
  console.error(err);
  process.exit(1);
}); 
--- END FILE: src\db\migrate.ts ---

--- START FILE: src\db\schema.ts ---
// This file will be populated by drizzle-kit introspect 

import { pgTable, serial, varchar, timestamp, decimal, index, unique, integer } from 'drizzle-orm/pg-core';

export const candles = pgTable('candles', {
  id: serial('id').primaryKey(),
  symbol: varchar('symbol', { length: 32 }).notNull(),
  interval: varchar('interval', { length: 16 }).notNull(),
  marketType: varchar('market_type', { length: 32 }).notNull(),
  openTime: timestamp('open_time', { withTimezone: true }).notNull(),
  open: decimal('open', { precision: 18, scale: 8 }).notNull(),
  high: decimal('high', { precision: 18, scale: 8 }).notNull(),
  low: decimal('low', { precision: 18, scale: 8 }).notNull(),
  close: decimal('close', { precision: 18, scale: 8 }).notNull(),
  volume: decimal('volume', { precision: 18, scale: 8 }).notNull(),
}, (table) => {
  return {
    symbolIntervalMarketTimeIdx: index('symbol_interval_market_time_idx').on(table.symbol, table.interval, table.marketType, table.openTime),
    unq: unique('candles_unq').on(table.symbol, table.interval, table.marketType, table.openTime),
  };
});

export const tickers24h = pgTable('tickers_24h', {
  id: serial('id').primaryKey(),
  symbol: varchar('symbol', { length: 32 }).notNull(),
  marketType: varchar('market_type', { length: 32 }).notNull(),
  lastPrice: decimal('last_price', { precision: 18, scale: 8 }),
  priceChange: decimal('price_change', { precision: 18, scale: 8 }),
  priceChangePercent: decimal('price_change_percent', { precision: 18, scale: 4 }),
  highPrice: decimal('high_price', { precision: 18, scale: 8 }),
  lowPrice: decimal('low_price', { precision: 18, scale: 8 }),
  // Increased precision for volume fields to handle large crypto volumes
  // precision 24, scale 8 allows values up to 10^16 (quadrillions)
  volume: decimal('volume', { precision: 24, scale: 8 }),
  quoteVolume: decimal('quote_volume', { precision: 24, scale: 8 }),
  openTime: timestamp('open_time', { withTimezone: true }),
  closeTime: timestamp('close_time', { withTimezone: true }),
  count: integer('count'),
  lastUpdated: timestamp('last_updated', { withTimezone: true }).defaultNow().notNull(),
}, (table) => {
  return {
    marketTypeIdx: index('market_type_idx').on(table.marketType),
    quoteVolumeIdx: index('quote_volume_idx').on(table.quoteVolume),
    priceChangePercentIdx: index('price_change_percent_idx').on(table.priceChangePercent),
    unq: unique('tickers_24h_unq').on(table.symbol, table.marketType),
  };
});
--- END FILE: src\db\schema.ts ---

--- START FILE: src\features\Chart\Chart.tsx ---
"use client";

import React, { useRef, useEffect, useState, useCallback, useMemo, useId, createContext, useContext } from 'react';
import {
  createChart,
  IChartApi,
  UTCTimestamp,
  ISeriesApi,
  PriceLineOptions, 
  IPriceLine, 
  CandlestickSeries, 
  PriceFormatterFn, 
  BarPrice,
  LineWidth,
  CandlestickData,
  ChartOptions,
  DeepPartial,
  SeriesOptionsMap,
  TimeScaleOptions,
  PriceScaleOptions,
  MouseEventParams,
  IPriceScaleApi,
} from 'lightweight-charts';

// Import types and market hooks
import { Kline, MarketType } from '@/shared/index';
import { useHistoricalKlinesQuery } from '@/shared/market/hooks';
import { useWebSocketAutoConnect, WebSocketState } from '@/shared/market/websocket';

// UI components
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/shared/ui/dropdown-menu";
import { Icon } from '@/shared/ui/icons/all_Icon';
import { Skeleton } from "@/shared/ui/skeleton";

// Store
import { 
  useAppSettingsStore, 
  selectAutoHideScalesEnabled, 
  selectToggleAutoHideScales,
  selectChartBackgroundColor,
  selectChartTextColor,
  selectChartBorderColor,
  selectChartGridVertLinesVisible,
  selectChartGridHorzLinesVisible,
  selectChartGridVertLinesColor,
  selectChartGridHorzLinesColor,
  selectChartCandleBodyUpColor,
  selectChartCandleBodyDownColor,
  selectChartCandleBorderUpColor,
  selectChartCandleBorderDownColor,
  selectChartCandleWickUpColor,
  selectChartCandleWickDownColor,
  selectChartRightOffset,
  selectChartBarSpacing,
  selectChartTimeScaleTextColor,
  selectChartPriceLineColor,
} from '@/shared/store/settingsStore';

// Hooks
import { useInterval, useLatest } from 'react-use';
import { useChartIndicatorsIntegration } from './useChartIndicators';
import { usePriceScaleCountdownLabel } from './plugins/usePriceAndTimer';
import { useChartData, ChartDataHookResult, ChartDataHookOptions } from './useChartData';

// --- Constants ---
const AVAILABLE_TIMEFRAMES = ['1m', '5m', '15m', '30m', '1h', '2h', '4h', '1d', '1w'];
const EMPTY_KLINE_DATA: ReadonlyArray<Kline> = [];
const MINIMUM_MOVE = 0.00000001; // Used for priceFormat minMove
const DEFAULT_HISTORICAL_LIMIT = 1000; // Number of historical candles to fetch
const KLINE_UPDATE_DEBOUNCE_MS = 333; // Debounce time for WS updates callback
const COUNTDOWN_LABEL_PIXEL_OFFSET = 2; // Pixel offset for the countdown label on the price scale
const SCALE_DIMENSION_UPDATE_DELAY_MS = 50; // Delay for initial scale dimension calculation
const SCALE_VISIBILITY_UPDATE_DELAY_MS = 0; // Delay for updating dimensions after visibility change
const HIDDEN_SCALE_BAR_SPACING_ADJUSTMENT = 0.5; // How much to adjust bar spacing when scales are hidden

// --- Helper Functions ---

/** Calculates interval duration in seconds */
function getIntervalDurationSeconds(interval: string): number {
  const match = interval.match(/^(\d+)(\w)$/);
  if (!match) return 60;
  const value = parseInt(match[1], 10);
  const unit = match[2].toLowerCase();
  const unitMultipliers: Record<string, number> = { s: 1, m: 60, h: 3600, d: 86400, w: 604800 };
  return (unitMultipliers[unit] || 60) * value;
}

/** Determines price precision based on magnitude */
const getPrecision = (price: number): number => {
    if (!isFinite(price)) return 2;
    const absPrice = Math.abs(price);
  // If the price is effectively zero (or very close), display as 0.00
  if (absPrice < 1e-7) return 2; // Changed from 8 to 2 for near-zero values

  // Keep the existing logic for other thresholds
  const thresholds = [1e-6, 1e-4, 0.01, 1, 10, 100, 1000];
  const precisions = [8, 7, 6, 5, 4, 3, 2, 1];
    for (let i = 0; i < thresholds.length; i++) {
        if (absPrice < thresholds[i]) return precisions[i];
    }
    return precisions[precisions.length - 1];
};

/** Formats time remaining until the next bar closes */
function formatTimeToBarClose(ms: number, interval: string): string {
  if (!isFinite(ms) || ms < 0) {
    const unit = interval.slice(-1);
    const val = parseInt(interval.slice(0, -1), 10);
    if (unit === 'd' || unit === 'w') return '0d 0h';
    if (unit === 'h' || (unit === 'm' && val >= 60)) return '0h 0m';
    return '00:00';
  }
  const sec = Math.floor(ms / 1000);
  if (sec < 86400) { // Less than a day
    const h = Math.floor(sec / 3600);
    const m = Math.floor((sec % 3600) / 60);
    const s = sec % 60;
    return h > 0
      ? `${h.toString()}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`
      : `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  }
  const d = Math.floor(sec / 86400);
  const h = Math.floor((sec % 86400) / 3600);
  return `${d}d ${h}h`;
}

/** Formats price with dynamic precision */
function formatChartPrice(price: number): string {
  return isFinite(price) ? price.toFixed(getPrecision(price)) : '';
}

/** Creates a global price formatter function */
const createGlobalPriceFormatter = (): PriceFormatterFn => (price: BarPrice): string => {
  return formatChartPrice(Number(price));
};

/** Calculates time until next bar closes based on candle data and interval */
const calculateTimeToBarCloseFromCandle = (candleTime: number, interval: string, closeTime?: number): number => {
  if (typeof candleTime !== 'number' || !isFinite(candleTime)) return 0;
  const nowSec = Math.floor(Date.now() / 1000);
  if (closeTime && isFinite(closeTime)) {
    return Math.max(0, (closeTime - nowSec) * 1000);
  }
  const durationSeconds = getIntervalDurationSeconds(interval);
  if (!durationSeconds) return 0;
  const calculatedCloseTimeSec = candleTime + durationSeconds;
  return Math.max(0, (calculatedCloseTimeSec - nowSec) * 1000);
};

/** Safely removes a price line from a series */
const safeRemovePriceLine = (series: ISeriesApi<keyof SeriesOptionsMap> | null, line: IPriceLine | null): void => {
  if (series && line) {
    try {
      series.removePriceLine(line);
    } catch (e) { /* Ignore errors during cleanup */ }
  }
};

// --- React Context for Chart State ---
type ChartContextType = {
  chartId: string;
  timeToBarClose: number;
  setTimeToBarClose: React.Dispatch<React.SetStateAction<number>>;
};
const ChartContext = createContext<ChartContextType | null>(null);

const ChartContextProvider: React.FC<{children: React.ReactNode; chartId: string}> = ({ children, chartId }) => {
  const [timeToBarClose, setTimeToBarClose] = useState(0);

  // Memoize the context value to prevent unnecessary re-renders of consumers
  const contextValue = useMemo(() => ({
    chartId,
    timeToBarClose,
    setTimeToBarClose,
  }), [chartId, timeToBarClose, setTimeToBarClose]);

  return (
    <ChartContext.Provider value={contextValue}>
      {children}
    </ChartContext.Provider>
  );
};

const useChartContext = () => {
  const context = useContext(ChartContext);
  if (!context) throw new Error('useChartContext must be used within a ChartContextProvider');
  return context;
};

// --- Types ---
interface SimpleChartProps {
  symbol: string;
  marketType: MarketType;
  interval: string;
  onIntervalChange?: (newInterval: string) => void;
  onFullscreenToggle?: () => void;
}

interface ChartSettings {
  chartBackgroundColor: string;
  chartTextColor: string;
  gridVertLinesVisible: boolean;
  gridHorzLinesVisible: boolean;
  gridVertLinesColor: string;
  gridHorzLinesColor: string;
  borderColor: string;
  candleBodyUpColor: string;
  candleBodyDownColor: string;
  candleBorderUpColor: string;
  candleBorderDownColor: string;
  candleWickUpColor: string;
  candleWickDownColor: string;
  rightOffset: number;
  barSpacing: number;
  scaleLabelBackgroundColor: string;
  scaleLabelTextColor: string;
  chartPriceLineColor: string;
  autoHideScalesEnabled: boolean;
}

// --- Custom Hooks ---

/** Hook to manage chart initialization, options, and cleanup */
function useChartCore(
  chartContainerRef: React.RefObject<HTMLDivElement | null>,
  settings: ChartSettings
) {
  const chartRef = useRef<IChartApi | null>(null);
  const seriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const [isReady, setIsReady] = useState(false);

  const {
    chartBackgroundColor, chartTextColor, gridVertLinesColor, gridVertLinesVisible,
    gridHorzLinesColor, gridHorzLinesVisible, borderColor, barSpacing, rightOffset
  } = settings;

  const getBaseChartOptions = useCallback((): DeepPartial<ChartOptions> => ({
    layout: {
      background: { color: chartBackgroundColor },
      textColor: chartTextColor,
      attributionLogo: false,
    },
    grid: {
      vertLines: { color: gridVertLinesColor, visible: gridVertLinesVisible },
      horzLines: { color: gridHorzLinesColor, visible: gridHorzLinesVisible },
    },
    localization: {
      priceFormatter: createGlobalPriceFormatter(),
    },
    timeScale: {
      borderColor: borderColor,
      timeVisible: true,
      visible: true,
      barSpacing: barSpacing,
      rightOffset: rightOffset,
    },
    rightPriceScale: {
      borderColor: borderColor,
      visible: true,
    },
    crosshair: { mode: 0 },
    handleScroll: true,
    handleScale: true,
    autoSize: true,
  }), [
    chartBackgroundColor, chartTextColor, gridVertLinesColor, gridVertLinesVisible,
    gridHorzLinesColor, gridHorzLinesVisible, borderColor, barSpacing, rightOffset
  ]);

  // Destructure settings needed for candlestick options
  const {
    candleBodyUpColor, candleBodyDownColor, candleBorderUpColor, candleBorderDownColor,
    candleWickUpColor, candleWickDownColor, chartPriceLineColor
  } = settings;

  const getCandlestickOptions = useCallback((): DeepPartial<SeriesOptionsMap['Candlestick']> => ({
    upColor: candleBodyUpColor,
    downColor: candleBodyDownColor,
      borderVisible: true,
    borderUpColor: candleBorderUpColor,
    borderDownColor: candleBorderDownColor,
    wickUpColor: candleWickUpColor,
    wickDownColor: candleWickDownColor,
    priceLineVisible: true, // Controlled later maybe, but default to true
    lastValueVisible: false, // We use custom label
      priceLineWidth: 1 as LineWidth,
    priceLineColor: chartPriceLineColor,
      priceFormat: {
      type: 'price',
      minMove: MINIMUM_MOVE,
    },
  }), [
    // Explicitly list dependencies
    candleBodyUpColor, candleBodyDownColor, candleBorderUpColor, candleBorderDownColor,
    candleWickUpColor, candleWickDownColor, chartPriceLineColor
  ]);

  // Initialize chart and series
  useEffect(() => {
    if (!chartContainerRef.current) return;

    const chart = createChart(chartContainerRef.current, getBaseChartOptions());
    const candlestickSeries = chart.addSeries(CandlestickSeries, getCandlestickOptions());

    chartRef.current = chart;
    seriesRef.current = candlestickSeries;
    setIsReady(true);

    // Cleanup function
    return () => {
      const chartToRemove = chartRef.current;
      chartRef.current = null;
      seriesRef.current = null;
      setIsReady(false);
      if (chartToRemove) {
        try {
          chartToRemove.remove();
        } catch (e) { /* Ignore cleanup errors */ }
      }
    };
  }, [chartContainerRef, getBaseChartOptions, getCandlestickOptions]); // Re-create chart if base options change fundamentally

  // Apply general chart options reactively
  useEffect(() => {
    const chart = chartRef.current;
    if (!chart || !isReady) return;
    try {
      // Apply only layout, grid, localization, timescale, rightPriceScale options that might change
      const { layout, grid, localization, timeScale, rightPriceScale, ...rest } = getBaseChartOptions();
      // Don't re-apply formatter constantly if it hasn't changed structure
      const optionsToApply: DeepPartial<ChartOptions> = { layout, grid, timeScale, rightPriceScale };
      if (JSON.stringify(localization) !== JSON.stringify(chart.options().localization)) {
        // Explicitly cast to 'any' to avoid type errors when adding localization dynamically
        (optionsToApply as any).localization = localization;
      }
      // Prevent flicker by not touching visibility here
      // Visibility is handled by useScaleInteraction
      delete (optionsToApply.timeScale as DeepPartial<TimeScaleOptions> | undefined)?.visible;
      delete (optionsToApply.rightPriceScale as DeepPartial<PriceScaleOptions> | undefined)?.visible;
      
      chart.applyOptions(optionsToApply);
    } catch (error) { /* Ignore */ }
  }, [getBaseChartOptions, isReady]);

  // Apply series options reactively
  useEffect(() => {
    const series = seriesRef.current;
    if (!series || !isReady) return;
    try {
      series.applyOptions(getCandlestickOptions());
    } catch (error) { /* Ignore */ }
  }, [getCandlestickOptions, isReady]);

  return { chartRef, seriesRef, isReady, getBaseChartOptions };
}


/** Hook to manage the countdown timer */
function useCountdown(
  lastCandleRef: React.RefObject<Kline | null>,
  interval: string,
  isReady: boolean,
  setTimeToBarClose: React.Dispatch<React.SetStateAction<number>>
) {
  const latestLastCandle = useLatest(lastCandleRef.current);

  const updateCountdown = useCallback(() => {
    const currentCandle = latestLastCandle.current;
    const candleTime = currentCandle?.openTime ? Math.floor(currentCandle.openTime / 1000) : null;
    const candleCloseTime = currentCandle?.closeTime ? Math.floor(currentCandle.closeTime / 1000) : undefined;

    if (currentCandle?.isClosed) {
      setTimeToBarClose(0);
      return;
    }
    
    if (candleTime) {
      const timeToClose = calculateTimeToBarCloseFromCandle(candleTime, interval, candleCloseTime);
      setTimeToBarClose(timeToClose);
    } else {
      setTimeToBarClose(0);
    }
  }, [interval, setTimeToBarClose, latestLastCandle]);

  useInterval(updateCountdown, isReady ? 1000 : null);

  // Trigger initial update when ready or candle changes
  useEffect(() => {
    if (isReady) {
      updateCountdown();
    }
  }, [isReady, lastCandleRef.current, updateCountdown]); // Re-run if candle ref content changes
}

/** Hook to manage scale visibility, dimensions, and interaction */
function useScaleInteraction(
  chartRef: React.RefObject<IChartApi | null>,
  isReady: boolean,
  autoHideScalesEnabled: boolean,
  baseBarSpacing: number // Pass base bar spacing from options
) {
  const [scaleWidth, setScaleWidth] = useState(0);
  const [timeScaleHeight, setTimeScaleHeight] = useState(0);
  const [isHovered, setIsHovered] = useState(false); // Hovering over chart area
  
  const updateScaleDimensions = useCallback(() => {
    if (!chartRef.current || !isReady) return;
    try {
      const chart = chartRef.current;
      const priceScale = chart.priceScale('right');
      const timeScale = chart.timeScale();
      if (priceScale && timeScale) {
        const newWidth = priceScale.width();
        const newHeight = timeScale.height();
        // Update state only if dimensions actually changed
        setScaleWidth(prev => (newWidth !== prev ? newWidth : prev));
        setTimeScaleHeight(prev => (newHeight !== prev ? newHeight : prev));
      } else {
        // Reset if scales become unavailable
        setScaleWidth(0);
        setTimeScaleHeight(0);
      }
    } catch (e) { /* Ignore errors during dimension update */ }
  }, [isReady, chartRef]);

  // Effect for initial dimension calculation and resize listener
  useEffect(() => {
    if (!isReady) return;
    // Initial calculation might need slight delay for layout stabilization
    const initialTimeout = setTimeout(updateScaleDimensions, SCALE_DIMENSION_UPDATE_DELAY_MS); // Use constant
    window.addEventListener('resize', updateScaleDimensions);
    return () => {
      clearTimeout(initialTimeout);
      window.removeEventListener('resize', updateScaleDimensions);
    };
  }, [isReady, updateScaleDimensions]);

  // Effect to control scale visibility and update dimensions after visibility change
  useEffect(() => {
    const chart = chartRef.current;
    if (!chart || !isReady) return;

    const shouldBeVisible = !autoHideScalesEnabled || isHovered;

    try {
      const timeScaleOptions: DeepPartial<TimeScaleOptions> = {
        visible: shouldBeVisible,
        // Slightly adjust bar spacing when scales are hidden to potentially compensate for layout shifts
        barSpacing: shouldBeVisible ? baseBarSpacing : baseBarSpacing + HIDDEN_SCALE_BAR_SPACING_ADJUSTMENT, // Use constant
      };
      const priceScaleOptions: DeepPartial<PriceScaleOptions> = {
        visible: shouldBeVisible,
      };

      chart.applyOptions({
          timeScale: timeScaleOptions,
          rightPriceScale: priceScaleOptions
      });

      // Update dimensions shortly after visibility change to ensure correct measurements
      const visibilityTimeout = setTimeout(updateScaleDimensions, SCALE_VISIBILITY_UPDATE_DELAY_MS); // Use constant
      return () => clearTimeout(visibilityTimeout);

    } catch (error) { /* Ignore applyOptions errors */ }
  }, [isReady, isHovered, autoHideScalesEnabled, baseBarSpacing, chartRef, updateScaleDimensions]);

  // Event Handlers for Hover States
  const handleMouseEnter = useCallback(() => setIsHovered(true), []);
  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  return {
    scaleWidth, timeScaleHeight, isHovered,
    handleMouseEnter, handleMouseLeave,
  };
}


// --- UI Components ---

const EyeIconSvg: React.FC = React.memo(() => (
  <svg xmlns="http://www.w3.org/2000/svg" version="1.1" x="0" y="0" viewBox="0 0 512 512" width="14" height="14">
    <g>
      <circle cx="256" cy="256" r="80" fill="none" stroke="currentColor" strokeWidth="40" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10"></circle>
      <path d="M483.24 231.168c11.679 14.438 11.679 35.226 0 49.664C446.883 325.775 355.535 416 256 416S65.117 325.775 28.76 280.832c-11.679-14.438-11.679-35.226 0-49.664C65.117 186.225 156.465 96 256 96s190.883 90.225 227.24 135.168z" fill="none" stroke="currentColor" strokeWidth="40" strokeLinecap="round" strokeLinejoin="round" strokeMiterlimit="10"></path>
    </g>
  </svg>
));
EyeIconSvg.displayName = 'EyeIconSvg';

interface ToggleScalesButtonProps {
  isVisible: boolean;
  scaleWidth: number;
  timeScaleHeight: number;
  isButtonHovered: boolean;
  chartBackgroundColor: string; // Use direct prop
  autoHideScalesEnabled: boolean;
  onToggle: () => void;
  onContainerMouseEnter: () => void;
  onContainerMouseLeave: () => void;
  onButtonMouseEnter: () => void;
  onButtonMouseLeave: () => void;
}

const ToggleScalesVisibilityButton: React.FC<ToggleScalesButtonProps> = React.memo(({
  isVisible, scaleWidth, timeScaleHeight, isButtonHovered, chartBackgroundColor,
  autoHideScalesEnabled, onToggle, onContainerMouseEnter, onContainerMouseLeave,
  onButtonMouseEnter, onButtonMouseLeave,
}) => {
  // Render only if conditions met (visible, scale dimensions valid)
  if (!isVisible || scaleWidth <= 0 || timeScaleHeight <= 0) {
    return null;
  }

  return (
    <div 
      className="absolute z-5"
          style={{
            width: `calc(${scaleWidth}px - 2px)`, 
            height: '24px', // Adjusted height for the button area
            bottom: `${timeScaleHeight}px`,
            right: '0px', // Explicit right position
          }}
          onMouseEnter={onContainerMouseEnter}
          onMouseLeave={onContainerMouseLeave}
    >
      <button
        className="w-full h-full text-neutral-400 hover:text-neutral-100 transition-colors duration-150 flex items-center justify-center border-none focus:outline-none focus-visible:ring-1 focus-visible:ring-ring/50"
        style={{
           // Use the chart's background color directly for seamless integration
           backgroundColor: chartBackgroundColor,
        }}
        onClick={onToggle}
        title="Переключить автоскрытие шкал"
        onMouseEnter={onButtonMouseEnter}
        onMouseLeave={onButtonMouseLeave}
      >
        <EyeIconSvg />
      </button>
    </div>
  );
});
ToggleScalesVisibilityButton.displayName = 'ToggleScalesVisibilityButton';


// --- Main Chart Component Logic ---

const ChartContent: React.FC<SimpleChartProps & { uniqueChartId: string }> = React.memo(({
  symbol,
  marketType,
  interval,
  onIntervalChange,
  onFullscreenToggle,
  uniqueChartId
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const { timeToBarClose, setTimeToBarClose } = useChartContext();
  const [isPointerOverScaleCoords, setIsPointerOverScaleCoords] = useState(false);
  const [isButtonHovered, setIsButtonHovered] = useState(false);
  const handleButtonMouseEnter = useCallback(() => setIsButtonHovered(true), []);
  const handleButtonMouseLeave = useCallback(() => setIsButtonHovered(false), []);
  
  // Ensure WebSocket is always connected
  const wsConnectionState = useWebSocketAutoConnect();

  // <<< --- НОВЫЙ ЛОГ МОНТИРОВАНИЯ/РАЗМОНТИРОВАНИЯ --- >>>
  useEffect(() => {
    return () => {
      // Cleanup logic if needed
    };
  }, [uniqueChartId, symbol, marketType, interval]);
  // <<< --- КОНЕЦ НОВОГО ЛОГА --- >>>

  // --- Settings from Store ---
  // Select individual state pieces first
  const chartBackgroundColor = useAppSettingsStore(selectChartBackgroundColor);
  const chartTextColor = useAppSettingsStore(selectChartTextColor);
  const gridVertLinesVisible = useAppSettingsStore(selectChartGridVertLinesVisible);
  const gridHorzLinesVisible = useAppSettingsStore(selectChartGridHorzLinesVisible);
  const gridVertLinesColor = useAppSettingsStore(selectChartGridVertLinesColor);
  const gridHorzLinesColor = useAppSettingsStore(selectChartGridHorzLinesColor);
  const borderColor = useAppSettingsStore(selectChartBorderColor);
  const candleBodyUpColor = useAppSettingsStore(selectChartCandleBodyUpColor);
  const candleBodyDownColor = useAppSettingsStore(selectChartCandleBodyDownColor);
  const candleBorderUpColor = useAppSettingsStore(selectChartCandleBorderUpColor);
  const candleBorderDownColor = useAppSettingsStore(selectChartCandleBorderDownColor);
  const candleWickUpColor = useAppSettingsStore(selectChartCandleWickUpColor);
  const candleWickDownColor = useAppSettingsStore(selectChartCandleWickDownColor);
  const rightOffset = useAppSettingsStore(selectChartRightOffset);
  const barSpacing = useAppSettingsStore(selectChartBarSpacing);
  const timeScaleTextColor = useAppSettingsStore(selectChartTimeScaleTextColor);
  const chartPriceLineColor = useAppSettingsStore(selectChartPriceLineColor);
  const autoHideScalesEnabled = useAppSettingsStore(selectAutoHideScalesEnabled);
  const toggleAutoHideScales = useAppSettingsStore(selectToggleAutoHideScales);

  // Memoize the derived settings object based on selected values
  const settings = useMemo<ChartSettings>(() => ({
    chartBackgroundColor,
    chartTextColor,
    gridVertLinesVisible,
    gridHorzLinesVisible,
    // Provide defaults here to ensure string type for ChartSettings
    gridVertLinesColor: gridVertLinesColor ?? 'rgba(70, 70, 70, 0.5)',
    gridHorzLinesColor: gridHorzLinesColor ?? 'rgba(70, 70, 70, 0.5)',
    borderColor: borderColor ?? '#cccccc',
    candleBodyUpColor: candleBodyUpColor ?? '#26a69a',
    candleBodyDownColor: candleBodyDownColor ?? '#ef5350',
    candleBorderUpColor: candleBorderUpColor ?? '#26a69a',
    candleBorderDownColor: candleBorderDownColor ?? '#ef5350',
    candleWickUpColor: candleWickUpColor ?? '#26a69a',
    candleWickDownColor: candleWickDownColor ?? '#ef5350',
    rightOffset,
    barSpacing,
    scaleLabelBackgroundColor: chartBackgroundColor ?? '#1f2937',
    scaleLabelTextColor: timeScaleTextColor ?? '#d1d5db',
    chartPriceLineColor: chartPriceLineColor ?? '#cccccc',
    autoHideScalesEnabled,
  }), [
      chartBackgroundColor,
      chartTextColor,
      gridVertLinesVisible,
      gridHorzLinesVisible,
      gridVertLinesColor,
      gridHorzLinesColor,
      borderColor,
      candleBodyUpColor,
      candleBodyDownColor,
      candleBorderUpColor,
      candleBorderDownColor,
      candleWickUpColor,
      candleWickDownColor,
      rightOffset,
      barSpacing,
      timeScaleTextColor,
      chartPriceLineColor,
      autoHideScalesEnabled,
  ]);

  // --- Core Chart Hook ---
  const { chartRef, seriesRef, isReady, getBaseChartOptions } = useChartCore(chartContainerRef, settings);

  // --- Data Handling Hook ---
  // Callback to update price lines and countdown when data changes
  const handleDataUpdate = useCallback((lastKline: Kline | null, source: 'hist' | 'ws') => {
  }, [/* Dependencies if any */]); // <<<--- Удаляем removeCountdownLabel из зависимостей

  const handleWsError = useCallback((error: Error) => {
  }, [symbol, interval]);

  // Use the new useChartData hook
  const { 
    isInitialLoading, 
    isError, 
    errorMessage, 
    lastCandleRef, 
    lastKnownPriceRef, 
    processedKlineData, 
    rawKlines
  }: ChartDataHookResult = useChartData({
    chartRef,
    marketType,
    symbol,
    interval,
    seriesRef,
    isReady,
    uniqueChartId,
    onDataUpdate: handleDataUpdate,
    onSubscriptionError: handleWsError,
  } as ChartDataHookOptions); // Explicit cast to satisfy type checking if needed, or ensure options match

  // --- Countdown Hook ---
  useCountdown(lastCandleRef, interval, isReady, setTimeToBarClose);

  // --- Price Scale Countdown Label Hook (NEW) ---
    usePriceScaleCountdownLabel(
      chartRef.current,
      seriesRef.current ?? null,
      lastKnownPriceRef.current,
      timeToBarClose,
      interval,
      {
        priceTextColor: settings.scaleLabelTextColor,
        countdownTextColor: settings.scaleLabelTextColor,
        backgroundColor: settings.scaleLabelBackgroundColor,
      }
    );

  // --- Scale Interaction Hook ---
  const baseBarSpacing = useMemo(() => getBaseChartOptions().timeScale?.barSpacing ?? 6, [getBaseChartOptions]);
  const {
    scaleWidth, timeScaleHeight, isHovered,
    handleMouseEnter, handleMouseLeave,
  } = useScaleInteraction(chartRef, isReady, autoHideScalesEnabled, baseBarSpacing);

  // --- Indicator Integration Hook ---
  useChartIndicatorsIntegration(
    chartRef,
    uniqueChartId,
    rawKlines,
    symbol,
    interval as Kline['interval']
  );

  // --- Effect for Container Pointer Events Listener ---
  useEffect(() => {
    const container = chartContainerRef.current;
    if (!isReady || !container || scaleWidth <= 0) {
      // Ensure state is false if conditions aren't met
      if (isPointerOverScaleCoords) setIsPointerOverScaleCoords(false);
      return;
    }

    const handlePointerMove = (e: PointerEvent) => {
      if (!container) return; // Should not happen, but safety check
      const clientWidth = container.clientWidth;
      const rect = container.getBoundingClientRect();
      const localX = e.clientX - rect.left;
      const isOverScale = localX >= clientWidth - scaleWidth;

      // Always attempt to set state (React handles optimization)
      setIsPointerOverScaleCoords(isOverScale);
    };

    const handlePointerLeave = () => {
        // Always set to false when leaving the container
        setIsPointerOverScaleCoords(false);
    };

    // Attach listeners to the main chart container
    container.addEventListener('pointermove', handlePointerMove);
    container.addEventListener('pointerleave', handlePointerLeave);

    // Cleanup function
    return () => {
      container.removeEventListener('pointermove', handlePointerMove);
      container.removeEventListener('pointerleave', handlePointerLeave);
      // Reset state on cleanup
      setIsPointerOverScaleCoords(false);
    };

  // Re-run if readiness, container ref, or scaleWidth changes
  }, [isReady, chartContainerRef, scaleWidth, isPointerOverScaleCoords]); // Restore isPointerOverScaleCoords dependency


  // --- Interval Change Handling ---
  const handleIntervalSelect = useCallback((newInterval: string) => {
    onIntervalChange?.(newInterval);
  }, [onIntervalChange]);

  // --- Double Click for Fullscreen Effect ---
  useEffect(() => {
    const chart = chartRef.current;
    const container = chartContainerRef.current;
    if (!chart || !isReady || !container || !onFullscreenToggle) return; // Exit if chart/toggle not ready

    const handleChartDoubleClick = (param: MouseEventParams) => {
      if (!param.point) return; // Click outside the chart area

      const rect = container.getBoundingClientRect();
      const chartWidth = rect.width;
      const chartHeight = rect.height;

      // Check if the click occurred within the main chart area (excluding scales)
      const isClickInsideChartArea =
        param.point.x > 0 && param.point.x < chartWidth - scaleWidth &&
        param.point.y > 0 && param.point.y < chartHeight - timeScaleHeight;

      if (isClickInsideChartArea) {
        onFullscreenToggle();
      }
    };

    // Subscribe to the double-click event
    chart.subscribeDblClick(handleChartDoubleClick);

    // Cleanup: Unsubscribe when component unmounts or dependencies change
    return () => {
      try {
        chart.unsubscribeDblClick(handleChartDoubleClick);
      } catch (e) { /* Ignore cleanup errors */ }
    };
  // Dependencies: Ensure effect re-runs if any of these change
  }, [chartRef, isReady, onFullscreenToggle, scaleWidth, timeScaleHeight, chartContainerRef]);

  // --- Render Logic ---
  return (
    <div
      ref={chartContainerRef}
      className="chart-container relative h-full w-full border rounded-md overflow-hidden select-none cursor-crosshair"
      style={{ borderColor: settings.borderColor }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Показываем скелетон, если график еще не готов */}
      {!isReady && (
        <Skeleton className="absolute inset-0 z-30" /> // Высокий z-index, чтобы перекрыть все остальные элементы
      )}

      {/* Chart Header - отображаем только если график готов */}
      {isReady && (
        <div className="chart-header absolute top-0 left-0 right-0 z-10 p-2 flex items-center justify-between text-xs pointer-events-none">
          <div className="flex items-center pointer-events-auto bg-black/20 backdrop-blur-sm rounded">
            <span className="font-medium text-xs text-neutral-200 px-1.5 py-0.5">
                {symbol}
            </span>
            <DropdownMenu>
                <DropdownMenuTrigger>
                  <div className="flex items-center justify-center rounded-sm px-1 text-[10px] font-medium leading-none text-muted-foreground hover:bg-accent hover:text-accent-foreground">
                    <span>{interval}</span>
                    <Icon name="ChevronDown" />
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="pointer-events-auto">
                  {AVAILABLE_TIMEFRAMES.map((tf) => (
                    <DropdownMenuItem
                      key={tf}
                      disabled={tf === interval || isInitialLoading} // Use correct loading state variable
                      onSelect={() => handleIntervalSelect(tf)}
                    >
                      {tf}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="status-area flex items-center gap-2 pointer-events-auto">
            {isError && errorMessage && ( // Use correct error state variables
              <div className="text-xs text-red-400 truncate max-w-[150px] px-1.5 py-0.5 rounded bg-red-900/30 backdrop-blur-sm" title={errorMessage}>
                Load Error
              </div>
            )}
          </div>
        </div>
      )}

      {/* Toggle Scales Button - отображаем только если график готов */}
      {isReady && (
        <ToggleScalesVisibilityButton
          isVisible={isReady && (isPointerOverScaleCoords || isButtonHovered) && scaleWidth > 0 && timeScaleHeight > 0}
          scaleWidth={scaleWidth}
          timeScaleHeight={timeScaleHeight}
          isButtonHovered={isButtonHovered}
          chartBackgroundColor={settings.chartBackgroundColor}
          autoHideScalesEnabled={settings.autoHideScalesEnabled}
          onToggle={toggleAutoHideScales}
          onContainerMouseEnter={handleButtonMouseEnter}
          onContainerMouseLeave={handleButtonMouseLeave}
          onButtonMouseEnter={handleButtonMouseEnter}
          onButtonMouseLeave={handleButtonMouseLeave}
        />
      )}
    </div>
  );
});


// --- Entry Point Component ---
const SimpleChart: React.FC<SimpleChartProps> = React.memo(({
  symbol,
  marketType,
  interval,
  onIntervalChange,
  onFullscreenToggle
}) => {
  const uniqueChartId = useId();
  
  // Если нет допустимого символа или market type еще не определен, 
  // показываем один сплошной скелетон с рамкой
  if (!symbol || !marketType) {
    return (
      <div className="h-full w-full border border-border rounded-sm overflow-hidden">
        <Skeleton className="h-full w-full" />
      </div>
    );
  }
  
  return (
    <ChartContextProvider chartId={uniqueChartId}>
      <ChartContent
        key={`${symbol}-${marketType}-${interval}`}
        symbol={symbol}
        marketType={marketType}
        interval={interval}
        onIntervalChange={onIntervalChange}
        onFullscreenToggle={onFullscreenToggle}
        uniqueChartId={uniqueChartId}
      />
    </ChartContextProvider>
  );
});

export default SimpleChart;

// --- WDYR Integration ---
if (process.env.NODE_ENV === 'development') {
  (ChartContent as any).whyDidYouRender = true;
  (SimpleChart as any).whyDidYouRender = true;
}

--- END FILE: src\features\Chart\Chart.tsx ---

--- START FILE: src\features\Chart\ChartDisplay.tsx ---
import React, { useMemo, memo } from 'react';
import SimpleChart from '@/features/Chart/Chart'; // Assuming SimpleChart is here
import { Ticker, MarketType } from '@/shared/index';

const MemoizedSimpleChart = memo(SimpleChart);
const DEFAULT_TIMEFRAME_CONST = '1h'; // Match default from page.tsx

interface ChartDisplayManagerProps {
  isScreenerMode: boolean;
  screenerTickers: Ticker[];
  screenerTimeframe: string;
  screenerCurrentPage: number;
  selectedTickerSymbol: string | null;
  fullscreenChartIndex: number | null;
  chartIndices: number[];
  chartIntervals: Record<number, string>;
  getMarketTypeForSymbol: (symbol: string) => MarketType;
  intervalHandlers: Record<number, (newInterval: string) => void>;
  fullscreenHandlers: Record<number, () => void>;
  emptyChartMessage: React.ReactNode;
  totalCharts: number; // Needed for screenerTickers.slice
}

const ChartDisplayManager: React.FC<ChartDisplayManagerProps> = ({
  isScreenerMode,
  screenerTickers,
  screenerTimeframe,
  screenerCurrentPage,
  selectedTickerSymbol,
  fullscreenChartIndex,
  chartIndices,
  chartIntervals,
  getMarketTypeForSymbol,
  intervalHandlers,
  fullscreenHandlers,
  emptyChartMessage,
  totalCharts,
}) => {
  const renderCharts = useMemo(() => {
    // В режиме скринера
    if (isScreenerMode) {
      if (!screenerTickers.length) {
        return (
          <div className="flex items-center justify-center h-full text-muted-foreground col-span-full row-span-full">
            <div className="text-center">
              <div>Нет данных для скринера</div>
              <div className="text-xs mt-1">Проверьте настройки фильтрации</div>
            </div>
          </div>
        );
      }

      // Handle fullscreen display for screener mode
      // Note: fullscreenChartIndex is the index within the *currently visible page* of tickers.
      // We need to calculate the actual index in the screenerTickers array.
      const startIndexForPage = (screenerCurrentPage - 1) * totalCharts;

      if (fullscreenChartIndex !== null) {
        const actualTickerIndex = startIndexForPage + fullscreenChartIndex;
        if (screenerTickers[actualTickerIndex]) {
          const ticker = screenerTickers[actualTickerIndex];
          const currentMarketType = ticker.marketType;
          const currentInterval = screenerTimeframe; 
          
          return (
            <div key={`screener-chart-fullscreen-${actualTickerIndex}`} className="absolute inset-0 z-50 bg-background">
              <MemoizedSimpleChart
                symbol={ticker.symbol}
                marketType={currentMarketType}
                interval={currentInterval}
                onIntervalChange={intervalHandlers[fullscreenChartIndex]} 
                onFullscreenToggle={fullscreenHandlers[fullscreenChartIndex]}
              />
            </div>
          );
        }
      }
      
      // Calculate the slice of tickers for the current page
      const endIndexForPage = startIndexForPage + totalCharts;
      const visibleTickers = screenerTickers.slice(startIndexForPage, endIndexForPage);
      
      return visibleTickers.map((ticker, pageIndex) => {
        // pageIndex is the index within the visibleTickers array (0 to totalCharts-1)
        // This is what should be used for accessing intervalHandlers and fullscreenHandlers
        const currentMarketType = ticker.marketType;
        const currentInterval = screenerTimeframe; // Все графики используют один таймфрейм

        return (
          <div key={`screener-chart-${ticker.symbol}-${pageIndex}`} className="bg-background relative h-full w-full">
            <MemoizedSimpleChart
              symbol={ticker.symbol}
              marketType={currentMarketType}
              interval={currentInterval}
              onIntervalChange={intervalHandlers[pageIndex]} // Use pageIndex
              onFullscreenToggle={fullscreenHandlers[pageIndex]} // Use pageIndex
            />
          </div>
        );
      });
    }

    // В режиме фокуса (обычная логика)
    if (!selectedTickerSymbol) return emptyChartMessage;
  
    if (fullscreenChartIndex !== null) {
      const index = fullscreenChartIndex;
      const currentMarketType = getMarketTypeForSymbol(selectedTickerSymbol);
      const currentInterval = chartIntervals[index] || DEFAULT_TIMEFRAME_CONST;
      
      return (
        <div key={`focus-chart-${index}`} className="absolute inset-0 z-50 bg-background">
          <MemoizedSimpleChart
            symbol={selectedTickerSymbol}
            marketType={currentMarketType}
            interval={currentInterval}
            onIntervalChange={intervalHandlers[index]}
            onFullscreenToggle={fullscreenHandlers[index]}
          />
        </div>
      );
    }
    
    return chartIndices.map((index) => {
      const currentMarketType = getMarketTypeForSymbol(selectedTickerSymbol);
      const currentInterval = chartIntervals[index] || DEFAULT_TIMEFRAME_CONST;

      return (
        <div key={`focus-chart-${index}`} className="bg-background relative h-full w-full">
          <MemoizedSimpleChart
            symbol={selectedTickerSymbol}
            marketType={currentMarketType}
            interval={currentInterval}
            onIntervalChange={intervalHandlers[index]}
            onFullscreenToggle={fullscreenHandlers[index]}
          />
        </div>
      );
    });
  }, [
    isScreenerMode,
    screenerTickers,
    screenerTimeframe,
    screenerCurrentPage,
    selectedTickerSymbol, 
    fullscreenChartIndex, 
    chartIndices, 
    chartIntervals, 
    getMarketTypeForSymbol, 
    intervalHandlers, 
    fullscreenHandlers, 
    emptyChartMessage,
    totalCharts
  ]);

  return <>{renderCharts}</>;
};

export default ChartDisplayManager; 
--- END FILE: src\features\Chart\ChartDisplay.tsx ---

--- START FILE: src\features\Chart\useChartData.ts ---
"use client";

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
  ISeriesApi,
  IChartApi,
  UTCTimestamp,
  CandlestickData,
  SeriesOptionsMap,
} from 'lightweight-charts';
import {
  Kline,
  MarketType,
  useHistoricalKlinesQuery,
  useMarketDataManager,
} from '@/shared/index';
import { useMarketStore, getKlinesCacheKey } from '@/shared/store/marketStore';

const KLINE_UPDATE_DEBOUNCE_MS = 333; // Debounce time for WS updates callback

// --- Types ---
export interface ChartDataHookOptions {
  chartRef: React.RefObject<IChartApi | null>;
  marketType: MarketType;
  symbol: string;
  interval: string;
  seriesRef: React.RefObject<ISeriesApi<'Candlestick'> | null>;
  isReady: boolean;
  onDataUpdate: (lastKline: Kline | null, source: 'hist' | 'ws') => void;
  onSubscriptionError: (error: Error) => void;
}

export interface ChartDataHookResult {
  isInitialLoading: boolean;
  isError: boolean;
  errorMessage: string | null;
  lastCandleRef: React.RefObject<Kline | null>;
  lastKnownPriceRef: React.RefObject<number | null>;
  processedKlineData: ReadonlyArray<CandlestickData>;
  rawKlines: ReadonlyArray<Kline>;
}

/**
 * Custom hook to manage data loading (historical + WebSocket) for a chart.
 *
 * @param options - Configuration options for the hook.
 * @returns An object containing loading state, error state, and processed data.
 */
export function useChartData({
  chartRef,
  marketType,
  symbol,
  interval,
  seriesRef,
  isReady,
  // uniqueChartId, // Not directly used in this hook's logic after refactor, consider removing if not needed for logging/debugging
  onDataUpdate,
  onSubscriptionError,
}: ChartDataHookOptions): ChartDataHookResult {
  const [processedKlineData, setProcessedKlineData] = useState<ReadonlyArray<CandlestickData>>([]);
  const [rawKlines, setRawKlines] = useState<ReadonlyArray<Kline>>([]);
  const [isSnapshotLoading, setIsSnapshotLoading] = useState(true);
  const lastCandleRef = useRef<Kline | null>(null);
  const lastKnownPriceRef = useRef<number | null>(null);
  const isInitialLoadComplete = useRef(false);
  const updatePendingRef = useRef(false);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const rafIdRef = useRef<number | null>(null);
  const wsBufferRef = useRef<Kline[]>([]);

  const prevSymbolRef = useRef(symbol);
  const prevIntervalRef = useRef(interval);
  const prevMarketTypeRef = useRef(marketType);

  const { data: infiniteHistoricalData, isLoading: isHistoricalLoading, error: fetchError } = useHistoricalKlinesQuery(symbol, interval, marketType);

  // Extract flat array from InfiniteData structure
  const historicalData = infiniteHistoricalData?.pages?.flat() || [];
  const [isHistoricalLoadingInitial, setIsHistoricalLoadingInitial] = useState(true);
  const [historicalError, setHistoricalError] = useState<Error | null>(null);

  // Используем централизованный менеджер данных для подписки на обновления
  const marketDataManager = useMarketDataManager();

  useEffect(() => {
    if (fetchError) {
      setHistoricalError(fetchError);
    }
  }, [fetchError]);

  useEffect(() => {
    setIsHistoricalLoadingInitial(isHistoricalLoading);
  }, [isHistoricalLoading]);

  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const isHistoricalError = !!historicalError;

  const formatKlineToCandlestick = useCallback((kline: Kline): CandlestickData => ({
    time: Math.floor(kline.openTime / 1000) as UTCTimestamp,
    open: kline.open,
    high: kline.high,
    low: kline.low,
    close: kline.close,
  }), []);

  // Добавим функцию для проверки времени kline относительно последней свечи
  const isValidKlineUpdate = useCallback((kline: Kline): boolean => {
    const lastKline = lastCandleRef.current;
    
    // Если нет последней свечи, то любое обновление валидно
    if (!lastKline) return true;
    
    // Обновление для той же свечи или более новой свечи - валидно
    return kline.openTime >= lastKline.openTime;
  }, []);

  const safeUpdateSeries = useCallback((kline: Kline) => {
    const series = seriesRef.current;
    if (!series || !isInitialLoadComplete.current) {
      return;
    }

    // Проверка на валидность обновления перед любыми действиями
    if (!isValidKlineUpdate(kline)) {
      console.warn('[ChartData] Skipping update for older kline:', {
        symbol: kline.symbol, 
        interval: kline.interval,
        klineTime: new Date(kline.openTime).toISOString(),
        lastCandleTime: lastCandleRef.current ? new Date(lastCandleRef.current.openTime).toISOString() : 'none'
      });
      return;
    }

    if (rafIdRef.current !== null) {
      cancelAnimationFrame(rafIdRef.current);
      rafIdRef.current = null;
    }
    rafIdRef.current = requestAnimationFrame(() => {
      rafIdRef.current = null;
      const currentSeries = seriesRef.current;
      if (!currentSeries) {
        return;
      }
      try {
        const klineData = formatKlineToCandlestick(kline);
        currentSeries.update(klineData);
        lastCandleRef.current = kline;
        lastKnownPriceRef.current = kline.close;

        setProcessedKlineData(prevData => {
          const lastItem = prevData.length > 0 ? prevData[prevData.length - 1] : null;
          const newKlineData = formatKlineToCandlestick(kline);
          let nextData = prevData;
          if (lastItem && lastItem.time === newKlineData.time) {
            if (
              lastItem.open !== newKlineData.open ||
              lastItem.high !== newKlineData.high ||
              lastItem.low !== newKlineData.low ||
              lastItem.close !== newKlineData.close
            ) {
              nextData = [...prevData.slice(0, -1), newKlineData];
            }
          } else if (!lastItem || newKlineData.time > lastItem.time) {
            nextData = [...prevData, newKlineData];
          }
          return nextData;
        });
        setRawKlines(prevRawKlines => {
          const lastRawKline = prevRawKlines.length > 0 ? prevRawKlines[prevRawKlines.length - 1] : null;
          if (lastRawKline && lastRawKline.openTime === kline.openTime) {
            // Replace last kline if time matches
            return [...prevRawKlines.slice(0, -1), kline];
          } else if (!lastRawKline || kline.openTime > lastRawKline.openTime) {
            // Add new kline
            return [...prevRawKlines, kline];
          }
          return prevRawKlines; // Should not happen if data is ordered
        });
        updatePendingRef.current = true;
        if (updateTimeoutRef.current) {
          clearTimeout(updateTimeoutRef.current);
        }
        updateTimeoutRef.current = setTimeout(() => {
          if (updatePendingRef.current) {
            onDataUpdate(lastCandleRef.current, 'ws');
            updatePendingRef.current = false;
          }
          updateTimeoutRef.current = null;
        }, KLINE_UPDATE_DEBOUNCE_MS);
      } catch (e) {
        if (seriesRef.current) {
          console.error(`[ChartData] Error updating series:`, e);
          onSubscriptionError(e instanceof Error ? e : new Error(String(e)));
        }
      }
    });
  }, [seriesRef, onDataUpdate, onSubscriptionError, formatKlineToCandlestick, isValidKlineUpdate]);

  // Effect for resetting state when symbol, interval, or marketType changes
  useEffect(() => {
    const series = seriesRef.current;
    const chart = chartRef.current;

    // Determine if any key parameter has changed
    const symbolChanged = prevSymbolRef.current !== symbol;
    const intervalChanged = prevIntervalRef.current !== interval;
    const marketTypeChanged = prevMarketTypeRef.current !== marketType;

    if (!isReady || !series || !chart) {
      return;
    }

    // If any key parameter changed, reset the chart state
    if (symbolChanged || intervalChanged || marketTypeChanged) {
      console.debug(`[ChartData] Parameters changed. Resetting chart state for: ${symbol} ${interval} ${marketType}`);
      
      // Update refs for previous values
      if (symbolChanged) prevSymbolRef.current = symbol;
      if (intervalChanged) prevIntervalRef.current = interval;
      if (marketTypeChanged) prevMarketTypeRef.current = marketType;

      // Clear existing series data
      try { seriesRef.current?.setData([]); } catch(e) {/* ignore */}
      
      // Reset internal state
      setProcessedKlineData([]);
      setRawKlines([]);
      lastCandleRef.current = null;
      lastKnownPriceRef.current = null;
      isInitialLoadComplete.current = false; // CRITICAL: Ensure new historical data will be processed
      setIsSnapshotLoading(true); // Indicate that we are now loading new snapshot
      setIsHistoricalLoadingInitial(true); // Indicate that historical data load is pending for the new params
      wsBufferRef.current = []; // Clear any buffered WebSocket messages from the old symbol/interval
      setHistoricalError(null); // Clear previous historical errors
      setErrorMessage(null); // Clear previous general errors
    }
  }, [isReady, seriesRef, chartRef, symbol, interval, marketType]); // Dependencies for detecting change and readiness

  // Effect for processing historical data
  useEffect(() => {
    const series = seriesRef.current;
    const chart = chartRef.current;

    if (!isReady || !series || !chart) {
      return;
    }

    if (historicalData && historicalData.length > 0) {
      setIsHistoricalLoadingInitial(false); // Historical data has been fetched (or attempted)

      if (!isInitialLoadComplete.current) { // Process only if initial load for current params is not yet complete
        let finalKlineData: CandlestickData[] = [];
        let lastAppliedKline: Kline | null = null;
        let finalRawKlines: Kline[] = [];

        const sortedHistoricalData = [...historicalData].sort((a, b) => a.openTime - b.openTime);
        
        finalKlineData = sortedHistoricalData.map(formatKlineToCandlestick);
        finalRawKlines = [...sortedHistoricalData];
        lastAppliedKline = sortedHistoricalData[sortedHistoricalData.length - 1];
        const buffer = wsBufferRef.current;

        if (buffer.length > 0 && lastAppliedKline) {
          const sortedBuffer = buffer
            .sort((a, b) => a.openTime - b.openTime)
            .filter(item => item.openTime >= lastAppliedKline!.openTime);
            
          const lastHistTime = lastAppliedKline.openTime;
          sortedBuffer.forEach(bufferedKline => {
            const bufferedKlineTime = bufferedKline.openTime;
            if (bufferedKlineTime === lastHistTime) {
              const updatedCandle = formatKlineToCandlestick(bufferedKline);
              if (finalKlineData.length > 0) {
                finalKlineData[finalKlineData.length - 1] = updatedCandle;
              }
              lastAppliedKline = bufferedKline;
            } else if (bufferedKlineTime > lastHistTime) {
              finalKlineData.push(formatKlineToCandlestick(bufferedKline));
              lastAppliedKline = bufferedKline;
            }
          });
        }

        try {
          series.setData(finalKlineData);
          const priceScale = chart.priceScale('right');
          if (priceScale) priceScale.applyOptions({ autoScale: true });

          setProcessedKlineData(finalKlineData);
          setRawKlines(finalRawKlines);
          lastCandleRef.current = lastAppliedKline;
          lastKnownPriceRef.current = lastAppliedKline?.close ?? null;
          setHistoricalError(null); // Clear any previous error if data is successfully applied
          setErrorMessage(null);
        } catch (e) {
          try { series.setData([]); } catch (e2) { /* ignore */ }
          finalKlineData = [];
          finalRawKlines = [];
          lastAppliedKline = null;
          const err = e instanceof Error ? e : new Error(String(e));
          setHistoricalError(err);
          setErrorMessage(`Error applying historical data: ${err.message}`);
        }

        isInitialLoadComplete.current = true; // Mark initial load as complete for current params
        setIsSnapshotLoading(false); // Snapshot is now loaded
        onDataUpdate(lastCandleRef.current, 'hist');
        wsBufferRef.current = []; // Clear buffer after processing
      }
    } else if (!isHistoricalLoading && (!historicalData || historicalData.length === 0)) {
      setIsHistoricalLoadingInitial(false);
      if (!isInitialLoadComplete.current) {
        try { series.setData([]); } catch (e) { /* ignore */ }
        setProcessedKlineData([]);
        setRawKlines([]);
        lastCandleRef.current = null;
        lastKnownPriceRef.current = null;
        setHistoricalError(null); // No data is not an error in itself, but clear previous errors
        setErrorMessage('No historical data available.');

        isInitialLoadComplete.current = true; // Restore this line: if loading finished and data is empty, consider it complete.
        setIsSnapshotLoading(false);
        onDataUpdate(null, 'hist');
        wsBufferRef.current = [];
      }
    }
  }, [
    isReady, seriesRef, chartRef, symbol, interval, marketType, // Ensure re-run if these change (though reset effect should handle primary reset)
    historicalData, isHistoricalLoading, formatKlineToCandlestick, onDataUpdate
  ]);

  // Effect for handling historical fetch errors
  useEffect(() => {
    if (isHistoricalError && historicalError) {
      // This error is from the SWR query hook (useHistoricalKlinesQuery)
      setErrorMessage(historicalError.message || 'Failed to load historical data.');
      setIsSnapshotLoading(false); // Stop loading indication
      setIsHistoricalLoadingInitial(false);
      if (!isInitialLoadComplete.current) { // If initial load never completed due to error
        setProcessedKlineData([]);
        setRawKlines([]);
        lastCandleRef.current = null;
        lastKnownPriceRef.current = null;
        try { seriesRef.current?.setData([]); } catch(e) {/* ignore */}
        isInitialLoadComplete.current = true; // Mark as 'complete' to prevent further processing attempts for this error state
        onDataUpdate(null, 'hist'); // Notify parent of no data
      }
    }
  }, [isHistoricalError, historicalError, onDataUpdate, seriesRef]);

  // Теперь используем централизованный менеджер для подписки на WebSocket обновления
  useEffect(() => {
    if (!symbol || !interval || !marketType || !isReady) return;
    
    // Используем нашу новую функцию подписки из менеджера данных
    const unsubscribe = marketDataManager.subscribeToSymbol(symbol, interval, marketType);
    
    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [symbol, interval, marketType, isReady, marketDataManager]);

  // Обработчик для WebSocket обновлений
  useEffect(() => {
    // Подписываемся на обновления свечей из глобального хранилища
    const cacheKey = getKlinesCacheKey(symbol, interval, marketType);
    
    // Функция получения последней свечи из хранилища
    const getLatestKline = () => {
      const state = useMarketStore.getState();
      const klines = state.klines[cacheKey] || [];
      return klines.length > 0 ? klines[klines.length - 1] : null;
    };
    
    // Начальная проверка последней свечи
    const latestKline = getLatestKline();
    if (latestKline) {
      if (isInitialLoadComplete.current) {
        safeUpdateSeries(latestKline);
      } else {
        wsBufferRef.current.push(latestKline);
      }
    }
    
    // Подписка на изменения хранилища
    const unsubscribe = useMarketStore.subscribe(() => {
      const latestKline = getLatestKline();
      if (!latestKline) return;
      
      if (isInitialLoadComplete.current) {
        safeUpdateSeries(latestKline);
      } else {
        wsBufferRef.current.push(latestKline);
      }
    });
    
    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [symbol, interval, marketType, safeUpdateSeries]);

  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
        updateTimeoutRef.current = null;
      }
      if (rafIdRef.current !== null) {
        cancelAnimationFrame(rafIdRef.current);
        rafIdRef.current = null;
      }
    };
  }, []);

  const combinedIsLoading = isHistoricalLoadingInitial || isSnapshotLoading;
  // isHistoricalError is from the SWR query, errorMessage is local state
  const combinedIsError = !!historicalError || !!errorMessage; 
  const combinedErrorMessage = historicalError?.message || errorMessage;

  return {
    isInitialLoading: combinedIsLoading,
    isError: combinedIsError,
    errorMessage: combinedErrorMessage,
    lastCandleRef,
    lastKnownPriceRef,
    processedKlineData,
    rawKlines,
  };
} 
--- END FILE: src\features\Chart\useChartData.ts ---

--- START FILE: src\features\Chart\useChartIndicators.ts ---
import {
  useEffect,
  useMemo,
  useRef,
  useCallback
} from 'react';
import {
  IChartApi,
  ISeriesApi,
  SeriesType,
  LineSeries,
  HistogramSeries,
  AreaSeries,
  SeriesDataItemTypeMap,
  LineWidth,
  IPaneApi,
  Time,
  PriceScaleOptions,
  LineStyle,
  CandlestickData,
  UTCTimestamp,
} from 'lightweight-charts';
import { useIndicatorStore } from '@/shared/store/indicatorStore';
import { findIndicatorDefinitionById } from '@/features/Indicators/registry';
import type { IndicatorInstance, CalculatedIndicatorResult, IndicatorOutputInfo, Kline, KlineData } from '@/shared/index'; // Assuming types are exported from index

const EMPTY_INSTANCES: IndicatorInstance[] = [];
const MIN_MAIN_PANE_HEIGHT = 0.3;
const TARGET_MAIN_PANE_HEIGHT = 0.7;

const typeToSeriesMap: Record<string, SeriesType> = {
  line: 'Line',
  histogram: 'Histogram',
  area: 'Area',
  point: 'Line', // <<< Added placeholder mapping for 'point'. Review if specific handling needed.
};

const isValidLineWidth = (width: number | undefined): width is LineWidth =>
  typeof width === 'number' && [1, 2, 3, 4].includes(width);

interface SeriesInfo {
  series: ISeriesApi<SeriesType>;
  paneIndex: number; // Use index instead of paneId
  outputId: string;
  instanceId: string;
}

// Renamed from PaneCustomizationOptions to align with Chart.tsx
interface PaneOptions {
  separatorColor?: string;
  separatorHoverColor?: string;
  enableResize?: boolean;
}

// Renamed and simplified SeriesOptions
type IndicatorSeriesOptions = {
  lastValueVisible?: boolean;
  priceLineVisible?: boolean;
  priceScaleId?: string;
  color?: string;
  lineStyle?: LineStyle;
  lineWidth?: LineWidth;
  // Histogram options (may need adjustment based on definition output)
  // Area options (may need adjustment)
  topColor?: string; // For Area
  bottomColor?: string; // For Area
  lineColor?: string; // For Area baseline? Check lightweight-charts docs
};

/**
 * Hook to manage the integration of technical indicators onto a Lightweight Chart instance.
 * Handles adding, removing, and updating indicator series and panes based on zustand store state.
 *
 * @param chartRef Ref to the IChartApi instance.
 * @param chartId Unique identifier for the chart.
 * @param klineData Processed candlestick data used for main series and indicator calculations.
 * @param symbol Symbol of the chart.
 * @param interval Interval of the chart.
 * @param paneOptions Optional configuration for pane appearance and behavior.
 */
export const useChartIndicatorsIntegration = (
  chartRef: React.RefObject<IChartApi | null>,
  chartId: string,
  // Input should be the processed CandlestickData[] ready for the main series,
  // but indicator calculations might still need the original KlineData structure.
  // We'll fetch KlineData within the effect if needed for calculations.
  klineData: ReadonlyArray<Kline>,
  symbol: string,
  interval: Kline['interval'],
  paneOptions?: PaneOptions
) => {
  const chartIndicators = useIndicatorStore(
    useMemo(() =>
      (state) => state.indicatorsByChartId[chartId] || EMPTY_INSTANCES,
      [chartId]
    )
  );
  const globalIndicators = useIndicatorStore(
    useMemo(() => (state) => state.globalIndicators || EMPTY_INSTANCES, [])
  );

  // Combine local and global indicators for this chart instance
  const indicators = useMemo(() => {
    const combined = [
      ...chartIndicators,
      ...globalIndicators.map(indicator => ({ ...indicator, chartId })) // Ensure local chartId context
    ];
    // console.log(`[useChartIndicators] Combined indicators for ${chartId}:`, combined.length);
    return combined;
  }, [chartIndicators, globalIndicators, chartId]);

  const indicatorSeriesMapRef = useRef<Record<string, Record<string, SeriesInfo>>>({}); // instanceId -> outputId -> SeriesInfo
  const panesRef = useRef<IPaneApi<Time>[]>([]);
  const paneHeightConfigRef = useRef<number[]>([]); // Target heights [main, pane1, pane2, ...]

  const updatePanesRef = useCallback((chart: IChartApi) => {
    try {
      panesRef.current = chart.panes();
    } catch (e) {
      console.error(`[Chart ${chartId}] Error getting panes:`, e);
    }
  }, [chartId]);

  // Function to create a series on a specific pane index
  const createIndicatorSeries = useCallback((
    chart: IChartApi,
    type: SeriesType,
    options: IndicatorSeriesOptions,
    targetPaneIndex: number
  ): ISeriesApi<SeriesType> | null => {
    try {
      // console.log(`[Chart ${chartId}] Creating ${type} series on pane ${targetPaneIndex} with options:`, options);
      if (type === 'Line') return chart.addSeries(LineSeries, options, targetPaneIndex);
      if (type === 'Histogram') return chart.addSeries(HistogramSeries, options, targetPaneIndex);
      if (type === 'Area') return chart.addSeries(AreaSeries, options, targetPaneIndex); // Assuming AreaSeries is needed
      console.warn(`[Chart ${chartId}] Unsupported series type: ${type}`);
      return null;
    } catch (e) {
      console.error(`[Chart ${chartId}] Error creating ${type} series on pane ${targetPaneIndex}:`, e);
      return null;
    }
  }, [chartId]);

  // Map instanceId to its target pane index (0 for main, 1+ for indicator panes)
  const paneInstanceMap = useMemo(() => {
    const map: Record<string, number> = {};
    let paneIndex = 1; // Start indicator panes from index 1
    indicators.forEach(instance => {
      const def = findIndicatorDefinitionById(instance.indicatorId);
      if (def && !def.plotOnMainPane) { // Changed from def?.pane === 'separate'
        map[instance.instanceId] = paneIndex++;
      } else {
        map[instance.instanceId] = 0; // Overlay on main pane (index 0)
      }
    });
    // console.log(`[useChartIndicators] Pane map for ${chartId}:`, map);
    return map;
  }, [indicators, chartId]);

  // Main effect for managing indicator lifecycles
  useEffect(() => {
    const chart = chartRef.current;
    // Ensure chart is valid and we have some base data to calculate from
    if (!chart || !klineData || klineData.length === 0) {
       // Clean up existing indicator series if chart becomes invalid or data disappears
       if (chart && Object.keys(indicatorSeriesMapRef.current).length > 0) {
           console.log(`[Chart ${chartId}] Cleaning up indicators due to invalid chart or no data.`);
           Object.values(indicatorSeriesMapRef.current).forEach(outputs => {
               Object.values(outputs).forEach(({ series }) => {
                   try { chart.removeSeries(series); } catch { /* ignore */ }
               });
           });
           indicatorSeriesMapRef.current = {};
           // Attempt to remove extra panes, assuming pane 0 is main
           try {
               const currentPanes = chart.panes();
               for (let i = currentPanes.length - 1; i > 0; i--) {
                   chart.removePane(i);
               }
               // Reset main pane height? Maybe not necessary if it's removed/recreated
               if (currentPanes[0]) currentPanes[0].setHeight(1);
               updatePanesRef(chart); // Update pane refs after potential removal
           } catch (e) {
               console.warn(`[Chart ${chartId}] Error cleaning up panes:`, e);
           }
       }
       return;
    }

    // Check chart validity
    try {
      chart.options();
    } catch {
      console.warn(`[Chart ${chartId}] Chart instance seems destroyed. Aborting indicator update.`);
      return;
    }

    // Convert CandlestickData back to KlineData if needed by calculation functions
    // This is potentially inefficient; ideally calculations accept CandlestickData or adapt
    const calculationInputData: ReadonlyArray<Kline> = klineData;

    const activeInstanceIds = new Set(indicators.map(i => i.instanceId));
    const currentSeriesMap = indicatorSeriesMapRef.current;
    let panesNeedUpdate = false; // Flag to trigger pane height adjustments

    // --- Step 1: Remove Stale Series & Panes ---
    const instancesToRemove = Object.keys(currentSeriesMap).filter(id => !activeInstanceIds.has(id));
    if (instancesToRemove.length > 0) {
      panesNeedUpdate = true; // Pane heights might change
      instancesToRemove.forEach(instanceId => {
        // console.log(`[Chart ${chartId}] Removing stale indicator instance: ${instanceId}`);
        Object.values(currentSeriesMap[instanceId]).forEach(({ series, outputId }) => {
          try {
            chart.removeSeries(series);
          } catch (e) {
            console.warn(`[Chart ${chartId}] Could not remove series ${outputId} for ${instanceId}:`, e);
          }
        });
        delete currentSeriesMap[instanceId];
      });
    }

    // --- Step 2: Calculate Target Pane Heights ---
    const paneIndicators = indicators.filter(i => {
      const def = findIndicatorDefinitionById(i.indicatorId);
      return def && !def.plotOnMainPane; // Changed from def?.pane === 'separate'
    });
    const targetPaneCount = 1 + paneIndicators.length; // Main pane + indicator panes
    const newPaneHeights: number[] = [];

    if (targetPaneCount === 1) {
      newPaneHeights.push(1); // Only main pane
    } else {
      const mainPaneHeight = Math.max(MIN_MAIN_PANE_HEIGHT, TARGET_MAIN_PANE_HEIGHT);
      const remainingHeight = 1 - mainPaneHeight;
      // Ensure non-negative height for indicators
      const indicatorPaneHeight = Math.max(0, remainingHeight / paneIndicators.length);

      newPaneHeights.push(mainPaneHeight);
      paneIndicators.forEach(() => newPaneHeights.push(indicatorPaneHeight));

      // Normalize heights in case of rounding errors or constraints
      const sum = newPaneHeights.reduce((acc, h) => acc + h, 0);
      if (Math.abs(sum - 1) > 1e-6) {
        console.warn(`[Chart ${chartId}] Normalizing pane heights (Sum: ${sum})`);
        for (let i = 0; i < newPaneHeights.length; i++) {
          newPaneHeights[i] /= sum;
        }
      }
    }
    // Store target heights, only apply later if needed
    paneHeightConfigRef.current = newPaneHeights;


    // --- Step 3: Apply General Chart/Pane Options (Minimal) ---
    // Avoid applying options that might conflict with Chart.tsx management
    // Only apply pane-specific options if provided
    try {
        if (paneOptions) {
            const layoutConfig: { panes?: { separatorColor?: string; separatorHoverColor?: string; enableResize?: boolean } } = { panes: {} };
            if (paneOptions.separatorColor) layoutConfig.panes!.separatorColor = paneOptions.separatorColor;
            if (paneOptions.separatorHoverColor) layoutConfig.panes!.separatorHoverColor = paneOptions.separatorHoverColor;
            layoutConfig.panes!.enableResize = paneOptions.enableResize !== undefined ? paneOptions.enableResize : true;
            //chart.applyOptions({ layout: layoutConfig }); // Be cautious applying layout here
        }

        // Adjust main pane scale margins slightly if indicator panes exist
        const mainPaneScaleMargins = { top: 0.1, bottom: targetPaneCount > 1 ? 0.05 : 0.1 };
        chart.priceScale('right').applyOptions({ scaleMargins: mainPaneScaleMargins });

    } catch (e) {
       console.error(`[Chart ${chartId}] Error applying general chart/pane options:`, e);
    }

    // --- Step 4: Process Active Indicators (Create/Update Series) ---
    indicators.forEach(instance => {
      if (instance.visible === false) {
          // console.log(`[Chart ${chartId}] Skipping calculation/drawing for non-visible instance: ${instance.instanceId}`);
          // Ensure any existing series for this hidden instance are removed (e.g., if visibility was toggled off)
          if (currentSeriesMap[instance.instanceId]) {
              console.log(`[Chart ${chartId}] Removing series for now hidden instance: ${instance.instanceId}`);
              panesNeedUpdate = true; // Pane might need removal/height adjustment
              Object.values(currentSeriesMap[instance.instanceId]).forEach(({ series }) => { try { chart.removeSeries(series); } catch {} });
              delete currentSeriesMap[instance.instanceId];
          }
          return; // Skip processing this non-visible indicator
      }

      const def = findIndicatorDefinitionById(instance.indicatorId);
      if (!def) {
        console.warn(`[Chart ${chartId}] Definition not found for indicatorId: ${instance.indicatorId}`);
        // Remove any lingering series if definition disappears
        if (currentSeriesMap[instance.instanceId]) {
           panesNeedUpdate = true;
           Object.values(currentSeriesMap[instance.instanceId]).forEach(({ series }) => { try { chart.removeSeries(series); } catch {} });
           delete currentSeriesMap[instance.instanceId];
        }
        return;
      }

      let calculatedData: CalculatedIndicatorResult[] = []; // Ensure correct type based on calculate function's return
      try {
        // Pass the instance parameters directly
        // Prepare data for calculation: array of close prices as a common case
        const closePrices = calculationInputData.map(d => d.close);
        // Преобразуем результат из CalculatedIndicatorResult[] к требуемому типу
        const result = def.calculate(closePrices as unknown as KlineData[] | number[], instance.params);
        calculatedData = result;

        // Ensure calculatedData length matches input data length by padding start with undefined/null objects
        // Calculation functions should ideally return arrays matching input length.
        if (calculatedData.length < calculationInputData.length) {
             // Создаем объекты заполнения с пустыми values для соответствия требуемому формату
             const padding = Array(calculationInputData.length - calculatedData.length).fill(null).map(() => ({
                time: 0, // Временная метка будет заменена
                values: {}
             }));
             calculatedData = [...padding, ...calculatedData];
        } else if (calculatedData.length > calculationInputData.length) {
             calculatedData = calculatedData.slice(calculatedData.length - calculationInputData.length);
        }

      } catch (e) {
        console.error(`[Chart ${chartId}] Error calculating indicator ${instance.indicatorId}:`, e);
        if (currentSeriesMap[instance.instanceId]) {
           panesNeedUpdate = true;
           Object.values(currentSeriesMap[instance.instanceId]).forEach(({ series }) => { try { chart.removeSeries(series); } catch {} });
           delete currentSeriesMap[instance.instanceId];
        }
        return; // Skip to next indicator
      }

      // If calculation results in no data points (e.g., insufficient history), remove existing series
      // We check the content of calculatedData later during formatting.
      const isEmptyCalculation = calculatedData.every(d => Object.values(d.values).every(v => v === undefined || v === null));
      if (isEmptyCalculation) {
         if (currentSeriesMap[instance.instanceId]) {
             console.log(`[Chart ${chartId}] Removing series for ${instance.indicatorId} due to empty calculation results.`);
             panesNeedUpdate = true;
             Object.values(currentSeriesMap[instance.instanceId]).forEach(({ series }) => { try { chart.removeSeries(series); } catch {} });
             delete currentSeriesMap[instance.instanceId];
         }
         return; // Skip to next indicator
      }


      const targetPaneIndex = paneInstanceMap[instance.instanceId]; // 0 for main, 1+ for indicator panes
      const isSeparatePane = def && !def.plotOnMainPane; // Changed from def.pane === 'separate'

      if (isSeparatePane && targetPaneIndex < 1) {
        console.error(`[Chart ${chartId}] Invalid targetPaneIndex (${targetPaneIndex}) for separate pane indicator ${instance.indicatorId}. Skipping.`);
        return; // Skip this indicator instance
      }

      if (!currentSeriesMap[instance.instanceId]) {
        // console.log(`[Chart ${chartId}] Initializing series map for new instance: ${instance.instanceId}`);
        currentSeriesMap[instance.instanceId] = {};
        panesNeedUpdate = true; // New instance might require pane height adjustment
      }

      // Process each output defined for the indicator (e.g., SMA line, RSI line)
      def.outputs.forEach(output => {
        const outputType = output.type || 'line'; // Предоставляем тип по умолчанию, если не определен
        const seriesType = typeToSeriesMap[outputType];
        
        if (!seriesType) {
          console.warn(`[Chart ${chartId}] Unsupported output type "${outputType}" for indicator ${instance.indicatorId}, output ${output.id}`);
          return; // Skip this output
        }

        // Format data for the specific series type
        // Match calculation results with the original klineData times
        const formattedData = klineData.map((kline, index) => {
           const calcResult = calculatedData[index]; // Assumes arrays are aligned
           const val = calcResult?.values?.[output.id];

           // Basic validation: time must exist, value must be finite number
           // For Kline, time is openTime and should be in ms for lightweight-charts after conversion
           const timeForChart = kline?.openTime ? (Math.floor(kline.openTime / 1000) as UTCTimestamp) : undefined;

           if (timeForChart === undefined || val === undefined || val === null || !isFinite(Number(val))) {
             return null; // Skip invalid data points
           }

           switch (seriesType) {
               case 'Histogram':
                   let color: string | undefined = undefined;
                   const colorValue = calcResult?.values?.[`${output.id}_color`];
                   if (typeof colorValue === 'string') {
                       color = colorValue;
                   }
                   return { time: timeForChart, value: Number(val), color }; // Include color if available
               case 'Line':
               case 'Area': // Area uses same basic data structure
               default:
                   return { time: timeForChart, value: Number(val) };
           }
        }).filter(Boolean) as SeriesDataItemTypeMap[typeof seriesType][]; // Filter out nulls


        // If no valid data points exist for this output after formatting, remove its series
        if (formattedData.length === 0) {
          const existingSeriesInfo = currentSeriesMap[instance.instanceId]?.[output.id];
          if (existingSeriesInfo) {
            // console.log(`[Chart ${chartId}] Removing series ${output.id} for ${instance.indicatorId} due to no formatted data.`);
            try { chart.removeSeries(existingSeriesInfo.series); } catch {}
            delete currentSeriesMap[instance.instanceId][output.id];
            // If this was the last output for the instance, remove the instance entry
            if (Object.keys(currentSeriesMap[instance.instanceId]).length === 0) {
               delete currentSeriesMap[instance.instanceId];
               panesNeedUpdate = true;
            }
          }
          return; // Skip to next output
        }

        // --- Prepare Series Options ---
        const priceScaleId = isSeparatePane ? 'right' : 'right'; // Use main right scale for overlay, dedicated right scale for separate panes
        const styleOverrides = instance.styleOverrides || {}; // Get style overrides or empty object

        const baseOptions: IndicatorSeriesOptions = {
          lastValueVisible: false, // Typically indicator values aren't shown on scale by default
          priceLineVisible: false, // Hide horizontal price line for indicator value
          priceScaleId: priceScaleId,
        };

        // Start with default styles from definition
        const defaultStyles: IndicatorSeriesOptions = {};
        const outputLineWidth = output.lineWidth; // Get from definition
        const lineWidth = isValidLineWidth(outputLineWidth) ? outputLineWidth : 2; // Default to 2 if invalid

        switch (seriesType) {
            case 'Line':
                defaultStyles.color = output.color ?? '#ffffff';
                defaultStyles.lineStyle = output.lineStyle ?? LineStyle.Solid;
                defaultStyles.lineWidth = lineWidth;
                break;
            case 'Histogram':
                defaultStyles.color = output.color ?? '#26a69a';
                // Histograms might have specific base values, check def/docs
                // styleOptions.base = output.base ?? 0;
                break;
            case 'Area':
                defaultStyles.lineColor = output.color ?? '#2962FF';
                defaultStyles.topColor = output.color ? `${output.color}33` : 'rgba(41, 98, 255, 0.2)';
                defaultStyles.bottomColor = output.color ? `${output.color}00` : 'rgba(41, 98, 255, 0)';
                defaultStyles.lineWidth = lineWidth;
                defaultStyles.lineStyle = output.lineStyle ?? LineStyle.Solid;
                break;
        }

        // Apply overrides from instance state
        const finalSeriesOptions = { ...baseOptions, ...defaultStyles };

        // Visibility override (applies to the series itself, not options)
        const isVisibleOverride = styleOverrides[`${output.id}_visible`];
        const isVisible = isVisibleOverride !== undefined ? Boolean(isVisibleOverride) : true; // Default to true if not overridden

        // Apply other style overrides to options
        const colorOverride = styleOverrides[`${output.id}_color`];
        const lineWidthOverride = styleOverrides[`${output.id}_lineWidth`];
        const lineStyleOverride = styleOverrides[`${output.id}_lineStyle`];
        const opacityOverridePercent = styleOverrides[`${output.id}_opacity`]; // Stored as 0-100

        if (colorOverride !== undefined) finalSeriesOptions.color = String(colorOverride);
        if (lineWidthOverride !== undefined && isValidLineWidth(Number(lineWidthOverride))) finalSeriesOptions.lineWidth = Number(lineWidthOverride) as LineWidth;
        if (lineStyleOverride !== undefined && Object.values(LineStyle).includes(Number(lineStyleOverride))) finalSeriesOptions.lineStyle = Number(lineStyleOverride) as LineStyle;

        // Handle opacity - needs careful application based on series type
        if (opacityOverridePercent !== undefined && finalSeriesOptions.color) { // Only apply if color exists
            const opacity = Math.max(0, Math.min(1, Number(opacityOverridePercent) / 100)); // Convert 0-100 to 0-1
            const rgbaColor = hexOrRgbToRgba(finalSeriesOptions.color, opacity); // Use helper
            finalSeriesOptions.color = rgbaColor;

            // Apply opacity to Area series colors too
            if (seriesType === 'Area') {
                 // This logic might need refinement based on how colors are defined/overridden
                 const baseLineColor = String(colorOverride ?? defaultStyles.lineColor ?? '#2962FF');
                 finalSeriesOptions.lineColor = hexOrRgbToRgba(baseLineColor, opacity); // Apply opacity to area line color too
                 finalSeriesOptions.topColor = hexOrRgbToRgba(baseLineColor, opacity * 0.2); // Adjust opacity factor as needed
                 finalSeriesOptions.bottomColor = hexOrRgbToRgba(baseLineColor, opacity * 0.05);
            }
        }

        // --- Create or Update Series ---
        const existingSeriesInfo = currentSeriesMap[instance.instanceId]?.[output.id];

        if (existingSeriesInfo) {
          // Update existing series
          try {
             // Check if pane index needs changing (though unlikely for existing series)
             if(existingSeriesInfo.paneIndex !== targetPaneIndex) {
                 console.warn(`[Chart ${chartId}] Pane index mismatch for existing series ${output.id}. Recreating.`)
                 // Recreate instead of moving - simpler? AddSeries handles pane target.
                 chart.removeSeries(existingSeriesInfo.series);
                 const newSeries = createIndicatorSeries(chart, seriesType, finalSeriesOptions, targetPaneIndex);
                 if (newSeries) {
                     newSeries.setData(formattedData);
                     currentSeriesMap[instance.instanceId][output.id] = {
                         series: newSeries,
                         paneIndex: targetPaneIndex,
                         outputId: output.id,
                         instanceId: instance.instanceId
                     };
                 } else {
                     delete currentSeriesMap[instance.instanceId][output.id]; // Remove if creation failed
                     panesNeedUpdate = true;
                 }
             } else {
                // Just apply options and data
                existingSeriesInfo.series.applyOptions(finalSeriesOptions);
                existingSeriesInfo.series.setData(formattedData);
                // console.log(`[Chart ${chartId}] Updated series ${output.id} for ${instance.indicatorId} on pane ${targetPaneIndex}`);
             }
          } catch (e) {
            console.error(`[Chart ${chartId}] Error updating series ${output.id} for ${instance.indicatorId}:`, e);
            // Attempt to remove problematic series
            try { chart.removeSeries(existingSeriesInfo.series); } catch {}
            delete currentSeriesMap[instance.instanceId][output.id];
            if (Object.keys(currentSeriesMap[instance.instanceId]).length === 0) {
                delete currentSeriesMap[instance.instanceId];
                panesNeedUpdate = true;
            }
          }
        } else {
          // Only create if visible according to override
          if (isVisible) {
              const newSeries = createIndicatorSeries(chart, seriesType, finalSeriesOptions, targetPaneIndex);
              if (newSeries) {
                newSeries.setData(formattedData);
                currentSeriesMap[instance.instanceId][output.id] = {
                  series: newSeries,
                  paneIndex: targetPaneIndex,
                  outputId: output.id,
                  instanceId: instance.instanceId
                };
                 panesNeedUpdate = true; // New series might require pane setup
              } else {
                 console.error(`[Chart ${chartId}] Failed to create series ${output.id} for ${instance.indicatorId}`);
              }
          } else {
              // console.log(`[Chart ${chartId}] Skipping creation of hidden series ${output.id} for ${instance.indicatorId}`);
          }
        }
      }); // End of output loop
    }); // End of indicator instance loop


     // --- Step 5: Adjust Panes (Heights and Scales) - Deferred ---
     // This needs careful coordination with Chart.tsx which might also manage panes/scales.
     // Only run if panesNeedUpdate is true.
     if (panesNeedUpdate) {
         // Use setTimeout to defer execution slightly, allowing chart internals to potentially settle
         // and avoiding potential conflicts if Chart.tsx is also modifying panes/scales in the same render cycle.
         setTimeout(() => {
             const currentChart = chartRef.current; // Re-check chart validity inside timeout
             if (!currentChart) return;

             try {
                 const currentPanes = currentChart.panes();
                 const targetHeights = paneHeightConfigRef.current;
                 const targetCount = targetHeights.length;
                 const currentCount = currentPanes.length;
                 // console.log(`[Chart ${chartId} Deferred] Adjusting panes. Current: ${currentCount}, Target: ${targetCount}, Heights:`, targetHeights);


                 // Remove excess panes (from bottom up)
                 if (currentCount > targetCount) {
                     for (let i = currentCount - 1; i >= targetCount; i--) {
                         try {
                             // console.log(`[Chart ${chartId} Deferred] Removing excess pane ${i}`);
                             currentChart.removePane(i);
                         } catch (e) {
                             console.error(`[Chart ${chartId} Deferred] Error removing excess pane ${i}:`, e);
                         }
                     }
                 }
                 // Note: Adding panes is implicitly handled by chart.addSeries(..., paneIndex)
                 // If a series is added to a non-existent pane index (e.g., 1 when only 0 exists),
                 // lightweight-charts should create the intermediate pane(s).

                 // Apply target heights AFTER potential removals/additions
                 const finalPanes = currentChart.panes(); // Get panes again
                 if (finalPanes.length !== targetCount) {
                     console.warn(`[Chart ${chartId} Deferred] Pane count mismatch after add/remove. Expected ${targetCount}, Got ${finalPanes.length}. Applying heights to available panes.`);
                 }

                 targetHeights.forEach((height, index) => {
                     if (finalPanes[index]) {
                         try {
                              // Avoid setting height if it's already correct (reduces flicker)
                             // This requires reading the current height, which might not be reliable immediately
                             // const currentHeight = finalPanes[index].height(); // Check if height() method exists
                             // if (Math.abs(currentHeight - height) > 1e-4) {
                                finalPanes[index].setHeight(height);
                             // }
                         } catch (e) {
                             console.error(`[Chart ${chartId} Deferred] Error setting height for pane ${index}:`, e);
                         }
                     } else {
                         console.warn(`[Chart ${chartId} Deferred] Tried to set height for non-existent pane index ${index}`);
                     }
                 });

                 // Update pane references after potential changes
                 updatePanesRef(currentChart);

                 // --- Configure Indicator Pane Price Scales ---
                 // Skip main pane (index 0), configure scales for index 1+
                 for (let i = 1; i < finalPanes.length; i++) {
                     const pane = finalPanes[i];
                     if (!pane) continue;

                     try {
                         // Get the right price scale for this specific pane
                         const rightPriceScale = pane.priceScale('right');
                         if (rightPriceScale) {
                             // Apply desired options for indicator scales
                             rightPriceScale.applyOptions({
                                 visible: true, // Ensure visible
                                 autoScale: true, // Usually desired for indicators
                                 scaleMargins: { top: 0.15, bottom: 0.15 }, // Generous margins for indicator panes
                                 // Potentially disable price formatter if indicators have own formatting needs?
                                 // Or inherit global formatter? Needs decision.
                             });
                         }
                     } catch (e) {
                         console.error(`[Chart ${chartId} Deferred] Error configuring right price scale for pane ${i}:`, e);
                     }
                 }

                 // --- Optional: Fit Content (Use with Caution) ---
                 // Fit content can reset zoom/scroll, which might be undesirable.
                 // Avoid if Chart.tsx handles zoom/scroll state.
                 // try {
                 //     // currentChart.timeScale().fitContent();
                 // } catch (e) {
                 //     console.error(`[Chart ${chartId} Deferred] Error fitting time scale content:`, e);
                 // }

             } catch (e) {
                 console.error(`[Chart ${chartId} Deferred] Error during pane height/scale configuration:`, e);
             }
         }, 0); // Delay slightly
     } // End if(panesNeedUpdate)

  }, [
      chartRef,
      chartId,
      indicators, // Combined list drives the updates
      klineData, // Base data for calculations
      paneOptions, // Pane styling options
      paneInstanceMap, // Mapping changes if indicators change
      createIndicatorSeries, // Callback dep
      updatePanesRef, // Callback dep
      symbol,
      interval,
    ]
  );

  // Return minimal state/actions needed by the Chart component, if any.
  // Currently, this hook primarily synchronizes store state to the chart.
  // We might need to return pane control functions if Chart.tsx doesn't handle it.
  // return {
  //   panes: panesRef.current, // Expose current panes? Use cautiously
  // };
}; 

// Helper function to convert hex/rgb to rgba with specific alpha
function hexOrRgbToRgba(color: string, alpha: number): string {
    alpha = Math.max(0, Math.min(1, alpha)); // Clamp alpha 0-1

    // Handle HEX (#RRGGBB or #RGB)
    let m = color.match(/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
    if (m) {
        return `rgba(${parseInt(m[1], 16)}, ${parseInt(m[2], 16)}, ${parseInt(m[3], 16)}, ${alpha})`;
    }
    m = color.match(/^#?([a-f\d])([a-f\d])([a-f\d])$/i);
    if (m) {
        return `rgba(${parseInt(m[1] + m[1], 16)}, ${parseInt(m[2] + m[2], 16)}, ${parseInt(m[3] + m[3], 16)}, ${alpha})`;
    }

    // Handle rgb(r, g, b)
    m = color.match(/^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/i);
    if (m) {
        return `rgba(${m[1]}, ${m[2]}, ${m[3]}, ${alpha})`;
    }

    // Handle rgba(r, g, b, a) - just replace alpha
    m = color.match(/^(rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*)[0-9\.]+(\s*\))$/i);
    if (m) {
        return `${m[1]}${alpha}${m[2]}`;
    }

    // Return original color if format is unknown
    console.warn(`[hexOrRgbToRgba] Could not parse color format: ${color}. Returning original.`);
    return color;
} 
--- END FILE: src\features\Chart\useChartIndicators.ts ---

--- START FILE: src\features\ControlPanel\ControlPanel.tsx ---
import React, { useState, useEffect, useCallback, useMemo, memo, forwardRef, ForwardedRef } from 'react';
import { LayoutType, WSConnectionStatus, MarketType, ChartType } from '@/shared/index';
import LayoutSelector from '../GridSelector/Grid';
import StatusBar, { StatusBarProps } from '../StatusBar/StatusBar';
import { Icon, type IconName } from '@/shared/ui/icons/all_Icon';
// Correct the import name
import { IndicatorsPanelTrigger } from '../Indicators/Indicators';
import SettingsButton from '../SettingButton/Setting';
import TickerSearch from '../TickerSearch/TickerSearch';
import { Button } from '@/shared/ui/button';
import { TickerTable } from '../TickerManagement/Table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/shared/ui/tooltip";
import { ModeControl } from '../ModeControl';

// --- Interfaces ---
export interface ControlPanelProps {
  className?: string;
  selectedSymbol: string | null;
  loading?: boolean;
  error?: string | null;
  lastUpdate?: number | null;
  onRefresh?: () => void;
  isWsConnected?: boolean;
  connectionDetails?: {
    name: string;
    status: WSConnectionStatus;
    details?: string;
  }[];
  totalTickers?: number;
  filteredTickers?: number;
  isTableCollapsed?: boolean;
  onToggleTableCollapse?: () => void;
  isFiltering?: boolean;
  activeChartId?: string;
  layoutType: LayoutType;
  selectedMarketTypes: MarketType[];
  aggregateVolumeAndTrades?: boolean;
  setLayoutType: (layout: LayoutType) => void;
  dragConstraintsRef: React.RefObject<HTMLDivElement | null>;
  onSearchChange: (query: string) => void;
}

// --- Component ---
const ControlPanelComponentInternal = forwardRef<HTMLDivElement, ControlPanelProps>(({
    className,
    selectedSymbol,
    loading,
    error,
    lastUpdate,
    onRefresh,
    isWsConnected,
    connectionDetails,
    totalTickers,
    filteredTickers,
    isTableCollapsed,
    onToggleTableCollapse,
    isFiltering,
    activeChartId = 'chart-0',
    layoutType,
    selectedMarketTypes,
    aggregateVolumeAndTrades,
    setLayoutType,
    dragConstraintsRef,
    onSearchChange,
}, ref: ForwardedRef<HTMLDivElement>) => {
  // Remove local state for chart type toggle
  // const [localChartType, setLocalChartType] = useState<ChartType>(ChartType.Candles);

  // Determine StatusBar props (remains the same)
  const statusBarProps: StatusBarProps = {
      loading,
      error,
      lastUpdate,
      onRefresh,
      connectionDetails: connectionDetails as any,
      totalTickers,
      filteredTickers,
  };

  // Determine current market type display (remains the same)
  const currentMarketType = useMemo(() => {
    const types = selectedMarketTypes ?? [];
    if (types.length === 0) return 'N/A';
    if (types.length === 1) return types[0];
    return 'ALL';
  }, [selectedMarketTypes]);

  return (
    <div ref={ref} className={`relative flex items-center justify-between border-b border-l border-r border-border rounded-bl-lg rounded-br-lg h-9 flex-shrink-0 px-2 w-full ${className || ''}`}>
      {/* Left Section */}
      <div className="flex items-center gap-1">
         {/* Collapse Button */}
         {onToggleTableCollapse && (
           <div className="collapse-button-container" title={isTableCollapsed ? "Развернуть таблицу" : "Свернуть таблицу"}>
             <Button
               variant="ghost"
               size="icon"
               className="absolute -top-4 right-4 h-7 w-7 rounded-full border"
               onClick={onToggleTableCollapse}
             >
               <Icon
                 name={isTableCollapsed ? "PanelBottomClose" : "PanelTopClose" }
                 className="rotate-90"
               />
             </Button>
           </div>
         )}

         {/* Settings Button */}
         <SettingsButton dragConstraintsRef={dragConstraintsRef} />

         {/* Ticker Search Component */}
         <div className="search-container w-[180px] h-[28px]">
           <TickerSearch
             selectedSymbol={selectedSymbol}
             isFiltering={isFiltering}
             className="h-full"
             onSearchChange={onSearchChange}
           />
         </div>

         {/* Layout Selector */}
         <div className="grid-selector-wrapper ml-1 flex items-center h-full min-w-[32px] relative z-10">
           <LayoutSelector
              currentLayout={layoutType}
              onLayoutChange={setLayoutType}
           />
         </div>

         {/* Indicators Button - Correct the component usage */}
         <div className="indicator-button-wrapper ml-1 flex items-center h-full">
           <IndicatorsPanelTrigger chartId={activeChartId} />
         </div>
      </div>

      {/* Center Section - Mode Control */}
      <div className="flex items-center h-full">
        <TooltipProvider>
          <ModeControl className="h-[28px]" />
        </TooltipProvider>
      </div>

      {/* Right Section - Status Bar */}
      <StatusBar {...statusBarProps} />
    </div>
  );
});

ControlPanelComponentInternal.displayName = 'ControlPanelComponentInternal';

// --- Export memoized component ---
const ControlPanel = memo(ControlPanelComponentInternal);
export default ControlPanel;

--- END FILE: src\features\ControlPanel\ControlPanel.tsx ---

--- START FILE: src\features\GridSelector\Grid.tsx ---
import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
// Удаляем импорт Framer Motion
// import { motion, AnimatePresence } from 'framer-motion';
import { LayoutType } from '@/shared/index'; // Импортируем тип из централизованной папки типов
// import { ReactComponent as GridIcon } from './GridIcon.svg';
import { Icon } from '@/shared/ui/icons/all_Icon'; // NEW IMPORT
// import './Grid.scss'; // Удаляем импорт SCSS
import { Popover, PopoverContent, PopoverTrigger } from "@/shared/ui/popover"
import { Button } from "@/shared/ui/button"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/shared/ui/tooltip"
import { 
  useAppSettingsStore, 
  selectLayoutType, 
  selectViewMode,
  selectLayoutTypeForCurrentMode,
  selectSetLayoutTypeForMode 
} from '@/shared/store/settingsStore';
import { DropdownMenuTrigger } from "@/shared/ui/dropdown-menu"

// Определяем типы для пропсов
interface LayoutSelectorProps {
  currentLayout: LayoutType;
  onLayoutChange: (layout: LayoutType) => void;
}

/**
 * Компонент для выбора типа компоновки графиков.
 */
const LayoutSelector: React.FC<LayoutSelectorProps> = ({ currentLayout, onLayoutChange }) => {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [hoveredCell, setHoveredCell] = useState<{ row: number, col: number } | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [popupPosition, setPopupPosition] = useState<{ top: number; left: number }>({ top: 0, left: 0 });
  const buttonRef = useRef<HTMLButtonElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);

  // Get current mode from store
  const currentMode = useAppSettingsStore(selectViewMode);
  const currentModeLayout = useAppSettingsStore(selectLayoutTypeForCurrentMode);
  const setLayoutTypeForMode = useAppSettingsStore(selectSetLayoutTypeForMode);

  // Client-side effect
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  // Парсим текущий макет для текущего режима для определения выбранных строк и столбцов
  const [currentRows, currentCols] = (currentModeLayout && typeof currentModeLayout === 'string') 
    ? currentModeLayout.split('x').map(Number)
    : [1, 1]; // Дефолтные значения

  // Функция для открытия всплывающего окна
  const openPopup = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setPopupPosition({
        top: rect.bottom + 4,
        left: rect.left
      });
    }
    setIsPopupOpen(true);
  };

  // Функция для закрытия всплывающего окна
  const closePopup = () => {
    setIsPopupOpen(false);
    setHoveredCell(null);
  };

  // Обработчик клика вне компонента и ESC для закрытия попапа (обновлен для Portal)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isPopupOpen &&
        popupRef.current && 
        !popupRef.current.contains(event.target as Node) &&
        buttonRef.current && 
        !buttonRef.current.contains(event.target as Node)
      ) {
        closePopup();
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isPopupOpen) {
        closePopup();
      }
    };

    if (isPopupOpen && isClient) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isPopupOpen, isClient]);

  // Функция для выбора размеров сетки
  const handleCellSelect = (row: number, col: number) => {
    // Проверяем, что выбранные размеры допустимы
    if (row >= 1 && row <= 7 && col >= 1 && col <= 7) {
      const newLayout = `${row}x${col}` as LayoutType;
      
      // Сохраняем выбор для текущего режима
      setLayoutTypeForMode(currentMode, newLayout);
      
      // Также вызываем переданный обработчик для обратной совместимости
      onLayoutChange(newLayout);
      closePopup();
    }
  };

  // Функция обработки наведения на ячейку
  const handleCellHover = (row: number, col: number) => {
    setHoveredCell({ row, col });
  };

  // Функция для определения, должна ли ячейка быть активной
  const isCellActive = (row: number, col: number) => {
    if (hoveredCell) {
      return row <= hoveredCell.row && col <= hoveredCell.col;
    }
    return row <= currentRows && col <= currentCols;
  };

  // Позиционирование попапа через Portal для правильного z-index

  return (
    // layoutSelector -> relative flex items-center h-full z-50
    <div className="relative flex items-center h-full z-50">
      <Button 
        ref={buttonRef}
        variant="ghost"
        size="icon"
        className="group"
        onClick={() => isPopupOpen ? closePopup() : openPopup()}
        title="Выбрать сетку"
        data-state={isPopupOpen ? 'open' : 'closed'}
        aria-label="Select grid layout"
      >
        <Icon 
          name="GridLayout" 
          className="text-muted-foreground group-hover:text-foreground"
        />
      </Button>
      
      {/* Render popup using Portal */}
      {isClient && isPopupOpen && typeof document !== 'undefined' && createPortal(
        <div 
          ref={popupRef} 
          className="fixed z-[100] w-[280px] bg-gradient-to-b from-card to-background border border-border rounded-md shadow-lg overflow-hidden p-3"
          style={{ 
            top: popupPosition.top,
            left: popupPosition.left,
            animation: 'popupFadeIn 0.2s ease-out' 
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* popupHeader -> Увеличиваем нижний отступ */}
          <div className="flex items-center justify-between mb-3">
            {/* popupTitle -> Увеличиваем шрифт */}
            <span className="text-sm font-medium text-muted-foreground">
              Grid {currentMode === 'focus' ? 'Focus' : 'Screener'}
            </span>
            {/* gridSizeInfo -> Увеличиваем шрифт */}
            <div className="text-base font-semibold text-primary px-1.5 py-0.5 rounded-sm bg-primary/10">
              {hoveredCell 
                ? `${hoveredCell.row} × ${hoveredCell.col} = ${hoveredCell.row * hoveredCell.col}` 
                : `${currentRows} × ${currentCols} = ${currentRows * currentCols}`}
            </div>
          </div>
          
          {/* interactiveGrid -> Смягчаем фон */}
          <div className="flex flex-col w-full bg-muted/30 rounded-sm overflow-hidden border border-border/10">
            {/* Верхняя нумерация столбцов */}
            {/* columnNumbers -> Увеличиваем высоту и отступ */}
            <div className="flex h-[24px] ml-[24px] pr-[3px]"> {/* Увеличены h, ml, pr */} 
              {Array.from({ length: 7 }, (_, i) => (
                // number -> Увеличиваем шрифт
                <div key={`col-${i}`} className="flex-1 flex items-center justify-center text-xs font-medium text-muted-foreground select-none">{i + 1}</div>
              ))}
            </div>
            
            {/* gridContent -> */}
            <div className="flex flex-1">
              {/* Левая нумерация строк */}
              {/* rowNumbers -> Увеличиваем ширину и отступ */}
              <div className="flex flex-col w-[24px] pt-[3px]"> {/* Увеличены w, pt */} 
                {Array.from({ length: 7 }, (_, i) => (
                  // number -> Увеличиваем высоту и шрифт (h = cell height + gap)
                  <div key={`row-${i}`} className="h-[31px] flex items-center justify-center text-xs font-medium text-muted-foreground select-none">{i + 1}</div>
                ))}
              </div>
              
              {/* Сетка ячеек */}
              {/* cellsGrid -> Увеличиваем gap и padding */}
              <div className="flex-1 flex flex-col gap-[3px] p-[3px]">
                {Array.from({ length: 7 }, (_, rowIndex) => (
                  // gridRow -> Увеличиваем высоту и gap
                  <div key={`row-${rowIndex}`} className="flex h-[28px] gap-[3px]">
                    {Array.from({ length: 7 }, (_, colIndex) => {
                      const row = rowIndex + 1;
                      const col = colIndex + 1;
                      const isActive = isCellActive(row, col);
                      
                      return (
                        // cell -> Убираем scale, оставляем только закрашивание
                        <div 
                          key={`cell-${row}-${col}`} 
                          className={[
                            "flex-1 rounded-sm transition-colors duration-150 ease-in-out cursor-pointer",
                            "bg-background hover:bg-accent/60", 
                            isActive && "bg-primary" // Убрали scale, оставили только цвет
                          ].filter(Boolean).join(' ')}
                          onMouseEnter={() => handleCellHover(row, col)}
                          onClick={() => handleCellSelect(row, col)}
                          title={`${row} × ${col}`}
                        />
                      );
                    })}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default LayoutSelector; 
--- END FILE: src\features\GridSelector\Grid.tsx ---

--- START FILE: src\features\Indicators\Indicators.tsx ---
import React, { useState, useMemo, useEffect, useCallback, memo, useRef } from 'react';
import { createPortal } from 'react-dom'; // Import createPortal
import { useIndicatorStore } from '@/shared/store/indicatorStore';
import { getAvailableIndicators, findIndicatorDefinitionById } from './registry';
import type {
  IndicatorInstance,
  IndicatorDefinition,
  IndicatorParamValue,
  IndicatorStyle,
  IndicatorParams
} from '@/shared/index';
import { Icon } from '@/shared/ui/icons/all_Icon';
import { LineStyle } from 'lightweight-charts';
import { useAppSettingsStore, selectAddIndicatorColumn, selectRemoveIndicatorColumn } from '@/shared/store/settingsStore';
import { ScrollArea } from '@/shared/ui/scroll-area';
import { Button } from '@/shared/ui/button';
import { IndicatorColumnConfig } from '@/shared/index';
import { cn } from '@/shared/lib/utils';

type TimeFrame = string; // Определяем тип TimeFrame как string

// Локальная функция для получения параметров индикатора
const getLocalDefaultIndicatorParams = (indicator: IndicatorDefinition): { params: Record<string, IndicatorParamValue>, styleOverrides: Record<string, Partial<IndicatorStyle>>, tableOutputIds: string[], tableTimeframe: TimeFrame } => {
    const params = indicator.params.reduce((acc, paramDef) => {
        acc[paramDef.id as string] = paramDef.defaultValue;
        return acc;
    }, {} as Record<string, IndicatorParamValue>);

    const styleOverrides: Record<string, Partial<IndicatorStyle>> = {};
    if (indicator.defaultStyle) {
        Object.entries(indicator.defaultStyle).forEach(([outputId, style]) => {
            styleOverrides[outputId] = { ...style };
        });
    }
    indicator.outputs.forEach(output => {
        if (!styleOverrides[output.id]) {
            styleOverrides[output.id] = {
                color: output.color,
                lineWidth: output.lineWidth,
                lineStyle: output.lineStyle,
                opacity: output.opacity,
            };
        }
    });

    // Default table settings (e.g., show first output, default timeframe)
    const tableOutputIds = indicator.outputs.length > 0 ? [indicator.outputs[0].id] : [];
    const tableTimeframe: TimeFrame = '1h'; // Or some other sensible default

    return { params, styleOverrides, tableOutputIds, tableTimeframe };
};

// Mapping LineStyle enum to display names remains the same
const lineStyleOptions = [
    { value: String(LineStyle.Solid), label: 'Line' },
    { value: String(LineStyle.Dotted), label: 'Dotted' },
    { value: String(LineStyle.Dashed), label: 'Dashed' },
    { value: String(LineStyle.LargeDashed), label: 'Large Dashed' },
    { value: String(LineStyle.SparseDotted), label: 'Sparse Dotted' },
];


// --- Types ---
type IndicatorStatus = 'not-added' | 'global' | 'local';

// Define the update type locally as it's only used here
type IndicatorInstanceUpdate = Partial<Pick<IndicatorInstance, 'params' | 'styleOverrides' | 'visible' | 'tableOutputIds' | 'tableTimeframe'>>;

// --- Indicator Settings Dialog Component (Remains mostly the same) ---
interface IndicatorSettingsDialogProps {
    indicatorInstance: IndicatorInstance;
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
}

const DIALOG_DEBOUNCE_MS = 300;

const IndicatorSettingsDialog: React.FC<IndicatorSettingsDialogProps> = ({
    indicatorInstance,
    open,
    onOpenChange,
}) => {
    if (!open) return null;

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50" 
             onClick={() => onOpenChange?.(false)}>
            <div className="bg-background p-6 rounded-lg shadow-lg w-[400px] max-h-[80vh] overflow-auto"
                 onClick={(e) => e.stopPropagation()}>
                <div className="flex justify-between mb-4">
                    <h2 className="text-lg font-semibold">Indicator Settings</h2>
                    <button onClick={() => onOpenChange?.(false)} className="text-muted-foreground hover:text-foreground">
                        ✕
                    </button>
                </div>
                <div className="mb-4">
                    <p className="mb-2">Indicator: {indicatorInstance.indicatorId}</p>
                    <p className="mb-2">Instance ID: {indicatorInstance.instanceId}</p>
                </div>
                <div className="text-center mt-4">
                    <button 
                        onClick={() => onOpenChange?.(false)}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90">
                        Close
                    </button>
                </div>
            </div>
        </div>
    );
};

// --- Combined Indicator Item Component (Remains mostly the same) ---
interface CombinedIndicatorItemProps {
    definition: IndicatorDefinition<IndicatorParams>;
    status: IndicatorStatus;
    instance?: IndicatorInstance;
    chartId: string;
    onOpenSettings: (instance: IndicatorInstance) => void;
}

const CombinedIndicatorItem: React.FC<CombinedIndicatorItemProps> = memo(({
    definition,
    status,
    instance,
    chartId,
    onOpenSettings
}) => {
    const removeIndicator = useIndicatorStore(state => state.removeIndicator);
    const makeIndicatorGlobal = useIndicatorStore(state => state.makeIndicatorGlobal);
    const makeIndicatorLocal = useIndicatorStore(state => state.makeIndicatorLocal);
    // Get actions from appSettingsStore
    const addIndicatorColumn = useAppSettingsStore(selectAddIndicatorColumn);
    const removeIndicatorColumn = useAppSettingsStore(selectRemoveIndicatorColumn);

    const displayName = definition.name;

    // Handler to add indicator globally (visible on charts)
    const handleAddGlobal = useCallback((e: React.MouseEvent) => {
        e.stopPropagation();
        // Используем функцию из indicatorStore через getState() для добавления индикатора
        useIndicatorStore.getState().addIndicator(chartId, definition, {}, true);
    }, [chartId, definition]);

    // RE-ADD Handler to add indicator ONLY as a table column
    const handleAddTableColumn = useCallback((e: React.MouseEvent) => {
        e.stopPropagation();
        // 1. Add instance to indicatorStore manually to set visible: false and get consistent ID
        const defaults = getLocalDefaultIndicatorParams(definition);
        const newInstanceId = crypto.randomUUID();

        // Construct the new indicator instance data
        const newIndicatorInstance: Omit<IndicatorInstance, 'instanceId'> = {
            chartId: 'global', // Store instance globally for table access
            indicatorId: definition.id,
            name: definition.name, // Added name property
            params: defaults.params,
            visible: false, // *** KEY: Not visible on chart by default ***
            styleOverrides: defaults.styleOverrides,
            tableOutputIds: defaults.tableOutputIds,
            tableTimeframe: defaults.tableTimeframe,
        };
        const indicatorToAdd: IndicatorInstance = {
            instanceId: newInstanceId,
            ...newIndicatorInstance,
        };

        // Manually add the instance to the global store state
        useIndicatorStore.setState(state => {
            // Prevent duplicates just in case
            if (!state.globalIndicators.some((ind) => ind.instanceId === newInstanceId)) {
                state.globalIndicators.push(indicatorToAdd);
                console.log('[handleAddTableColumn] Added instance to indicatorStore (visible=false):', indicatorToAdd);
            }
        });

        // 2. Add column configuration to appSettingsStore using the generated ID
        const indicatorColumnConfigToAdd: IndicatorColumnConfig = {
            instanceId: indicatorToAdd.instanceId,
            indicatorId: definition.id,
            name: definition.name, // Use definition.name for the column name, required by IndicatorColumnConfig
            timeframe: defaults.tableTimeframe,
            parameters: Object.entries(defaults.params).reduce((acc, [key, value]) => {
                // Предотвращаем null-значения, заменяя их на пустую строку
                acc[key] = value === null ? '' : value;
                return acc;
            }, {} as Record<string, string | number | boolean>),
            // Use the first outputId for the table column, required by IndicatorColumnConfig
            outputId: defaults.tableOutputIds?.[0] || '', // Assuming the first output is used for the table column
        };
        console.log('[handleAddTableColumn] Adding column config to appSettingsStore:', indicatorColumnConfigToAdd);
        addIndicatorColumn(indicatorColumnConfigToAdd);

    }, [definition, addIndicatorColumn]);

    const handleRemove = useCallback((e: React.MouseEvent) => {
        e.stopPropagation();
        if (instance) {
            removeIndicator(instance.instanceId, status === 'global' ? undefined : chartId);
            removeIndicatorColumn(instance.instanceId);
        }
    }, [instance, status, chartId, removeIndicator, removeIndicatorColumn]);

    const handleMakeGlobal = useCallback((e: React.MouseEvent) => {
        e.stopPropagation();
        if (instance && status === 'local') {
            makeIndicatorGlobal(instance.instanceId, chartId);
        }
    }, [makeIndicatorGlobal, instance, chartId, status]);

    const handleMakeLocal = useCallback((e: React.MouseEvent) => {
        e.stopPropagation();
        if (instance && status === 'global') {
            makeIndicatorLocal(instance.instanceId, chartId);
        }
    }, [makeIndicatorLocal, instance, chartId, status]);

    const handleSettingsClick = useCallback((e: React.MouseEvent) => {
        e.stopPropagation();
        if (instance) {
            onOpenSettings(instance);
        }
    }, [instance, onOpenSettings]);

    const handleItemClick = useCallback((e: React.MouseEvent) => {
         e.stopPropagation();
    }, []);

    return (
        <div
            className={[
                "flex items-center justify-between p-1.5 rounded-md hover:bg-muted cursor-default"
            ].join(' ')}
            onClick={handleItemClick}
        >
            <div className="flex-1 overflow-hidden pr-2">
                <span
                    className={[
                        "text-xs font-medium truncate block",
                        status !== 'not-added' ? "text-foreground" : "text-muted-foreground"
                    ].join(' ')}
                    title={displayName}
                >
                    {displayName}
                </span>
            </div>
            <div className="flex items-center space-x-0.5 flex-shrink-0">
                {status === 'not-added' ? (
                    <>
                        {/* RE-ADD Button to Add ONLY to Table */}
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 text-muted-foreground hover:text-blue-500" // Adjusted hover color
                            title={`Add ${definition.name} as Table Column`}
                            onClick={handleAddTableColumn}
                            aria-label={`Add ${definition.name} as table column`}
                        >
                            {/* Use TablePlus icon */}
                            <Icon name="TablePlus" />
                        </Button>
                        {/* Button to Add Globally (to chart) */}
                         <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 text-primary hover:text-primary"
                            title={`Add ${definition.name} to Chart (Global)`}
                            onClick={handleAddGlobal}
                            aria-label={`Add ${definition.name} to chart`}
                         >
                             <Icon name="PlusCircle" />
                         </Button>
                    </>
                ) : (
                    <>
                        {/* Settings Button */}
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7"
                            onClick={handleSettingsClick}
                            title="Indicator Settings"
                            disabled={!instance}
                            aria-label={`Settings for ${definition.name}`}
                        >
                            <Icon name="Settings" />
                        </Button>

                         {/* Global/Local Toggle Button */}
                        {status === 'global' ? (
                             <Button
                                 variant="ghost"
                                 size="icon"
                                 className="h-7 w-7"
                                 onClick={handleMakeLocal}
                                 title="Make Local"
                                 disabled={!instance}
                                 aria-label={`Make ${definition.name} local`}
                             >
                                 <Icon name="Circle" />
                             </Button>
                         ) : (
                             <Button
                                 variant="ghost"
                                 size="icon"
                                 className="h-7 w-7 text-primary hover:text-primary"
                                 onClick={handleMakeGlobal}
                                 title="Make Global"
                                 disabled={!instance}
                                 aria-label={`Make ${definition.name} global`}
                             >
                                 <Icon name="Globe" />
                             </Button>
                         )}

                        {/* Remove Button */}
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 text-destructive hover:text-destructive"
                            onClick={handleRemove}
                            title="Remove Indicator"
                            disabled={!instance}
                            aria-label={`Remove ${definition.name}`}
                        >
                            <Icon name="Trash" />
                        </Button>
                    </>
                )}
            </div>
        </div>
    );
});
CombinedIndicatorItem.displayName = 'CombinedIndicatorItem';

// --- Indicators List Component (Remains mostly the same) ---
interface IndicatorsListProps {
    chartId: string;
    onOpenSettings: (instance: IndicatorInstance) => void;
}

// Explicit type for combined indicator item
type CombinedIndicatorEntry = {
  definition: IndicatorDefinition;
  status: IndicatorStatus;
  instance?: IndicatorInstance;
};

const selectGlobalIndicators = (state: ReturnType<typeof useIndicatorStore.getState>) => state.globalIndicators;
const selectChartIndicators = (chartId: string) => (state: ReturnType<typeof useIndicatorStore.getState>) => state.indicatorsByChartId[chartId];

const IndicatorsList: React.FC<IndicatorsListProps> = ({ chartId, onOpenSettings }) => {
    const availableDefinitions: IndicatorDefinition[] = useMemo(() => getAvailableIndicators(), []); // Correctly use getAvailableIndicators()
    const globalInstancesData = useIndicatorStore(selectGlobalIndicators);
    const chartInstancesData = useIndicatorStore(useMemo(() => selectChartIndicators(chartId), [chartId]));

    const globalInstanceMap = useMemo(() => {
        const instances = globalInstancesData ?? [];
        const map = new Map<string, IndicatorInstance>();
        instances.forEach((inst: IndicatorInstance) => map.set(inst.indicatorId, inst));
        return map;
    }, [globalInstancesData]);

    const chartInstanceMap = useMemo(() => {
        const instances = chartInstancesData ?? [];
        const map = new Map<string, IndicatorInstance>();
        instances.forEach((inst: IndicatorInstance) => map.set(inst.indicatorId, inst));
        return map;
    }, [chartInstancesData]);

    const combinedIndicators: CombinedIndicatorEntry[] = useMemo(() => {
        return availableDefinitions.map((definition: IndicatorDefinition): CombinedIndicatorEntry => { // Ensure definition is typed
            const globalInstance = globalInstanceMap.get(definition.id);
            const chartInstance = chartInstanceMap.get(definition.id);

            let status: IndicatorStatus = 'not-added';
            let instance: IndicatorInstance | undefined = undefined;

            if (globalInstance) {
                status = 'global';
                instance = globalInstance;
            } else if (chartInstance) {
                status = 'local';
                instance = chartInstance;
            }

            return { definition, status, instance };
        }).sort((a: CombinedIndicatorEntry, b: CombinedIndicatorEntry) => a.definition.name.localeCompare(b.definition.name)); // Ensure a and b are typed
    }, [availableDefinitions, globalInstanceMap, chartInstanceMap]);

    if (availableDefinitions.length === 0) {
        return (
            <div className="p-4 text-center text-xs text-muted-foreground italic">
                No indicators available.
            </div>
        );
    }

    return (
        <div className="space-y-0.5">
            {combinedIndicators.map(({ definition, status, instance }: CombinedIndicatorEntry) => ( // Ensure destructuring is typed
                <CombinedIndicatorItem
                    key={definition.id}
                    definition={definition}
                    status={status}
                    instance={instance}
                    chartId={chartId}
                    onOpenSettings={onOpenSettings}
                />
            ))}
        </div>
    );
};

// --- NEW: Indicators Panel Component (Similar to Settings Panel) ---
interface IndicatorsPanelProps {
    chartId: string;
    isOpen: boolean;
    setIsOpen: (isOpen: boolean) => void;
    onOpenSettings: (instance: IndicatorInstance) => void;
    initialPosition: { top: number | string; left: number | string };
}

const panelVariants = {};

const IndicatorsPanel: React.FC<IndicatorsPanelProps> = ({
    chartId,
    isOpen,
    setIsOpen,
    onOpenSettings,
    initialPosition,
}) => {
    const panelRef = useRef<HTMLDivElement>(null);

    // Close panel if clicking outside
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (panelRef.current && !panelRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };
        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, setIsOpen]);

    return (
        <div
            ref={panelRef}
            className="w-[350px] h-[450px] bg-[rgba(35,38,46,0.9)] backdrop-blur-md backdrop-saturate-160 border border-border/10 rounded-lg shadow-lg overflow-hidden flex flex-col text-sm text-foreground z-40 cursor-default"
            style={{
                position: 'fixed',
                top: initialPosition.top,
                left: initialPosition.left,
            }}
            onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside
        >
            {/* Panel Header */}
            <div className="flex items-center justify-between h-8 px-2 bg-muted/80 border-b border-border/10 flex-shrink-0">
                <div
                    className="flex items-center flex-grow h-full gap-1.5"
                >
                    <Icon name="Indicators" className="group relative h-5 w-5 shrink-0 pointer-events-none" />
                    <h3 className="m-0 text-[13px] font-semibold text-foreground select-none leading-none pointer-events-none">Indicators</h3>
                </div>
                <div className="flex items-center gap-1 h-full" onPointerDown={(e) => e.stopPropagation()}>
                    <button
                        className="bg-transparent border-none text-muted-foreground leading-none cursor-pointer p-0 transition-all duration-150 ease-in-out rounded-full flex items-center justify-center w-5 h-5 flex-shrink-0 hover:text-foreground hover:scale-105 hover:bg-accent/10 text-lg font-light"
                        onClick={() => setIsOpen(false)}
                        title="Close"
                    >
                        ✕
                    </button>
                </div>
            </div>
            {/* Panel Content */}
            <ScrollArea className="flex-grow">
                <div className="p-1.5"> {/* Add padding around the list */}
                    <IndicatorsList
                        chartId={chartId}
                        onOpenSettings={onOpenSettings}
                    />
                </div>
            </ScrollArea>
        </div>
    );
};

// --- Main Indicators Trigger Component (Exported) ---
// Renamed from IndicatorsButton to IndicatorsPanelTrigger
interface IndicatorsPanelTriggerProps {
    chartId: string;
}

export const IndicatorsPanelTrigger: React.FC<IndicatorsPanelTriggerProps> = ({ chartId }) => {
    const [isOpen, setIsOpen] = useState(false);
    const [editingIndicatorInstance, setEditingIndicatorInstance] = useState<IndicatorInstance | null>(null);
    const buttonRef = useRef<HTMLButtonElement>(null); // Ref for the trigger button
    const [initialPosition, setInitialPosition] = useState<{ top: number | string; left: number | string }>({ top: '50px', left: '50px' }); // Default position
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
        setIsClient(true);
    }, []);

    const handleOpenSettings = useCallback((instance: IndicatorInstance) => {
        setEditingIndicatorInstance(instance);
        setIsOpen(false); 
    }, []);

    const handleSettingsDialogChange = useCallback((open: boolean) => {
        if (!open) {
            setEditingIndicatorInstance(null);
        }
    }, []);

    useEffect(() => {
        if (isOpen && buttonRef.current) {
          requestAnimationFrame(() => {
            if (buttonRef.current) { 
              const rect = buttonRef.current.getBoundingClientRect();
              setInitialPosition({
                top: rect.bottom + 4,
                left: rect.left
              });
            }
          });
        }
      }, [isOpen]);


    return (
        <> 
            {/* Trigger Button - Replaced with shadcn Button */}
            <Button
                ref={buttonRef}
                variant="ghost"
                size="icon"
                className={cn( "group", // Keep group for icon styling
                    isOpen && "bg-primary/15 rotate-180" // Apply rotation when open
                )}
                onClick={() => setIsOpen(!isOpen)}
                title="Indicators"
                data-state={isOpen ? 'open' : 'closed'}
                aria-label="Open indicators panel"
            >
                {/* Icon inherits text color from Button, hover from group-hover */}
                <Icon 
                    name="Indicators" 
                    className={cn( "text-muted-foreground", "group-hover:text-foreground" )} 
                />
            </Button>

            {/* Render Panel using Portal only on client */}
            {isClient && isOpen && typeof document !== 'undefined' && createPortal(
                <IndicatorsPanel
                    chartId={chartId}
                    isOpen={isOpen}
                    setIsOpen={setIsOpen}
                    onOpenSettings={handleOpenSettings}
                    initialPosition={initialPosition}
                />,
                document.body
            )}

             {/* Settings Dialog remains the same */}
             {isClient && editingIndicatorInstance && (
                 <IndicatorSettingsDialog
                     indicatorInstance={editingIndicatorInstance}
                     open={!!editingIndicatorInstance}
                     onOpenChange={handleSettingsDialogChange}
                 />
             )}
        </>
    );
};


--- END FILE: src\features\Indicators\Indicators.tsx ---

--- START FILE: src\features\Indicators\registry.ts ---
// src/indicators/registry.ts
import type { IndicatorDefinition, IndicatorParams } from '@/shared/index';

// Импортируем определения индикаторов из папки definitions
import { SMA } from './definitions/sma';
import { RSI } from './definitions/rsi';
// TODO: Импортировать другие определения...

/**
 * Централизованный реестр всех доступных индикаторов в системе.
 * Ключ - ID индикатора, значение - его определение.
 */
export const indicatorRegistry: Record<string, IndicatorDefinition<any>> = {
  [SMA.id]: SMA,
  [RSI.id]: RSI,
  // TODO: Добавить другие индикаторы
};

/**
 * Получает список всех определений индикаторов из реестра.
 * @returns Массив определений индикаторов.
 */
export const getAvailableIndicators = (): IndicatorDefinition<any>[] => {
  return Object.values(indicatorRegistry);
};

/**
 * Находит определение индикатора по его ID.
 * @param indicatorId - ID искомого индикатора.
 * @returns Определение индикатора или undefined, если не найден.
 */
export const findIndicatorDefinitionById = (
  indicatorId: string
): IndicatorDefinition<any> | undefined => {
  return indicatorRegistry[indicatorId];
}; 
--- END FILE: src\features\Indicators\registry.ts ---

--- START FILE: src\features\Indicators\sources.ts ---
import type { KlineData } from '@/shared/index';

/**
 * Standard data sources available for indicator calculations.
 */
export const INDICATOR_SOURCES = [
  'open',
  'high',
  'low',
  'close',
  'hl2',    // (high + low) / 2
  'hlc3',   // (high + low + close) / 3
  'ohlc4',  // (open + high + low + close) / 4
  'volume'
] as const; // Use 'as const' for strict typing

export type IndicatorSource = typeof INDICATOR_SOURCES[number];

/**
 * Calculates the value for a given data point based on the selected source.
 *
 * @param dataPoint - The Kline data point (OHLCV).
 * @param source - The selected indicator source.
 * @returns The calculated value for the source, or NaN if source is invalid or data is missing.
 */
export function getIndicatorSourceValue(dataPoint: KlineData | null | undefined, source: IndicatorSource): number {
  if (!dataPoint) {
    return NaN;
  }

  switch (source) {
    case 'open':
      return dataPoint.open;
    case 'high':
      return dataPoint.high;
    case 'low':
      return dataPoint.low;
    case 'close':
      return dataPoint.close;
    case 'hl2':
      return (dataPoint.high + dataPoint.low) / 2;
    case 'hlc3':
      return (dataPoint.high + dataPoint.low + dataPoint.close) / 3;
    case 'ohlc4':
      return (dataPoint.open + dataPoint.high + dataPoint.low + dataPoint.close) / 4;
    case 'volume':
      // Ensure volume exists and is a number, return NaN otherwise
      return typeof dataPoint.volume === 'number' ? dataPoint.volume : NaN;
    default:
      // Should not happen with strict typing, but handle defensively
      console.warn(`[getIndicatorSourceValue] Unknown source: ${source}`);
      return NaN;
  }
} 
--- END FILE: src\features\Indicators\sources.ts ---

--- START FILE: src\features\ModeControl\index.ts ---
export { default as ModeControl } from './ModeControl';
export type { default } from './ModeControl'; 
--- END FILE: src\features\ModeControl\index.ts ---

--- START FILE: src\features\ModeControl\ModeControl.tsx ---
import React, { useState, useEffect, useCallback, memo, useMemo } from 'react';
import { Icon } from '@/shared/ui/icons/all_Icon';
import { Button } from '@/shared/ui/button';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/shared/ui/select';
import { Separator } from '@/shared/ui/separator';
import { Switch } from '@/shared/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/shared/ui/tooltip";
import { 
  ViewMode, 
  ScreenerSettings, 
  DEFAULT_TIMEFRAMES, 
  SCREENER_CHART_COUNTS,
  SCREENER_UPDATE_INTERVALS
} from '@/shared/index';
import { type ScreenerSortBy, type KlineInterval } from '@/shared/schemas';
import { 
  useAppSettingsStore,
  selectViewMode,
  selectScreenerSettings,
  selectSetViewMode,
  selectUpdateScreenerSettings,
  selectLayoutTypeForCurrentMode
} from '@/shared/store/settingsStore';
import { cn } from '@/shared/lib/utils';
import { useScreenerTickers } from './useScreenerTickers';

interface ModeControlProps {
  className?: string;
}

const ScreenerSortOptions: { value: ScreenerSortBy; label: string; icon: string }[] = [
  { value: 'volume', label: 'Volume', icon: 'BarChart' },
  { value: 'trades', label: 'Trades', icon: 'CandlestickChart' },
  { value: 'price_change', label: 'Price Change', icon: 'ChevronUp' },
  { value: 'volume_change', label: 'Volume Change', icon: 'ChevronDown' },
];

const ModeControl: React.FC<ModeControlProps> = ({ className }) => {
  // Store access
  const currentMode = useAppSettingsStore(selectViewMode);
  const screenerSettings = useAppSettingsStore(selectScreenerSettings);
  const setViewMode = useAppSettingsStore(selectSetViewMode);
  const updateScreenerSettings = useAppSettingsStore(selectUpdateScreenerSettings);
  const currentLayoutType = useAppSettingsStore(selectLayoutTypeForCurrentMode);

  // Hook for screener tickers data (to get total count for pagination)
  const { screenerTickers } = useScreenerTickers();

  const [countdown, setCountdown] = useState<number | null>(null);

  // Handle mode switch
  const handleModeChange = useCallback((mode: ViewMode) => {
    setViewMode(mode);
    console.log(`Switched to ${mode} mode`);
  }, [setViewMode]);

  // Handle screener settings update (simplified: only updates passed values, no autoUpdate side-effects here)
  const handleScreenerUpdate = useCallback((updates: Partial<ScreenerSettings>) => {
    updateScreenerSettings(updates);
    console.log('Screener settings updated:', updates);
  }, [updateScreenerSettings]);

  // Handle sort option click with cycle logic
  const handleSortClick = useCallback((sortBy: ScreenerSortBy) => {
    if (screenerSettings.sortBy === sortBy) {
      // Same sort type - toggle order
      const newOrder = screenerSettings.sortOrder === 'desc' ? 'asc' : 'desc';
      handleScreenerUpdate({ sortOrder: newOrder });
    } else {
      // Different sort type - set new type with desc as default
      handleScreenerUpdate({ 
        sortBy, 
        sortOrder: 'desc' 
      });
    }
  }, [screenerSettings.sortBy, screenerSettings.sortOrder, handleScreenerUpdate]);

  // Auto-update effect
  useEffect(() => {
    let intervalId: NodeJS.Timeout | undefined = undefined;

    if (screenerSettings.autoUpdate && (screenerSettings.updateInterval ?? 0) > 0) {
      // Start or reset countdown
      setCountdown(screenerSettings.updateInterval ?? 5);

      intervalId = setInterval(() => {
        setCountdown(prevCountdown => {
          if (prevCountdown === null || prevCountdown <= 1) {
            // Time to trigger update and reset countdown
            console.log('Screener auto-updating via timer...'); 
            // TODO: Call actual update function here
            return screenerSettings.updateInterval ?? 5; // Reset to full interval
          }
          return prevCountdown - 1;
        });
      }, 1000);
    } else {
      // Clear countdown if autoUpdate is off or interval is "Off"
      setCountdown(null);
      if (intervalId) {
        clearInterval(intervalId);
      }
    }

    // Cleanup function
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [screenerSettings.autoUpdate, screenerSettings.updateInterval]);

  // Manual update handler for the new Refresh button
  const handleManualRefreshClick = useCallback(() => {
    console.log('Manual refresh clicked, auto-update disabled, interval set to Off');
    updateScreenerSettings({ autoUpdate: false, updateInterval: 0 }); 
    setCountdown(null); // Stop countdown on manual refresh
  }, [updateScreenerSettings]);

  // Handler for the Update Interval Select
  const handleIntervalSelect = useCallback((intervalValue: string) => {
    const newInterval = parseInt(intervalValue);
    // Reset current page to 1 when interval changes
    if (newInterval === 0) { // "Off" is selected
      updateScreenerSettings({ updateInterval: 0, autoUpdate: false, currentPage: 1 });
      setCountdown(null); // Stop countdown if "Off" is selected
    } else {
      updateScreenerSettings({ updateInterval: newInterval, autoUpdate: true, currentPage: 1 });
      setCountdown(newInterval); // Start countdown immediately
    }
  }, [updateScreenerSettings]);

  // Pagination logic
  const itemsPerPage = useMemo(() => {
    const layout = currentMode === 'screener' ? currentLayoutType : '1x1';
    if (!layout) return 1;
    const [rows, cols] = layout.split('x').map(Number);
    return rows * cols;
  }, [currentMode, currentLayoutType]);

  const totalPages = useMemo(() => {
    if (screenerTickers.length === 0 || itemsPerPage === 0) return 1;
    return Math.ceil(screenerTickers.length / itemsPerPage);
  }, [screenerTickers.length, itemsPerPage]);

  const currentPage = screenerSettings.currentPage || 1;

  const handlePageChange = useCallback((newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      updateScreenerSettings({ currentPage: newPage });
    }
  }, [totalPages, updateScreenerSettings]);

  return (
    <div className={`relative flex items-center gap-1 ml-4 ${className || ''}`}>
      {/* Separator added to the left of the mode toggle button */}
      <Separator orientation="vertical" className="h-4" />
      
      {/* Mode Toggle Button */}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 w-auto px-2 transition-all duration-200 text-muted-foreground hover:text-foreground hover:bg-accent/50"
            onClick={() => handleModeChange(currentMode === 'focus' ? 'screener' : 'focus')}
          >
            {currentMode === 'focus' ? 'Focus' : 'Screener'}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>
            {currentMode === 'focus'
              ? 'Switch to Screener mode (Multiple tickers table)'
              : 'Switch to Focus mode (Detailed single ticker analysis)'}
          </p>
        </TooltipContent>
      </Tooltip>

      {/* Screener Settings */}
      {currentMode === 'screener' && (
        <>
          {/* <Separator orientation="vertical" className="h-4" /> */}
          {/* The above Separator has been removed (commented out for clarity, will be deleted by apply model) */}

          {/* Timeframe Selection */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Select
                value={screenerSettings.timeframe}
                onValueChange={(value) => handleScreenerUpdate({ timeframe: value as KlineInterval })}
              >
                <SelectTrigger className="h-7 w-14 p-1 text-xs" icon={null}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="min-w-[12rem]">
                  <div className="grid grid-cols-2 gap-x-1">
                    <div>
                      {DEFAULT_TIMEFRAMES.slice(0, Math.ceil(DEFAULT_TIMEFRAMES.length / 2)).map((tf) => (
                        <SelectItem key={tf.value} value={tf.value}>
                          {tf.label}
                        </SelectItem>
                      ))}
                    </div>
                    <div>
                      {DEFAULT_TIMEFRAMES.slice(Math.ceil(DEFAULT_TIMEFRAMES.length / 2)).map((tf) => (
                        <SelectItem key={tf.value} value={tf.value}>
                          {tf.label}
                        </SelectItem>
                      ))}
                    </div>
                  </div>
                </SelectContent>
              </Select>
            </TooltipTrigger>
            <TooltipContent>
              <p>Timeframe for all screener charts</p>
            </TooltipContent>
          </Tooltip>

          {/* Sort Settings */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Select
                value={screenerSettings.sortBy}
                onValueChange={(value) => handleSortClick(value as ScreenerSortBy)}
              >
                <SelectTrigger 
                  className="h-7 w-28 pl-2 pr-1 py-1 text-xs"
                  icon={null}
                >
                  <div className="flex items-center justify-between w-full gap-1">
                    <SelectValue />
                    <div
                      role="button"
                      tabIndex={0}
                      className="h-4 w-4 p-0 opacity-70 hover:opacity-100 focus:ring-0 shrink-0 inline-flex items-center justify-center rounded-md cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        const newOrder = screenerSettings.sortOrder === 'desc' ? 'asc' : 'desc';
                        handleScreenerUpdate({ sortOrder: newOrder });
                      }}
                      onPointerDown={(e) => e.stopPropagation()}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          e.stopPropagation();
                          const newOrder = screenerSettings.sortOrder === 'desc' ? 'asc' : 'desc';
                          handleScreenerUpdate({ sortOrder: newOrder });
                        }
                      }}
                      aria-label="Toggle sort order"
                    >
                      <Icon 
                        name="Sorting"
                        className={cn( screenerSettings.sortOrder === 'asc' && "transform scale-y-[-1]" )}
                      />
                    </div>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {ScreenerSortOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-1">
                        <Icon name={option.icon as any} className="w-3 h-3" />
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </TooltipTrigger>
            <TooltipContent>
              <p>
                Sort by: {screenerSettings.sortBy}. Order: {screenerSettings.sortOrder}. Click icon to toggle order.
              </p>
            </TooltipContent>
          </Tooltip>

          {/* Auto-update Interval Select & Manual Update Button */}
          <div className="flex items-center gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Select
                  value={screenerSettings.updateInterval?.toString() ?? '5'}
                  onValueChange={handleIntervalSelect}
                >
                  <SelectTrigger 
                    className="h-7 w-15 pl-2 pr-1 py-1 text-xs flex items-center justify-between gap-1"
                    icon={null}
                  >
                    <div className="flex-grow overflow-hidden whitespace-nowrap text-left">
                      {screenerSettings.autoUpdate && countdown !== null && (screenerSettings.updateInterval ?? 0) !== 0 ? (
                        <span className="text-xs">{countdown}s</span>
                      ) : (
                        <SelectValue /> 
                      )}
                    </div>
                    <div
                      role="button"
                      tabIndex={0}
                      className="h-4 w-4 p-0 opacity-60 hover:opacity-100 focus:ring-0 shrink-0 inline-flex items-center justify-center rounded-md cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation(); 
                        handleManualRefreshClick();
                      }}
                      onPointerDown={(e) => { 
                        e.stopPropagation();
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          e.stopPropagation();
                          handleManualRefreshClick();
                        }
                      }}
                      aria-label="Manual refresh"
                    >
                      <Icon name="Refresh" className="w-2.5 h-2.5" /> 
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {SCREENER_UPDATE_INTERVALS.map((interval) => (
                      <SelectItem key={interval.value} value={interval.value.toString()}>
                        {interval.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </TooltipTrigger>
              <TooltipContent>
                <p>Top charts update interval. Click icon to refresh manually.</p>
              </TooltipContent>
            </Tooltip>
          </div>

          {/* Screener Pagination Control */}
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center h-7 border border-input bg-background rounded-md text-xs shrink-0">
                <Button
                  variant="ghost"
                  className="h-full w-7 p-0 rounded-r-none hover:bg-accent -mr-px z-10 flex items-center justify-center"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                  aria-label="Previous page"
                >
                  <Icon name="ChevronRight" className="h-3.5 w-3.5 transform scale-x-[-1]" />
                </Button>
                <div className="tabular-nums whitespace-nowrap text-muted-foreground text-center select-none px-0.5 min-w-[28px]">
                  {currentPage}/{totalPages}
                </div>
                <Button
                  variant="ghost"
                  className="h-full w-7 p-0 rounded-l-none hover:bg-accent -ml-px z-10 flex items-center justify-center"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                  aria-label="Next page"
                >
                  <Icon name="ChevronRight" className="h-3.5 w-3.5" />
                </Button>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Navigate screener pages</p>
            </TooltipContent>
          </Tooltip>
        </>
      )}
    </div>
  );
};

export default memo(ModeControl); 
--- END FILE: src\features\ModeControl\ModeControl.tsx ---

--- START FILE: src\features\ModeControl\useScreenerTickers.ts ---
import { useMemo } from 'react';
import { Ticker, ProcessedTicker } from '@/shared/index'; // Changed back from direct import
// import type { Ticker } from '@/shared/types/market.types'; // Removed direct import
// import type { ProcessedTicker } from '@/shared/index'; 
import { type ScreenerSortBy } from '@/shared/schemas';
import { 
  useAppSettingsStore,
  selectViewMode,
  selectScreenerSettings,
  selectSelectedPairs,
  selectMarketTypes
} from '@/shared/store/settingsStore';
import { 
  useMarketStore
} from '@/shared/store/marketStore';

/**
 * Хук для получения топ тикеров для скринера
 * Фильтрует и сортирует тикеры согласно настройкам скринера
 */
export const useScreenerTickers = () => {
  // Store access
  const currentMode = useAppSettingsStore(selectViewMode);
  const screenerSettings = useAppSettingsStore(selectScreenerSettings);
  const selectedPairs = useAppSettingsStore(selectSelectedPairs);
  const selectedMarketTypes = useAppSettingsStore(selectMarketTypes);
  
  // Market data
  const tickers = useMarketStore(state => state.tickers || {});

  // Объединяем все тикеры и фильтруем по selectedMarketTypes
  const allTickers = useMemo(() => {
    // Преобразуем объект тикеров в массив
    const allTickersArray = Object.values(tickers);
    
    if (!selectedMarketTypes || selectedMarketTypes.length === 0) {
      return allTickersArray; // If no market types selected, return all (should not happen based on UI)
    }

    return allTickersArray.filter(ticker => 
      selectedMarketTypes.includes(ticker.marketType)
    );
  }, [tickers, selectedMarketTypes]);

  // Фильтрация и сортировка тикеров для скринера
  const screenerTickers = useMemo(() => {
    if (currentMode !== 'screener') {
      return []; // Не возвращаем тикеры если не в режиме скринера
    }

    // Фильтрация по торговым парам
    let filteredTickers = allTickers.filter(ticker => {
      // Если нет выбранных пар, показываем все
      if (!selectedPairs.length) return true;
      
      // Проверяем есть ли символ с выбранными парами
      return selectedPairs.some(pair => ticker.symbol.endsWith(pair));
    });

    // Фильтрация по минимальным значениям из настроек скринера
    filteredTickers = filteredTickers.filter(ticker => {
      if (screenerSettings.minVolume && ticker.volume && ticker.volume < screenerSettings.minVolume) {
        return false;
      }
      if (screenerSettings.minTrades && ticker.count && ticker.count < screenerSettings.minTrades) {
        return false;
      }
      return true;
    });

    // Сортировка по выбранной метрике
    const sortedTickers = [...filteredTickers].sort((a, b) => {
      let aValue: number;
      let bValue: number;

      switch (screenerSettings.sortBy) {
        case 'volume':
          aValue = a.quoteVolume || 0; // Используем quoteVolume (объем в USDT)
          bValue = b.quoteVolume || 0;
          break;
        case 'trades':
          aValue = a.count || 0;
          bValue = b.count || 0;
          break;
        case 'price_change':
          aValue = a.priceChangePercent || 0;
          bValue = b.priceChangePercent || 0;
          break;
        case 'volume_change':
          // Для изменения объема пока используем просто объем
          // В будущем можно добавить отдельное поле для изменения объема
          aValue = a.volume || 0;
          bValue = b.volume || 0;
          break;
        default:
          aValue = a.quoteVolume || 0;
          bValue = b.quoteVolume || 0;
      }

      // Применяем направление сортировки
      if (screenerSettings.sortOrder === 'desc') {
        return bValue - aValue; // От большего к меньшему
      } else {
        return aValue - bValue; // От меньшего к большему
      }
    });

    // Преобразуем в ProcessedTicker для совместимости с интерфейсом скринера
    const processedTickers = sortedTickers.map(ticker => {
      const symbol = ticker.symbol;
      const parts = symbol.match(/^(.+?)([A-Z]{3,}|[1-9A-Z]{4,})$/);
      const baseAsset = parts ? parts[1] : symbol;
      const quoteAsset = parts ? parts[2] : '';
      
      return {
        ...ticker,
        baseAsset,
        quoteAsset,
        priceChangeAbs: ticker.priceChange,
      } as ProcessedTicker;
    });

    // Возвращаем все отсортированные тикеры - количество ограничивается сеткой в main page
    return processedTickers;
  }, [
    currentMode,
    allTickers,
    selectedPairs,
    screenerSettings
  ]);

  // Дополнительная информация для отладки
  const screenerInfo = useMemo(() => {
    if (currentMode !== 'screener') {
      return null;
    }

    return {
      totalTickers: allTickers.length,
      filteredCount: screenerTickers.length,
      sortBy: screenerSettings.sortBy,
      sortOrder: screenerSettings.sortOrder,
      timeframe: screenerSettings.timeframe,
    };
  }, [
    currentMode,
    allTickers.length,
    screenerTickers.length,
    screenerSettings.sortBy,
    screenerSettings.sortOrder,
    screenerSettings.timeframe
  ]);

  return {
    /** Топ тикеры для отображения в скринере */
    screenerTickers,
    /** Информация о скринере для отладки */
    screenerInfo,
    /** Находимся ли в режиме скринера */
    isScreenerMode: currentMode === 'screener',
    /** Настройки скринера */
    screenerSettings
  };
}; 
--- END FILE: src\features\ModeControl\useScreenerTickers.ts ---

--- START FILE: src\features\StatusBar\StatusBar.tsx ---
import React, { useEffect, useState, useRef, useMemo, memo, useCallback } from 'react';
import { WSConnectionStatus as ConnectionStatus } from '@/shared/index'; // NEW: Explicitly import and alias
import { motion, AnimatePresence } from 'framer-motion'; // Оставляем для анимации индикатора
import { Icon } from '@/shared/ui/icons/all_Icon'; // NEW IMPORT
import { Button } from '../../shared/ui/button'; // Компонент кнопки shadcn
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../shared/ui/tooltip'; // Компоненты Tooltip shadcn
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../shared/ui/popover';
import { cn } from '@/shared/lib/utils';

export interface StatusBarProps {
  loading?: boolean;
  error?: any;
  lastUpdate?: number | null;
  onRefresh?: () => void;
  connectionDetails?: {
    name: string;
    status: ConnectionStatus; // This uses the type alias
    details?: string;
  }[];
  totalTickers?: number;
  filteredTickers?: number;
}

// Map connection status to Tailwind background and shadow colors
const statusStyles: Record<ConnectionStatus | 'default', { bg: string; shadow: string; text: string }> = {
  [ConnectionStatus.OPEN]: { bg: 'bg-green-500', shadow: 'shadow-[0_0_4px_1px_rgba(34,197,94,0.5)]', text: 'text-green-500' },
  [ConnectionStatus.CLOSED]: { bg: 'bg-red-500', shadow: 'shadow-[0_0_4px_1px_rgba(239,68,68,0.5)]', text: 'text-red-500' },
  [ConnectionStatus.CONNECTING]: { bg: 'bg-yellow-500', shadow: 'shadow-[0_0_4px_1px_rgba(234,179,8,0.5)]', text: 'text-yellow-500' },
  [ConnectionStatus.CLOSING]: { bg: 'bg-orange-500', shadow: 'shadow-[0_0_4px_1px_rgba(249,115,22,0.5)]', text: 'text-orange-500' },
  [ConnectionStatus.ERROR]: { bg: 'bg-red-600', shadow: 'shadow-[0_0_4px_1px_rgba(220,38,38,0.5)]', text: 'text-red-600' },
  default: { bg: 'bg-gray-400', shadow: 'shadow-[0_0_4px_1px_rgba(156,163,175,0.5)]', text: 'text-gray-400' },
};

// Map connection status to human-readable text
const statusText: Record<ConnectionStatus, string> = {
  [ConnectionStatus.OPEN]: 'Connected',
  [ConnectionStatus.CONNECTING]: 'Connecting...',
  [ConnectionStatus.CLOSING]: 'Closing...',
  [ConnectionStatus.CLOSED]: 'Disconnected',
  [ConnectionStatus.ERROR]: 'Error',
};

const StatusBarComponent: React.FC<StatusBarProps> = ({
  loading,
  error,
  lastUpdate,
  onRefresh,
  connectionDetails = [],
  totalTickers = 0,
  filteredTickers = 0,
}) => {
  const prevLastUpdateRef = useRef<number | null>(null);
  const [shouldAnimate, setShouldAnimate] = useState<boolean>(false);

  // Determine the primary status for the indicator
  const primaryStatus = useMemo((): ConnectionStatus => {
      if (error) return ConnectionStatus.ERROR;
      if (connectionDetails.length > 0) {
          // Prioritize based on severity
          if (connectionDetails.some(d => d.status === ConnectionStatus.ERROR)) return ConnectionStatus.ERROR;
          if (connectionDetails.some(d => d.status === ConnectionStatus.CLOSED)) return ConnectionStatus.CLOSED;
          if (connectionDetails.some(d => d.status === ConnectionStatus.CLOSING)) return ConnectionStatus.CLOSING;
          if (connectionDetails.some(d => d.status === ConnectionStatus.CONNECTING)) return ConnectionStatus.CONNECTING;
          return ConnectionStatus.OPEN;
      }
      return ConnectionStatus.CLOSED;
  }, [connectionDetails, error]);

  const { bg: indicatorBg, shadow: indicatorShadow } = statusStyles[primaryStatus] ?? statusStyles.default;

  const indicatorTitle = useMemo(() => {
    if (primaryStatus === ConnectionStatus.ERROR) return `Error: ${error?.message || (typeof error === 'string' ? error : 'Unknown')}`;
    if (primaryStatus === ConnectionStatus.OPEN) return 'All connections established';
    if (primaryStatus === ConnectionStatus.CONNECTING) return 'Attempting to connect...';
    if (primaryStatus === ConnectionStatus.CLOSED) return 'Connections lost';
    if (primaryStatus === ConnectionStatus.CLOSING) return 'Closing connections...';
    return 'Status unavailable';
  }, [primaryStatus, error]);

  // Animation variants for the indicator pulse
  const indicatorAnimation = {
    initial: { scale: 1, opacity: 0.8 },
    animate: {
      scale: [1, 1.3, 1],
      opacity: [0.8, 1, 0.8],
      transition: {
        duration: 1,
        ease: "easeInOut",
        repeat: 1, // Pulse once
      },
    },
  };

  // Trigger animation on lastUpdate change
  useEffect(() => {
    if (lastUpdate && lastUpdate !== prevLastUpdateRef.current) {
      prevLastUpdateRef.current = lastUpdate;
      setShouldAnimate(true);
      // Reset animation state after it completes (duration * repeat count)
      const timerId = setTimeout(() => setShouldAnimate(false), indicatorAnimation.animate.transition.duration * 1000 * (indicatorAnimation.animate.transition.repeat || 0));
      return () => clearTimeout(timerId);
    }
  }, [lastUpdate]);

  // --- Memoize Tooltip Content ---
  const connectionDetailsContent = useMemo(() => {
    // Function defined inside useMemo to capture current props/state
    const renderDetails = () => (
        <div className="w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2">
           {/* Flex container for Heading and Refresh Button */}
           <div className="mb-2 flex items-center justify-between border-b pb-2">
             <h4 className="text-sm font-semibold text-foreground">
               Connection Status
             </h4>
             {/* Refresh Button - Moved here, removed inner Tooltip */}
             {onRefresh && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onRefresh}
                  disabled={loading}
                  className={cn(
                  )}
                  aria-label="Refresh Data"
                >
                  <Icon name="Refresh" className={cn(
                      "h-4 w-4",
                      loading ? 'animate-spin' : ''
                  )} />
                </Button>
             )}
           </div>
          <ul className="list-none p-0">
            {connectionDetails.length > 0 ? connectionDetails.map((conn, index) => {
              const { text: statusColor } = statusStyles[conn.status] ?? statusStyles.default;
              const text = statusText[conn.status] ?? 'Unknown';
              // Use a more stable key if possible, e.g., conn.name if unique
              return (
                <li key={conn.name || index} className={`flex flex-wrap items-center gap-x-2 py-1 text-xs ${statusColor}`}>
                  <span className="w-[90px] flex-shrink-0 font-medium text-foreground">{conn.name}:</span>
                  <span className="font-medium">{text}</span>
                  {conn.status === ConnectionStatus.ERROR && conn.details && (
                    <span className="mt-0.5 w-full pl-[98px] text-xs text-muted-foreground">
                      ({conn.details})
                    </span>
                  )}
                </li>
              );
            }) : (
              <li className={`flex flex-wrap items-center gap-x-2 py-1 text-xs ${statusStyles[primaryStatus]?.text ?? statusStyles.default.text}`}>
                  <span className="w-[90px] flex-shrink-0 font-medium text-foreground">Overall:</span>
                  <span className="font-medium">{statusText[primaryStatus as keyof typeof statusText] ?? 'Unknown'}</span>
                  {primaryStatus === ConnectionStatus.ERROR && error && (
                    <span className="mt-0.5 w-full pl-[98px] text-xs text-muted-foreground">
                      ({typeof error === 'string' ? error : error?.message || 'Unknown error'})
                    </span>
                  )}
                </li>
            )}
          </ul>

          {/* Ticker Information */}
          {(totalTickers > 0 || filteredTickers > 0) && (
              <div className="mt-3 border-t pt-2">
                <h4 className="mb-2 text-sm font-semibold text-foreground">Ticker Information</h4>
                <div className="flex flex-col gap-1">
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">Total fetched:</span>
                    <span className="font-mono font-medium text-foreground">{totalTickers}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">Displayed:</span>
                    <span className="font-mono font-medium text-foreground">{filteredTickers}</span>
                  </div>
                  {totalTickers > 0 && filteredTickers < totalTickers && totalTickers !== 0 && ( // Added check for totalTickers !== 0 to prevent NaN
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Filtered out:</span>
                      <span className="font-mono font-medium text-foreground">
                        {totalTickers - filteredTickers}
                        ({Math.round(((totalTickers - filteredTickers) / totalTickers) * 100)}%)
                      </span>
                    </div>
                  )}
                </div>
              </div>
          )}
        </div>
    );
    return renderDetails(); // Execute the function to get the JSX
  }, [connectionDetails, primaryStatus, error, totalTickers, filteredTickers, onRefresh, loading]); // Dependencies for useMemo

  return (
    <TooltipProvider delayDuration={300}> {/* Keep Provider for potential other tooltips */}
      <div className="ml-auto flex h-8 items-center gap-2 px-2 font-mono text-xs">
        {/* Popover wraps the indicator */}
        <Popover>
          <PopoverTrigger asChild>
            <motion.div
              key={shouldAnimate ? 'animate' : 'idle'} // Trigger re-render for animation restart
              className={`h-2 w-2 flex-shrink-0 cursor-pointer rounded-full ${indicatorBg} ${indicatorShadow} transition-all duration-300 ease-in-out`} // Added cursor-pointer
              title={indicatorTitle} // Basic HTML title for accessibility
              variants={indicatorAnimation}
              initial="initial"
              animate={shouldAnimate ? 'animate' : 'initial'}
            />
          </PopoverTrigger>
          <PopoverContent side="bottom" align="end" className="p-0 border-none bg-transparent shadow-none">
            {/* Use the memoized content */}
            {connectionDetailsContent}
          </PopoverContent>
        </Popover>
      </div>
    </TooltipProvider>
  );
};

const StatusBar = memo(StatusBarComponent);

export default StatusBar; 
--- END FILE: src\features\StatusBar\StatusBar.tsx ---

--- START FILE: src\features\TickerManagement\index.ts ---
export { TickerTable } from './Table';
export { useProcessedTableData } from './useTableUIstate';
export { useIndicatorDataForTable } from './useTableUIstate'; 
--- END FILE: src\features\TickerManagement\index.ts ---

--- START FILE: src\features\TickerManagement\Table.tsx ---
"use client"

import React, { useRef, useEffect, KeyboardEvent, useCallback, useMemo, useTransition, CSSProperties, memo, useState } from 'react';
import {
  ColumnDef, ColumnSizingState, Header, Cell, OnChangeFn,
  SortingState, flexRender, getCoreRowModel, getSortedRowModel,
  getFilteredRowModel, useReactTable, Row, RowData, TableMeta
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { clsx } from 'clsx';
import { throttle } from 'throttle-debounce';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Identifier } from 'dnd-core';
import { Skeleton } from '@/shared/ui/skeleton';
import { Ticker, MarketType, ProcessedTicker, ColumnWidths, IndicatorColumnConfig, CalculatedIndicatorResult, WSConnectionStatus as ConnectionStatus, KlineInterval, TradingViewSymbol } from '@/shared/index';
import { 
  useAppSettingsStore, 
  selectTableCompactness,
  selectUiFontSize, 
  selectUiFontFamily, 
  selectShowVolumeInUSD, 
  selectIndicatorColumns, 
  selectMarketTypes 
} from '@/shared/store/settingsStore';
import { useMarketStore, DEFAULT_MARKET_TYPE, useWebSocketConnection } from '@/shared/index';
import { useTableStateConfig } from './useTableUIstate';
import { useProcessedTableData, useIndicatorDataForTable } from './useTableData';

// --- Constants ---
const DND_ITEM_TYPE = 'column';
const BASE_BODY_ROW_HEIGHT = 34;
const MIN_BODY_ROW_HEIGHT = 20;
const BASE_BODY_CELL_PADDING_X = 8;
const MIN_BODY_CELL_PADDING_X = 4;
const BASE_HEADER_CELL_PADDING_X = 6;
const MIN_HEADER_CELL_PADDING_X = 4;
const SKELETON_ROW_COUNT = 40;

// --- Helper Functions ---
const formatNumber = (value: number | null | undefined, fractionDigits = 2): string =>
  (value === null || value === undefined || isNaN(value)) ? '-' : value.toFixed(fractionDigits);

const formatPercent = (value: number | null | undefined): string =>
  (value === null || value === undefined || isNaN(value) || typeof value !== 'number') ? '-' : `${value.toFixed(2)}%`;

const formatAbbreviatedInteger = (value: number | null | undefined): string => {
  if (value === null || value === undefined || isNaN(value)) return '-';
  if (value === 0) return '0';

  const absValue = Math.abs(value);
  const sign = value < 0 ? '-' : '';
  const roundedValue = Math.round(absValue);

  if (roundedValue >= 1e12) return `${sign}${Math.round(roundedValue / 1e12)}T`;
  if (roundedValue >= 1e9)  return `${sign}${Math.round(roundedValue / 1e9)}B`;
  if (roundedValue >= 1e6)  return `${sign}${Math.round(roundedValue / 1e6)}M`;
  if (roundedValue >= 1e3)  return `${sign}${Math.round(roundedValue / 1e3)}K`;

  return `${sign}${roundedValue}`;
};

const formatDynamicPrecisionPrice = (value: number | null | undefined): string => {
  if (value === null || value === undefined || isNaN(value)) return '-';

  const maxPrecision = 8;
  const minPrecision = 2;
  let numStr = value.toFixed(maxPrecision);

  if (!numStr.includes('.')) return value.toFixed(0);

  while (numStr.endsWith('0') && numStr.length - numStr.indexOf('.') - 1 > minPrecision) {
    numStr = numStr.slice(0, -1);
  }
  return numStr.endsWith('.') ? numStr.slice(0, -1) : numStr;
};

// --- Table Row Cell Rendering ---
const renderCellContent = (
  cell: Cell<ProcessedTicker, unknown>,
  selectedPairs: string[],
  indicatorColumns: IndicatorColumnConfig[],
  indicatorDataMap: Map<string, Map<string, any>>,
  indicatorQueryStatusMap: Map<string, 'pending' | 'success' | 'error'>
): React.ReactNode => {
  const { column, row, getContext } = cell;
  const columnId = column.id;
  const cellValue = cell.getValue();
  const originalData = row.original as ProcessedTicker;

  const indicatorConfig = indicatorColumns.find(col => col.instanceId === columnId);

  if (indicatorConfig) {
    const indicatorDataKey = `${indicatorConfig.indicatorId}_${indicatorConfig.timeframe || '1h'}_${originalData.marketType}`;
    const queryStatus = indicatorQueryStatusMap.get(indicatorDataKey);

    if (queryStatus === undefined || queryStatus === 'pending') {
      return <Skeleton className="h-4 w-10" />;
    } else if (queryStatus === 'error') {
      return <span className="text-destructive text-xs" title="Error loading data">Err</span>;
    }

    const dataForSymbol = indicatorDataMap.get(indicatorDataKey)?.get(originalData.symbol);
    
    let value = null;
    
    if (dataForSymbol) {
      if (dataForSymbol.values && indicatorConfig.outputId in dataForSymbol.values) {
        value = dataForSymbol.values[indicatorConfig.outputId];
      } else if (dataForSymbol.data && dataForSymbol.data.length > 0) {
        const lastDataPoint = dataForSymbol.data[dataForSymbol.data.length - 1];
        if (lastDataPoint && indicatorConfig.outputId in lastDataPoint) {
          value = lastDataPoint[indicatorConfig.outputId];
        }
      }
    }
    
    if (value !== null && value !== undefined) {
      if (typeof value === 'number') return formatNumber(value, 2);
      return String(value);
    } else {
      return '-';
    }
  }

  switch (columnId) {
    case 'symbol': {
      const originalSymbol = cellValue as string;
      if (selectedPairs.length === 1 && selectedPairs[0] !== 'OTHR' && originalSymbol.endsWith(selectedPairs[0])) {
        return originalSymbol.slice(0, -selectedPairs[0].length);
      }
      return originalSymbol;
    }
    case 'price':
      return formatDynamicPrecisionPrice(cellValue as number | null);
    case 'volume':
      return formatAbbreviatedInteger(cellValue as number | null);
    case 'trade':
      return formatAbbreviatedInteger(originalData.count); 
    case 'spread':
      return formatPercent(cellValue as number | null);
    case 'change': {
      const value = cellValue as number | null;
      if (value === null || isNaN(value)) return '-';
      const colorClass = value > 0 ? 'text-positive' : value < 0 ? 'text-negative' : '';
      return <span className={colorClass}>{formatPercent(value)}</span>;
    }
    default:
      return flexRender(column.columnDef.cell, getContext());
  }
};

// --- Table Row Component ---
interface TableRowComponentProps {
  virtualRow: ReturnType<ReturnType<typeof useVirtualizer<HTMLDivElement, Element>>['getVirtualItems']>[number];
  row: Row<ProcessedTicker>;
  isSelected: boolean;
  bodyCellPaddingX: number;
  handleRowClick: (symbol: string) => void;
  selectedPairs: string[];
  indicatorColumns: IndicatorColumnConfig[];
  indicatorDataMap: Map<string, Map<string, any>>;
  indicatorQueryStatusMap: Map<string, 'pending' | 'success' | 'error'>;
  renderCell: typeof renderCellContent;
}

const TableRowComponent = memo(({
  virtualRow,
  row,
  isSelected,
  bodyCellPaddingX,
  handleRowClick,
  selectedPairs,
  indicatorColumns,
  indicatorDataMap,
  indicatorQueryStatusMap,
  renderCell
}: TableRowComponentProps) => {
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    handleRowClick(row.original.symbol);
  }, [handleRowClick, row.original.symbol]);

  const rowStyle = useMemo<CSSProperties>(() => ({
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: `${virtualRow.size}px`,
    transform: `translateY(${virtualRow.start}px)`,
    contain: 'layout style',
  }), [virtualRow.size, virtualRow.start]);

  return (
    <div
      style={rowStyle}
      className={clsx(
        'flex w-full items-center group transition-none',
        'hover:bg-primary/5',
        isSelected && 'bg-primary/10',
        'cursor-pointer'
      )}
      onClick={handleClick}
      data-index={virtualRow.index}
      role="row"
    >
      {isSelected && <div className="absolute left-0 top-[5%] bottom-[5%] w-0.5 bg-primary rounded-r-sm" />}
      {row.getVisibleCells().map((cell) => {
        const meta = cell.column.columnDef.meta as { textAlignment?: string } | undefined;
        const isCentered = meta?.textAlignment === 'text-center';
        const cellContent = renderCell(
          cell,
          selectedPairs,
          indicatorColumns,
          indicatorDataMap,
          indicatorQueryStatusMap
        );

        return (
          <div
            key={cell.id}
            className={clsx(
              'overflow-hidden whitespace-nowrap align-middle h-full flex',
              isCentered ? 'justify-center items-center' : 'items-center',
              meta?.textAlignment ?? 'text-left'
            )}
            style={{
              width: cell.column.getSize(),
              paddingLeft: `${bodyCellPaddingX}px`,
              paddingRight: `${bodyCellPaddingX}px`,
            }}
            title={typeof cellContent === 'string' ? cellContent : String(cell.getValue() ?? '')}
            role="gridcell"
          >
            {cellContent}
          </div>
        );
      })}
    </div>
  );
}, (prevProps, nextProps) => {
  if (prevProps.isSelected !== nextProps.isSelected) return false;
  if (prevProps.bodyCellPaddingX !== nextProps.bodyCellPaddingX) return false;
  
  if (prevProps.virtualRow.index !== nextProps.virtualRow.index ||
      prevProps.virtualRow.size !== nextProps.virtualRow.size ||
      prevProps.virtualRow.start !== nextProps.virtualRow.start) {
    return false;
  }

  if (prevProps.row.original !== nextProps.row.original) {
    return false;
  }

  if (prevProps.selectedPairs.length !== nextProps.selectedPairs.length ||
      !prevProps.selectedPairs.every((val, idx) => val === nextProps.selectedPairs[idx])) {
    return false;
  }
  
  if (prevProps.indicatorColumns.length !== nextProps.indicatorColumns.length ||
      !prevProps.indicatorColumns.every((prevCol, idx) => {
        const nextCol = nextProps.indicatorColumns[idx];
        return prevCol.instanceId === nextCol.instanceId && 
               prevCol.outputId === nextCol.outputId &&
               prevCol.timeframe === nextCol.timeframe &&
               prevCol.name === nextCol.name;
      })
  ) {
    return false;
  }

  const symbol = nextProps.row.original.symbol; 
  for (const col of nextProps.indicatorColumns) {
    const key = `${col.indicatorId}_${col.timeframe || '1h'}_${nextProps.row.original.marketType}`;
    
    const prevIndicatorMapForKey = prevProps.indicatorDataMap.get(key);
    const nextIndicatorMapForKey = nextProps.indicatorDataMap.get(key);
    const prevStatus = prevProps.indicatorQueryStatusMap.get(key);
    const nextStatus = nextProps.indicatorQueryStatusMap.get(key);

    if (prevStatus !== nextStatus) return false;

    if (nextStatus === 'pending' || nextStatus === 'error') {
        continue;
    }

    if (prevIndicatorMapForKey !== nextIndicatorMapForKey) {
      const prevData = prevIndicatorMapForKey?.get(symbol);
      const nextData = nextIndicatorMapForKey?.get(symbol);
      if (prevData !== nextData) return false; 
    } else if (prevIndicatorMapForKey && nextIndicatorMapForKey) { 
       const prevData = prevIndicatorMapForKey.get(symbol);
       const nextData = nextIndicatorMapForKey.get(symbol);
       if (prevData !== nextData) return false;
    }
  }
  
  return true;
});

TableRowComponent.displayName = 'TableRowComponent';

// --- Table Header Cell with DND ---
interface DragItem { id: string; type: string; }
interface TableHeaderCellProps {
  header: Header<ProcessedTicker, unknown>;
  columnOrder: string[];
  reorderColumn: (draggedId: string, targetId: string) => void;
}

const TableHeaderCellComponent: React.FC<TableHeaderCellProps> = ({ header, columnOrder, reorderColumn }) => {
  const ref = useRef<HTMLDivElement>(null);
  const dragRef = useRef<HTMLDivElement>(null);
  const { column } = header;
  const { id } = column;
  const meta = column.columnDef.meta as { textAlignment?: string } | undefined;
  const compactnessFactor = header.getContext().table.options.meta?.compactnessFactor ?? 0;

  const [{ handlerId }, drop] = useDrop<DragItem, void, { handlerId: Identifier | null }>({
    accept: DND_ITEM_TYPE,
    hover: (item: DragItem, monitor) => {
      if (!ref.current || item.id === id) return;
      const dragId = item.id;
      const hoverId = id;
      const hoverBoundingRect = ref.current.getBoundingClientRect();
      const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;
      const hoverClientX = clientOffset.x - hoverBoundingRect.left;
      const dragIndex = columnOrder.indexOf(dragId);
      const hoverIndex = columnOrder.indexOf(hoverId);
      if ((dragIndex < hoverIndex && hoverClientX < hoverMiddleX) ||
          (dragIndex > hoverIndex && hoverClientX > hoverMiddleX)) {
        return;
      }
      reorderColumn(dragId, hoverId);
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: DND_ITEM_TYPE,
    item: () => ({ id, type: DND_ITEM_TYPE }),
    collect: (monitor) => ({ isDragging: monitor.isDragging() }),
  });

  drop(ref);
  drag(dragRef);

  const isResizing = column.getIsResizing();
  const throttledResizeHandler = useMemo(() => throttle(125, header.getResizeHandler()), [header]);

  const delayedSortHandler = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (column.getCanSort()) {
      header.column.getToggleSortingHandler()?.(e);
    }
  }, [header, column]);

  const headerCellPaddingX = useMemo(() =>
    Math.round(BASE_HEADER_CELL_PADDING_X - (BASE_HEADER_CELL_PADDING_X - MIN_HEADER_CELL_PADDING_X) * compactnessFactor)
  , [compactnessFactor]);

  const getSortTooltip = () => {
    if (!column.getCanSort()) return undefined;
    switch (column.getNextSortingOrder()) {
      case 'asc': return 'Sort ascending';
      case 'desc': return 'Sort descending';
      default: return 'Clear sort';
    }
  };

  return (
    <div
      ref={ref}
      key={id}
      className="relative flex items-center select-none group"
      style={{ width: header.getSize(), opacity: isDragging ? 0.5 : 1 }}
      data-column-id={id}
      data-handler-id={handlerId}
      role="columnheader"
      aria-sort={column.getIsSorted() === 'asc' ? 'ascending' : column.getIsSorted() === 'desc' ? 'descending' : undefined}
    >
      <div className="flex items-center w-full h-full overflow-hidden">
        <div
          ref={dragRef}
          className={clsx(
            'flex items-center flex-grow h-full overflow-hidden whitespace-nowrap',
            column.getCanSort() ? 'cursor-pointer' : 'cursor-move',
            meta?.textAlignment === 'text-center' && 'justify-center'
          )}
          style={{
            paddingLeft: `${headerCellPaddingX}px`,
            paddingRight: `${headerCellPaddingX}px`
          }}
          onClick={delayedSortHandler}
          title={getSortTooltip()}
        >
          {flexRender(column.columnDef.header, header.getContext())}
          {column.getIsSorted() && (
            <div className="absolute bottom-0 left-0 right-0 h-px bg-primary"></div>
          )}
        </div>

        {column.getCanResize() && (
          <div
            onMouseDown={(e) => { e.stopPropagation(); throttledResizeHandler(e); }}
            onTouchStart={(e) => { e.stopPropagation(); throttledResizeHandler(e); }}
            className={clsx(
              'absolute top-0 right-0 h-full w-2 cursor-ew-resize select-none touch-none z-10',
              'bg-transparent group-hover:bg-primary/20 active:bg-primary/40 transition-colors duration-150',
              isResizing && 'bg-primary/40'
            )}
            onClick={(e) => e.stopPropagation()}
            role="separator"
            aria-orientation="vertical"
          >
            <div className={clsx(
              "absolute top-[15%] bottom-[15%] right-[3px] w-px transition-colors duration-150 ease-in-out",
              "bg-transparent group-hover:bg-primary/50",
              isResizing && "bg-primary/50"
            )}></div>
          </div>
        )}
      </div>
    </div>
  );
};

const TableHeaderCell = memo(TableHeaderCellComponent);
TableHeaderCell.displayName = 'TableHeaderCell';

// --- Main TickerTable Component ---
interface TickerTableProps {
  searchQuery: string;
  aggregateVolumeAndTrades: boolean;
  onCountsChange: (counts: { total: number; filtered: number }) => void;
  onSymbolSelect: (symbol: string) => void;
  selectedSymbol: string | null;
}

export const TickerTable = memo<TickerTableProps>(({
  searchQuery,
  aggregateVolumeAndTrades,
  onCountsChange,
  onSymbolSelect,
  selectedSymbol: externalSelectedSymbol,
}) => {
  const parentRef = useRef<HTMLDivElement>(null);
  const [isProcessingInteraction, startTransition] = useTransition();
  const [hasDataEverLoaded, setHasDataEverLoaded] = useState(false);

  const indicatorColumns = useAppSettingsStore(selectIndicatorColumns);
  const tableCompactness = useAppSettingsStore(selectTableCompactness);
  const uiFontSize = useAppSettingsStore(selectUiFontSize);
  const uiFontFamily = useAppSettingsStore(selectUiFontFamily);
  const showVolumeInUSD = useAppSettingsStore(selectShowVolumeInUSD);
  const selectedPairsFromStore = useAppSettingsStore(state => state.selectedPairs);
  const selectedMarketTypesFromStore = useAppSettingsStore(selectMarketTypes);

  const spotTickers = useMarketStore(state => state.processedSpotTickers);
  const futuresTickers = useMarketStore(state => state.processedFuturesTickers);

  const {
    tableSortingState,
    columnOrder, 
    tableVisibilityState,
    tableSizingState,
    updateZustandSortConfigs,
    setZustandColumnOrder,
    setZustandColumnSizing
  } = useTableStateConfig();

  const { combinedTickers, finalFilteredData } = useProcessedTableData({
    searchQuery,
    aggregateVolumeAndTrades,
    selectedPairs: selectedPairsFromStore,
  });

  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);
  const { indicatorDataMap, indicatorQueryStatusMap } = useIndicatorDataForTable();
  const { status: wsStatus } = useWebSocketConnection();
  
  useEffect(() => {
    const marketsToSubscribe = selectedMarketTypesFromStore || [];
    if (marketsToSubscribe.length > 0) {
        useMarketStore.getState().setSelectedMarketType(marketsToSubscribe[0]);
    } else {
        useMarketStore.getState().setSelectedMarketType(DEFAULT_MARKET_TYPE);
    }
    // No explicit cleanup for setSelectedMarketType, it just sets the state.
    // If WebSocket subscriptions were managed here, cleanup would be needed.
  }, [selectedMarketTypesFromStore]); 
  
  useEffect(() => {
    console.log(`[TickerTable] WebSocket status: ${wsStatus}`);
  }, [wsStatus]);
  
  useEffect(() => {
    if (hasDataEverLoaded) {
      setLastUpdateTime(new Date());
    }
  }, [spotTickers, futuresTickers, hasDataEverLoaded]); 

  const compactnessFactor = useMemo(() =>
    Math.max(0, Math.min(1, (tableCompactness ?? 0) / 20)),
  [tableCompactness]);

  const bodyRowHeight = useMemo(() =>
    Math.round(BASE_BODY_ROW_HEIGHT - (BASE_BODY_ROW_HEIGHT - MIN_BODY_ROW_HEIGHT) * compactnessFactor),
  [compactnessFactor]);

  const bodyCellPaddingX = useMemo(() =>
    Math.round(BASE_BODY_CELL_PADDING_X - (BASE_BODY_CELL_PADDING_X - MIN_BODY_CELL_PADDING_X) * compactnessFactor),
  [compactnessFactor]);
  
  const headerRowHeight = useMemo(() => 36, []); 
  
  const fontSizeClass = useMemo(() => {
    if (uiFontSize <= 12) return 'text-xs';
    if (uiFontSize <= 14) return 'text-sm';
    return 'text-base';
  }, [uiFontSize]);
  
  const fontFamilyClass = useMemo(() => {
    switch (uiFontFamily) {
      case 'mono': return 'font-mono';
      case 'serif': return 'font-serif';
      default: return 'font-sans';
    }
  }, [uiFontFamily]);
  
  const compactClasses = useMemo(() => {
    if (compactnessFactor > 0.7) return 'compact-extreme';
    if (compactnessFactor > 0.3) return 'compact-medium';
    return 'compact-normal';
  }, [compactnessFactor]);

  const tableStyles = useMemo<CSSProperties>(() => ({
    '--table-body-row-height': `${bodyRowHeight}px`,
    '--table-body-cell-padding-x': `${bodyCellPaddingX}px`,
    '--table-header-cell-padding-x': `${BASE_HEADER_CELL_PADDING_X}px`,
    fontSize: `${uiFontSize}px`,
    fontFamily: uiFontFamily,
  }), [bodyRowHeight, bodyCellPaddingX, uiFontSize, uiFontFamily]);

  const columns = useMemo<ColumnDef<ProcessedTicker>[]>(() => {
    const standardColumns: ColumnDef<ProcessedTicker>[] = [
      {
        accessorKey: 'symbol',
        header: ({ table }) => (
          <>
            Ticker
            <span className="text-muted-foreground text-[10px] ml-1">
              ({table.getFilteredRowModel().rows.length})
            </span>
          </>
        ),
        cell: info => info.getValue<string>(),
        size: 150,
        id: 'symbol',
        enableResizing: true,
        enableSorting: true,
      },
      {
        accessorKey: 'price',
        header: 'Price',
        cell: info => info.getValue<number>(),
        size: 100,
        id: 'price',
        meta: { textAlignment: 'text-right' },
        enableResizing: true,
        enableSorting: true,
      },
      {
        accessorKey: 'priceChangePercent',
        header: 'Cng %',
        cell: info => info.getValue<number>(),
        size: 90,
        id: 'change',
        meta: { textAlignment: 'text-center' },
        enableResizing: true,
        enableSorting: true,
      },
      {
        accessorKey: showVolumeInUSD ? 'quoteVolume' : 'volume',
        header: showVolumeInUSD ? 'Vol $' : 'Vol',
        cell: info => info.getValue<number>(),
        size: 130,
        id: 'volume',
        meta: { textAlignment: 'text-center' },
        enableResizing: true,
        enableSorting: true,
      },
      {
        accessorKey: 'count', // Corrected from trades
        header: 'Trades',
        cell: info => info.getValue<number>(),
        size: 100,
        id: 'trade',
        meta: { textAlignment: 'text-center' },
        enableResizing: true,
        enableSorting: true,
      },
      {
        accessorKey: 'spread',
        header: 'Spread %',
        cell: info => info.getValue<number>(),
        size: 90,
        id: 'spread',
        meta: { textAlignment: 'text-right' },
        enableResizing: true,
        enableSorting: true,
      },
    ];

    const dynamicIndicatorColumns: ColumnDef<ProcessedTicker>[] = indicatorColumns.map(config => ({
      id: config.instanceId, 
      header: `${config.name} (${config.timeframe})`,
      cell: info => null, 
      size: 100, 
      meta: { textAlignment: 'text-right' }, 
      enableResizing: true,
      enableSorting: false, 
    }));
    return [...standardColumns, ...dynamicIndicatorColumns];
  }, [showVolumeInUSD, indicatorColumns]); 

  const tableMeta = useMemo<TableMeta<ProcessedTicker>>(() => ({
    selectedTickerSymbol: externalSelectedSymbol,
    setSelectedTickerSymbol: onSymbolSelect,
    selectedPairs: selectedPairsFromStore,
    compactnessFactor: compactnessFactor,
    indicatorColumns: indicatorColumns, 
    indicatorDataMap: indicatorDataMap, 
    indicatorQueryStatusMap: indicatorQueryStatusMap, 
  }), [
    externalSelectedSymbol, onSymbolSelect, selectedPairsFromStore,
    compactnessFactor, indicatorColumns, 
    indicatorDataMap, indicatorQueryStatusMap, 
  ]);

  const table = useReactTable({
    data: finalFilteredData,
    columns, 
    state: {
      sorting: tableSortingState,
      columnOrder,
      columnVisibility: tableVisibilityState,
      columnSizing: tableSizingState,
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    columnResizeMode: 'onChange',
    enableSorting: true,
    enableColumnResizing: true,
    enableSortingRemoval: false,
    meta: tableMeta, 
  });

  const { rows } = table.getRowModel();
  const totalSize = table.getTotalSize(); 

  const rowVirtualizer = useVirtualizer({
    count: rows.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => bodyRowHeight, 
    overscan: 10, 
  });

  useEffect(() => {
    rowVirtualizer.measure();
  }, [bodyRowHeight, rowVirtualizer]);

  const virtualItems = rowVirtualizer.getVirtualItems();
  const totalVirtualHeight = rowVirtualizer.getTotalSize();

  const handleRowClick = useCallback((symbol: string) => {
    onSymbolSelect(symbol);
  }, [onSymbolSelect]);

  const selectedRowIndex = useMemo(() => {
    if (!externalSelectedSymbol) return -1;
    return rows.findIndex(row => row.original.symbol === externalSelectedSymbol);
  }, [rows, externalSelectedSymbol]);

  const handleKeyDown = useCallback((event: KeyboardEvent<HTMLDivElement>) => {
    if (!rows.length || !parentRef.current) return;
    const currentIndex = selectedRowIndex;
    let nextIndex = currentIndex;
    if (event.key === 'ArrowUp') {
      event.preventDefault();
      nextIndex = Math.max(0, currentIndex - 1);
    } else if (event.key === 'ArrowDown') {
      event.preventDefault();
      nextIndex = currentIndex === -1 ? 0 : Math.min(rows.length - 1, currentIndex + 1);
    } else {
      return; 
    }
    if (nextIndex !== currentIndex && nextIndex >= 0 && nextIndex < rows.length) {
      const nextSymbol = rows[nextIndex]?.original?.symbol;
      if (nextSymbol) {
        onSymbolSelect(nextSymbol);
        rowVirtualizer.scrollToIndex?.(nextIndex, { align: 'auto', behavior: 'smooth' });
      }
    }
  }, [rows, selectedRowIndex, onSymbolSelect, rowVirtualizer]);

  const reorderColumn = useCallback((draggedId: string, targetId: string) => {
    if (draggedId === targetId) return;
    startTransition(() => {
      const currentOrder = table.getState().columnOrder;
      const dragIndex = currentOrder.indexOf(draggedId);
      const targetIndex = currentOrder.indexOf(targetId);
      if (dragIndex !== -1 && targetIndex !== -1) {
        const newOrder = [...currentOrder];
        newOrder.splice(dragIndex, 1);
        newOrder.splice(targetIndex, 0, draggedId);
        setZustandColumnOrder(newOrder);
      }
    });
  }, [table, setZustandColumnOrder, startTransition]);

  const handleSortingChange: OnChangeFn<SortingState> = useCallback((updaterOrValue) => {
    startTransition(() => {
      const newSortingState = typeof updaterOrValue === 'function'
        ? updaterOrValue(table.getState().sorting)
        : updaterOrValue;
      updateZustandSortConfigs(newSortingState);
      if (newSortingState.length > 0) {
        rowVirtualizer.scrollToIndex?.(0, { align: 'start', behavior: 'auto' });
      } else {
        console.warn("handleSortingChange called with empty state despite enableSortingRemoval=false");
      }
    });
  }, [table, updateZustandSortConfigs, startTransition, rowVirtualizer]);

  const handleColumnSizingChange: OnChangeFn<ColumnSizingState> = useCallback((updater) => {
    startTransition(() => {
        const newSizingState = typeof updater === 'function' ? updater(table.getState().columnSizing) : updater;
        const currentZustandSizing = useAppSettingsStore.getState().columnWidths;
        const newFullSizing: ColumnWidths = { ...currentZustandSizing };
        for (const key in newSizingState) {
            if (Object.prototype.hasOwnProperty.call(newSizingState, key) && key !== 'isResizing') {
                const value = newSizingState[key];
                if (typeof value === 'number') {
                    (newFullSizing as any)[key] = value;
                }
            }
        }
        setZustandColumnSizing(newFullSizing);
    });
  }, [table, setZustandColumnSizing, startTransition]);

  useEffect(() => {
    table.setOptions(prev => ({
      ...prev,
      onSortingChange: handleSortingChange,
      onColumnSizingChange: handleColumnSizingChange,
      meta: {
        selectedTickerSymbol: externalSelectedSymbol,
        setSelectedTickerSymbol: onSymbolSelect,
        selectedPairs: selectedPairsFromStore,
        compactnessFactor: compactnessFactor,
        indicatorColumns: indicatorColumns,
        indicatorDataMap: indicatorDataMap,
        indicatorQueryStatusMap: indicatorQueryStatusMap,
      }
    }));
  }, [
    table, handleSortingChange, handleColumnSizingChange,
    externalSelectedSymbol, onSymbolSelect, selectedPairsFromStore,
    compactnessFactor, indicatorColumns,
    indicatorDataMap, indicatorQueryStatusMap,
  ]);

  const renderLoadingSkeletons = () => (
    <div className="w-full h-full flex flex-col justify-start p-1 overflow-hidden">
      {Array.from({ length: SKELETON_ROW_COUNT }).map((_, i) => (
          <div
            key={`skeleton-row-${i}`}
            className="flex items-center shrink-0"
            style={{
            height: `${bodyRowHeight}px`,
            paddingLeft: `${bodyCellPaddingX}px`,
            paddingRight: `${bodyCellPaddingX}px`,
           }}
        >
          <Skeleton className={clsx(
              "h-[60%] w-full",
              isProcessingInteraction && "opacity-50 transition-opacity duration-300"
          )} />
          </div>
        ))}
      </div>
    );

  const renderEmptyState = () => {
    const hasOriginalData = combinedTickers.length > 0;
    let message = "No results found.";
    if (!hasOriginalData && !hasDataEverLoaded) {
        message = "Loading data...";
    } else if (!hasOriginalData) {
      message = "No data available.";
    } else if (searchQuery && finalFilteredData.length === 0) {
      message = `No tickers match '${searchQuery}'.`;
    } else if (finalFilteredData.length === 0) {
      message = "No tickers match current filters.";
    }
    return (
      <div className="absolute inset-0 flex items-center justify-center h-full text-muted-foreground">
        {message}
      </div>
    );
  };

  const renderTableBody = () => {
    const showSkeleton = !hasDataEverLoaded && combinedTickers.length === 0;
    const showEmptyState = !showSkeleton && rows.length === 0;
    const containerHeight = showSkeleton ? '100%' : (rows.length > 0 ? `${totalVirtualHeight}px` : '100%');

    return (
      <div
        ref={parentRef}
        className={clsx(
          "flex-grow relative outline-none h-full overflow-y-auto overflow-x-hidden hide-native-scrollbar",
        )}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="grid"
        aria-rowcount={rows.length}
        style={{ willChange: 'transform' }}
      >
        <div
          className="relative w-full"
          style={{
            minHeight: showSkeleton || showEmptyState ? '300px' : `${totalVirtualHeight}px`,
            height: containerHeight,
          }}
        >
          {showSkeleton ? renderLoadingSkeletons() :
           showEmptyState ? renderEmptyState() :
            virtualItems.map((virtualRow) => {
              const row = rows[virtualRow.index];
              if (!row) {
                console.warn(`TableRowComponent requested for non-existent row at index ${virtualRow.index}`);
                return null;
              }
              const isSelected = row.original.symbol === externalSelectedSymbol;
              return (
                <TableRowComponent
                  key={row.original.symbol}
                  virtualRow={virtualRow}
                  row={row}
                  isSelected={isSelected}
                  bodyCellPaddingX={bodyCellPaddingX}
                  handleRowClick={handleRowClick}
                  selectedPairs={selectedPairsFromStore}
                  indicatorColumns={indicatorColumns}
                  indicatorDataMap={indicatorDataMap}
                  indicatorQueryStatusMap={indicatorQueryStatusMap}
                  renderCell={renderCellContent}
                />
              );
            })
          }
        </div>
      </div>
    );
  };

  useEffect(() => {

    // Update counts for parent component
    onCountsChange({
      total: combinedTickers.length,
      filtered: finalFilteredData.length
    });
    
    // Set hasDataEverLoaded flag when we receive data
    if (combinedTickers.length > 0 && !hasDataEverLoaded) {
      setHasDataEverLoaded(true);
    }
  }, [combinedTickers, finalFilteredData, hasDataEverLoaded]);

  return (
    <div
      className={clsx(
        'h-full w-full flex flex-col overflow-hidden',
        compactClasses,
        'text-sm',
        fontSizeClass,
        fontFamilyClass,
        isProcessingInteraction && 'opacity-80'
      )}
      style={{ minHeight: '100px' }}
    >
      <DndProvider backend={HTML5Backend}>
        <div
          className="shrink-0 flex overflow-hidden"
          style={{
            height: `${headerRowHeight}px`,
            width: `${table.getTotalSize()}px`,
          }}
        >
          {table.getHeaderGroups().map((headerGroup) => (
            <div
              key={headerGroup.id}
              className="flex h-9 border-b border-border"
              style={{ width: `${totalSize}px` }}
            >
              {headerGroup.headers.map((header) => (
                <TableHeaderCell
                  key={header.id}
                  header={header}
                  columnOrder={columnOrder}
                  reorderColumn={reorderColumn}
                />
              ))}
            </div>
          ))}
        </div>
      </DndProvider>
      {renderTableBody()}
    </div>
  );
});

TickerTable.displayName = 'TickerTable';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    selectedTickerSymbol: string | null;
    setSelectedTickerSymbol: (symbol: string) => void;
    selectedPairs: string[];
    compactnessFactor?: number;
    indicatorColumns?: IndicatorColumnConfig[];
    indicatorDataMap?: Map<string, Map<string, any>>;
    indicatorQueryStatusMap?: Map<string, 'pending' | 'success' | 'error'>;
  }
}

--- END FILE: src\features\TickerManagement\Table.tsx ---

--- START FILE: src\features\TickerManagement\useTableData.ts ---
import { useMemo } from 'react';
import { Ticker, MarketType, ProcessedTicker, CalculatedIndicatorResult, IndicatorColumnConfig } from '@/shared/index';
import {
  useAppSettingsStore,
  selectMarketTypes,
  selectShowVolumeInUSD,
  selectMinVolume,
  selectMinTrades,
  selectAvailablePairs,
  selectIndicatorColumns,
} from '@/shared/store/settingsStore';
import {
  useMarketStore,
} from '@/shared/store/marketStore';

// --- Constants for filtering ---
const MIN_ACTIVITY_INTERVAL_MS = 24 * 60 * 60 * 1000; // 24 hours

// --- Helper Functions for Ticker Processing and Filtering ---

/**
 * Filters an array of tickers based on various criteria like market type, selected pairs,
 * volume, trades, and a search query.
 * @param tickers Array of ProcessedTicker objects to filter.
 * @param selectedMarketTypes Array of selected market types.
 * @param selectedPairs Array of selected trading pairs (e.g., 'USDT', 'BTC').
 * @param availablePairs Array of all available quote assets.
 * @param minVolume Minimum volume filter (in millions).
 * @param minTrades Minimum number of trades.
 * @param showVolumeInUSD Boolean indicating if volume should be checked in USD.
 * @param searchQuery Optional search query string.
 * @returns A filtered array of ProcessedTicker objects.
 */
const filterTickers = (
  tickers: ProcessedTicker[],
  selectedMarketTypes: MarketType[],
  selectedPairs: string[],
  availablePairs: string[],
  minVolume: number,
  minTrades: number,
  showVolumeInUSD: boolean,
  searchQuery?: string
): ProcessedTicker[] => {
  if (!tickers) return [];

  const minVolumeThreshold = minVolume * 1e6; // Pre-calculate threshold for volume in millions

  const lowerCaseQuery = searchQuery?.toLowerCase();
  // Ensure selectedPairs is always an array
  const currentSelectedPairs = selectedPairs || [];
  const concreteSelectedPairs = currentSelectedPairs.filter(p => p !== 'OTHR'); // Pairs other than 'OTHR'
  const isOthrSelected = currentSelectedPairs.includes('OTHR'); // Check if 'OTHR' (other pairs) is selected
  const hasConcreteSelection = concreteSelectedPairs.length > 0;
  const hasAnyPairSelection = currentSelectedPairs.length > 0;
  // All available quote assets excluding 'OTHR'
  const allAvailableConcreteQuotes = availablePairs.filter(p => p !== 'OTHR');

  return tickers.filter(ticker => {
    // Filter by search query
    if (lowerCaseQuery && !ticker.symbol.toLowerCase().includes(lowerCaseQuery)) {
      return false;
    }
    // Filter by market type
    if (selectedMarketTypes.length > 0 && !selectedMarketTypes.includes(ticker.marketType)) {
      return false;
    }
    // Filter by selected trading pairs
    if (hasAnyPairSelection) {
      const endsWithSelectedConcrete = hasConcreteSelection && concreteSelectedPairs.some(pair => ticker.symbol.endsWith(pair));
      const endsWithAvailableConcrete = allAvailableConcreteQuotes.some(pair => ticker.symbol.endsWith(pair));
      // Logic for 'OTHR': include if it doesn't end with any available concrete quote
      // Logic for concrete pairs: include if it ends with one of the selected concrete pairs
      const shouldKeep = (isOthrSelected && !endsWithAvailableConcrete) || (hasConcreteSelection && endsWithSelectedConcrete);
      if (!shouldKeep) {
        return false;
      }
    }
    // Filter by minimum volume
    if (minVolume > 0) {
      const volumeToCheck = showVolumeInUSD ? ticker.quoteVolume : ticker.volume;
      if (volumeToCheck == null || volumeToCheck < minVolumeThreshold) {
        return false;
      }
    }
    // Filter by minimum trades
    if (minTrades > 0) {
      const tradesValue = ticker.count;
      if (tradesValue == null || tradesValue < minTrades) {
        return false;
      }
    }
    return true;
  });
};

/**
 * Aggregates volume and trade counts for tickers that exist in both spot and futures markets.
 * If a ticker has a counterpart in the other market type, their volumes and counts are summed.
 * @param tickers Array of ProcessedTicker objects to aggregate.
 * @param spotTickers Array of all spot processed tickers.
 * @param futuresTickers Array of all futures processed tickers.
 * @returns An array of ProcessedTicker objects with aggregated data where applicable.
 */
const aggregateTickers = (
  tickers: ProcessedTicker[],
  spotTickers: ProcessedTicker[],
  futuresTickers: ProcessedTicker[]
): ProcessedTicker[] => {
  return tickers.map(ticker => {
    const { symbol, marketType } = ticker;
    let counterpartTicker: ProcessedTicker | undefined;

    // Find the counterpart in the other market
    if (marketType === 'spot') {
      counterpartTicker = futuresTickers.find(t => t.symbol === symbol);
    } else if (marketType === 'futures') {
      counterpartTicker = spotTickers.find(t => t.symbol === symbol);
    }

    // If no counterpart, return the original ticker
    if (!counterpartTicker) return ticker;

    // Aggregate volume and trades
    return {
      ...ticker,
      volume: (ticker.volume ?? 0) + (counterpartTicker.volume ?? 0),
      quoteVolume: (ticker.quoteVolume ?? 0) + (counterpartTicker.quoteVolume ?? 0),
      count: (ticker.count ?? 0) + (counterpartTicker.count ?? 0),
    };
  });
};

/**
 * Props for the useProcessedTableData hook.
 */
interface UseProcessedTableDataProps {
  searchQuery: string;
  aggregateVolumeAndTrades: boolean;
  selectedPairs?: string[]; // Allow selectedPairs to be initially undefined or an empty array
}

/**
 * Custom hook to process and filter ticker data for the table.
 * It combines spot and futures tickers, filters them based on user settings and search query,
 * optionally aggregates data for symbols present in both markets, and then processes them for display.
 * @param props - The properties for processing table data.
 * @returns An object containing combined raw tickers and the final filtered & processed data.
 */
export const useProcessedTableData = ({
  searchQuery,
  aggregateVolumeAndTrades,
  selectedPairs: propsSelectedPairs 
}: UseProcessedTableDataProps) => {
  // Retrieve raw ticker data from the market store
  const tickers = useMarketStore(state => state.tickers); // Обновлено: теперь tickers - это Record<string, Ticker>

  // Retrieve filter settings from the app settings store
  const selectedMarketTypes = useAppSettingsStore(selectMarketTypes);
  const showVolumeInUSD = useAppSettingsStore(selectShowVolumeInUSD);
  const minVolume = useAppSettingsStore(selectMinVolume);
  const minTrades = useAppSettingsStore(selectMinTrades);
  const availablePairs = useAppSettingsStore(selectAvailablePairs);
  // Use selectedPairs from props if provided, otherwise default to empty array for filtering logic
  const selectedPairsForFilter = propsSelectedPairs ?? [];

  // Преобразуем объект tickers в массив для обработки
  const combinedTickers: ProcessedTicker[] = useMemo(() => 
    Object.values(tickers).map(ticker => {
      // Преобразуем Ticker в ProcessedTicker
      const symbol = ticker.symbol;
      const parts = symbol.match(/^(.+?)([A-Z]{3,}|[1-9A-Z]{4,})$/);
      const baseAsset = parts ? parts[1] : symbol;
      const quoteAsset = parts ? parts[2] : '';
      const priceChangeAbs = ticker.priceChange;

      return {
        ...ticker,
        baseAsset,
        quoteAsset,
        priceChangeAbs,
      };
    })
  , [tickers]);

  // Отдельно выделяем спотовые и фьючерсные тикеры для агрегации
  const spotTickers = useMemo(() => 
    combinedTickers.filter(ticker => ticker.marketType === 'spot')
  , [combinedTickers]);

  const futuresTickers = useMemo(() => 
    combinedTickers.filter(ticker => ticker.marketType === 'futures')
  , [combinedTickers]);

  // Memoized final filtered and processed data
  const finalFilteredData = useMemo(() => {
    // Step 1: Initial filtering based on settings and search query
    const initiallyFiltered = filterTickers(
      combinedTickers,
      selectedMarketTypes,
      selectedPairsForFilter,
      availablePairs,
      minVolume,
      minTrades,
      showVolumeInUSD,
      searchQuery
    );

    // Step 2: Aggregate data if the option is enabled
    const aggregatedOrOriginal: ProcessedTicker[] = aggregateVolumeAndTrades
      ? aggregateTickers(initiallyFiltered, spotTickers, futuresTickers)
      : initiallyFiltered;

    return aggregatedOrOriginal;
  }, [
    combinedTickers,
    spotTickers,
    futuresTickers,
    selectedMarketTypes,
    selectedPairsForFilter,
    availablePairs,
    minVolume,
    minTrades,
    showVolumeInUSD,
    searchQuery,
    aggregateVolumeAndTrades
  ]);

  return { combinedTickers, finalFilteredData };
};

// --- Hook for Indicator Data ---

/**
 * Result type for the useIndicatorDataForTable hook.
 */
interface UseIndicatorDataForTableResult {
  indicatorDataMap: Map<string, Map<string, CalculatedIndicatorResult>>;
  indicatorQueryStatusMap: Map<string, 'pending' | 'success' | 'error'>;
}

// Placeholder for useMarketIndicators hook, which would fetch indicator data.
// This function is a stand-in and needs to be replaced with actual implementation.
// const useMarketIndicators = (indicatorId: string, timeframe: string, marketType: MarketType): {loading: boolean, error: Error | null, indicators: any[]} => ({loading: true, error: null, indicators: []});

/**
 * Custom hook to prepare indicator data for the table.
 * This is currently a placeholder and needs to be implemented to fetch and process
 * indicator data based on selected indicator columns and market types.
 * @returns An object containing maps for indicator data and their query statuses.
 */
export const useIndicatorDataForTable = (): UseIndicatorDataForTableResult => {
  const indicatorColumns = useAppSettingsStore(selectIndicatorColumns);
  const selectedMarketTypes = useAppSettingsStore(selectMarketTypes); 

  // Temporarily return empty maps and pending status as useMarketIndicators is not available or implemented.
  // This section needs to be updated once indicator data fetching is in place.
  const indicatorDataMap = useMemo(() => new Map<string, Map<string, CalculatedIndicatorResult>>(), []);
  const indicatorQueryStatusMap = useMemo(() => new Map<string, 'pending' | 'success' | 'error'>(), []);

  /* 
  // Original logic commented out as useMarketIndicators is not resolved.
  // This demonstrates how it might be structured.
  const indicatorHooks = useMemo(() => {
    const hooks: Array<{
      key: string;
      // hook: ReturnType<typeof useMarketIndicators>; // This would be the actual type
      hook: any; // Temporary type to avoid error with placeholder
    }> = [];
    
    (selectedMarketTypes || []).forEach(marketType => { 
      indicatorColumns.forEach(col => {
        const timeframe = col.timeframe || '1h'; // Default timeframe if not specified
        const key = `${col.indicatorId}_${timeframe}_${marketType}`;
        
        // Example of how a real hook might be used:
        // hooks.push({
        //   key,
        //   hook: useMarketIndicators(col.indicatorId, timeframe, marketType) 
        // });
      });
    });
    
    return hooks;
  }, [indicatorColumns, selectedMarketTypes]);

  const { indicatorDataMap, indicatorQueryStatusMap } = useMemo(() => {
    const dataMap = new Map<string, Map<string, CalculatedIndicatorResult>>();
    const statusMap = new Map<string, 'pending' | 'success' | 'error'> ();

    indicatorHooks.forEach(({ key, hook }) => {
      // Based on the hook's state, populate statusMap and dataMap
      if (hook.loading) {
        statusMap.set(key, 'pending');
      } else if (hook.error) {
        statusMap.set(key, 'error');
      } else {
        statusMap.set(key, 'success');
      }

      if (hook.indicators && hook.indicators.length > 0) {
        const symbolMap = new Map<string, CalculatedIndicatorResult>();
        // Assuming hook.indicators is an array of CalculatedIndicatorResult
        hook.indicators.forEach((item: CalculatedIndicatorResult) => symbolMap.set(item.symbol, item)); 
        dataMap.set(key, symbolMap);
      }
    });

    return { indicatorDataMap: dataMap, indicatorQueryStatusMap: statusMap };
  }, [indicatorHooks]);
  */

  return { indicatorDataMap, indicatorQueryStatusMap };
};

--- END FILE: src\features\TickerManagement\useTableData.ts ---

--- START FILE: src\features\TickerManagement\useTableUIstate.ts ---
// Hooks specific to the TickerManagement feature will go here.
// export {}; // Placeholder - can be removed if other exports exist or added if truly empty.

import { useMemo } from 'react';
// Removed Ticker, MarketType, ProcessedTicker as they are not directly used by useTableStateConfig
// but ColumnWidths is used indirectly via AppSettingsColumnWidths.
import {
  useAppSettingsStore,
  selectSortConfigs,
  selectColumnOrder,
  selectVisibleColumns,
  selectColumnWidths,
  selectUpdateSortConfigs,
  selectSetColumnOrder,
  selectSetColumnWidths,
} from '@/shared/store/settingsStore';
// MarketStore is not used by useTableStateConfig, so it's removed.

// --- Hook for Table State Configuration ---
import {
  SortingState, ColumnOrderState, VisibilityState, ColumnSizingState,
} from '@tanstack/react-table';
import { ColumnWidths as AppSettingsColumnWidths } from '@/shared/index'; // Keep this for type casting

/**
 * Interface for the result of the useTableStateConfig hook.
 * Defines the shape of the table state configuration object.
 */
interface UseTableStateConfigResult {
  tableSortingState: SortingState;
  columnOrder: ColumnOrderState;
  tableVisibilityState: VisibilityState;
  tableSizingState: ColumnSizingState;
  // Zustand setters for updating table state in the global store
  updateZustandSortConfigs: (newSortingState: SortingState) => void;
  setZustandColumnOrder: (newOrder: ColumnOrderState) => void;
  setZustandColumnSizing: (newSizing: AppSettingsColumnWidths) => void;
}

/**
 * Custom hook to manage and retrieve table state configuration from Zustand store.
 * This hook centralizes the logic for accessing and updating table-related settings
 * such as sorting, column order, visibility, and sizing.
 * @returns An object containing the current table state and functions to update it.
 */
export const useTableStateConfig = (): UseTableStateConfigResult => {
  // Selectors for reading current table state from Zustand
  const sortConfigs = useAppSettingsStore(selectSortConfigs);
  const columnOrder = useAppSettingsStore(selectColumnOrder);
  const columnVisibility = useAppSettingsStore(selectVisibleColumns);
  const columnSizing = useAppSettingsStore(selectColumnWidths);

  // Selectors for action functions to update table state in Zustand
  const updateZustandSortConfigs = useAppSettingsStore(selectUpdateSortConfigs);
  const setZustandColumnOrder = useAppSettingsStore(selectSetColumnOrder);
  const zustandSetColumnSizing = useAppSettingsStore(selectSetColumnWidths);

  // Memoized transformation of sortConfigs from store to TanStack Table format
  const tableSortingState = useMemo<SortingState>(() =>
    sortConfigs.map(s => ({
      id: s.id === 'quoteVolumeRaw' ? 'volume' : s.id, // Handle potential legacy ID
      desc: s.desc
    })),
  [sortConfigs]);

  // Memoized transformation of columnVisibility from store to TanStack Table format
  const tableVisibilityState = useMemo<VisibilityState>(() => {
    return Object.entries(columnVisibility).reduce((acc, [key, value]) => {
      if (typeof value === 'boolean') acc[key] = value;
      return acc;
    }, {} as VisibilityState);
  }, [columnVisibility]);

  // Memoized transformation of columnSizing from store to TanStack Table format
  const tableSizingState = useMemo<ColumnSizingState>(() => {
    return Object.entries(columnSizing).reduce((acc, [key, value]) => {
      if (typeof value === 'number') {
        const correctedKey = key === 'quoteVolumeRaw' ? 'volume' : key; // Handle potential legacy ID
        acc[correctedKey] = value;
      }
      return acc;
    }, {} as ColumnSizingState);
  }, [columnSizing]);

  return {
    tableSortingState,
    columnOrder, // Directly use from store, as it's already in ColumnOrderState format
    tableVisibilityState,
    tableSizingState,
    updateZustandSortConfigs, // Pass through Zustand action
    setZustandColumnOrder,    // Pass through Zustand action
    setZustandColumnSizing: zustandSetColumnSizing, // Pass through Zustand action
  };
}; 
--- END FILE: src\features\TickerManagement\useTableUIstate.ts ---

--- START FILE: src\features\TickerSearch\TickerSearch.tsx ---
"use client";

import React, { useState, useEffect } from 'react';
import { Input } from '@/shared/ui/input';
import { Icon } from '@/shared/ui/icons/all_Icon';
import { Button } from '@/shared/ui/button';
import { cn } from '@/shared/lib/utils';

interface TickerSearchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  /** Initial search value */
  initialValue?: string;
  /** Callback when the search value changes */
  onSearchChange: (value: string) => void;
  /** Placeholder text for the input */
  placeholder?: string;
  /** Additional class names */
  className?: string;
  /** Debounce time in milliseconds */
  debounceMs?: number;
   /** Symbol to display when input is empty */
  selectedSymbol?: string | null;
   /** Indicator if filtering is active */
  isFiltering?: boolean;
}

const TickerSearch: React.FC<TickerSearchProps> = ({ 
  initialValue = '', 
  onSearchChange, 
  placeholder = "", 
  className, 
  debounceMs = 300,
  selectedSymbol,
  isFiltering,
  ...props 
}) => {
  const [localSearch, setLocalSearch] = useState(initialValue);
  const [hasFocus, setHasFocus] = useState(false);

  // Debounce effect
  useEffect(() => {
    const handler = setTimeout(() => {
      onSearchChange(localSearch);
    }, debounceMs);
    return () => clearTimeout(handler);
  }, [localSearch, onSearchChange, debounceMs]);

  // Update local state if initialValue changes externally (e.g., reset)
  useEffect(() => {
    setLocalSearch(initialValue);
  }, [initialValue]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearch(event.target.value.toUpperCase()); // Update local state immediately
  };

  const handleClearSearch = () => {
    setLocalSearch(''); // Clear local state, useEffect will trigger onSearchChange
  };

  const handleBlur = () => {
    setHasFocus(false);
  };

  return (
    <div className={cn(
      "relative w-full border border-input rounded-md",
      "h-full",
      className
      // Removed focus:outline-none as it might interfere with standard input focus behavior if needed later
      )}>
      <span className={cn(
        "absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground flex items-center justify-center pointer-events-none z-10",
        isFiltering && 'animate-pulse' // Keep pulse animation if desired
      )}>
         <Icon name="Search" className="h-4 w-4" />
      </span>
      {/* Show selected symbol only if input is empty and symbol exists */}
      {selectedSymbol && !localSearch && (
        <span className="absolute left-8 top-1/2 -translate-y-1/2 text-muted-foreground text-xs font-medium whitespace-nowrap cursor-default z-0 pointer-events-none select-none">
          {selectedSymbol}
        </span>
      )}
      <Input
        type="text"
        value={localSearch}
        onChange={handleInputChange}
        onFocus={() => setHasFocus(true)}
        onBlur={handleBlur}
        placeholder={placeholder}
        className={cn(
          // Remove default h-10, make input fill container height, adjust padding, remove border, set leading
          'h-full w-full rounded-md border-0 bg-background pl-8 pr-8 text-sm outline-none placeholder:text-muted-foreground py-0 leading-none', 
          isFiltering && 'text-primary', // Highlight input text when filtering
          'ring-0 focus:ring-0 focus-visible:ring-0 focus:outline-none focus-visible:outline-none' 
        )}
        {...props}
      />
      {(localSearch || hasFocus || isFiltering) && (
        <button
          className="absolute inset-y-0 right-2 flex items-center"
          onClick={handleClearSearch}
          aria-label="Clear search"
        >
          <Icon name="X" />
        </button>
      )}
    </div>
  );
};

export default TickerSearch; 
--- END FILE: src\features\TickerSearch\TickerSearch.tsx ---

--- START FILE: src\server\adapters\binance.adapter.ts ---
import {
  MarketType,
  ExchangeTickerData,
  ExchangeKlineData,
  ExchangeSymbolData,
  CoreTicker,
  CoreKline,
  CoreSymbol,
} from '@/shared/index';
import { BaseExchangeAdapter } from './exchange.adapter';

/**
 * Adapter for Binance exchange data
 * Converts Binance API responses to our internal data format
 */
export class BinanceAdapter extends BaseExchangeAdapter {
  readonly exchangeName = 'binance';
  
  /**
   * Convert Binance ticker data to core ticker format
   * @param data Raw ticker data from Binance API
   * @param marketType Market type (spot or futures)
   * @returns Standardized core ticker object
   */
  adaptTicker(data: ExchangeTickerData, marketType: MarketType): CoreTicker {
    return {
      // Required fields
      symbol: data.symbol,
      marketType,
      price: this.safeParseFloat(data.lastPrice),
      timestamp: data.closeTime || Date.now(),
      
      // Optional fields with standardized names
      change: this.safeParseFloat(data.priceChange),
      changePercent: this.safeParseFloat(data.priceChangePercent),
      volume: this.safeParseFloat(data.volume),
      quoteVolume: this.safeParseFloat(data.quoteVolume),
      high: this.safeParseFloat(data.highPrice),
      low: this.safeParseFloat(data.lowPrice),
      count: data.count || 0,
      
      // Additional fields that might be available
      open: this.safeParseFloat(data.openPrice),
      weightedAvgPrice: this.safeParseFloat(data.weightedAvgPrice),
      prevClose: this.safeParseFloat(data.prevClosePrice),
      bidPrice: this.safeParseFloat(data.bidPrice),
      bidQty: this.safeParseFloat(data.bidQty),
      askPrice: this.safeParseFloat(data.askPrice),
      askQty: this.safeParseFloat(data.askQty),
      firstTradeId: data.firstId,
      lastTradeId: data.lastId,
    };
  }
  
  /**
   * Convert Binance kline data to core kline format
   * @param data Raw kline data from Binance API
   * @param marketType Market type (spot or futures)
   * @param interval Kline interval
   * @returns Standardized core kline object
   */
  adaptKline(data: ExchangeKlineData, marketType: MarketType, interval: string): CoreKline {
    // Handle array format from Binance API
    if (Array.isArray(data)) {
      return {
        symbol: '', // Symbol must be provided separately for array format
        marketType,
        interval,
        openTime: data[0] as number,
        open: this.safeParseFloat(data[1]),
        high: this.safeParseFloat(data[2]),
        low: this.safeParseFloat(data[3]),
        close: this.safeParseFloat(data[4]),
        volume: this.safeParseFloat(data[5]),
        closeTime: data[6] as number,
        quoteVolume: this.safeParseFloat(data[7]),
        trades: data[8] as number,
        isClosed: true, // Historical klines are always closed
      };
    }
    
    // Handle object format (usually from WebSocket)
    if (data.k) {
      const k = data.k;
      return {
        symbol: data.s || '',
        marketType,
        interval: k.i || interval,
        openTime: k.t,
        open: this.safeParseFloat(k.o),
        high: this.safeParseFloat(k.h),
        low: this.safeParseFloat(k.l),
        close: this.safeParseFloat(k.c),
        volume: this.safeParseFloat(k.v),
        closeTime: k.T,
        quoteVolume: this.safeParseFloat(k.q),
        trades: k.n,
        isClosed: k.x,
      };
    }
    
    // Fallback for other formats
    return {
      symbol: data.symbol || '',
      marketType,
      interval,
      openTime: data.openTime || 0,
      open: this.safeParseFloat(data.open),
      high: this.safeParseFloat(data.high),
      low: this.safeParseFloat(data.low),
      close: this.safeParseFloat(data.close),
      volume: this.safeParseFloat(data.volume),
      closeTime: data.closeTime || 0,
      quoteVolume: this.safeParseFloat(data.quoteVolume),
      trades: data.trades,
      isClosed: data.isClosed !== false, // Default to true
    };
  }
  
  /**
   * Convert Binance symbol data to core symbol format
   * @param data Raw symbol data from Binance API
   * @param marketType Market type (spot or futures)
   * @returns Standardized core symbol object
   */
  adaptSymbol(data: ExchangeSymbolData, marketType: MarketType): CoreSymbol {
    return {
      symbol: data.symbol,
      marketType,
      baseAsset: data.baseAsset,
      quoteAsset: data.quoteAsset,
      status: data.status,
      pricePrecision: data.pricePrecision || data.quotePrecision || 0,
      quantityPrecision: data.quantityPrecision || data.baseAssetPrecision || 0,
      isSpotTradingAllowed: data.isSpotTradingAllowed !== false,
      isMarginTradingAllowed: !!data.isMarginTradingAllowed,
    };
  }
}

// Export singleton instance
export const binanceAdapter = new BinanceAdapter(); 
--- END FILE: src\server\adapters\binance.adapter.ts ---

--- START FILE: src\server\adapters\exchange.adapter.ts ---
import {
  MarketType,
  ExchangeTickerData,
  ExchangeKlineData,
  ExchangeSymbolData,
  CoreTicker,
  CoreKline,
  CoreSymbol,
  ExchangeName
} from '@/shared/index';

/**
 * Base adapter interface for exchange data
 * All exchange-specific adapters should implement this interface
 */
export interface ExchangeAdapter {
  /**
   * Name of the exchange
   */
  readonly exchangeName: ExchangeName;
  
  /**
   * Convert exchange ticker data to core ticker format
   */
  adaptTicker(data: ExchangeTickerData, marketType: MarketType): CoreTicker;
  
  /**
   * Convert exchange kline data to core kline format
   */
  adaptKline(data: ExchangeKlineData, marketType: MarketType, interval: string): CoreKline;
  
  /**
   * Convert exchange symbol data to core symbol format
   */
  adaptSymbol(data: ExchangeSymbolData, marketType: MarketType): CoreSymbol;
  
  /**
   * Convert exchange ticker data array to core ticker format array
   */
  adaptTickers(data: ExchangeTickerData[], marketType: MarketType): CoreTicker[];
  
  /**
   * Convert exchange kline data array to core kline format array
   */
  adaptKlines(data: ExchangeKlineData[], marketType: MarketType, interval: string): CoreKline[];
  
  /**
   * Convert exchange symbol data array to core symbol format array
   */
  adaptSymbols(data: ExchangeSymbolData[], marketType: MarketType): CoreSymbol[];
}

/**
 * Abstract base class for exchange adapters
 * Provides default implementations for array conversions
 */
export abstract class BaseExchangeAdapter implements ExchangeAdapter {
  abstract readonly exchangeName: ExchangeName;
  
  /**
   * Convert a single ticker from exchange format to core format
   * Must be implemented by specific exchange adapters
   */
  abstract adaptTicker(data: ExchangeTickerData, marketType: MarketType): CoreTicker;
  
  /**
   * Convert a single kline from exchange format to core format
   * Must be implemented by specific exchange adapters
   */
  abstract adaptKline(data: ExchangeKlineData, marketType: MarketType, interval: string): CoreKline;
  
  /**
   * Convert a single symbol from exchange format to core format
   * Must be implemented by specific exchange adapters
   */
  abstract adaptSymbol(data: ExchangeSymbolData, marketType: MarketType): CoreSymbol;
  
  /**
   * Convert an array of tickers from exchange format to core format
   * Uses the single ticker adapter internally
   */
  adaptTickers(data: ExchangeTickerData[], marketType: MarketType): CoreTicker[] {
    return data.map(item => this.adaptTicker(item, marketType));
  }
  
  /**
   * Convert an array of klines from exchange format to core format
   * Uses the single kline adapter internally
   */
  adaptKlines(data: ExchangeKlineData[], marketType: MarketType, interval: string): CoreKline[] {
    return data.map(item => this.adaptKline(item, marketType, interval));
  }
  
  /**
   * Convert an array of symbols from exchange format to core format
   * Uses the single symbol adapter internally
   */
  adaptSymbols(data: ExchangeSymbolData[], marketType: MarketType): CoreSymbol[] {
    return data.map(item => this.adaptSymbol(item, marketType));
  }
  
  /**
   * Safely parse a string to number, returning 0 if invalid
   */
  protected safeParseFloat(value: any): number {
    if (value === undefined || value === null) return 0;
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }
  
  /**
   * Safely convert a value to number, with a default fallback
   */
  protected toNumber(value: any, defaultValue: number = 0): number {
    if (value === undefined || value === null) return defaultValue;
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? defaultValue : parsed;
    }
    return defaultValue;
  }
} 
--- END FILE: src\server\adapters\exchange.adapter.ts ---

--- START FILE: src\server\api\v1\klineRoutes.ts ---
import { FastifyInstance } from 'fastify';
import { getKlines, KlineParams } from '@/server/services/klineService.js';
import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { MarketTypeSchema } from '@/shared/schemas/market.schema';

// Zod schema for kline query parameters validation
const GetKlinesSchema = z.object({
  symbol: z.string({ description: 'The trading symbol (e.g., BTCUSDT)' }),
  interval: z.string({ description: 'The interval of klines (e.g., 1m, 1h, 1d)' }),
  marketType: MarketTypeSchema,
  limit: z.coerce.number().int().positive().optional().default(1000).describe('Number of klines to retrieve'),
  endTime: z.coerce.number().int().positive().optional().describe('End time for historical data'),
});

type GetKlinesQuery = z.infer<typeof GetKlinesSchema>;

// Convert Zod schema to JSON schema for Fastify
const GetKlinesJsonSchema = zodToJsonSchema(GetKlinesSchema, 'GetKlinesSchema');

/**
 * @description Registers the kline routes.
 * @param {FastifyInstance} fastify - The Fastify instance.
 */
export async function klineRoutes(fastify: FastifyInstance) {
  fastify.route<{ Querystring: GetKlinesQuery }>({
    method: 'GET',
    url: '/klines',
    schema: {
      querystring: GetKlinesJsonSchema,
      response: {
        200: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              symbol: { type: 'string' },
              interval: { type: 'string' },
              marketType: { type: 'string' },
              openTime: { type: 'number' },
              open: { type: 'string' },
              high: { type: 'string' },
              low: { type: 'string' },
              close: { type: 'string' },
              volume: { type: 'string' },
            },
            required: ['openTime', 'open', 'high', 'low', 'close', 'volume'],
          },
        },
      },
    },
    handler: async (request, reply) => {
      try {
        const klineParams: KlineParams = request.query;
        const klines = await getKlines(klineParams);
        
        // Serialize numbers to strings for the API response, matching the schema.
        const serializedKlines = klines.map(kline => ({
          ...kline,
          open: kline.open?.toString(),
          high: kline.high?.toString(),
          low: kline.low?.toString(),
          close: kline.close?.toString(),
          volume: kline.volume?.toString(),
        }));

        return reply.send(serializedKlines);
      } catch (error) {
        request.log.error(error, 'Failed to get klines');
        reply.status(500).send({ error: 'Internal Server Error' });
      }
    },
  });
} 
--- END FILE: src\server\api\v1\klineRoutes.ts ---

--- START FILE: src\server\api\v1\tickerRoutes.ts ---
import { FastifyInstance } from 'fastify';
import { getTickers, TickerQueryParams } from '@/server/services/tickerService.js';
import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';
import { MarketTypeSchema } from '@/shared/schemas/market.schema.js';

// Zod schema for ticker query parameters validation
const GetTickersSchema = z.object({
  marketTypes: z.array(MarketTypeSchema).optional()
    .describe('Market types to filter by (e.g., "spot", "futures")'),
  quoteAssets: z.array(z.string()).optional()
    .describe('Quote assets to filter by (e.g., "USDT", "BTC")'),
  minVolume: z.coerce.number().positive().optional()
    .describe('Minimum volume filter'),
  minTrades: z.coerce.number().int().positive().optional()
    .describe('Minimum number of trades filter'),
  sortBy: z.string().optional()
    .describe('Field to sort by (e.g., "volume", "priceChangePercent")'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
    .describe('Sort order (asc or desc)'),
  limit: z.coerce.number().int().positive().optional().default(100)
    .describe('Number of tickers to retrieve'),
  offset: z.coerce.number().int().nonnegative().optional().default(0)
    .describe('Number of tickers to skip'),
  searchQuery: z.string().optional()
    .describe('Text to search for in symbol names'),
});

type GetTickersQuery = z.infer<typeof GetTickersSchema>;

// Convert Zod schema to JSON schema for Fastify
const GetTickersJsonSchema = zodToJsonSchema(GetTickersSchema, 'GetTickersSchema');

/**
 * @description Registers the ticker routes.
 * @param {FastifyInstance} fastify - The Fastify instance.
 */
export async function tickerRoutes(fastify: FastifyInstance) {
  /**
   * @api {get} /api/v1/tickers Get all tickers
   * @apiName GetTickers
   * @apiGroup Tickers
   * @apiDescription Get tickers with filtering, sorting and pagination
   * 
   * @apiQuery {String[]} [marketTypes] Market types to filter by (e.g., "spot", "futures")
   * @apiQuery {String[]} [quoteAssets] Quote assets to filter by (e.g., "USDT", "BTC")
   * @apiQuery {Number} [minVolume] Minimum volume filter
   * @apiQuery {Number} [minTrades] Minimum number of trades filter
   * @apiQuery {String} [sortBy] Sort by field
   * @apiQuery {String} [sortOrder=desc] Sort order ("asc" or "desc")
   * @apiQuery {Number} [limit=100] Limit number of results
   * @apiQuery {Number} [offset=0] Offset for pagination
   * @apiQuery {String} [searchQuery] Search by symbol name
   * 
   * @apiSuccess {Object[]} tickers List of tickers
   * 
   * @apiNote Current implementation uses in-memory filtering and sorting,
   * which is suitable for development but not for large datasets.
   * This should be optimized to use database queries when the data volume grows.
   */
  fastify.route<{ Querystring: GetTickersQuery }>({
    method: 'GET',
    url: '/tickers',
    schema: {
      querystring: GetTickersJsonSchema,
      response: {
        200: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              symbol: { type: 'string' },
              marketType: { type: 'string' },
              lastPrice: { type: 'number' },
              priceChange: { type: 'number' },
              priceChangePercent: { type: 'number' },
              highPrice: { type: 'number' },
              lowPrice: { type: 'number' },
              volume: { type: 'number' },
              quoteVolume: { type: 'number' },
              count: { type: 'number' },
              lastUpdated: { type: 'number' },
              // Additional fields that might be useful for the client
              openTime: { type: 'number' },
              closeTime: { type: 'number' },
              weightedAvgPrice: { type: 'number' },
              prevClosePrice: { type: 'number' },
              bidPrice: { type: 'number' },
              bidQty: { type: 'number' },
              askPrice: { type: 'number' },
              askQty: { type: 'number' },
            },
            required: ['symbol', 'marketType', 'lastPrice', 'priceChangePercent', 'volume', 'lastUpdated'],
          },
        },
      },
    },
    handler: async (request, reply) => {
      try {
        const params: TickerQueryParams = request.query;
        const tickers = await getTickers(params);
        
        return reply.send(tickers);
      } catch (error) {
        request.log.error(error, 'Failed to get tickers');
        return reply.status(500).send({ error: 'Internal Server Error' });
      }
    },
  });
} 
--- END FILE: src\server\api\v1\tickerRoutes.ts ---

--- START FILE: src\server\api\v1\websocketRoutes.ts ---
import { FastifyInstance } from 'fastify';
import { WebSocketManager } from '../../services/webSocketService.js';
import websocket from '@fastify/websocket';

// WebSocket маршруты
export async function websocketRoutes(fastify: FastifyInstance) {
  // Создание синглтона WebSocketManager
  const wsManager = WebSocketManager.getInstance();

  // Основной WebSocket эндпоинт для рыночных данных
  fastify.get('/market', { websocket: true }, (connection, req) => {
    fastify.log.info({ clientId: req.id }, 'WebSocket client connected');
    
    // Fastify WebSocket предоставляет connection с полем socket,
    // что соответствует ожидаемому типу SocketStream в WebSocketManager
    wsManager.handleConnection(connection, req);
  });
} 
--- END FILE: src\server\api\v1\websocketRoutes.ts ---

--- START FILE: src\server\lib\redis.ts ---
import { Redis } from 'ioredis';
import { pino } from 'pino';

const logger = pino({ name: 'redis', level: 'info' });

const REDIS_HOST = process.env.REDIS_HOST || 'localhost';
const REDIS_PORT = process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT, 10) : 6379;
const REDIS_PASSWORD = process.env.REDIS_PASSWORD;
const REDIS_DB = process.env.REDIS_DB ? parseInt(process.env.REDIS_DB, 10) : 0;

const redisConfig = {
  host: REDIS_HOST,
  port: REDIS_PORT,
  password: REDIS_PASSWORD || undefined,
  db: REDIS_DB,
  maxRetriesPerRequest: 3,
  retryStrategy: (times: number) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  },
};

// Main Redis client for commands (GET, SET, etc.)
export const redisClient = new Redis(redisConfig);

// Separate client for subscription (blocking)
export const redisPubSub = new Redis(redisConfig);

// Connect to Redis and setup event handlers
export async function connectRedis(): Promise<void> {
  try {
    // Setup event handlers
    redisClient.on('error', (err: Error) => {
      logger.error({ err }, 'Redis client error');
    });

    redisClient.on('connect', () => {
      logger.info('Redis client connected');
    });

    redisPubSub.on('error', (err: Error) => {
      logger.error({ err }, 'Redis PubSub client error');
    });

    redisPubSub.on('connect', () => {
      logger.info('Redis PubSub client connected');
    });

    // Return a promise that resolves when both clients are ready
    await Promise.all([
      new Promise<void>((resolve) => {
        redisClient.once('ready', () => {
          logger.info('Redis client ready');
          resolve();
        });
      }),
      new Promise<void>((resolve) => {
        redisPubSub.once('ready', () => {
          logger.info('Redis PubSub client ready');
          resolve();
        });
      }),
    ]);

    logger.info('Redis clients successfully initialized');
  } catch (err) {
    logger.error({ err }, 'Failed to connect to Redis');
    throw err;
  }
}

// Cache helper functions
export async function getFromCache<T = unknown>(
  key: string,
  ttl: number = 300
): Promise<T | null> {
  try {
    const data = await redisClient.get(key);
    if (!data) return null;
    return JSON.parse(data) as T;
  } catch (err) {
    logger.error({ err, key }, 'Error getting data from cache');
    return null;
  }
}

export async function setInCache(
  key: string,
  value: unknown,
  ttlSeconds: number = 300
): Promise<boolean> {
  try {
    await redisClient.set(key, JSON.stringify(value), 'EX', ttlSeconds);
    return true;
  } catch (err) {
    logger.error({ err, key }, 'Error setting data in cache');
    return false;
  }
}

// PubSub pattern functions
export async function publishMessage(channel: string, message: unknown): Promise<void> {
  try {
    await redisClient.publish(channel, JSON.stringify(message));
  } catch (err) {
    logger.error({ err, channel }, 'Error publishing message');
    throw err; // Re-throw to allow caller to handle the error
  }
}

// Function to subscribe to a channel and register a callback
export function subscribeToChannel(channel: string, callback: (message: unknown) => void): void {
  try {
    redisPubSub.subscribe(channel).then(() => {
      logger.info({ channel }, 'Subscribed to channel');
    }).catch((err) => {
      logger.error({ err, channel }, 'Error subscribing to channel');
    });

    // Handle message events
    redisPubSub.on('message', (subscribedChannel: string, message: string) => {
      if (subscribedChannel === channel) {
        try {
          const parsed = JSON.parse(message);
          callback(parsed);
        } catch (parseErr) {
          logger.error({ err: parseErr, channel }, 'Error parsing message from channel');
        }
      }
    });
  } catch (subscribeErr) {
    logger.error({ err: subscribeErr, channel }, 'Error in subscription setup');
  }
}

// Function to unsubscribe from a channel
export function unsubscribeFromChannel(channel: string): void {
  try {
    redisPubSub.unsubscribe(channel);
    logger.info({ channel }, 'Unsubscribed from channel');
  } catch (err) {
    logger.error({ err, channel }, 'Error unsubscribing from channel');
  }
}

// Close Redis clients
export async function closeRedisConnections(): Promise<void> {
  try {
    await redisClient.quit();
    await redisPubSub.quit();
    logger.info('Redis connections closed');
  } catch (err) {
    logger.error({ err }, 'Error closing Redis connections');
    // Force disconnect if quit fails
    redisClient.disconnect();
    redisPubSub.disconnect();
  }
} 
--- END FILE: src\server\lib\redis.ts ---

--- START FILE: src\server\main.ts ---
import Fastify, { FastifyRequest, FastifyReply } from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import fastifyWebsocket from '@fastify/websocket';
import { pino } from 'pino';
import { klineRoutes } from './api/v1/klineRoutes.js';
import { tickerRoutes } from './api/v1/tickerRoutes.js';
import { websocketRoutes } from './api/v1/websocketRoutes.js';
import { connectRedis, closeRedisConnections } from './lib/redis.js';
import { exchangeDataCollector } from './services/exchangeDataCollectorService.js';
import { initializeDataCollection, shutdownDataCollection } from './services/dataCollector.js';

// Initialize Fastify app
const fastify = Fastify({
  logger: {
    level: 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname',
      },
    },
  },
});

// Register essential plugins
fastify.register(helmet);
fastify.register(cors, {
  // Configure CORS options here
  origin: '*', // Be more specific in production
});

// Register WebSocket support
fastify.register(fastifyWebsocket, {
  options: { 
    maxPayload: 1048576, // 1MB
  }
});

// Define a simple health check route
fastify.get('/', async (request: FastifyRequest, reply: FastifyReply) => {
  return { status: 'ok' };
});

// Register API routes
fastify.register(klineRoutes, { prefix: '/api/v1' });
fastify.register(tickerRoutes, { prefix: '/api/v1' });
fastify.register(websocketRoutes, { prefix: '/ws' });

// Function to start the server
const start = async () => {
  try {
    // Initialize Redis connection
    await connectRedis();
    
    // Initialize Exchange Data Collector
    await exchangeDataCollector.initialize();
    
    // Initialize Data Collection from exchanges
    await initializeDataCollection();
    
    // Listen on port 3000 by default, or from environment variable
    await fastify.listen({ port: process.env.PORT ? parseInt(process.env.PORT, 10) : 3000, host: '0.0.0.0' });
    fastify.log.info(`Server listening on ${fastify.server.address()}`);
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

// Handle graceful shutdown
const shutdown = async (signal: string) => {
  fastify.log.info(`Received ${signal}, shutting down gracefully...`);
  
  try {
    // Stop Data Collection
    await shutdownDataCollection();
    
    // Shutdown Exchange Data Collector
    await exchangeDataCollector.shutdown();
    
    // Close Fastify server
    await fastify.close();
    
    // Close Redis connections
    await closeRedisConnections();
    
    fastify.log.info('Server shutdown complete');
    process.exit(0);
  } catch (err) {
    fastify.log.error(err, 'Error during shutdown');
    process.exit(1);
  }
};

// Register shutdown handlers
process.on('SIGTERM', () => shutdown('SIGTERM'));
process.on('SIGINT', () => shutdown('SIGINT'));

start();

--- END FILE: src\server\main.ts ---

--- START FILE: src\server\services\binance.service.ts ---
import { EventEmitter } from 'events';
import pino from 'pino';
import WebSocket from 'ws';
import { z } from 'zod';

import {
  ExchangeService,
  SymbolInfo,
  KlineOptions,
  KlineEvent,
  TickerEvent,
} from './exchange.interface';
import {
  MarketType,
  CoreTicker,
  CoreKline,
  KlineIntervalSchema,
} from '@/shared/index';
import { binanceAdapter } from '../adapters/binance.adapter';

// Local logger for this service
const logger = pino({ name: 'BinanceService', level: process.env.LOG_LEVEL || 'info' });

// Binance API configuration
const API_CONFIG = {
  baseUrls: {
    spot: 'https://api.binance.com',
    futures: 'https://fapi.binance.com',
  },
  wsUrls: {
    spot: 'wss://stream.binance.com:9443',
    futures: 'wss://fstream.binance.com',
  },
};

// Supported intervals on Binance for validation
const SUPPORTED_INTERVALS = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M'];

// WebSocket connection states
const WebSocketState = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
};

type MarketTypeConfig = typeof API_CONFIG.baseUrls;

/**
 * Implements the ExchangeService interface for Binance.
 * Handles both REST API calls for historical data and WebSocket connections
 * for real-time data streams. It emits standardized events for data updates.
 */
export class BinanceService extends EventEmitter implements ExchangeService {
  public readonly exchangeName = 'binance';
  private static instance: BinanceService;

  private wsConnections: Record<MarketType, WebSocket | null> = {
    [MarketType.Spot]: null,
    [MarketType.Futures]: null,
  };
  private connecting: Record<MarketType, boolean> = {
    [MarketType.Spot]: false,
    [MarketType.Futures]: false,
  };
  private reconnectTimers: Record<MarketType, NodeJS.Timeout | null> = {
    [MarketType.Spot]: null,
    [MarketType.Futures]: null,
  };
  private reconnectAttempts: Record<MarketType, number> = {
    [MarketType.Spot]: 0,
    [MarketType.Futures]: 0,
  };
  
  // Tracks active subscriptions by stream name and their reference count
  private activeSubscriptions: Map<string, { marketType: MarketType; count: number }> = new Map();

  private constructor() {
    super();
    // Increase max listeners to handle multiple subscriptions
    this.setMaxListeners(100);
  }

  /**
   * Gets the singleton instance of the BinanceService.
   */
  public static getInstance(): BinanceService {
    if (!BinanceService.instance) {
      BinanceService.instance = new BinanceService();
    }
    return BinanceService.instance;
  }

  /**
   * Initializes WebSocket connections to the exchange.
   */
  public async initialize(): Promise<void> {
    logger.info('Initializing BinanceService...');
    await Promise.all([this.connect(MarketType.Spot), this.connect(MarketType.Futures)]);
    logger.info('BinanceService initialized successfully.');
  }

  /**
   * Closes all connections and cleans up resources.
   */
  public async shutdown(): Promise<void> {
    logger.info('Shutting down BinanceService...');
    // Clear all event listeners to prevent memory leaks
    this.removeAllListeners();
    await Promise.all([this.disconnect(MarketType.Spot), this.disconnect(MarketType.Futures)]);
    // Clear any pending reconnect timers
    Object.values(this.reconnectTimers).forEach(timer => timer && clearTimeout(timer));
    logger.info('BinanceService shut down successfully.');
  }

  // --- REST API Methods ---

  public async fetchSymbols(marketType: MarketType): Promise<SymbolInfo[]> {
    const url = marketType === MarketType.Spot 
      ? `${API_CONFIG.baseUrls.spot}/api/v3/exchangeInfo`
      : `${API_CONFIG.baseUrls.futures}/fapi/v1/exchangeInfo`;
    
    const response = await this.fetchAPI(url);
    
    // Zod schema for validation
    const SymbolSchema = z.object({
      symbol: z.string(),
      status: z.string(),
      baseAsset: z.string(),
      quoteAsset: z.string(),
    });

    const ExchangeInfoSchema = z.object({
      symbols: z.array(SymbolSchema),
    });

    const parsed = ExchangeInfoSchema.safeParse(response);
    if (!parsed.success) {
      logger.error({ error: parsed.error }, `Failed to parse symbols for ${marketType}`);
      throw new Error(`Invalid symbols data received from exchange for ${marketType}.`);
    }

    return parsed.data.symbols
      .filter(s => s.status === 'TRADING')
      .map(s => binanceAdapter.adaptSymbol(s, marketType));
  }

  public async fetchTickers(marketType: MarketType): Promise<CoreTicker[]> {
    const url = marketType === MarketType.Spot
      ? `${API_CONFIG.baseUrls.spot}/api/v3/ticker/24hr`
      : `${API_CONFIG.baseUrls.futures}/fapi/v1/ticker/24hr`;

    try {
      const response = await this.fetchAPI(url);
      
      if (!Array.isArray(response)) {
        logger.error(`Invalid response format for ${marketType} tickers: expected array`);
        throw new Error(`Invalid response format for ${marketType} tickers`);
      }
      
      // Use the adapter to convert Binance data to our internal format
      const tickers = binanceAdapter.adaptTickers(response, marketType);
      
      logger.debug(`Successfully fetched ${tickers.length} ${marketType} tickers`);
      return tickers;
    } catch (error) {
      logger.error({ error }, `Failed to fetch ${marketType} tickers`);
      throw new Error(`Failed to fetch ${marketType} tickers: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async fetchKlines(
    marketType: MarketType,
    symbol: string,
    interval: string,
    options: KlineOptions = { limit: 500 }
  ): Promise<CoreKline[]> {
    const parsedInterval = KlineIntervalSchema.safeParse(interval);
    if (!parsedInterval.success) {
      throw new Error(`Invalid interval provided to fetchKlines: ${interval}`);
    }

    const baseUrl = API_CONFIG.baseUrls[marketType];
    const endpoint = marketType === MarketType.Spot ? '/api/v3/klines' : '/fapi/v1/klines';
    const url = new URL(endpoint, baseUrl);
    
    url.searchParams.append('symbol', symbol);
    url.searchParams.append('interval', interval);
    if (options.limit) url.searchParams.append('limit', String(options.limit));
    if (options.startTime) url.searchParams.append('startTime', String(options.startTime));
    if (options.endTime) url.searchParams.append('endTime', String(options.endTime));

    const response = await this.fetchAPI(url.toString());
    
    const KlineSchema = z.tuple([
      z.number(), // Open time
      z.string(), // Open
      z.string(), // High
      z.string(), // Low
      z.string(), // Close
      z.string(), // Volume
      z.number(), // Close time
      z.string(), // Quote asset volume
      z.number(), // Number of trades
      z.string(), // Taker buy base asset volume
      z.string(), // Taker buy quote asset volume
      z.string(), // Ignore
    ]);

    const KlinesSchema = z.array(KlineSchema);
    const parsed = KlinesSchema.safeParse(response);
    
    if (!parsed.success) {
      logger.error({ error: parsed.error }, `Failed to parse klines for ${symbol}`);
      throw new Error(`Invalid kline data received for ${symbol}.`);
    }

    return binanceAdapter.adaptKlines(parsed.data, marketType, interval);
  }

  // --- WebSocket Subscription Methods ---

  public subscribeTickers(marketType: MarketType): () => void {
    const streamName = '!ticker@arr';
    return this.manageSubscription(marketType, streamName);
  }

  public subscribeKline(marketType: MarketType, symbol: string, interval: string): () => void {
    if (!SUPPORTED_INTERVALS.includes(interval)) {
      const errorMsg = `Unsupported interval: ${interval}. Cannot subscribe.`;
      logger.warn({ marketType, symbol, interval }, errorMsg);
      // Return a no-op function for invalid subscriptions
      return () => {}; 
    }
    const streamName = `${symbol.toLowerCase()}@kline_${interval}`;
    return this.manageSubscription(marketType, streamName);
  }

  // --- Private Helper Methods ---

  /**
   * Generic fetch wrapper for Binance REST API.
   */
  private async fetchAPI(url: string): Promise<any> {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
      }
      return await response.json();
    } catch (error) {
      logger.error({ error, url }, 'API call failed');
      throw error;
    }
  }

  /**
   * Manages the logic for subscribing and returning an unsubscribe function.
   */
  private manageSubscription(marketType: MarketType, streamName: string): () => void {
    this.subscribeToStream(marketType, streamName);
    
    // Return a function that can be called to unsubscribe
    return () => {
      this.unsubscribeFromStream(marketType, streamName);
    };
  }

  // --- Internal WebSocket Connection and Subscription Management ---
  // (Adapted from ExchangeDataCollectorService)

  private subscribeToStream(marketType: MarketType, streamName: string): void {
    const sub = this.activeSubscriptions.get(streamName) || { marketType, count: 0 };
    sub.count++;
    this.activeSubscriptions.set(streamName, sub);
    
    // Only send subscribe message on first subscription
    if (sub.count === 1) {
      this.sendSubscriptionMessage(marketType, [streamName], 'SUBSCRIBE');
    }
  }

  private unsubscribeFromStream(marketType: MarketType, streamName: string): void {
    const sub = this.activeSubscriptions.get(streamName);
    if (!sub) return;

    sub.count--;
    if (sub.count <= 0) {
      this.activeSubscriptions.delete(streamName);
      this.sendSubscriptionMessage(marketType, [streamName], 'UNSUBSCRIBE');
    } else {
      this.activeSubscriptions.set(streamName, sub);
    }
  }

  private sendSubscriptionMessage(
    marketType: MarketType, 
    streams: string[],
    method: 'SUBSCRIBE' | 'UNSUBSCRIBE'
  ): void {
    const ws = this.wsConnections[marketType];
    if (!ws || ws.readyState !== WebSocketState.OPEN) {
      logger.warn({ marketType, streams, method }, 'Cannot send subscription message, WebSocket not connected.');
      // If the connection is down, the resubscribe logic will handle it upon reconnection.
      return;
    }
    const message = { method, params: streams, id: Date.now() };
    ws.send(JSON.stringify(message));
    logger.info({ marketType, method, streams: streams.length }, `Sent ${method} request.`);
  }

  private connect(marketType: MarketType): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.wsConnections[marketType] || this.connecting[marketType]) {
        logger.info({ marketType }, 'WebSocket connection already exists or is in progress.');
        return resolve();
      }

      this.connecting[marketType] = true;
      const url = `${API_CONFIG.wsUrls[marketType as keyof MarketTypeConfig]}/stream`;
      logger.info({ marketType, url }, 'Connecting to WebSocket...');
      
      const ws = new WebSocket(url);
      
      ws.on('open', () => {
        logger.info({ marketType }, 'WebSocket connection established.');
        clearTimeout(connectionTimeout);
        this.connecting[marketType] = false;
        this.wsConnections[marketType] = ws;
        this.reconnectAttempts[marketType] = 0;
        this.resubscribe(marketType);
        this.emit('reconnect', { marketType }); // Emit reconnect event
        resolve();
      });

      ws.on('message', (data: Buffer) => this.handleMessage(marketType, data));
      ws.on('close', (code, reason) => this.handleClose(marketType, code, reason.toString()));
      ws.on('error', (error) => this.handleError(marketType, error));
      
      const connectionTimeout = setTimeout(() => {
        if (this.connecting[marketType]) {
          this.connecting[marketType] = false;
          ws.terminate();
          const err = new Error(`WebSocket connection timeout for ${marketType}`);
          logger.error({ marketType }, err.message);
          reject(err);
        }
      }, 10000);
    });
  }

  private disconnect(marketType: MarketType): Promise<void> {
    return new Promise(resolve => {
      const ws = this.wsConnections[marketType];
      if (!ws) return resolve();

      // Clear reconnect timer to prevent auto-reconnect on manual disconnect
      if (this.reconnectTimers[marketType]) {
        clearTimeout(this.reconnectTimers[marketType] as NodeJS.Timeout);
        this.reconnectTimers[marketType] = null;
      }
      
      if (ws.readyState === WebSocketState.OPEN || ws.readyState === WebSocketState.CONNECTING) {
        ws.once('close', () => {
          this.wsConnections[marketType] = null;
          logger.info({ marketType }, 'WebSocket disconnected.');
          resolve();
        });
        ws.close(1000, 'Client-initiated shutdown');
      } else {
        this.wsConnections[marketType] = null;
        resolve();
      }
    });
  }

  private handleMessage(marketType: MarketType, data: Buffer): void {
    try {
      const message = JSON.parse(data.toString());
      if (message.stream) {
        // This is a multiplexed stream message
        const streamType = message.stream.split('@')[1];

        if (streamType.startsWith('kline')) {
          this.processKlineUpdate(marketType, message.data);
        } else if (streamType === 'ticker_1h' || streamType === 'ticker' || streamType === 'ticker@arr') {
          this.processTickerUpdate(marketType, message.data);
        }
      }
    } catch (error) {
      logger.error({ marketType, error }, 'Error processing WebSocket message');
    }
  }

  private processKlineUpdate(marketType: MarketType, data: any): void {
    const klineData = binanceAdapter.adaptKline(data.k, marketType, data.k.i);
    if (klineData) {
      const event: KlineEvent = {
        marketType,
        symbol: klineData.symbol,
        interval: klineData.interval,
        data: [klineData], // Emitting kline data as an array
      };
      this.emit('kline', event);
    }
  }

  private processTickerUpdate(marketType: MarketType, data: any | any[]): void {
    const tickerData = Array.isArray(data) ? data : [data];
    const tickers = binanceAdapter.adaptTickers(tickerData, marketType);
    
    if (tickers.length > 0) {
      const event: TickerEvent = {
        marketType,
        data: tickers,
      };
      this.emit('ticker', event);
    }
  }

  private handleClose(marketType: MarketType, code: number, reason: string): void {
    logger.warn({ marketType, code, reason }, 'WebSocket connection closed.');
    this.wsConnections[marketType] = null;
    this.emit('error', { type: 'error', marketType, message: `WebSocket closed: ${reason} (code: ${code})` });
    // Don't auto-reconnect on normal closure (1000) by client
    if (code !== 1000) {
      this.scheduleReconnect(marketType);
    }
  }

  private handleError(marketType: MarketType, error: Error): void {
    logger.error({ marketType, error }, 'WebSocket error occurred.');
    this.emit('error', { type: 'error', marketType, message: error.message || 'Unknown WebSocket error' });
    // The 'close' event will usually follow, which will trigger reconnect logic.
  }

  private scheduleReconnect(marketType: MarketType): void {
    if (this.reconnectTimers[marketType]) return; // Already scheduled

    this.reconnectAttempts[marketType]++;
    const delay = Math.min(30000, 1000 * Math.pow(1.5, this.reconnectAttempts[marketType]));
    logger.info({ marketType, attempt: this.reconnectAttempts[marketType], delay }, `Scheduling reconnect...`);
    
    this.reconnectTimers[marketType] = setTimeout(() => {
      this.reconnectTimers[marketType] = null;
      this.connect(marketType).catch(err => {
        logger.error({ marketType, err }, 'Reconnect attempt failed.');
        // If connect fails, it will call handleClose/handleError, which can schedule another reconnect
      });
    }, delay);
  }

  private resubscribe(marketType: MarketType): void {
    const streamsToResubscribe = Array.from(this.activeSubscriptions.entries())
      .filter(([, sub]) => sub.marketType === marketType)
      .map(([streamName]) => streamName);

    if (streamsToResubscribe.length > 0) {
      logger.info({ marketType, count: streamsToResubscribe.length }, 'Resubscribing to active streams...');
      this.sendSubscriptionMessage(marketType, streamsToResubscribe, 'SUBSCRIBE');
    }
  }
}

export const binanceService = BinanceService.getInstance();

--- END FILE: src\server\services\binance.service.ts ---

--- START FILE: src\server\services\dataCollector.ts ---
import { pino } from 'pino';
import { binanceService } from './binance.service.js';
import { saveTickers, processTickerUpdate } from './tickerService.js';
import { MarketType, FullTicker, Ticker, CoreTicker } from '@/shared/schemas/market.schema';
import { exchangeDataCollector } from './exchangeDataCollectorService.js';

// Configure logger
const logger = pino({ name: 'data-collector', level: 'info' });

// Intervals for data updates
const UPDATE_INTERVALS = {
  symbols: 60 * 60 * 1000, // 1 hour
  tickers: 5 * 60 * 1000,  // 5 minutes
};

let symbolsUpdateInterval: NodeJS.Timeout | null = null;
let tickersUpdateInterval: NodeJS.Timeout | null = null;
let klineSubscriptions: { unsubscribe: () => void }[] = [];
let tickerSubscriptions: { unsubscribe: () => void }[] = [];

// Symbols we want to subscribe to for real-time updates
const WATCHED_SYMBOLS = [
  'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT', 
  'DOGEUSDT', 'XRPUSDT', 'DOTUSDT', 'UNIUSDT', 'LTCUSDT'
];

// Intervals we want to track
const WATCHED_INTERVALS = ['1m', '5m', '15m', '1h', '4h', '1d'];

/**
 * Convert CoreTicker to Ticker format expected by saveTickers
 * @param coreTicker CoreTicker from exchange adapter
 * @returns Ticker object for database storage
 */
function convertCoreTickerToTicker(coreTicker: CoreTicker): Ticker {
  return {
    symbol: coreTicker.symbol,
    marketType: coreTicker.marketType,
    lastPrice: coreTicker.price,
    priceChange: coreTicker.change || 0,
    priceChangePercent: coreTicker.changePercent || 0,
    weightedAvgPrice: coreTicker.weightedAvgPrice || 0,
    prevClosePrice: coreTicker.prevClose || 0,
    openPrice: coreTicker.open || 0,
    highPrice: coreTicker.high || 0,
    lowPrice: coreTicker.low || 0,
    volume: coreTicker.volume || 0,
    quoteVolume: coreTicker.quoteVolume || 0,
    openTime: coreTicker.timestamp,
    closeTime: coreTicker.timestamp,
    count: coreTicker.count || 0,
    bidPrice: coreTicker.bidPrice || 0,
    bidQty: coreTicker.bidQty || 0,
    askPrice: coreTicker.askPrice || 0,
    askQty: coreTicker.askQty || 0,
    lastQty: 0, // Not available in CoreTicker
    firstId: coreTicker.firstTradeId || 0,
    lastId: coreTicker.lastTradeId || 0,
    lastUpdated: Date.now(),
  };
}

/**
 * Initialize data collection from exchanges
 */
export async function initializeDataCollection(): Promise<void> {
  try {
    logger.info('Initializing data collection from exchanges...');
    
    // Initialize binance service
    await binanceService.initialize();
    
    // Fetch initial data for all markets
    await fetchInitialData();
    
    // Set up real-time subscriptions
    await subscribeToRealTimeData();
    
    // Set up regular polling for all symbols and tickers
    setupPeriodicUpdates();
    
    logger.info('Data collection initialized successfully.');
  } catch (error) {
    logger.error({ error }, 'Failed to initialize data collection');
    throw error;
  }
}

/**
 * Fetch initial data from exchanges
 */
async function fetchInitialData(): Promise<void> {
  try {
    logger.info('Fetching initial data...');
    
    // Fetch tickers for both spot and futures markets
    await fetchAllTickers();
    
    logger.info('Initial data fetch completed.');
  } catch (error) {
    logger.error({ error }, 'Failed to fetch initial data');
    throw error;
  }
}

/**
 * Set up periodic updates for all data
 */
function setupPeriodicUpdates(): void {
  logger.info('Setting up periodic updates...');
  
  // Schedule regular updates for symbols
  symbolsUpdateInterval = setInterval(async () => {
    try {
      await fetchAllSymbols();
    } catch (err) {
      logger.error({ err }, 'Error updating symbols');
    }
  }, UPDATE_INTERVALS.symbols);
  
  // Schedule regular updates for tickers
  tickersUpdateInterval = setInterval(async () => {
    try {
      await fetchAllTickers();
    } catch (err) {
      logger.error({ err }, 'Error updating tickers');
    }
  }, UPDATE_INTERVALS.tickers);
  
  logger.info('Periodic updates scheduled.');
}

/**
 * Fetch all symbols from exchanges
 */
async function fetchAllSymbols(): Promise<void> {
  try {
    logger.info('Fetching all symbols...');
    
    // Fetch symbols from spot and futures markets
    const spotSymbols = await binanceService.fetchSymbols(MarketType.Spot);
    const futuresSymbols = await binanceService.fetchSymbols(MarketType.Futures);
    
    // Store symbols in database or in-memory cache if needed
    
    logger.info(`Fetched ${spotSymbols.length} spot symbols and ${futuresSymbols.length} futures symbols.`);
  } catch (error) {
    logger.error({ error }, 'Failed to fetch symbols');
    throw error;
  }
}

/**
 * Fetch tickers for all markets
 */
async function fetchAllTickers(): Promise<void> {
  try {
    logger.info('Fetching all tickers...');
    
    // Fetch tickers from spot and futures markets
    const spotTickers = await binanceService.fetchTickers(MarketType.Spot);
    const futuresTickers = await binanceService.fetchTickers(MarketType.Futures);
    
    // Convert CoreTickers to Tickers and save to database
    const convertedTickers = [...spotTickers, ...futuresTickers].map(convertCoreTickerToTicker);
    await saveTickers(convertedTickers);
    
    logger.info(`Fetched ${spotTickers.length} spot tickers and ${futuresTickers.length} futures tickers.`);
  } catch (error) {
    logger.error({ error }, 'Failed to fetch tickers');
    throw error;
  }
}

/**
 * Subscribe to real-time data updates
 */
async function subscribeToRealTimeData(): Promise<void> {
  try {
    logger.info('Setting up real-time data subscriptions...');
    
    // Subscribe to ticker updates for both markets
    const spotTickerSub = binanceService.subscribeTickers(MarketType.Spot);
    const futuresTickerSub = binanceService.subscribeTickers(MarketType.Futures);
    
    tickerSubscriptions.push({ unsubscribe: spotTickerSub });
    tickerSubscriptions.push({ unsubscribe: futuresTickerSub });
    
    // Handle ticker events
    binanceService.on('ticker', (event) => {
      // Process each ticker in the event
      event.data.forEach((ticker: FullTicker) => processTickerUpdate(ticker));
    });
    
    // Subscribe to kline updates for watched symbols
    await subscribeToKlines();
    
    logger.info('Real-time data subscriptions established.');
  } catch (error) {
    logger.error({ error }, 'Failed to set up real-time data subscriptions');
    throw error;
  }
}

/**
 * Subscribe to kline/candlestick updates for watched symbols using batch subscriptions
 */
async function subscribeToKlines(): Promise<void> {
  // Clear any existing kline subscriptions
  unsubscribeAllKlines();

  logger.info('Subscribing to kline updates for watched symbols using batch subscriptions...');

  // Create symbol-interval pairs for batch subscription
  const symbolIntervalPairs = [];
  for (const symbol of WATCHED_SYMBOLS) {
    for (const interval of WATCHED_INTERVALS) {
      symbolIntervalPairs.push({ symbol, interval });
    }
  }

  // Subscribe to each market type using batch subscriptions
  for (const marketType of [MarketType.Spot, MarketType.Futures]) {
    // Use only exchangeDataCollector with batch subscription for efficiency
    // This handles both data processing and DB storage
    await exchangeDataCollector.subscribeKlinesBatch(marketType, symbolIntervalPairs);

    logger.info(
      { marketType, symbolCount: WATCHED_SYMBOLS.length, intervalCount: WATCHED_INTERVALS.length },
      `Batch subscribed to ${symbolIntervalPairs.length} kline streams`
    );
  }

  // Handle kline events from Binance service
  binanceService.on('kline', (_event) => {
    // Process kline event if needed
  });

  logger.info(`Subscribed to kline updates for ${WATCHED_SYMBOLS.length} symbols and ${WATCHED_INTERVALS.length} intervals.`);
}

/**
 * Unsubscribe from all kline updates
 */
function unsubscribeAllKlines(): void {
  if (klineSubscriptions.length > 0) {
    logger.info(`Unsubscribing from ${klineSubscriptions.length} kline streams...`);
    
    klineSubscriptions.forEach(sub => sub.unsubscribe());
    klineSubscriptions = [];
    
    logger.info('Unsubscribed from all kline streams.');
  }
}

/**
 * Shutdown data collection
 */
export async function shutdownDataCollection(): Promise<void> {
  try {
    logger.info('Shutting down data collection...');
    
    // Clear all intervals
    if (symbolsUpdateInterval) clearInterval(symbolsUpdateInterval);
    if (tickersUpdateInterval) clearInterval(tickersUpdateInterval);
    
    // Unsubscribe from real-time updates
    unsubscribeAllKlines();
    
    // Unsubscribe from ticker updates
    tickerSubscriptions.forEach(sub => sub.unsubscribe());
    tickerSubscriptions = [];
    
    // Shutdown binance service
    await binanceService.shutdown();
    
    logger.info('Data collection shutdown complete.');
  } catch (error) {
    logger.error({ error }, 'Error during data collection shutdown');
    throw error;
  }
} 
--- END FILE: src\server\services\dataCollector.ts ---

--- START FILE: src\server\services\exchange.interface.ts ---
import { EventEmitter } from 'events';
import {
  MarketType,
  CoreTicker,
  CoreKline,
  CoreSymbol,
  Kline,
  Ticker,
} from '@/shared/index';

/**
 * Information about a trading symbol
 */
export interface SymbolInfo {
  symbol: string;
  marketType: MarketType;
  baseAsset: string;
  quoteAsset: string;
  status?: string;
}

/**
 * Options for fetching klines
 */
export interface KlineOptions {
  limit?: number;
  startTime?: number;
  endTime?: number;
}

/**
 * Event data for kline updates
 */
export interface KlineEvent {
  marketType: MarketType;
  data: CoreKline[];
  symbol?: string;
  interval?: string;
}

/**
 * Event data for ticker updates
 */
export interface TickerEvent {
  marketType: MarketType;
  data: CoreTicker[];
}

/**
 * Interface for exchange services
 * All exchange implementations should follow this interface
 */
export interface ExchangeService extends EventEmitter {
  readonly exchangeName: string;
  
  /**
   * Initialize the exchange service
   */
  initialize(): Promise<void>;
  
  /**
   * Clean up resources and close connections
   */
  shutdown(): Promise<void>;
  
  /**
   * Fetch available trading symbols
   */
  fetchSymbols(marketType: MarketType): Promise<CoreSymbol[]>;
  
  /**
   * Fetch ticker data for all symbols
   */
  fetchTickers(marketType: MarketType): Promise<CoreTicker[]>;
  
  /**
   * Fetch historical kline data
   */
  fetchKlines(marketType: MarketType, symbol: string, interval: string, options?: KlineOptions): Promise<CoreKline[]>;
  
  /**
   * Subscribe to ticker updates
   * Returns an unsubscribe function
   */
  subscribeTickers(marketType: MarketType): () => void;
  
  /**
   * Subscribe to kline updates for a specific symbol and interval
   * Returns an unsubscribe function
   */
  subscribeKline(marketType: MarketType, symbol: string, interval: string): () => void;
} 
--- END FILE: src\server\services\exchange.interface.ts ---

--- START FILE: src\server\services\exchangeDataCollectorService.ts ---
import { pino } from 'pino';
import {
  Kline,
  MarketType,
  Ticker,
} from '@/shared/schemas/market.schema';
import { publishMessage } from '../lib/redis.js';
import { WebSocketManager } from './webSocketService.js';
import { FastifyInstance } from 'fastify';
import { WebSocket } from 'ws';
import { processTickerUpdate } from './tickerService.js';

// Configure logger
const logger = pino({ name: 'exchange-collector', level: 'info' });

// Supported intervals on Binance
const SUPPORTED_INTERVALS = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M'];

// Rate limiting constants based on Binance documentation
const RATE_LIMITS = {
  // Maximum streams per connection
  MAX_STREAMS_PER_CONNECTION: 1024,
  // Maximum messages per second (conservative values)
  SPOT_MAX_MESSAGES_PER_SECOND: 4, // Binance allows 5, we use 4 to be safe
  FUTURES_MAX_MESSAGES_PER_SECOND: 8, // Binance allows 10, we use 8 to be safe
  // Batch size for subscriptions (to avoid hitting message rate limits)
  MAX_STREAMS_PER_BATCH: 50,
  // Delay between batch requests (milliseconds)
  BATCH_DELAY_MS: 1000,
};

// API Configuration
type MarketTypeConfig = Record<MarketType, string>;

const API_CONFIG = {
  baseUrls: {
    spot: 'https://api.binance.com',
    futures: 'https://fapi.binance.com',
  } as MarketTypeConfig,
  wsUrls: {
    spot: 'wss://stream.binance.com:9443/ws',
    futures: 'wss://fstream.binance.com/ws',
  } as MarketTypeConfig,
};

// Interface for WebSocket connection that wraps the native WebSocket
interface WebSocketConnection {
  send: (data: string) => void;
  readyState: number;
  terminate: () => void;
  close: (code: number, reason: string) => void;
  on: (event: string, handler: (...args: any[]) => void) => void;
  once: (event: string, handler: (...args: any[]) => void) => void;
}

// Константы для состояния WebSocket (стандартные значения из спецификации WebSocket)
const WebSocketState = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
};

/**
 * Service for collecting data from cryptocurrency exchanges using Fastify WebSocket
 */
export class ExchangeDataCollectorService {
  private static instance: ExchangeDataCollectorService;
  private wsConnections: Record<MarketType, WebSocketConnection | null> = {
    [MarketType.Spot]: null,
    [MarketType.Futures]: null,
  };
  private connecting: Record<MarketType, boolean> = {
    [MarketType.Spot]: false,
    [MarketType.Futures]: false,
  };
  private reconnectTimers: Record<MarketType, NodeJS.Timeout | null> = {
    [MarketType.Spot]: null,
    [MarketType.Futures]: null,
  };
  private reconnectAttempts: Record<MarketType, number> = {
    [MarketType.Spot]: 0,
    [MarketType.Futures]: 0,
  };
  // Track active subscriptions
  private activeSubscriptions: Record<MarketType, Set<string>> = {
    [MarketType.Spot]: new Set(),
    [MarketType.Futures]: new Set(),
  };

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Get the singleton instance of ExchangeDataCollectorService
   */
  public static getInstance(): ExchangeDataCollectorService {
    if (!ExchangeDataCollectorService.instance) {
      ExchangeDataCollectorService.instance = new ExchangeDataCollectorService();
    }
    return ExchangeDataCollectorService.instance;
  }

  /**
   * Initialize the service
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing ExchangeDataCollectorService');

      // Connect to WebSockets for market data with detailed logging
      logger.info('Connecting to WebSocket endpoints...');
      const spotConnection = this.connect(MarketType.Spot);
      const futuresConnection = this.connect(MarketType.Futures);

      await Promise.all([spotConnection, futuresConnection]);

      // Verify connections are established
      const spotConnected = this.wsConnections[MarketType.Spot] !== null;
      const futuresConnected = this.wsConnections[MarketType.Futures] !== null;

      logger.info({
        spotConnected,
        futuresConnected,
        spotState: this.wsConnections[MarketType.Spot]?.readyState,
        futuresState: this.wsConnections[MarketType.Futures]?.readyState
      }, 'WebSocket connection status');

      if (!spotConnected || !futuresConnected) {
        throw new Error('Failed to establish all required WebSocket connections');
      }

      logger.info('ExchangeDataCollectorService initialized successfully');
    } catch (error: any) {
      logger.error(error, 'Error initializing ExchangeDataCollectorService');
      throw error;
    }
  }

  /**
   * Shutdown the service
   */
  public async shutdown(): Promise<void> {
    try {
      logger.info('Shutting down ExchangeDataCollectorService');
      // Disconnect WebSockets
      await Promise.all([
        this.disconnect(MarketType.Spot),
        this.disconnect(MarketType.Futures)
      ]);
      
      // Clear any reconnect timers
      (Object.keys(this.reconnectTimers) as MarketType[]).forEach(marketType => {
        const timer = this.reconnectTimers[marketType];
        if (timer) {
          clearTimeout(timer);
          this.reconnectTimers[marketType] = null;
        }
      });

      logger.info('ExchangeDataCollectorService shutdown successfully');
    } catch (error: any) {
      logger.error(error, 'Error shutting down ExchangeDataCollectorService');
      throw error;
    }
  }

  /**
   * Connect to exchange WebSocket for the specified market type
   */
  public connect(marketType: MarketType): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.wsConnections[marketType] || this.connecting[marketType]) {
        logger.info({ marketType }, 'WebSocket connection already exists or in progress');
        resolve();
        return;
      }

      this.connecting[marketType] = true;
      const wsUrl = API_CONFIG.wsUrls[marketType];

      try {
        logger.info({ marketType, wsUrl }, 'Connecting to exchange WebSocket');

        const ws = new WebSocket(wsUrl);
        const connection: WebSocketConnection = ws as unknown as WebSocketConnection;

        // Set event handlers
        connection.on('open', () => this.handleOpen(marketType, connection));
        connection.on('message', (data: Buffer) => this.handleMessage(marketType, data));
        connection.on('close', (code: number, reason: string) => this.handleClose(marketType, code, reason.toString()));
        connection.on('error', (error: Error) => this.handleError(marketType, error));

        // Set a timeout for connection
        const connectionTimeout = setTimeout(() => {
          if (this.connecting[marketType]) {
            this.connecting[marketType] = false;
            if (typeof connection.terminate === 'function') {
              connection.terminate();
            }
            const err = new Error(`WebSocket connection timeout for ${marketType}`);
            logger.error({ marketType }, err.message);
            reject(err);
          }
        }, 10000); // 10 second timeout

        // When connection is successful, clear timeout and resolve
        connection.once('open', () => {
          clearTimeout(connectionTimeout);
          this.connecting[marketType] = false;
          this.wsConnections[marketType] = connection;
          this.reconnectAttempts[marketType] = 0;
          resolve();
        });

      } catch (error) {
        this.connecting[marketType] = false;
        logger.error({ marketType, error }, 'Error creating WebSocket connection');
        reject(error);
      }
    });
  }

  /**
   * Disconnect from exchange WebSocket
   */
  public disconnect(marketType: MarketType): Promise<void> {
    return new Promise((resolve) => {
      const connection = this.wsConnections[marketType];
      if (!connection) {
        resolve();
        return;
      }

      // Clear reconnect timer if exists
      const timer = this.reconnectTimers[marketType];
      if (timer) {
        clearTimeout(timer);
        this.reconnectTimers[marketType] = null;
      }

      // Only set the event handler if the socket is open
      if (connection.readyState === WebSocketState.OPEN ||
          connection.readyState === WebSocketState.CONNECTING) {
        connection.once('close', () => {
          this.wsConnections[marketType] = null;
          this.activeSubscriptions[marketType].clear();
          logger.info({ marketType }, 'WebSocket disconnected');
          resolve();
        });

        // Close the connection with a normal close code
        connection.close(1000, 'Disconnecting');
      } else {
        this.wsConnections[marketType] = null;
        this.activeSubscriptions[marketType].clear();
        resolve();
      }
    });
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(marketType: MarketType, connection: WebSocketConnection): void {
    logger.info({ marketType }, 'WebSocket connection established');
    this.wsConnections[marketType] = connection;
    // Resubscribe to active streams upon reconnection
    this.resubscribe(marketType);
  }

  /**
   * Handle WebSocket message event
   */
  private handleMessage(marketType: MarketType, data: Buffer): void {
    try {
      const message = JSON.parse(data.toString());
      
      // Handle different message types
      if (message.e === 'kline') {
        // Handle kline update
        this.processKlineUpdate(marketType, message);
      } else if (message.e === '24hrTicker') {
        // Handle ticker update
        this.processTickerUpdate(marketType, message);
      }
    } catch (error: any) {
      logger.error({ marketType, error }, 'Error processing WebSocket message');
    }
  }

  /**
   * Handle WebSocket close event
   */
  private handleClose(marketType: MarketType, code: number, reason: string): void {
    logger.warn({ marketType, code, reason }, 'WebSocket connection closed');
    this.wsConnections[marketType] = null;
    this.scheduleReconnect(marketType);
  }

  /**
   * Handle WebSocket error event
   */
  private handleError(marketType: MarketType, error: Error): void {
    logger.error({ marketType, error }, 'WebSocket error');
    // Error will be followed by close event, which will initiate reconnect
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  private scheduleReconnect(marketType: MarketType): void {
    // If there is already a reconnect timer, don't schedule another one
    if (this.reconnectTimers[marketType]) {
      return;
    }

    const attempt = this.reconnectAttempts[marketType] + 1;
    this.reconnectAttempts[marketType] = attempt;

    // Calculate delay with exponential backoff, capped at 30 seconds
    const delay = Math.min(Math.pow(1.5, attempt) * 1000, 30000);

    logger.info(
      { marketType, attempt, delay },
      `Scheduling reconnect in ${delay}ms`
    );

    this.reconnectTimers[marketType] = setTimeout(async () => {
      this.reconnectTimers[marketType] = null;
      
      try {
        await this.connect(marketType);
        logger.info({ marketType }, 'Reconnected successfully');
      } catch (error: any) {
        logger.error({ marketType, error }, 'Reconnect failed');
        this.scheduleReconnect(marketType);
      }
    }, delay);
  }

  /**
   * Subscribe to a stream on exchange WebSocket
   */
  public async subscribe(marketType: MarketType, streamName: string): Promise<void> {
    await this.subscribeBatch(marketType, [streamName]);
  }

  /**
   * Subscribe to multiple streams on exchange WebSocket in batches
   * This respects rate limits and splits large requests into smaller chunks
   */
  public async subscribeBatch(marketType: MarketType, streamNames: string[]): Promise<void> {
    if (streamNames.length === 0) {
      logger.warn({ marketType }, 'No stream names provided for batch subscription');
      return;
    }

    const connection = this.wsConnections[marketType];
    if (!connection || connection.readyState !== WebSocketState.OPEN) {
      logger.warn(
        { marketType, streamCount: streamNames.length, connectionState: connection?.readyState },
        'Cannot subscribe, WebSocket not connected'
      );

      // Add all streams to active subscriptions for later resubscription
      streamNames.forEach(streamName => {
        this.activeSubscriptions[marketType].add(streamName);
      });

      this.connect(marketType).catch((error: Error) => {
        logger.error({ marketType, error }, 'Failed to connect for batch subscription');
      });
      return;
    }

    // Add all streams to active subscriptions
    streamNames.forEach(streamName => {
      this.activeSubscriptions[marketType].add(streamName);
    });

    // Split into smaller batches to respect rate limits
    const batches = [];
    for (let i = 0; i < streamNames.length; i += RATE_LIMITS.MAX_STREAMS_PER_BATCH) {
      batches.push(streamNames.slice(i, i + RATE_LIMITS.MAX_STREAMS_PER_BATCH));
    }

    logger.info(
      { marketType, totalStreams: streamNames.length, batchCount: batches.length },
      `Sending ${batches.length} batch subscription request(s) for ${streamNames.length} streams`
    );

    // Send batches with delays to respect rate limits
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];

      // Add delay between batches (except for the first one)
      if (i > 0) {
        await new Promise(resolve => setTimeout(resolve, RATE_LIMITS.BATCH_DELAY_MS));
      }

      const subscribeMsg = {
        method: 'SUBSCRIBE',
        params: batch,
        id: Date.now() + i, // Unique ID for each batch
      };

      try {
        connection.send(JSON.stringify(subscribeMsg));
        logger.info(
          {
            marketType,
            batchIndex: i + 1,
            batchSize: batch.length,
            totalBatches: batches.length,
            sampleStreams: batch.slice(0, 2)
          },
          `Sent batch ${i + 1}/${batches.length} subscription request`
        );
      } catch (error: any) {
        logger.error(
          { marketType, batchIndex: i + 1, batchSize: batch.length, error },
          'Error sending batch subscription request'
        );
      }
    }
  }

  /**
   * Unsubscribe from a stream on exchange WebSocket
   */
  public unsubscribe(marketType: MarketType, streamName: string): void {
    // Remove from active subscriptions
    this.activeSubscriptions[marketType].delete(streamName);

    const connection = this.wsConnections[marketType];
    if (!connection || connection.readyState !== WebSocketState.OPEN) {
      logger.warn(
        { marketType, streamName, connectionState: connection?.readyState },
        'Cannot unsubscribe, WebSocket not connected'
      );
      return;
    }

    // Send unsubscription message
    const unsubscribeMsg = {
      method: 'UNSUBSCRIBE',
      params: [streamName],
      id: Date.now(),
    };

    try {
      connection.send(JSON.stringify(unsubscribeMsg));
      logger.info({ marketType, streamName }, 'Sent unsubscription request');
    } catch (error: any) {
      logger.error(
        { marketType, streamName, error },
        'Error sending unsubscription request'
      );
    }
  }

  /**
   * Resubscribe to all active streams after reconnection
   */
  private async resubscribe(marketType: MarketType): Promise<void> {
    const streams = Array.from(this.activeSubscriptions[marketType]);
    if (streams.length === 0) {
      return;
    }

    logger.info(
      { marketType, streamCount: streams.length },
      'Resubscribing to streams after reconnection'
    );

    // Use batch subscription for resubscription
    await this.subscribeBatch(marketType, streams);
  }

  /**
   * Subscribe to kline updates for a symbol and interval
   */
  public async subscribeKline(marketType: MarketType, symbol: string, interval: string): Promise<void> {
    // Validate interval
    if (!SUPPORTED_INTERVALS.includes(interval)) {
      logger.warn(
        { marketType, symbol, interval, supportedIntervals: SUPPORTED_INTERVALS },
        'Unsupported interval, not subscribing'
      );
      return;
    }

    // Create stream name based on Binance format
    const streamName = `${symbol.toLowerCase()}@kline_${interval}`;

    await this.subscribe(marketType, streamName);
  }

  /**
   * Subscribe to kline updates for multiple symbols and intervals in batch
   * This is more efficient than individual subscriptions and respects rate limits
   */
  public async subscribeKlinesBatch(
    marketType: MarketType,
    symbolIntervalPairs: Array<{ symbol: string; interval: string }>
  ): Promise<void> {
    // Filter valid intervals and create stream names
    const streamNames: string[] = [];
    const invalidPairs: Array<{ symbol: string; interval: string }> = [];

    for (const { symbol, interval } of symbolIntervalPairs) {
      if (!SUPPORTED_INTERVALS.includes(interval)) {
        invalidPairs.push({ symbol, interval });
        continue;
      }

      const streamName = `${symbol.toLowerCase()}@kline_${interval}`;
      streamNames.push(streamName);
    }

    // Log invalid intervals if any
    if (invalidPairs.length > 0) {
      logger.warn(
        { marketType, invalidPairs, supportedIntervals: SUPPORTED_INTERVALS },
        `Skipping ${invalidPairs.length} invalid interval(s)`
      );
    }

    // Subscribe to all valid streams in batch
    if (streamNames.length > 0) {
      await this.subscribeBatch(marketType, streamNames);
    } else {
      logger.warn({ marketType }, 'No valid symbol-interval pairs to subscribe to');
    }
  }

  /**
   * Process kline update from WebSocket
   */
  private async processKlineUpdate(marketType: MarketType, message: any): Promise<void> {
    try {
      if (message.e === 'kline') {
        const klineData = message.k;

        const kline: Kline = {
          symbol: message.s,
          interval: klineData.i,
          marketType: marketType,
          openTime: klineData.t,
          open: klineData.o,
          high: klineData.h,
          low: klineData.l,
          close: klineData.c,
          volume: klineData.v,
          closeTime: klineData.T,
          quoteVolume: klineData.q,
          trades: klineData.n,
          isClosed: klineData.x,
        };

        if (kline.isClosed) {
          await this.saveKlineToDB(kline);
        }
        
        this.publishKlineUpdate(kline);
      }
    } catch (error: any) {
      logger.error({ error, message }, 'Error processing kline update');
    }
  }

  /**
   * Save a kline to the database.
   * This is a placeholder and should be implemented with Drizzle.
   * @param kline The kline data to save.
   */
  private async saveKlineToDB(kline: Kline): Promise<void> {
    try {
      // Import db and schema from the database configuration
      const { db, schema } = await import('../../db/index.js');
      
      // Convert timestamp to Date object
      const openTime = new Date(kline.openTime);
      
      // Insert kline data into the database
      await db.insert(schema.candles)
        .values({
          symbol: kline.symbol,
          interval: kline.interval,
          marketType: kline.marketType,
          openTime: openTime,
          open: kline.open.toString(),
          high: kline.high.toString(),
          low: kline.low.toString(),
          close: kline.close.toString(),
          volume: kline.volume.toString(),
        })
        .onConflictDoUpdate({
          target: [schema.candles.symbol, schema.candles.interval, schema.candles.marketType, schema.candles.openTime],
          set: {
            open: kline.open.toString(),
            high: kline.high.toString(),
            low: kline.low.toString(),
            close: kline.close.toString(),
            volume: kline.volume.toString(),
          }
        });
      
      logger.info({ 
        symbol: kline.symbol, 
        interval: kline.interval, 
        openTime: kline.openTime 
      }, 'Kline data saved to database');
    } catch (error) {
      logger.error({ 
        error, 
        symbol: kline.symbol, 
        interval: kline.interval, 
        openTime: kline.openTime 
      }, 'Failed to save kline to database');
    }
  }

  /**
   * Publish kline update to Redis
   */
  private publishKlineUpdate(kline: Kline): void {
    const channel = `pubsub:kline:${kline.symbol}:${kline.interval}:${kline.marketType}`;
    publishMessage(channel, kline);
    logger.debug({ channel }, 'Published kline update to Redis');
  }

  // Add a new method to process ticker updates
  private async processTickerUpdate(marketType: MarketType, message: any): Promise<void> {
    try {
      if (message.e === '24hrTicker') {
        const ticker: Ticker = {
          symbol: message.s,
          marketType: marketType,
          lastPrice: parseFloat(message.c),
          priceChange: parseFloat(message.p),
          priceChangePercent: parseFloat(message.P),
          volume: parseFloat(message.v),
          quoteVolume: parseFloat(message.q),
          count: message.n,
          lastUpdated: message.E,
          weightedAvgPrice: parseFloat(message.w),
          openPrice: parseFloat(message.o),
          highPrice: parseFloat(message.h),
          lowPrice: parseFloat(message.l),
          lastQty: parseFloat(message.Q),
          openTime: message.O,
          closeTime: message.C,
          firstId: message.F,
          lastId: message.L,
          bidPrice: parseFloat(message.b),
          bidQty: parseFloat(message.B),
          askPrice: parseFloat(message.a),
          askQty: parseFloat(message.A),
          prevClosePrice: parseFloat(message.x),
        };
        
        // Save ticker to DB using the ticker service
        await processTickerUpdate(ticker);
        
        // Publish ticker update to Redis
        this.publishTickerUpdate(ticker);
      }
    } catch (error: any) {
      logger.error({ error, message }, 'Error processing ticker update');
    }
  }

  /**
   * Publish ticker update to Redis
   */
  private publishTickerUpdate(ticker: Ticker): void {
    const channel = `ticker:${ticker.marketType}:${ticker.symbol}`;
    try {
      publishMessage(channel, JSON.stringify(ticker));
      logger.debug({ channel }, 'Published ticker update to Redis');
    } catch (error: any) {
      logger.error({ error }, 'Error publishing ticker update to Redis');
    }
  }

  /**
   * Fetch historical klines from the exchange API and save them to the database
   */
  public async fetchAndSaveHistoricalKlines(
    marketType: MarketType,
    symbol: string,
    interval: string,
    limit: number = 1000,
    startTime?: number,
    endTime?: number
  ): Promise<void> {
    // Will be implemented to fetch historical data from exchange API
    // This method will call REST API and save the results to the database
  }
}

// Create a singleton instance
export const exchangeDataCollector = ExchangeDataCollectorService.getInstance(); 
--- END FILE: src\server\services\exchangeDataCollectorService.ts ---

--- START FILE: src\server\services\klineService.ts ---
import { db, schema } from '@/db';
import { Kline, KlineIntervalSchema } from '@/shared/schemas/market.schema';
import { MarketType } from '@/shared/schemas/market.schema';
import { desc, eq, and } from 'drizzle-orm';

// The parameters for fetching kline data from the API routes
export interface KlineParams {
  symbol: string;
  interval: string;
  marketType: MarketType;
  limit?: number;
  endTime?: number;
}

/**
 * @module klineService
 * @description This module provides functions for fetching Kline data from the database.
 */

/**
 * Fetches historical Kline data for a given symbol, interval, and market type.
 * It queries the database and converts the records to the application's standard Kline format (with numbers).
 *
 * @param params - The parameters for fetching the Kline data.
 * @returns {Promise<Partial<Kline>[]>} A promise that resolves to an array of partial Kline data, sorted from oldest to newest.
 */
export async function getKlines(params: KlineParams): Promise<Partial<Kline>[]> {
  const { symbol, interval, marketType, limit = 1000 } = params;

  // Validate interval against the schema
  const parsedInterval = KlineIntervalSchema.safeParse(interval);
  if (!parsedInterval.success) {
    throw new Error(`Invalid interval: ${interval}`);
  }

  const klinesFromDb = await db
    .select({
      symbol: schema.candles.symbol,
      interval: schema.candles.interval,
      marketType: schema.candles.marketType,
      openTime: schema.candles.openTime,
      open: schema.candles.open,
      high: schema.candles.high,
      low: schema.candles.low,
      close: schema.candles.close,
      volume: schema.candles.volume,
    })
    .from(schema.candles)
    .where(
      and(
        eq(schema.candles.symbol, symbol),
        eq(schema.candles.interval, interval),
        eq(schema.candles.marketType, marketType)
      )
    )
    .orderBy(desc(schema.candles.openTime))
    .limit(limit);

  // Map the database records to a format that matches the Kline type as much as possible.
  const klines = klinesFromDb.map((candle) => {
    return {
      symbol: candle.symbol,
      interval: parsedInterval.data,
      marketType: candle.marketType as MarketType,
      openTime: candle.openTime.getTime(),
      open: parseFloat(candle.open),
      high: parseFloat(candle.high),
      low: parseFloat(candle.low),
      close: parseFloat(candle.close),
      volume: parseFloat(candle.volume),
    };
  });

  return klines.reverse();
} 
--- END FILE: src\server\services\klineService.ts ---

--- START FILE: src\server\services\tickerService.ts ---
import { db } from '@/db';
import { Ticker, MarketType } from '@/shared/index';
import { tickers24h } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';
import pino from 'pino';
import { publishMessage, getFromCache, setInCache } from '../lib/redis.js';

// Configure logger
const logger = pino({ name: 'ticker-service', level: 'info' });

// Type for ticker query parameters
export interface TickerQueryParams {
  marketTypes?: MarketType[];
  quoteAssets?: string[];
  minVolume?: number;
  minTrades?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  searchQuery?: string;
}

// Cache key builder
const getTickersCacheKey = (params: TickerQueryParams): string => {
  return `tickers:${JSON.stringify(params)}`;
};

/**
 * Service for handling ticker data persistence
 */
export class TickerService {
  /**
   * Fetches all tickers for a specific market type from the database
   * @param marketType - The market type ('spot' or 'futures')
   * @returns A promise that resolves to an array of tickers
   */
  async getTickersByMarket(marketType: MarketType): Promise<Ticker[]> {
    try {
      const results = await db
        .select()
        .from(tickers24h)
        .where(eq(tickers24h.marketType, marketType));
      
      return results.map(this.mapDbRowToTicker);
    } catch (error) {
      logger.error(`Error fetching ${marketType} tickers from DB:`, error);
      return [];
    }
  }

  /**
   * Upserts multiple tickers into the database
   * @param tickers - An array of ticker data to upsert
   * @returns A promise that resolves when the operation is complete
   */
  async upsertTickers(tickers: Ticker[]): Promise<void> {
    if (tickers.length === 0) {
      return;
    }

    const values = tickers.map(ticker => ({
      symbol: ticker.symbol,
      marketType: ticker.marketType,
      lastPrice: ticker.lastPrice.toString(),
      priceChange: ticker.priceChange.toString(),
      priceChangePercent: ticker.priceChangePercent.toString(),
      highPrice: ticker.highPrice.toString(),
      lowPrice: ticker.lowPrice.toString(),
      volume: ticker.volume.toString(),
      quoteVolume: ticker.quoteVolume.toString(),
      openTime: new Date(ticker.openTime),
      closeTime: new Date(ticker.closeTime),
      count: ticker.count,
      lastUpdated: new Date(ticker.lastUpdated),
      // Optional fields
      weightedAvgPrice: ticker.weightedAvgPrice?.toString(),
      prevClosePrice: ticker.prevClosePrice?.toString(),
      lastQty: ticker.lastQty?.toString(),
      bidPrice: ticker.bidPrice?.toString(),
      bidQty: ticker.bidQty?.toString(),
      askPrice: ticker.askPrice?.toString(),
      askQty: ticker.askQty?.toString(),
      openPrice: ticker.openPrice?.toString(),
      firstId: ticker.firstId,
      lastId: ticker.lastId,
    }));

    try {
      await db.insert(tickers24h)
        .values(values)
        .onConflictDoUpdate({
          target: [tickers24h.symbol, tickers24h.marketType],
          set: {
            lastPrice: sql.raw('excluded.last_price'),
            priceChange: sql.raw('excluded.price_change'),
            priceChangePercent: sql.raw('excluded.price_change_percent'),
            highPrice: sql.raw('excluded.high_price'),
            lowPrice: sql.raw('excluded.low_price'),
            volume: sql.raw('excluded.volume'),
            quoteVolume: sql.raw('excluded.quote_volume'),
            openTime: sql.raw('excluded.open_time'),
            closeTime: sql.raw('excluded.close_time'),
            count: sql.raw('excluded.count'),
            lastUpdated: sql.raw('excluded.last_updated'),
          }
        });
    } catch (error) {
      logger.error('Error upserting tickers:', error);
    }
  }

  /**
   * Maps a database row to a Ticker object
   * @param dbRow - The database row from the tickers24h table
   * @returns A Ticker object
   */
  private mapDbRowToTicker(dbRow: any): Ticker {
    return {
      symbol: dbRow.symbol,
      marketType: dbRow.marketType as MarketType,
      lastPrice: parseFloat(dbRow.lastPrice || '0'),
      priceChange: parseFloat(dbRow.priceChange || '0'),
      priceChangePercent: parseFloat(dbRow.priceChangePercent || '0'),
      highPrice: parseFloat(dbRow.highPrice || '0'),
      lowPrice: parseFloat(dbRow.lowPrice || '0'),
      volume: parseFloat(dbRow.volume || '0'),
      quoteVolume: parseFloat(dbRow.quoteVolume || '0'),
      openTime: dbRow.openTime.getTime(),
      closeTime: dbRow.closeTime.getTime(),
      count: dbRow.count || 0,
      lastUpdated: dbRow.lastUpdated.getTime(),
      weightedAvgPrice: dbRow.weightedAvgPrice ? parseFloat(dbRow.weightedAvgPrice) : 0,
      prevClosePrice: dbRow.prevClosePrice ? parseFloat(dbRow.prevClosePrice) : 0,
      lastQty: dbRow.lastQty ? parseFloat(dbRow.lastQty) : 0,
      bidPrice: dbRow.bidPrice ? parseFloat(dbRow.bidPrice) : 0,
      bidQty: dbRow.bidQty ? parseFloat(dbRow.bidQty) : 0,
      askPrice: dbRow.askPrice ? parseFloat(dbRow.askPrice) : 0,
      askQty: dbRow.askQty ? parseFloat(dbRow.askQty) : 0,
      openPrice: dbRow.openPrice ? parseFloat(dbRow.openPrice) : 0,
      firstId: dbRow.firstId || 0,
      lastId: dbRow.lastId || 0,
    };
  }
}

// Export a singleton instance of the service
export const tickerService = new TickerService();

/**
 * Save a ticker to the database
 * @param ticker The ticker data to save
 */
export async function saveTicker(ticker: Ticker): Promise<void> {
  try {
    // Validate required fields
    if (!ticker.symbol || !ticker.marketType) {
      throw new Error(`Invalid ticker data: missing symbol or marketType for ticker ${JSON.stringify(ticker)}`);
    }

    await db.insert(tickers24h)
      .values({
        symbol: ticker.symbol,
        marketType: ticker.marketType,
        lastPrice: ticker.lastPrice?.toString() || '0',
        priceChange: ticker.priceChange?.toString() || '0',
        priceChangePercent: ticker.priceChangePercent?.toString() || '0',
        highPrice: ticker.highPrice?.toString() || '0',
        lowPrice: ticker.lowPrice?.toString() || '0',
        volume: ticker.volume?.toString() || '0',
        quoteVolume: ticker.quoteVolume?.toString() || '0',
        openTime: ticker.openTime ? new Date(ticker.openTime) : null,
        closeTime: ticker.closeTime ? new Date(ticker.closeTime) : null,
        count: ticker.count || 0,
        lastUpdated: new Date(ticker.lastUpdated || Date.now()),
      })
      .onConflictDoUpdate({
        target: [tickers24h.symbol, tickers24h.marketType],
        set: {
          lastPrice: ticker.lastPrice?.toString() || '0',
          priceChange: ticker.priceChange?.toString() || '0',
          priceChangePercent: ticker.priceChangePercent?.toString() || '0',
          highPrice: ticker.highPrice?.toString() || '0',
          lowPrice: ticker.lowPrice?.toString() || '0',
          volume: ticker.volume?.toString() || '0',
          quoteVolume: ticker.quoteVolume?.toString() || '0',
          openTime: ticker.openTime ? new Date(ticker.openTime) : undefined,
          closeTime: ticker.closeTime ? new Date(ticker.closeTime) : undefined,
          count: ticker.count || 0,
          lastUpdated: new Date(ticker.lastUpdated || Date.now()),
        }
      });

    // Publish update to Redis for real-time subscribers
    try {
      const pubSubChannel = `pubsub:ticker:${ticker.symbol}:${ticker.marketType}`;
      await publishMessage(pubSubChannel, ticker);
    } catch (pubError) {
      logger.warn({ error: pubError, symbol: ticker.symbol, marketType: ticker.marketType }, 'Failed to publish ticker update');
    }

    logger.debug({ symbol: ticker.symbol, marketType: ticker.marketType }, 'Ticker saved to database');
  } catch (error) {
    // Enhanced error logging with full details
    logger.error({
      error: error,
      errorMessage: error instanceof Error ? error.message : 'Unknown error type',
      errorStack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.constructor.name : typeof error,
      symbol: ticker.symbol,
      marketType: ticker.marketType,
      tickerData: ticker,
    }, 'Failed to save ticker to database');

    // Re-throw the error so calling code can handle it appropriately
    throw error;
  }
}

/**
 * Save multiple tickers to the database in a single batch
 * @param tickers Array of ticker data to save
 */
export async function saveTickers(tickers: Ticker[]): Promise<void> {
  const startTime = Date.now();

  try {
    if (!tickers.length) {
      logger.debug('No tickers to save, skipping operation');
      return;
    }

    logger.info(`Starting to save ${tickers.length} tickers to database`);

    // Process in batches of 100 to avoid overwhelming the database and reduce transaction time
    const batchSize = 100;
    let savedCount = 0;

    for (let i = 0; i < tickers.length; i += batchSize) {
      const batch = tickers.slice(i, i + batchSize);

      try {
        // Convert each ticker to the format expected by the database
        const values = batch.map(ticker => {
          // Validate required fields before processing
          if (!ticker.symbol || !ticker.marketType) {
            throw new Error(`Invalid ticker data: missing symbol or marketType for ticker ${JSON.stringify(ticker)}`);
          }

          return {
            symbol: ticker.symbol,
            marketType: ticker.marketType,
            lastPrice: ticker.lastPrice?.toString() || '0',
            priceChange: ticker.priceChange?.toString() || '0',
            priceChangePercent: ticker.priceChangePercent?.toString() || '0',
            highPrice: ticker.highPrice?.toString() || '0',
            lowPrice: ticker.lowPrice?.toString() || '0',
            volume: ticker.volume?.toString() || '0',
            quoteVolume: ticker.quoteVolume?.toString() || '0',
            openTime: ticker.openTime ? new Date(ticker.openTime) : null,
            closeTime: ticker.closeTime ? new Date(ticker.closeTime) : null,
            count: ticker.count || 0,
            lastUpdated: new Date(ticker.lastUpdated || Date.now()),
          };
        });

        // Use optimized batch insert with single upsert operation
        await db.insert(tickers24h)
          .values(values)
          .onConflictDoUpdate({
            target: [tickers24h.symbol, tickers24h.marketType],
            set: {
              lastPrice: sql.raw('excluded.last_price'),
              priceChange: sql.raw('excluded.price_change'),
              priceChangePercent: sql.raw('excluded.price_change_percent'),
              highPrice: sql.raw('excluded.high_price'),
              lowPrice: sql.raw('excluded.low_price'),
              volume: sql.raw('excluded.volume'),
              quoteVolume: sql.raw('excluded.quote_volume'),
              openTime: sql.raw('excluded.open_time'),
              closeTime: sql.raw('excluded.close_time'),
              count: sql.raw('excluded.count'),
              lastUpdated: sql.raw('excluded.last_updated'),
            }
          });

        savedCount += batch.length;

        // Log progress for large batches
        if (tickers.length > 100) {
          logger.debug(`Saved batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(tickers.length / batchSize)}: ${savedCount}/${tickers.length} tickers`);
        }

      } catch (batchError) {
        // Log detailed error for this specific batch
        logger.error({
          error: batchError,
          message: batchError instanceof Error ? batchError.message : 'Unknown batch error',
          stack: batchError instanceof Error ? batchError.stack : undefined,
          batchIndex: Math.floor(i / batchSize),
          batchSize: batch.length,
          batchSymbols: batch.slice(0, 5).map(t => t.symbol), // Log first 5 symbols for debugging
        }, `Failed to save batch ${Math.floor(i / batchSize) + 1} of tickers`);

        // Continue with next batch instead of failing completely
        continue;
      }
    }

    // Publish batch update notification only if we saved some tickers
    if (savedCount > 0) {
      try {
        await publishMessage('pubsub:tickers:batch_update', {
          count: savedCount,
          marketTypes: [...new Set(tickers.map(t => t.marketType))],
          timestamp: Date.now(),
        });
      } catch (pubError) {
        logger.warn({ error: pubError }, 'Failed to publish batch update notification');
      }
    }

    const duration = Date.now() - startTime;
    logger.info(`Successfully saved ${savedCount}/${tickers.length} tickers to database in ${duration}ms`);

  } catch (error) {
    const duration = Date.now() - startTime;

    // Enhanced error logging with full details
    logger.error({
      error: error,
      errorMessage: error instanceof Error ? error.message : 'Unknown error type',
      errorStack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.constructor.name : typeof error,
      tickersCount: tickers.length,
      duration: duration,
      sampleTickers: tickers.slice(0, 3).map(t => ({ symbol: t.symbol, marketType: t.marketType })),
    }, `Failed to save ${tickers.length} tickers to database`);

    // Re-throw the error so calling code can handle it appropriately
    throw error;
  }
}

/**
 * Extract quote asset from symbol (e.g., BTCUSDT -> USDT)
 * Used for filtering by quote asset
 */
const extractQuoteAsset = (symbol: string): string => {
  // Common quote assets to check for
  const quoteAssets = ['USDT', 'BTC', 'ETH', 'BNB', 'BUSD', 'USD', 'TUSD', 'USDC', 'DAI', 'EUR', 'GBP'];
  
  for (const asset of quoteAssets) {
    if (symbol.endsWith(asset)) {
      return asset;
    }
  }
  
  // Default fallback: try to extract the last 3-4 characters
  const matches = symbol.match(/^(.+?)([A-Z]{3,}|[1-9A-Z]{4,})$/);
  return matches ? matches[2] : '';
};

/**
 * Get tickers with filtering, sorting and pagination
 * @param params Query parameters for filtering, sorting and pagination
 * @returns List of tickers matching the filter criteria
 * 
 * @todo Optimize for large datasets by moving filtering and sorting to database queries
 */
export async function getTickers(params: TickerQueryParams): Promise<Ticker[]> {
  try {
    const startTime = Date.now();
    const cacheKey = getTickersCacheKey(params);
    
    // Try to get from cache first
    const cachedData = await getFromCache<Ticker[]>(cacheKey, 30); // 30 seconds TTL
    if (cachedData) {
      logger.debug({ cacheKey, responseTime: Date.now() - startTime }, 'Returning tickers from cache');
      return cachedData;
    }
    
    // For now, get all tickers and filter in-memory to avoid Drizzle typing issues
    const allTickers = await db.select().from(tickers24h);
    
    // Filter results in memory
    let filteredTickers = allTickers;
    
    // Apply market type filter
    if (params.marketTypes && params.marketTypes.length > 0) {
      filteredTickers = filteredTickers.filter(ticker => 
        params.marketTypes!.includes(ticker.marketType as MarketType)
      );
    }
    
    // Apply quote asset filter
    if (params.quoteAssets && params.quoteAssets.length > 0) {
      filteredTickers = filteredTickers.filter(ticker => {
        return params.quoteAssets!.some(asset => ticker.symbol.endsWith(asset));
      });
    }
    
    // Apply volume filter
    if (params.minVolume !== undefined && params.minVolume > 0) {
      filteredTickers = filteredTickers.filter(ticker => {
        const volume = parseFloat(ticker.quoteVolume?.toString() || '0');
        return volume >= params.minVolume!;
      });
    }
    
    // Apply trade count filter
    if (params.minTrades !== undefined && params.minTrades > 0) {
      filteredTickers = filteredTickers.filter(ticker => 
        (ticker.count || 0) >= params.minTrades!
      );
    }
    
    // Apply text search
    if (params.searchQuery) {
      const query = params.searchQuery.toLowerCase();
      filteredTickers = filteredTickers.filter(ticker => 
        ticker.symbol.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    if (params.sortBy) {
      // Используем безопасный доступ к свойствам
      filteredTickers.sort((a, b) => {
        let aValue = 0;
        let bValue = 0;
        
        // Явно проверяем каждое возможное свойство сортировки
        switch (params.sortBy) {
          case 'lastPrice':
            aValue = parseFloat(a.lastPrice?.toString() || '0');
            bValue = parseFloat(b.lastPrice?.toString() || '0');
            break;
          case 'priceChange':
            aValue = parseFloat(a.priceChange?.toString() || '0');
            bValue = parseFloat(b.priceChange?.toString() || '0');
            break;
          case 'priceChangePercent':
            aValue = parseFloat(a.priceChangePercent?.toString() || '0');
            bValue = parseFloat(b.priceChangePercent?.toString() || '0');
            break;
          case 'highPrice':
            aValue = parseFloat(a.highPrice?.toString() || '0');
            bValue = parseFloat(b.highPrice?.toString() || '0');
            break;
          case 'lowPrice':
            aValue = parseFloat(a.lowPrice?.toString() || '0');
            bValue = parseFloat(b.lowPrice?.toString() || '0');
            break;
          case 'volume':
            aValue = parseFloat(a.volume?.toString() || '0');
            bValue = parseFloat(b.volume?.toString() || '0');
            break;
          case 'quoteVolume':
            aValue = parseFloat(a.quoteVolume?.toString() || '0');
            bValue = parseFloat(b.quoteVolume?.toString() || '0');
            break;
          case 'count':
            aValue = a.count || 0;
            bValue = b.count || 0;
            break;
          default:
            // По умолчанию сортировка по символу
            return params.sortOrder === 'asc' 
              ? a.symbol.localeCompare(b.symbol)
              : b.symbol.localeCompare(a.symbol);
        }
        
        return params.sortOrder === 'asc' 
          ? aValue - bValue 
          : bValue - aValue;
      });
    } else {
      // Default sort by quoteVolume desc
      filteredTickers.sort((a, b) => {
        const aVolume = parseFloat(a.quoteVolume?.toString() || '0');
        const bVolume = parseFloat(b.quoteVolume?.toString() || '0');
        
        return bVolume - aVolume; // Descending
      });
    }
    
    // Apply pagination
    if (params.offset !== undefined) {
      filteredTickers = filteredTickers.slice(params.offset);
    }
    
    if (params.limit !== undefined) {
      filteredTickers = filteredTickers.slice(0, params.limit);
    }
    
    // Convert database model to Ticker
    const tickers: Ticker[] = filteredTickers.map(row => ({
      symbol: row.symbol,
      marketType: row.marketType as MarketType,
      lastPrice: parseFloat(row.lastPrice?.toString() || '0'),
      priceChange: parseFloat(row.priceChange?.toString() || '0'),
      priceChangePercent: parseFloat(row.priceChangePercent?.toString() || '0'),
      openPrice: parseFloat(row.lastPrice?.toString() || '0'), // Usually same as last if not stored
      highPrice: parseFloat(row.highPrice?.toString() || '0'),
      lowPrice: parseFloat(row.lowPrice?.toString() || '0'),
      volume: parseFloat(row.volume?.toString() || '0'),
      quoteVolume: parseFloat(row.quoteVolume?.toString() || '0'),
      openTime: row.openTime?.getTime() || 0,
      closeTime: row.closeTime?.getTime() || 0,
      count: row.count || 0,
      lastUpdated: row.lastUpdated.getTime(),
      
      // Additional required Ticker fields
      weightedAvgPrice: parseFloat(row.lastPrice?.toString() || '0'),
      prevClosePrice: parseFloat(row.lastPrice?.toString() || '0'),
      lastQty: 0,
      bidPrice: 0,
      bidQty: 0,
      askPrice: 0,
      askQty: 0,
      firstId: 0,
      lastId: 0,
    }));
    
    // Store in cache
    await setInCache(cacheKey, tickers, 30); // 30 seconds TTL
    
    const responseTime = Date.now() - startTime;
    logger.info({ 
      count: tickers.length, 
      totalCount: allTickers.length, 
      responseTime,
      filters: {
        marketTypes: params.marketTypes,
        quoteAssets: params.quoteAssets,
        minVolume: params.minVolume,
        minTrades: params.minTrades,
        searchQuery: params.searchQuery
      }
    }, 'Retrieved tickers from database');
    
    return tickers;
  } catch (error) {
    logger.error({ error, params }, 'Failed to get tickers from database');
    throw error;
  }
}

/**
 * Process ticker update event and save to database
 */
export async function processTickerUpdate(ticker: Ticker): Promise<void> {
  await saveTicker(ticker);
}

/**
 * Process batch ticker update event and save to database
 */
export async function processTickersBatchUpdate(tickers: Ticker[]): Promise<void> {
  await saveTickers(tickers);
} 
--- END FILE: src\server\services\tickerService.ts ---

--- START FILE: src\server\services\webSocketService.ts ---
import { FastifyRequest } from 'fastify';
import { pino } from 'pino';
import { z } from 'zod';
import { subscribeToChannel, unsubscribeFromChannel, publishMessage } from '../lib/redis.js';

// Определяем базовые типы, которые нам нужны
type MarketType = 'spot' | 'futures';

interface Kline {
  symbol: string;
  marketType: MarketType;
  interval: string;
  openTime: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  closeTime?: number;
  quoteVolume?: number;
  trades?: number;
  isClosed?: boolean;
}

// Определяем тип для WebSocket соединения из Fastify
// Используем более точную типизацию, соответствующую WebSocket API
interface WebSocketLike {
  send: (data: string) => void;
  on(event: 'message', listener: (data: Buffer) => void): void;
  on(event: 'close', listener: () => void): void;
  on(event: 'error', listener: (error: Error) => void): void;
  close: () => void;
}

type SocketStream = {
  socket: WebSocketLike;
} | WebSocketLike;

// Создаем логгер
const logger = pino({ name: 'webSocketService', level: 'info' });

// Define message schemas
const SubscribeKlineMessageSchema = z.object({
  action: z.literal('subscribe'),
  type: z.literal('kline'),
  params: z.object({
    symbol: z.string(),
    interval: z.string(),
    marketType: z.string(),
  }),
});

const UnsubscribeKlineMessageSchema = z.object({
  action: z.literal('unsubscribe'),
  type: z.literal('kline'),
  params: z.object({
    symbol: z.string(),
    interval: z.string(),
    marketType: z.string(),
  }),
});

const MessageSchema = z.union([
  SubscribeKlineMessageSchema,
  UnsubscribeKlineMessageSchema,
]);

type WebSocketMessage = z.infer<typeof MessageSchema>;
type ClientSubscription = {
  symbol: string;
  interval: string;
  marketType: string;
  channel: string;
};

// Helper functions to work with SocketStream union type
function getWebSocket(connection: SocketStream): WebSocketLike {
  // Check if connection has socket property (SocketStream with socket)
  if ('socket' in connection) {
    return connection.socket;
  }
  // Otherwise, connection is direct WebSocket
  return connection;
}

/**
 * WebSocketManager - синглтон для управления WebSocket соединениями
 */
export class WebSocketManager {
  private static instance: WebSocketManager;
  private clients: Map<string, SocketStream> = new Map(); // clientId -> connection
  private subscriptions: Map<string, Set<string>> = new Map(); // channel -> Set of clientIds
  private clientSubscriptions: Map<string, Set<ClientSubscription>> = new Map(); // clientId -> Set of subscriptions
  private channelSubscribers: Map<string, number> = new Map(); // channel -> number of subscribers

  private constructor() {
    // Private constructor to implement singleton pattern
    logger.info('WebSocketManager initialized');
  }

  /**
   * Получить экземпляр WebSocketManager (синглтон)
   */
  public static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  /**
   * Обработать новое WebSocket соединение
   */
  public handleConnection(connection: SocketStream, request: FastifyRequest): void {
    const clientId = request.id as string;

    // Store client connection
    this.clients.set(clientId, connection);
    this.clientSubscriptions.set(clientId, new Set());

    logger.info({ clientId }, 'Client connected');

    // Get WebSocket instance from connection
    const ws = getWebSocket(connection);

    // Handle incoming messages
    ws.on('message', (message: Buffer) => {
      try {
        const parsedMessage = JSON.parse(message.toString());
        this.handleMessage(clientId, parsedMessage);
      } catch (err) {
        logger.error({ clientId, err }, 'Invalid message format');
        this.sendErrorToClient(clientId, 'Invalid message format');
      }
    });

    // Handle client disconnect
    ws.on('close', () => {
      this.handleDisconnect(clientId);
    });
  }

  /**
   * Обработать сообщение от клиента
   */
  private handleMessage(clientId: string, message: unknown): void {
    try {
      // Validate message format
      const validation = MessageSchema.safeParse(message);
      if (!validation.success) {
        logger.warn({ clientId, message, errors: validation.error.format() }, 'Message validation failed');
        this.sendErrorToClient(clientId, 'Invalid message format');
        return;
      }

      const validatedMessage = validation.data;
      logger.debug({ clientId, message: validatedMessage }, 'Received valid message');

      // Process message based on action
      switch (validatedMessage.action) {
        case 'subscribe':
          this.handleSubscription(clientId, validatedMessage);
          break;
        case 'unsubscribe':
          this.handleUnsubscription(clientId, validatedMessage);
          break;
        default:
          // This should not happen due to Zod validation
          this.sendErrorToClient(clientId, 'Unknown action');
      }
    } catch (err) {
      logger.error({ clientId, err, message }, 'Error processing message');
      this.sendErrorToClient(clientId, 'Error processing message');
    }
  }

  /**
   * Обработать подписку клиента
   */
  private handleSubscription(clientId: string, message: WebSocketMessage): void {
    if (message.action !== 'subscribe') return; // Type guard

    const { symbol, interval, marketType } = message.params;
    
    // Создать имя канала на основе параметров
    const channel = this.getChannelName(message.type, symbol, interval, marketType);

    // Получить текущие подписки клиента
    const clientSubs = this.clientSubscriptions.get(clientId) || new Set();
    
    // Проверить, подписан ли уже клиент на этот канал
    const existingSub = Array.from(clientSubs).find(
      (sub) => 
        sub.symbol === symbol && 
        sub.interval === interval && 
        sub.marketType === marketType
    );

    if (existingSub) {
      // Клиент уже подписан, отправляем уведомление
      logger.debug({ clientId, channel }, 'Client already subscribed to channel');
      this.sendMessageToClient(clientId, {
        type: 'subscribed', 
        channel,
        message: 'Already subscribed'
      });
      return;
    }

    // Добавляем подписку для клиента
    const subscription: ClientSubscription = { symbol, interval, marketType, channel };
    clientSubs.add(subscription);
    this.clientSubscriptions.set(clientId, clientSubs);

    // Добавляем клиента в список подписчиков канала
    const channelClients = this.subscriptions.get(channel) || new Set();
    channelClients.add(clientId);
    this.subscriptions.set(channel, channelClients);

    // Обновить счетчик подписчиков
    const subscribeCount = (this.channelSubscribers.get(channel) || 0) + 1;
    this.channelSubscribers.set(channel, subscribeCount);

    // Если это первая подписка на канал, подписаться в Redis
    if (subscribeCount === 1) {
      this.subscribeToRedisChannel(channel);
    }

    logger.info({ clientId, channel }, 'Client subscribed to channel');
    this.sendMessageToClient(clientId, {
      type: 'subscribed', 
      channel,
      message: 'Successfully subscribed'
    });
  }

  /**
   * Обработать отписку клиента
   */
  private handleUnsubscription(clientId: string, message: WebSocketMessage): void {
    if (message.action !== 'unsubscribe') return; // Type guard

    const { symbol, interval, marketType } = message.params;
    const channel = this.getChannelName(message.type, symbol, interval, marketType);

    this.unsubscribeClientFromChannel(clientId, channel);

    logger.info({ clientId, channel }, 'Client unsubscribed from channel');
    this.sendMessageToClient(clientId, {
      type: 'unsubscribed', 
      channel,
      message: 'Successfully unsubscribed'
    });
  }

  /**
   * Отписать клиента от канала
   */
  private unsubscribeClientFromChannel(clientId: string, channel: string): void {
    // Удаляем подписку из списка подписок клиента
    const clientSubs = this.clientSubscriptions.get(clientId);
    if (clientSubs) {
      const subToRemove = Array.from(clientSubs).find(sub => sub.channel === channel);
      if (subToRemove) {
        clientSubs.delete(subToRemove);
      }
    }

    // Удаляем клиента из списка подписчиков канала
    const channelClients = this.subscriptions.get(channel);
    if (channelClients) {
      channelClients.delete(clientId);
      
      // Если не осталось подписчиков, удаляем канал и отписываемся от Redis
      if (channelClients.size === 0) {
        this.subscriptions.delete(channel);
        this.channelSubscribers.set(channel, 0);
        this.unsubscribeFromRedisChannel(channel);
      } else {
        // Обновляем счетчик подписчиков
        const count = Math.max(0, (this.channelSubscribers.get(channel) || 1) - 1);
        this.channelSubscribers.set(channel, count);
      }
    }
  }

  /**
   * Обработать отключение клиента
   */
  private handleDisconnect(clientId: string): void {
    // Получить все подписки клиента
    const clientSubs = this.clientSubscriptions.get(clientId);
    if (clientSubs) {
      // Для каждой подписки удалить клиента из списка подписчиков
      clientSubs.forEach(sub => {
        const channelClients = this.subscriptions.get(sub.channel);
        if (channelClients) {
          channelClients.delete(clientId);
          
          // Если не осталось подписчиков, удаляем канал
          if (channelClients.size === 0) {
            this.subscriptions.delete(sub.channel);
            this.channelSubscribers.set(sub.channel, 0);
            this.unsubscribeFromRedisChannel(sub.channel);
          } else {
            // Обновляем счетчик подписчиков
            const count = Math.max(0, (this.channelSubscribers.get(sub.channel) || 1) - 1);
            this.channelSubscribers.set(sub.channel, count);
          }
        }
      });
    }
    
    // Удаляем клиента из списков
    this.clients.delete(clientId);
    this.clientSubscriptions.delete(clientId);
    
    logger.info({ clientId }, 'Client disconnected');
  }

  /**
   * Подписаться на Redis-канал и настроить обработку сообщений
   */
  private subscribeToRedisChannel(channel: string): void {
    logger.info({ channel }, 'Subscribing to Redis channel');
    
    // Подписываемся на Redis-канал
    subscribeToChannel(channel, (message) => {
      // Получаем всех клиентов, подписанных на канал
      const subscribers = this.subscriptions.get(channel);
      if (!subscribers || subscribers.size === 0) {
        return;
      }
      
      // Отправляем сообщение каждому клиенту
      subscribers.forEach(clientId => {
        this.sendMessageToClient(clientId, {
          type: 'update',
          channel,
          data: message
        });
      });
    });
  }

  /**
   * Отписаться от Redis-канала
   */
  private unsubscribeFromRedisChannel(channel: string): void {
    logger.info({ channel }, 'Unsubscribing from Redis channel');
    unsubscribeFromChannel(channel);
  }

  /**
   * Создать имя канала на основе типа данных и параметров
   */
  public getChannelName(type: string, symbol: string, interval: string, marketType: string): string {
    return `pubsub:${type}:${symbol}:${interval}:${marketType}`;
  }

  /**
   * Отправить сообщение клиенту
   */
  private sendMessageToClient(clientId: string, message: unknown): void {
    try {
      const connection = this.clients.get(clientId);
      if (!connection) {
        logger.warn({ clientId }, 'Attempt to send message to disconnected client');
        return;
      }

      const serializedMessage = JSON.stringify(message);
      const ws = getWebSocket(connection);
      ws.send(serializedMessage);
    } catch (err) {
      logger.error({ clientId, err }, 'Error sending message to client');
      // Если произошла ошибка при отправке, возможно клиент отключился
      // Удаляем клиента из списка подключенных
      this.handleDisconnect(clientId);
    }
  }

  /**
   * Отправить сообщение об ошибке клиенту
   */
  private sendErrorToClient(clientId: string, errorMessage: string): void {
    this.sendMessageToClient(clientId, {
      type: 'error',
      message: errorMessage
    });
  }

  /**
   * Публиковать обновление свечи
   */
  public publishKlineUpdate(kline: Kline): void {
    const { symbol, interval, marketType } = kline;
    const channel = this.getChannelName('kline', symbol, interval, marketType);
    
    // Публикуем обновление в Redis
    publishMessage(channel, kline);
  }
} 
--- END FILE: src\server\services\webSocketService.ts ---

--- START FILE: src\shared\index.ts ---
export * from './lib';
export * from './market';
export * from './schemas';
export * from './store';
export * from './ui';
--- END FILE: src\shared\index.ts ---

--- START FILE: src\shared\lib\DevTools.tsx ---
"use client";

import '@/shared/lib/wdyr'; // Import for side effects (initialization)
import { useEffect } from 'react';

export function DevTools() {
  useEffect(() => {
    // The import above handles the initialization
    // This component ensures the code runs on the client side
  }, []);
  return null; // This component doesn't render anything
} 
--- END FILE: src\shared\lib\DevTools.tsx ---

--- START FILE: src\shared\lib\ErrorBoundary.tsx ---
'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/shared/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/ui/card';
import { AlertTriangle, RefreshCw, Bug, Home, ExternalLink } from 'lucide-react';

// --- Types --- TODO: Consider moving to a .types.ts file if they grow
interface ErrorMonitoringService {
  captureException: (error: Error, errorInfo?: ErrorInfo, errorId?: string) => void;
  // Add other methods if needed, e.g., setUserContext, addBreadcrumb
}

// --- Placeholder for your actual error monitoring service integration --- TODO: Implement this!
// Example (replace with your actual service, e.g., Sentry, LogRocket, etc.)
const errorMonitoring: ErrorMonitoringService | null = (() => {
  if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
    // Example: Sentry integration (ensure Sentry SDK is initialized elsewhere)
    // if ((window as any).Sentry) {
    //   return {
    //     captureException: (error, errorInfo, errorId) => {
    //       (window as any).Sentry.withScope((scope: any) => {
    //         if (errorInfo) {
    //           scope.setExtras(errorInfo as Record<string, any>);
    //         }
    //         if (errorId) {
    //           scope.setTag('error_id', errorId);
    //         }
    //         (window as any).Sentry.captureException(error);
    //       });
    //     },
    //   };
    // }
    console.warn('ErrorMonitoringService: Production environment detected but no service is configured/initialized. Errors will only be logged to console.');
  }
  // In development or if no service, return a mock or null
  return {
    captureException: (error, errorInfo, errorId) => {
      console.info('[MockErrorMonitoring] captureException called:', { error, errorInfo, errorId });
    }
  };
})();

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  showDetailsInProd?: boolean;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId: string;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    errorId: 'N/A',
  };

  public static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: Math.random().toString(36).substring(2, 11),
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', {
      error,
      errorInfo,
      errorId: this.state.errorId,
    });
    
    this.setState({
      error,
      errorInfo,
    });

    // Log to external error monitoring service
    errorMonitoring?.captureException(error, errorInfo, this.state.errorId);

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo, this.state.errorId);
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      errorId: 'N/A',
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  public render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const showErrorDetails = process.env.NODE_ENV === 'development' || this.props.showDetailsInProd;

      return (
        <div className="min-h-screen w-full flex items-center justify-center p-4 bg-background text-foreground">
          <Card className="w-full max-w-2xl shadow-2xl">
            <CardHeader className="text-center border-b border-border">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10 mb-4">
                <AlertTriangle className="h-8 w-8 text-destructive" />
              </div>
              <CardTitle className="text-3xl font-bold">Что-то пошло не так</CardTitle>
              <CardDescription className="text-muted-foreground mt-2">
                Произошла неожиданная ошибка. Мы уже уведомлены (ID: {this.state.errorId}).
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              {showErrorDetails && this.state.error && (
                <details className="p-3 bg-muted/50 rounded-lg border border-border">
                  <summary className="cursor-pointer font-medium text-sm mb-2 text-destructive">
                    Техническая информация (для отладки)
                  </summary>
                  <div className="space-y-2 text-xs">
                    <p className="font-mono text-destructive-foreground bg-destructive/20 p-2 rounded">
                      <strong>Сообщение:</strong> {this.state.error.message}
                    </p>
                    {this.state.error.stack && (
                      <div>
                        <p className="font-medium mt-2">Stack trace:</p>
                        <pre className="whitespace-pre-wrap break-all p-2 bg-background rounded mt-1 border border-border overflow-auto max-h-40">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                    {this.state.errorInfo?.componentStack && (
                      <div>
                        <p className="font-medium mt-2">Component stack:</p>
                        <pre className="whitespace-pre-wrap break-all p-2 bg-background rounded mt-1 border border-border overflow-auto max-h-40">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}

              <div className="text-center text-muted-foreground text-sm">
                <p>Вы можете попробовать следующие действия:</p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                <Button onClick={this.handleRetry} variant="default" className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" /> Попробовать снова
                </Button>
                <Button onClick={this.handleReload} variant="outline" className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" /> Перезагрузить
                </Button>
                <Button onClick={this.handleGoHome} variant="outline" className="w-full">
                  <Home className="h-4 w-4 mr-2" /> На главную
                </Button>
              </div>

              {process.env.NODE_ENV === 'production' && !this.props.showDetailsInProd && (
                <p className="text-center text-xs text-muted-foreground pt-4">
                  Если проблема не исчезнет, пожалуйста, свяжитесь со службой поддержки, указав ID ошибки: 
                  <strong className="text-foreground">{this.state.errorId}</strong>.
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// HOC for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  boundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...boundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name || 'Component'})`;
  return WrappedComponent;
}

// Hook for error reporting in functional components
export function useErrorHandler() {
  return React.useCallback((error: Error, errorInfo?: ErrorInfo, context?: Record<string, any>) => {
    const errorId = Math.random().toString(36).substring(2, 11);
    console.error('Error caught by useErrorHandler:', {
      error,
      errorInfo,
      context,
      errorId
    });
    errorMonitoring?.captureException(error, { ...(errorInfo || {}), ...context }, errorId);
    // Here you might also want to show a global notification to the user
    // For example, using a toast library: toast.error(`An error occurred (ID: ${errorId}). Please try again.`);
  }, []);
}
--- END FILE: src\shared\lib\ErrorBoundary.tsx ---

--- START FILE: src\shared\lib\index.ts ---
export * from './utils';
export * from './ErrorBoundary';
export * from './DevTools';
export * from './wdyr'; // Assuming wdyr setup might export something or is imported for side effects
--- END FILE: src\shared\lib\index.ts ---

--- START FILE: src\shared\lib\utils.ts ---
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Utility function to merge Tailwind CSS classes with clsx.
 * @param inputs - Class values to merge.
 * @returns Merged class string.
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Throttles a function, ensuring it's called at most once per limit milliseconds.
 * @param func - The function to throttle.
 * @param limit - The throttle limit in milliseconds.
 * @returns A throttled version of the function.
 */
export function throttle<F extends (...args: any[]) => any>(func: F, limit: number) {
    let inThrottle: boolean;
    let lastResult: ReturnType<F>;
  
    return function (this: ThisParameterType<F>, ...args: Parameters<F>): ReturnType<F> {
      if (!inThrottle) {
        inThrottle = true;
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const context = this;
        setTimeout(() => (inThrottle = false), limit);
        lastResult = func.apply(context, args);
      }
      return lastResult;
    };
  }
  
  /**
   * Debounces a function, delaying its execution until after wait milliseconds have elapsed
   * since the last time it was invoked.
   * @param func - The function to debounce.
   * @param wait - The debounce wait time in milliseconds.
   * @returns A debounced version of the function.
   */
  export function debounce<F extends (...args: any[]) => any>(func: F, wait: number) {
    let timeout: NodeJS.Timeout | undefined;
    let lastResult: ReturnType<F>;
  
    return function (this: ThisParameterType<F>, ...args: Parameters<F>): ReturnType<F> {
      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const context = this;
      const later = () => {
        timeout = undefined;
        lastResult = func.apply(context, args);
      };
  
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      return lastResult; // This will return undefined if the function has not been called yet
    };
  } 


  export const hexFromRgba = (rgbaString: string): string => {
    const match = rgbaString.match(
      /^rgba?\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})(?:,\s*(\d(?:\.\d+)?))?\)$/
    );
    if (!match) {
      // Return a default color or throw an error if the format is unexpected
      console.warn(`Invalid RGBA string format: ${rgbaString}. Defaulting to #000000.`);
      return '#000000';
    }
  
    const r = parseInt(match[1], 10);
    const g = parseInt(match[2], 10);
    const b = parseInt(match[3], 10);
    // Alpha is optional, defaults to 1 if not present or invalid
    const a = match[4] ? parseFloat(match[4]) : 1;
  
    // Clamp alpha to [0, 1]
    const clampedAlpha = Math.max(0, Math.min(1, a));
  
    const toHex = (c: number) => {
      const hex = c.toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };
  
    const hexR = toHex(r);
    const hexG = toHex(g);
    const hexB = toHex(b);
  
    if (clampedAlpha === 1) {
      return `#${hexR}${hexG}${hexB}`;
    }
    // For alpha, convert to a 0-255 range and then to hex
    const hexA = toHex(Math.round(clampedAlpha * 255));
    return `#${hexR}${hexG}${hexB}${hexA}`;
  };
  
  export const rgbaFromHex = (hex: string, alpha = 1): string => {
    if (!hex || typeof hex !== 'string') {
      console.warn(`Invalid HEX string: ${hex}. Defaulting to rgba(0,0,0,${alpha}).`);
      return `rgba(0,0,0,${alpha})`;
    }
    let hexValue = hex.replace('#', '');
  
    if (hexValue.length === 3) {
      hexValue = hexValue
        .split('')
        .map((char) => char + char)
        .join('');
    }
    
    // Handle 8-digit hex (with alpha)
    let parsedAlpha = alpha;
    if (hexValue.length === 8) {
      const alphaHex = hexValue.substring(6, 8);
      parsedAlpha = parseInt(alphaHex, 16) / 255;
      hexValue = hexValue.substring(0, 6);
    } else if (hexValue.length !== 6) {
       console.warn(`Invalid HEX string length: ${hex}. Defaulting to rgba(0,0,0,${alpha}).`);
      return `rgba(0,0,0,${alpha})`;
    }
  
    const bigint = parseInt(hexValue, 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
  
    // Clamp provided alpha to [0, 1]
    const clampedAlphaInput = Math.max(0, Math.min(1, alpha));
    // Use parsedAlpha if hex had alpha, otherwise use clamped input alpha
    const finalAlpha = hexValue.length === 8 && hex.length > 7 ? parsedAlpha : clampedAlphaInput;
  
    return `rgba(${r},${g},${b},${Number(finalAlpha.toFixed(2))})`;
  };
  
  // Placeholder for other formatting functions, assuming they exist or will be added
  export const formatPrice = (price: string | number | null | undefined): string => {
    if (price === null || price === undefined) return '-';
    const num = Number(price);
    if (isNaN(num)) return '-';
    // Basic formatting, can be expanded
    return num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 8 });
  };
  
  export const formatPercent = (percent: string | number | null | undefined): string => {
    if (percent === null || percent === undefined) return '-';
    const num = Number(percent);
    if (isNaN(num)) return '-';
    return `${num.toFixed(2)}%`;
  };
  
  export const formatVolume = (volume: string | number | null | undefined): string => {
    if (volume === null || volume === undefined) return '-';
    const num = Number(volume);
    if (isNaN(num)) return '-';
    // Basic formatting, can be expanded (e.g., K, M, B for large numbers)
    return num.toLocaleString(undefined, { maximumFractionDigits: 2 });
  };
  
  export const formatCount = (count: string | number | null | undefined): string => {
    if (count === null || count === undefined) return '-';
    const num = Number(count);
    if (isNaN(num)) return '-';
    return num.toLocaleString();
  };
  
  export const formatSpread = (spread: string | number | null | undefined): string => {
    if (spread === null || spread === undefined) return '-';
    const num = Number(spread);
    if (isNaN(num)) return '-';
    return num.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 8 });
  }; 

// --- Helper function to compare map keys (moved from page.tsx) ---
export function haveSameKeys(mapA: Map<string, any>, mapB: Map<string, any>): boolean {
    if (mapA.size !== mapB.size) {
        return false;
    }
    for (const key of mapA.keys()) {
        if (!mapB.has(key)) {
            return false;
        }
    }
    return true;
}

/**
 * Type guard to check if a value is an Error object
 * @param value - The value to check
 * @returns True if the value is an Error object
 */
export function isError(value: unknown): value is Error {
    return value instanceof Error;
}


--- END FILE: src\shared\lib\utils.ts ---

--- START FILE: src\shared\lib\wdyr.ts ---
import React from 'react';

// Ensure this only runs in development and on the client side
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  const whyDidYouRender = require('@welldone-software/why-did-you-render');
  whyDidYouRender(React, {
    trackAllPureComponents: true, // Track all pure components
    trackHooks: true, // Track custom hooks
    logOwnerReasons: true, // Log reasons based on owner components
    collapseGroups: true, // Collapse console groups
  });
} 
--- END FILE: src\shared\lib\wdyr.ts ---

--- START FILE: src\shared\market\hooks.ts ---
import { useInfiniteQuery } from '@tanstack/react-query';
import {
  Kline,
  Ticker,
  MarketType,
  WSConnectionStatus,
} from '@/shared/schemas/market.schema';
import { z } from 'zod';
import { useEffect, useState, useCallback } from 'react';
import { useMarketStore } from '../store/marketStore';

// Zod schema for kline query parameters, should match backend
const GetKlinesSchema = z.object({
  symbol: z.string(),
  interval: z.string(),
  marketType: z.enum(['spot', 'futures']),
  limit: z.number().int().positive().optional(),
  endTime: z.number().int().positive().optional(),
});

type GetKlinesQuery = z.infer<typeof GetKlinesSchema>;

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api/v1';
const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3000/ws/market';

// Kline type as it comes from the API (with string prices)
type SerializedKline = Omit<Kline, 'open' | 'high' | 'low' | 'close' | 'volume'> & {
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
};

/**
 * @description Parses a serialized kline from the backend into the application's Kline format.
 * @param {SerializedKline} serializedKline - The kline object from the API response.
 * @returns {Kline} The parsed kline object with correct types (numbers).
 */
const parseKline = (serializedKline: SerializedKline): Kline => ({
  ...serializedKline,
  openTime: new Date(serializedKline.openTime).getTime(),
  open: parseFloat(serializedKline.open),
  high: parseFloat(serializedKline.high),
  low: parseFloat(serializedKline.low),
  close: parseFloat(serializedKline.close),
  volume: parseFloat(serializedKline.volume),
});

/**
 * @description Fetches historical klines from the backend API.
 * @param {GetKlinesQuery} params - The query parameters.
 * @returns {Promise<Kline[]>} A promise that resolves to an array of parsed klines.
 */
const fetchHistoricalKlines = async ({ pageParam, ...params }: GetKlinesQuery & { pageParam?: number }): Promise<Kline[]> => {
  const urlParams = new URLSearchParams({
    symbol: params.symbol,
    interval: params.interval,
    marketType: params.marketType,
    limit: String(params.limit || 1000),
  });

  if (pageParam) {
    urlParams.append('endTime', String(pageParam));
  }

  const response = await fetch(`${API_BASE_URL}/klines?${urlParams.toString()}`);

  if (!response.ok) {
    const errorBody = await response.json();
    throw new Error(errorBody.message || 'Failed to fetch klines');
  }

  const data: SerializedKline[] = await response.json();
  return data.map(parseKline);
};

// Функция для загрузки тикеров с бэкенда
const fetchTickers = async (marketTypes?: MarketType[]): Promise<Ticker[]> => {
  const urlParams = new URLSearchParams();
  
  if (marketTypes && marketTypes.length > 0) {
    marketTypes.forEach(type => urlParams.append('marketTypes', type));
  }
  
  const response = await fetch(`${API_BASE_URL}/tickers?${urlParams.toString()}`);
  
  if (!response.ok) {
    const errorBody = await response.json();
    throw new Error(errorBody.message || 'Failed to fetch tickers');
  }
  
  return await response.json();
};

export const getKlinesQueryKey = (symbol: string, interval: string, marketType: MarketType) => ['klines', marketType, symbol, interval];

/**
 * @description A TanStack Query hook to fetch historical kline data with infinite scrolling.
 * @param {string} symbol - The trading symbol.
 * @param {string} interval - The kline interval.
 * @param {MarketType} marketType - The market type ('spot' or 'futures').
 * @returns The result of the useInfiniteQuery hook.
 */
export function useHistoricalKlinesQuery(symbol: string, interval: string, marketType: MarketType) {
  return useInfiniteQuery({
    queryKey: getKlinesQueryKey(symbol, interval, marketType),
    queryFn: ({ pageParam }) => fetchHistoricalKlines({ symbol, interval, marketType, pageParam: pageParam as number | undefined }),
    getNextPageParam: (lastPage) => {
      // If the last page is empty or has fewer than 10 items, we've likely reached the end.
      if (!lastPage || lastPage.length < 10) {
        return undefined;
      }
      // The next page starts from the openTime of the oldest kline in the current page.
      return lastPage[0].openTime;
    },
    initialPageParam: undefined as number | undefined,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: !!symbol && !!interval && !!marketType,
  });
}

// WebSocket client
export const wsClient = {
  socket: null as WebSocket | null,
  status: WSConnectionStatus.Disconnected,
  onMessage: null as ((data: any) => void) | null,
  onStatusChange: null as ((status: WSConnectionStatus) => void) | null,

  connect(url: string = WS_BASE_URL) {
    if (this.socket) {
      this.disconnect();
    }

    try {
      this.socket = new WebSocket(url);
      this.status = WSConnectionStatus.Connecting;
      
      if (this.onStatusChange) {
        this.onStatusChange(WSConnectionStatus.Connecting);
      }

      this.socket.onopen = () => {
        this.status = WSConnectionStatus.Connected;
        if (this.onStatusChange) {
          this.onStatusChange(WSConnectionStatus.Connected);
        }
      };

      this.socket.onclose = () => {
        this.status = WSConnectionStatus.Disconnected;
        if (this.onStatusChange) {
          this.onStatusChange(WSConnectionStatus.Disconnected);
        }
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.status = WSConnectionStatus.Error;
        if (this.onStatusChange) {
          this.onStatusChange(WSConnectionStatus.Error);
        }
      };

      this.socket.onmessage = (event) => {
        if (this.onMessage) {
          try {
            const data = JSON.parse(event.data);
            this.onMessage(data);
          } catch (err) {
            console.error('Failed to parse WebSocket message:', err);
          }
        }
      };
    } catch (err) {
      console.error('Failed to connect WebSocket:', err);
      this.status = WSConnectionStatus.Error;
      if (this.onStatusChange) {
        this.onStatusChange(WSConnectionStatus.Error);
      }
    }
  },

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
      this.status = WSConnectionStatus.Disconnected;
      if (this.onStatusChange) {
        this.onStatusChange(WSConnectionStatus.Disconnected);
      }
    }
  },

  send(data: any) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
      return true;
    }
    return false;
  },

  subscribe(channel: string, params: any = {}) {
    return this.send({
      action: 'subscribe',
      channel,
      params
    });
  },

  unsubscribe(channel: string, params: any = {}) {
    return this.send({
      action: 'unsubscribe',
      channel,
      params
    });
  }
};

// Хук для работы с WebSocket соединением
export const useWebSocketConnection = () => {
  const [status, setStatus] = useState<WSConnectionStatus>(wsClient.status);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    wsClient.onStatusChange = setStatus;

    return () => {
      wsClient.onStatusChange = null;
    };
  }, []);

  const connect = useCallback((url: string = WS_BASE_URL) => {
    setError(null);
    try {
      wsClient.connect(url);
    } catch (err) {
      setError((err as Error).message);
    }
  }, []);

  const disconnect = useCallback(() => {
    wsClient.disconnect();
  }, []);

  return {
    status,
    error,
    connect,
    disconnect,
    send: wsClient.send.bind(wsClient),
    subscribe: wsClient.subscribe.bind(wsClient),
    unsubscribe: wsClient.unsubscribe.bind(wsClient)
  };
};

// Хук для автоматического подключения к WebSocket с URL
export const useWebSocketAutoConnectWithUrl = (url?: string) => {
  const { connect, disconnect, status } = useWebSocketConnection();

  useEffect(() => {
    if (url) {
      connect(url);
    } else {
      connect();
    }
    return () => disconnect();
  }, [url, connect, disconnect]);

  return status;
};

// Интерфейс для MarketDataManager
export interface MarketDataManager {
  wsStatus: WSConnectionStatus;
  isInitialLoading: boolean;
  isWsConnected: boolean;
  error: string | null;
  connectWebSocket: (url?: string) => void;
  loadInitialTickers: (marketTypes?: MarketType[]) => Promise<void>;
  subscribeToSymbol: (symbol: string, interval: string, marketType: MarketType) => () => void;
  subscribeToTicker: (symbol: string, marketType: MarketType) => boolean;
  unsubscribeFromTicker: (symbol: string, marketType: MarketType) => boolean;
  subscribeToKlines: (symbol: string, interval: string, marketType: MarketType) => boolean;
  unsubscribeFromKlines: (symbol: string, interval: string, marketType: MarketType) => boolean;
  setTickers: (tickers: Ticker[]) => void;
  setKlines: (symbol: string, interval: string, klines: Kline[]) => void;
  appendKlines: (symbol: string, interval: string, klines: Kline[]) => void;
  updateKline: (symbol: string, interval: string, kline: Kline) => void;
  clearData: () => void;
  setLoadingTickers: (isLoading: boolean) => void;
  setLoadingKlines: (symbol: string, interval: string, isLoading: boolean) => void;
  setTickersError: (error: string | null) => void;
  setKlinesError: (symbol: string, interval: string, error: string | null) => void;
}

// Хук для управления рыночными данными
export const useMarketDataManager = (): MarketDataManager => {
  const marketStore = useMarketStore();
  const [isInitialLoading, setIsInitialLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { status: wsStatus, connect, subscribe, unsubscribe } = useWebSocketConnection();
  
  const connectWebSocket = useCallback((url?: string) => {
    connect(url);
  }, [connect]);
  
  const loadInitialTickers = useCallback(async (marketTypes?: MarketType[]) => {
    try {
      setIsInitialLoading(true);
      setError(null);
      
      const tickers = await fetchTickers(marketTypes);
      marketStore.setTickers(tickers);
      
      setIsInitialLoading(false);
    } catch (err) {
      setError((err as Error).message);
      setIsInitialLoading(false);
    }
  }, [marketStore]);
  
  const subscribeToTicker = useCallback((symbol: string, marketType: MarketType) => {
    return subscribe('ticker', { symbol, marketType });
  }, [subscribe]);
  
  const unsubscribeFromTicker = useCallback((symbol: string, marketType: MarketType) => {
    return unsubscribe('ticker', { symbol, marketType });
  }, [unsubscribe]);
  
  const subscribeToKlines = useCallback((symbol: string, interval: string, marketType: MarketType) => {
    return subscribe('kline', { symbol, interval, marketType });
  }, [subscribe]);
  
  const unsubscribeFromKlines = useCallback((symbol: string, interval: string, marketType: MarketType) => {
    return unsubscribe('kline', { symbol, interval, marketType });
  }, [unsubscribe]);
  
  // Метод для подписки на данные символа
  const subscribeToSymbol = useCallback((symbol: string, interval: string, marketType: MarketType) => {
    // Подписываемся на обновления свечей
    subscribeToKlines(symbol, interval, marketType);
    
    // Возвращаем функцию отписки для использования в useEffect cleanup
    return () => {
      unsubscribeFromKlines(symbol, interval, marketType);
    };
  }, [subscribeToKlines, unsubscribeFromKlines]);
  
  return {
    ...marketStore,
    wsStatus,
    isInitialLoading,
    isWsConnected: wsStatus === WSConnectionStatus.Connected,
    error,
    connectWebSocket,
    loadInitialTickers,
    subscribeToSymbol,
    subscribeToTicker,
    unsubscribeFromTicker,
    subscribeToKlines,
    unsubscribeFromKlines
  };
}; 
--- END FILE: src\shared\market\hooks.ts ---

--- START FILE: src\shared\market\index.ts ---
export * from './hooks';
--- END FILE: src\shared\market\index.ts ---

--- START FILE: src\shared\market\websocket.ts ---
import { pino } from "pino";
import { useEffect, useState } from "react";

// Configure logger
const logger = pino({ 
  name: 'websocket-manager',
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  enabled: process.env.NODE_ENV !== 'test',
});

// WebSocket URL configuration
const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3000/ws';

// WebSocket connection states
export enum WebSocketState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
}

// WebSocket message types
export enum MessageType {
  SUBSCRIBE = 'subscribe',
  UNSUBSCRIBE = 'unsubscribe',
  SUBSCRIBED = 'subscribed',
  UNSUBSCRIBED = 'unsubscribed',
  UPDATE = 'update',
  ERROR = 'error',
}

// Subscription types
export enum SubscriptionType {
  KLINE = 'kline',
}

// Message parameters interfaces
export interface KlineParams {
  symbol: string;
  interval: string;
  marketType: string;
}

// Message for subscribing to klines
export interface SubscribeMessage<T> {
  action: 'subscribe' | 'unsubscribe';
  type: SubscriptionType;
  params: T;
}

// Messages from server
export interface ServerMessage {
  type: MessageType;
  channel?: string;
  message?: string;
  data?: unknown;
}

// Subscription callback type
export type SubscriptionCallback<T> = (data: T) => void;

// Subscription handler interface
interface SubscriptionHandler<T> {
  callback: SubscriptionCallback<T>;
  channel: string;
}

/**
 * Singleton class to manage WebSocket connection and subscriptions
 */
export class WebSocketManager {
  private static instance: WebSocketManager;
  private ws: WebSocket | null = null;
  private state: WebSocketState = WebSocketState.DISCONNECTED;
  private reconnectAttempts: number = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private reconnectInterval: number = 1000; // Start with 1s, will increase with backoff
  private maxReconnectInterval: number = 30000; // Max 30s interval between reconnect attempts
  private subscriptions: Map<string, SubscriptionHandler<unknown>> = new Map();
  private pendingMessages: Array<string> = []; // Messages to send when connection is established
  private autoReconnect: boolean = true;

  // Status callback to notify UI of connection changes
  private statusCallback?: (status: WebSocketState) => void;

  private constructor() {
    // Private constructor to ensure singleton
  }

  /**
   * Get the singleton instance
   */
  public static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  /**
   * Set a callback to be notified about connection status changes
   */
  public setStatusCallback(callback: (status: WebSocketState) => void): void {
    this.statusCallback = callback;
    // Immediately call with current status if connection exists
    if (this.statusCallback) {
      this.statusCallback(this.state);
    }
  }

  /**
   * Connect to the WebSocket server
   */
  public connect(): void {
    if (this.ws && (this.state === WebSocketState.CONNECTED || this.state === WebSocketState.CONNECTING)) {
      logger.debug('WebSocket already connected or connecting');
      return;
    }

    this.updateState(WebSocketState.CONNECTING);
    
    try {
      this.ws = new WebSocket(`${WS_BASE_URL}/market`);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      
      logger.debug('WebSocket connection initialized');
    } catch (error) {
      logger.error(error, 'Failed to initialize WebSocket connection');
      this.scheduleReconnect();
    }
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    this.autoReconnect = false;
    this.clearReconnectTimer();
    
    if (this.ws) {
      this.ws.onopen = null;
      this.ws.onmessage = null;
      this.ws.onclose = null;
      this.ws.onerror = null;
      
      if (this.state === WebSocketState.CONNECTED) {
        this.ws.close(1000, 'Client initiated disconnect');
      }
      
      this.ws = null;
    }
    
    this.updateState(WebSocketState.DISCONNECTED);
    logger.debug('WebSocket disconnected by client');
  }

  /**
   * Subscribe to kline updates
   */
  public subscribeKline(
    symbol: string,
    interval: string,
    marketType: string,
    callback: SubscriptionCallback<unknown>
  ): void {
    const channel = this.getKlineChannel(symbol, interval, marketType);
    
    // Store the subscription
    this.subscriptions.set(channel, { callback, channel });
    
    // Prepare subscription message
    const message: SubscribeMessage<KlineParams> = {
      action: 'subscribe',
      type: SubscriptionType.KLINE,
      params: { symbol, interval, marketType }
    };
    
    // Send or queue the message
    this.sendOrQueueMessage(message);
    logger.debug({ channel }, 'Subscribing to kline updates');
  }

  /**
   * Unsubscribe from kline updates
   */
  public unsubscribeKline(
    symbol: string,
    interval: string,
    marketType: string
  ): void {
    const channel = this.getKlineChannel(symbol, interval, marketType);
    
    // Prepare unsubscription message
    const message: SubscribeMessage<KlineParams> = {
      action: 'unsubscribe',
      type: SubscriptionType.KLINE,
      params: { symbol, interval, marketType }
    };
    
    // Send or queue the message
    this.sendOrQueueMessage(message);
    
    // Remove the subscription
    this.subscriptions.delete(channel);
    logger.debug({ channel }, 'Unsubscribing from kline updates');
  }

  /**
   * Send a message or queue it if not connected
   */
  private sendOrQueueMessage(message: unknown): void {
    const serialized = JSON.stringify(message);
    
    if (this.state === WebSocketState.CONNECTED && this.ws) {
      this.ws.send(serialized);
      logger.debug({ message }, 'Message sent');
    } else {
      // Queue the message to send when connected
      this.pendingMessages.push(serialized);
      logger.debug({ message }, 'Message queued (not connected)');
      
      // Ensure connection is established
      if (this.state === WebSocketState.DISCONNECTED) {
        this.connect();
      }
    }
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(): void {
    this.updateState(WebSocketState.CONNECTED);
    this.reconnectAttempts = 0;
    this.reconnectInterval = 1000; // Reset reconnect interval
    this.autoReconnect = true;
    
    logger.info('WebSocket connected');
    
    // Send any queued messages
    this.sendQueuedMessages();
    
    // Resubscribe to all active subscriptions
    this.resubscribeAll();
  }

  /**
   * Handle WebSocket message event
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data) as ServerMessage;
      
      if (message.type === MessageType.UPDATE && message.channel && message.data) {
        // Handle update message - call the appropriate callback
        const handler = this.subscriptions.get(message.channel);
        if (handler && handler.callback) {
          handler.callback(message.data);
        }
      } else if (message.type === MessageType.ERROR) {
        // Handle error message
        logger.error({ message }, 'Received error from server');
      } else {
        // Handle other message types (subscribed, unsubscribed, etc.)
        logger.debug({ message }, 'Received message from server');
      }
    } catch (error) {
      logger.error({ error, data: event.data }, 'Failed to parse message');
    }
  }

  /**
   * Handle WebSocket close event
   */
  private handleClose(event: CloseEvent): void {
    const wasConnected = this.state === WebSocketState.CONNECTED;
    this.updateState(WebSocketState.DISCONNECTED);
    
    // Log appropriate message based on close code
    if (event.code === 1000) {
      logger.info('WebSocket closed normally');
    } else {
      logger.warn({ code: event.code, reason: event.reason }, 'WebSocket closed unexpectedly');
    }
    
    // Attempt to reconnect if it wasn't a normal closure
    if (wasConnected && this.autoReconnect && event.code !== 1000) {
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket error event
   */
  private handleError(event: Event): void {
    logger.error(event, 'WebSocket error occurred');
    
    // State will be updated by the close handler which is typically called after an error
  }

  /**
   * Schedule a reconnection attempt with exponential backoff
   */
  private scheduleReconnect(): void {
    if (this.reconnectTimer !== null || !this.autoReconnect) {
      return;
    }
    
    this.updateState(WebSocketState.RECONNECTING);
    this.reconnectAttempts++;
    
    // Calculate backoff time (cap at maxReconnectInterval)
    const delay = Math.min(
      this.reconnectInterval * Math.pow(1.5, Math.min(this.reconnectAttempts - 1, 10)),
      this.maxReconnectInterval
    );
    
    logger.debug({ attempt: this.reconnectAttempts, delay }, 'Scheduling reconnection');
    
    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null;
      this.connect();
    }, delay);
  }

  /**
   * Clear the reconnect timer
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer !== null) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Send all queued messages
   */
  private sendQueuedMessages(): void {
    if (this.ws && this.state === WebSocketState.CONNECTED) {
      while (this.pendingMessages.length > 0) {
        const message = this.pendingMessages.shift();
        if (message) {
          this.ws.send(message);
          logger.debug('Sent queued message');
        }
      }
    }
  }

  /**
   * Resubscribe to all active subscriptions
   */
  private resubscribeAll(): void {
    if (this.subscriptions.size === 0) {
      return;
    }
    
    logger.debug({ count: this.subscriptions.size }, 'Resubscribing to all active channels');
    
    // Extract channel from subscription keys to parse parameters
    for (const [channel] of this.subscriptions.entries()) {
      // Parse channel name to extract parameters
      const parts = channel.split(':');
      if (parts.length === 5 && parts[1] === 'kline') {
        const type = parts[1];
        const symbol = parts[2];
        const interval = parts[3];
        const marketType = parts[4];
        
        const message: SubscribeMessage<KlineParams> = {
          action: 'subscribe',
          type: SubscriptionType.KLINE,
          params: { symbol, interval, marketType }
        };
        
        this.sendOrQueueMessage(message);
      }
    }
  }

  /**
   * Update the connection state and notify listeners
   */
  private updateState(newState: WebSocketState): void {
    if (this.state !== newState) {
      this.state = newState;
      
      // Notify status callback if present
      if (this.statusCallback) {
        this.statusCallback(newState);
      }
    }
  }

  /**
   * Generate a kline channel name from parameters
   */
  private getKlineChannel(symbol: string, interval: string, marketType: string): string {
    return `pubsub:kline:${symbol}:${interval}:${marketType}`;
  }

  /**
   * Get the current connection state
   */
  public getState(): WebSocketState {
    return this.state;
  }

  /**
   * Get the number of active subscriptions
   */
  public getSubscriptionCount(): number {
    return this.subscriptions.size;
  }

  /**
   * Check if the client is connected
   */
  public isConnected(): boolean {
    return this.state === WebSocketState.CONNECTED;
  }

  /**
   * Enable auto-reconnect behavior
   */
  public enableAutoReconnect(): void {
    this.autoReconnect = true;
    if (this.state === WebSocketState.DISCONNECTED) {
      this.connect();
    }
  }

  /**
   * Disable auto-reconnect behavior
   */
  public disableAutoReconnect(): void {
    this.autoReconnect = false;
  }
}

// Export singleton instance
export const wsManager = WebSocketManager.getInstance();

/**
 * Hook to ensure WebSocket is connected when component is mounted
 */
export function useWebSocketAutoConnect(): WebSocketState {
  const [connectionState, setConnectionState] = useState<WebSocketState>(
    wsManager.getState()
  );

  useEffect(() => {
    // Set up status callback to update our state
    wsManager.setStatusCallback(setConnectionState);
    
    // Connect if not already connected
    if (!wsManager.isConnected()) {
      wsManager.enableAutoReconnect();
      wsManager.connect();
    }
    
    // Cleanup function
    return () => {
      // Remove our status callback when component unmounts
      wsManager.setStatusCallback(() => {});
      // We don't disconnect on unmount as other components may be using the connection
    };
  }, []);

  return connectionState;
} 
--- END FILE: src\shared\market\websocket.ts ---

--- START FILE: src\shared\schemas\index.ts ---
export * from './market.schema';
export * from './settings.schema';
export * from './indicators.schema';

--- END FILE: src\shared\schemas\index.ts ---

--- START FILE: src\shared\schemas\indicators.schema.ts ---
import { z } from 'zod';
import { LineStyle, LineWidth } from 'lightweight-charts';
import { KlineDataSchema } from './market.schema';

/**
 * -----------------------------------------------
 * Indicator-Related Schemas
 * -----------------------------------------------
 * This file defines Zod schemas for technical indicator structures,
 * including parameters, styles, outputs, and definitions.
 */

// Basic value types for indicator parameters
export const IndicatorParamValueSchema = z.union([
  z.string(),
  z.number(),
  z.boolean(),
  z.null(),
]);
export type IndicatorParamValue = z.infer<typeof IndicatorParamValueSchema>;

// Schema for timeframe identifiers
export const TimeFrameSchema = z.string();
export type TimeFrame = z.infer<typeof TimeFrameSchema>;

// Schema for describing an indicator's input parameter
export const IndicatorParamSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['number', 'integer', 'string', 'boolean', 'select', 'source']),
  defaultValue: IndicatorParamValueSchema,
  min: z.number().optional(),
  max: z.number().optional(),
  step: z.number().optional(),
  options: z.array(z.object({
    value: IndicatorParamValueSchema,
    label: z.string(),
  })).optional(),
  description: z.string().optional(),
});
export type IndicatorParam = z.infer<typeof IndicatorParamSchema>;

// Schema for visual styling of indicator plots
export const IndicatorStyleSchema = z.object({
  color: z.string(),
  lineWidth: z.number().min(1).max(4).optional().transform(v => v as LineWidth | undefined),
  lineStyle: z.number().min(0).max(4).optional().transform(v => v as LineStyle | undefined),
  opacity: z.number().min(0).max(1).optional(),
  visible: z.boolean().optional(),
});
export type IndicatorStyle = z.infer<typeof IndicatorStyleSchema>;

// Schema for describing a single output of an indicator (e.g., a line or histogram)
export const IndicatorOutputInfoSchema = z.object({
  id: z.string(),
  name: z.string(),
  color: z.string(),
  type: z.enum(['line', 'histogram', 'area', 'point']),
  lineWidth: z.number().min(1).max(4).optional().transform(v => v as LineWidth | undefined),
  lineStyle: z.number().min(0).max(4).optional().transform(v => v as LineStyle | undefined),
  visibleInStatistics: z.boolean(),
  opacity: z.number().min(0).max(1).optional(),
  pane: z.string().optional(),
});
export type IndicatorOutputInfo = z.infer<typeof IndicatorOutputInfoSchema>;
export type IndicatorOutput = z.infer<typeof IndicatorOutputInfoSchema>; // Alias for compatibility

// Schema for a generic map of indicator parameters
export const IndicatorParamsSchema = z.record(IndicatorParamValueSchema);
export type IndicatorParams = z.infer<typeof IndicatorParamsSchema>;

// Schema for the result of an indicator calculation for a single time point
export const CalculatedIndicatorResultSchema = z.object({
  time: z.number(),
  values: z.record(z.union([z.number(), z.null()])),
});
export type CalculatedIndicatorResult = z.infer<typeof CalculatedIndicatorResultSchema>;

// Schema for an instance of an indicator on a chart
export const IndicatorInstanceSchema = z.object({
  instanceId: z.string(),
  chartId: z.union([z.string(), z.literal('global')]),
  indicatorId: z.string(),
  name: z.string(),
  params: IndicatorParamsSchema,
  visible: z.boolean(),
  styleOverrides: z.record(IndicatorStyleSchema.partial()),
  paneConfig: z.object({
    height: z.number().optional(),
  }).optional(),
  tableOutputIds: z.array(z.string()).optional(),
  tableTimeframe: z.string().optional(),
});
export type IndicatorInstance<TParams extends IndicatorParams = IndicatorParams> =
  Omit<z.infer<typeof IndicatorInstanceSchema>, 'params'> & { params: TParams };

// Schema for the full definition of an indicator
// Note: `calculate`, `formatValue`, and `getShortName` are functions and not part of the schema
// for validation purposes. They are added back to the derived TypeScript type.
export const IndicatorDefinitionSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  shortName: z.string().optional(),
  category: z.string(),
  hasPane: z.boolean(),
  plotOnMainPane: z.boolean().optional(),
  params: z.array(IndicatorParamSchema),
  outputs: z.array(IndicatorOutputInfoSchema),
  defaultStyle: z.record(IndicatorStyleSchema.partial()).optional(),
  defaultParams: IndicatorParamsSchema,
});

// The full TypeScript type for an indicator definition, including calculation functions
export type IndicatorDefinition<TParams extends IndicatorParams = IndicatorParams> =
  z.infer<typeof IndicatorDefinitionSchema> & {
  defaultParams: TParams;
  calculate: (data: (z.infer<typeof KlineDataSchema>)[] | number[], params: TParams) => CalculatedIndicatorResult[];
  formatValue?: (value: number, outputId: string) => string;
  getShortName?: (params: TParams) => string;
};

// Schema for calculated indicator data bundled with its metadata
export const CalculatedIndicatorWithMetadataSchema = IndicatorInstanceSchema.extend({
  symbol: z.string(),
  data: z.array(z.record(z.union([z.number(), z.null()]))),
  timestamps: z.array(z.number()),
});
export type CalculatedIndicatorWithMetadata = z.infer<typeof CalculatedIndicatorWithMetadataSchema>; 
--- END FILE: src\shared\schemas\indicators.schema.ts ---

--- START FILE: src\shared\schemas\market.schema.ts ---
import { z } from 'zod';

/**
 * -----------------------------------------------
 * Enums and Basic Types
 * -----------------------------------------------
 * This section defines fundamental enumerations and simple types used across the application's market data models.
 * These schemas provide a consistent and validated foundation for more complex data structures.
 */

/**
 * Enum for market types (e.g., spot or futures).
 * This is exported as a TypeScript enum to be used in other Zod schemas like `z.nativeEnum`.
 */
export enum MarketType {
  Spot = 'spot',
  Futures = 'futures'
}
export const MarketTypeSchema = z.nativeEnum(MarketType);

/**
 * Schema for kline/candlestick time intervals.
 * Defines the set of supported timeframes for chart data.
 */
export const KlineIntervalSchema = z.enum([
  '1m', '3m', '5m', '15m', '30m',
  '1h', '2h', '4h', '6h', '8h', '12h',
  '1d', '3d', '1w', '1M'
]);
export type KlineInterval = z.infer<typeof KlineIntervalSchema>;

/**
 * Schema for supported exchange identifiers.
 * This list can be expanded as more exchanges are integrated.
 */
export const ExchangeNameSchema = z.enum(['binance', 'kucoin', 'bybit', 'okx', 'huobi', 'custom']);
export type ExchangeName = z.infer<typeof ExchangeNameSchema>;

/**
 * Enum for WebSocket connection statuses.
 * Provides a clear, readable set of states for managing WebSocket connections.
 */
export enum WSConnectionStatus {
  Disconnected = "disconnected",
  Connecting = "connecting",
  Connected = "connected",
  Reconnecting = "reconnecting",
  Error = "error"
}
export const WSConnectionStatusSchema = z.nativeEnum(WSConnectionStatus);
export type WSConnectionStatusZod = z.infer<typeof WSConnectionStatusSchema>;

/**
 * Schema for UI View Modes, such as focused view or market screener.
 */
export const ViewModeSchema = z.enum(['focus', 'screener']);
export type ViewMode = z.infer<typeof ViewModeSchema>;


/**
 * -----------------------------------------------
 * Exchange-Specific Raw Data Schemas
 * -----------------------------------------------
 * Schemas in this section are designed to be flexible, accommodating raw data payloads from various external exchange APIs.
 * They serve as the initial validation layer before data is transformed into the core application format.
 */

/**
 * Schema for raw ticker data from any exchange.
 * Uses a record with `any` to accept diverse and unpredictable structures.
 */
export const ExchangeTickerDataSchema = z.record(z.any());
export type ExchangeTickerData = z.infer<typeof ExchangeTickerDataSchema>;

/**
 * Schema for raw kline/candlestick data from any exchange.
 */
export const ExchangeKlineDataSchema = z.record(z.any());
export type ExchangeKlineData = z.infer<typeof ExchangeKlineDataSchema>;

/**
 * Schema for raw symbol data from any exchange.
 */
export const ExchangeSymbolDataSchema = z.record(z.any());
export type ExchangeSymbolData = z.infer<typeof ExchangeSymbolDataSchema>;


/**
 * -----------------------------------------------
 * Internal Core Data Schemas
 * -----------------------------------------------
 * These schemas define the standardized, strictly-typed data structures used throughout the application's core logic.
 * Data from exchanges is transformed into these formats for consistent processing and usage.
 */

/**
 * Schema for core ticker data, representing a standardized ticker object.
 */
export const CoreTickerSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  price: z.number(),
  timestamp: z.number(),
  change: z.number().optional(),
  changePercent: z.number().optional(),
  volume: z.number().optional(),
  quoteVolume: z.number().optional(),
  high: z.number().optional(),
  low: z.number().optional(),
  count: z.number().optional(),
  open: z.number().optional(),
  weightedAvgPrice: z.number().optional(),
  prevClose: z.number().optional(),
  bidPrice: z.number().optional(),
  bidQty: z.number().optional(),
  askPrice: z.number().optional(),
  askQty: z.number().optional(),
  firstTradeId: z.number().optional(),
  lastTradeId: z.number().optional(),
});
export type CoreTicker = z.infer<typeof CoreTickerSchema>;

/**
 * Schema for core kline/candlestick data, standardized for internal use.
 */
export const CoreKlineSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  interval: z.string(), // String, as it can be any timeframe before validation against KlineIntervalSchema
  openTime: z.number(),
  open: z.number(),
  high: z.number(),
  low: z.number(),
  close: z.number(),
  volume: z.number(),
  closeTime: z.number(),
  quoteVolume: z.number().optional(),
  trades: z.number().optional(),
  isClosed: z.boolean().optional(),
});
export type CoreKline = z.infer<typeof CoreKlineSchema>;

/**
 * Schema for core symbol information, detailing asset properties.
 */
export const CoreSymbolSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  baseAsset: z.string(),
  quoteAsset: z.string(),
  status: z.string().optional(),
  pricePrecision: z.number().optional(),
  quantityPrecision: z.number().optional(),
  isSpotTradingAllowed: z.boolean().optional(),
  isMarginTradingAllowed: z.boolean().optional(),
});
export type CoreSymbol = z.infer<typeof CoreSymbolSchema>;


/**
 * -----------------------------------------------
 * Application-Specific and DTO Schemas
 * -----------------------------------------------
 * This section contains schemas for data transfer objects (DTOs) and other application-specific structures,
 * such as data formatted for UI components, database interaction, or API parameters.
 */

/**
 * Schema for candlestick/kline data from the database.
 * Historical data might have numeric fields stored as strings.
 */
export const KlineDbSchema = z.object({
  openTime: z.date(),
  open: z.string(),
  high: z.string(),
  low: z.string(),
  close: z.string(),
  volume: z.string(),
});
export type KlineDb = z.infer<typeof KlineDbSchema>;

/**
 * Schema for the primary kline/candlestick data representation used in business logic and UI.
 */
export const KlineSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  interval: KlineIntervalSchema,
  openTime: z.number(),
  open: z.number(),
  high: z.number(),
  low: z.number(),
  close: z.number(),
  volume: z.number(),
  closeTime: z.number(),
  quoteVolume: z.number(),
  trades: z.number(),
  isClosed: z.boolean().default(true)
});
export type Kline = z.infer<typeof KlineSchema>;

/**
 * Schema for parameters used when fetching kline data.
 */
export const KlineParamsSchema = z.object({
  symbol: z.string(),
  interval: z.string(),
  limit: z.number().optional(),
});
export type KlineParams = z.infer<typeof KlineParamsSchema>;

/**
 * Schema for full 24-hour ticker statistics.
 * This is the comprehensive ticker model.
 */
export const TickerSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  lastPrice: z.number(),
  priceChange: z.number(),
  priceChangePercent: z.number(),
  weightedAvgPrice: z.number().optional().default(0),
  prevClosePrice: z.number().optional().default(0),
  lastQty: z.number().optional().default(0),
  bidPrice: z.number().optional().default(0),
  bidQty: z.number().optional().default(0),
  askPrice: z.number().optional().default(0),
  askQty: z.number().optional().default(0),
  openPrice: z.number().optional().default(0),
  highPrice: z.number(),
  lowPrice: z.number(),
  volume: z.number(),
  quoteVolume: z.number(),
  openTime: z.number(),
  closeTime: z.number(),
  firstId: z.number(),
  lastId: z.number(),
  count: z.number(),
  lastUpdated: z.number(),
});
export type Ticker = z.infer<typeof TickerSchema>;

/**
 * Schema for a simplified ticker object, often used in high-level UI lists.
 * It picks essential fields and adds a 'price' alias for convenience.
 */
export const SimpleTickerSchema = TickerSchema.pick({
  symbol: true,
  marketType: true,
  lastPrice: true,
  priceChangePercent: true,
  volume: true,
  quoteVolume: true,
  count: true,
  lastUpdated: true,
}).transform(val => ({ ...val, price: val.lastPrice }));
export type SimpleTicker = z.infer<typeof SimpleTickerSchema>;


/**
 * Schema for a processed ticker with additional calculated fields, like USD volume.
 */
export const ProcessedTickerSchema = TickerSchema.extend({
  baseAsset: z.string(),
  quoteAsset: z.string(),
  priceChangeAbs: z.number(),
  volumeInUSD: z.number().optional(),
  aggregatedVolume: z.number().optional(),
  aggregatedTradeCount: z.number().optional(),
});
export type ProcessedTicker = z.infer<typeof ProcessedTickerSchema>;

/**
 * Schema for full ticker data with all possible fields from exchanges
 */
export const FullTickerSchema = TickerSchema.extend({
  lastTradeId: z.number().optional(),
  takerBuyBaseVolume: z.number().optional(),
  takerBuyQuoteVolume: z.number().optional(),
});
export type FullTicker = z.infer<typeof FullTickerSchema>;

/**
 * Schema for detailed symbol information used within the application.
 */
export const AppSymbolInfoSchema = z.object({
  symbol: z.string(),
  status: z.string(),
  baseAsset: z.string(),
  quoteAsset: z.string(),
  baseAssetPrecision: z.number(),
  quoteAssetPrecision: z.number(),
  marketType: MarketTypeSchema,
});
export type AppSymbolInfo = z.infer<typeof AppSymbolInfoSchema>;

/**
 * Schema for kline data specifically formatted for use in technical indicators.
 */
export const KlineDataSchema = z.object({
  time: z.number(),
  open: z.number(),
  high: z.number(),
  low: z.number(),
  close: z.number(),
  volume: z.number(),
});
export type KlineData = z.infer<typeof KlineDataSchema>;

/**
 * Schema for TradingView-compatible symbol information.
 */
export const TradingViewSymbolSchema = z.object({
  symbol: z.string(),
  full_name: z.string(),
  description: z.string(),
  exchange: z.string(),
  type: z.string(),
});
export type TradingViewSymbol = z.infer<typeof TradingViewSymbolSchema>;


/**
 * -----------------------------------------------
 * Charting Constants and Enums
 * -----------------------------------------------
 * This section defines constants and enumerations related to chart events and interactions.
 */

export const CHART_EVENT_NAMESPACE = 'chart';

export enum ChartEventType {
  CROSSHAIR_MOVE = 'crosshair-move',
  VISIBLE_RANGE_CHANGE = 'visible-range-change',
  TIME_SCALE_RESET = 'time-scale-reset',
}
export const ChartEventTypeSchema = z.nativeEnum(ChartEventType);



--- END FILE: src\shared\schemas\market.schema.ts ---

--- START FILE: src\shared\schemas\settings.schema.ts ---
import { z } from 'zod';
import { LineStyle, LineWidth } from 'lightweight-charts';
import { SortingState } from '@tanstack/react-table';
import {
  MarketType,
  MarketTypeSchema,
  ViewModeSchema,
  KlineIntervalSchema,
} from './market.schema';

/**
 * -----------------------------------------------
 * Settings-Related Constants
 * -----------------------------------------------
 */

/**
 * Schema for a single timeframe option used in UI selectors.
 */
export const TimeframeOptionSchema = z.object({
  value: KlineIntervalSchema,
  label: z.string(),
});
export type TimeframeOption = z.infer<typeof TimeframeOptionSchema>;

/**
 * Default list of timeframes for UI components.
 * Derived from the KlineIntervalSchema to ensure consistency.
 */
export const DEFAULT_TIMEFRAMES: TimeframeOption[] = KlineIntervalSchema.options.map(val => ({ value: val, label: val }));

/**
 * Predefined chart count options for the screener view.
 */
export const SCREENER_CHART_COUNTS = [4, 6, 9, 12, 16] as const;
export const ScreenerChartCountSchema = z
  .enum(SCREENER_CHART_COUNTS.map(String) as [string, ...string[]])
  .transform(Number);
export type ScreenerChartCount = z.infer<typeof ScreenerChartCountSchema>;

/**
 * Predefined update intervals for the screener's auto-update feature.
 */
export const SCREENER_UPDATE_INTERVALS = [
  { value: 0, label: 'Off' },
  { value: 3, label: '3s' },
  { value: 5, label: '5s' },
  { value: 10, label: '10s' },
  { value: 15, label: '15s' },
  { value: 30, label: '30s' },
  { value: 60, label: '1m' },
  { value: 120, label: '2m' },
  { value: 300, label: '5m' }
] as const;

/**
 * -----------------------------------------------
 * Settings-Related Enums and Basic Types
 * -----------------------------------------------
 */

/**
 * Enum for different chart rendering types.
 */
export enum ChartType {
  Candles = 'Candlestick',
  Line = 'Line',
  Area = 'Area',
  Bars = 'Bars',
}
export const ChartTypeSchema = z.nativeEnum(ChartType);

/**
 * Zod schema for LayoutType.
 * Defines the grid layout options for charts.
 * 'single' is for focus mode. 'grid_*' are for screener mode.
 */
export const LayoutTypeSchema = z.enum([
  'single',
  'grid_4',
  'grid_6',
  'grid_9',
  'grid_12',
  'grid_16',
  '1x1', '1x2', '1x3', '1x4', '1x5', '1x6', '1x7',
  '2x1', '2x2', '2x3', '2x4', '2x5', '2x6', '2x7',
  '3x1', '3x2', '3x3', '3x4', '3x5', '3x6', '3x7',
  '4x1', '4x2', '4x3', '4x4', '4x5', '4x6', '4x7',
  '5x1', '5x2', '5x3', '5x4', '5x5', '5x6', '5x7',
  '6x1', '6x2', '6x3', '6x4', '6x5', '6x6', '6x7',
  '7x1', '7x2', '7x3', '7x4', '7x5', '7x6', '7x7',
]);
export type LayoutType = z.infer<typeof LayoutTypeSchema>;

/**
 * Schema for Screener Sort By options.
 */
export const ScreenerSortBySchema = z.enum(['volume', 'trades', 'price_change', 'volume_change']);
export type ScreenerSortBy = z.infer<typeof ScreenerSortBySchema>;

/**
 * -----------------------------------------------
 * Component and Feature Settings Schemas
 * -----------------------------------------------
 */

/**
 * Schema for visible columns in tables.
 */
export const VisibleColumnsSchema = z.object({
  symbol: z.boolean().default(true),
  price: z.boolean().default(true),
  change: z.boolean().default(true),
  volume: z.boolean().default(true),
  trade: z.boolean().default(true),
  spread: z.boolean().default(true),
}).catchall(z.boolean());
export type VisibleColumns = z.infer<typeof VisibleColumnsSchema>;

/**
 * Schema for sorting configuration.
 * Aligned with TanStack Table's SortingState.
 */
export const SortConfigSchema = z.object({
  id: z.string(),
  desc: z.boolean(),
});
export type SortConfig = z.infer<typeof SortConfigSchema>;

/**
 * Schema for column widths in tables.
 */
export const ColumnWidthsSchema = z.object({
  symbol: z.number().min(20),
  price: z.number().min(20),
  change: z.number().min(20),
  volume: z.number().min(20),
  trade: z.number().min(20),
  spread: z.number().min(20)
}).catchall(z.number().min(20));
export type ColumnWidths = z.infer<typeof ColumnWidthsSchema>;

/**
 * Schema for column filters in tables.
 */
export const ColumnFiltersSchema = z.record(z.union([
  z.string(),
  z.number(),
  z.boolean(),
  z.null()
]));
export type ColumnFilters = z.infer<typeof ColumnFiltersSchema>;

/**
 * Schema for a single indicator column configuration.
 */
export const IndicatorColumnConfigSchema = z.object({
  instanceId: z.string(),
  indicatorId: z.string(),
  name: z.string(),
  timeframe: z.string(),
  parameters: z.record(z.union([z.string(), z.number(), z.boolean()])),
  outputId: z.string(),
  columnLabel: z.string().optional()
});
export type IndicatorColumnConfig = z.infer<typeof IndicatorColumnConfigSchema>;

/**
 * Schema for Screener settings.
 */
export const ScreenerSettingsSchema = z.object({
  sortBy: ScreenerSortBySchema.default('volume'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  marketTypes: z.array(MarketTypeSchema).default([MarketType.Spot, MarketType.Futures]),
  minVolume: z.number().default(1000000),
  minTrades: z.number().default(100),
  showVolumeInUSD: z.boolean().default(true),
  autoUpdateInterval: z.number().default(5),
  autoUpdate: z.boolean().default(false),
  updateInterval: z.number().default(5),
  chartCount: ScreenerChartCountSchema.default('16'),
  currentPage: z.number().int().positive().default(1),
  timeframe: KlineIntervalSchema.default('1h'),
});
export type ScreenerSettings = z.infer<typeof ScreenerSettingsSchema>;

/**
 * Schema for Mode Control settings, managing different UI modes.
 */
export const ModeControlSettingsSchema = z.object({
  screenerSettings: ScreenerSettingsSchema,
  layoutTypes: z
    .object({
      focus: LayoutTypeSchema.default('single'),
      screener: LayoutTypeSchema.default('grid_16'),
    })
    .default({
      focus: 'single',
      screener: 'grid_16',
    }),
  currentMode: ViewModeSchema.default('focus'),
});
export type ModeControlSettings = z.infer<typeof ModeControlSettingsSchema>;

/**
 * -----------------------------------------------
 * Main Application Settings Schema
 * -----------------------------------------------
 * This is the master schema that combines all other settings schemas.
 * It is used for the main application state managed by Zustand.
 */
export const AppSettingsSchema = z.object({
  // General UI settings
  isDarkMode: z.boolean().default(true),
  isFiltersShown: z.boolean().default(false),
  isGridShown: z.boolean().default(false),
  isDebugMode: z.boolean().default(false),
  uiFontFamily: z.string().default('Inter'),
  uiFontSize: z.number().default(14),
  isSyncEnabled: z.boolean().default(true),
  globalSearchTerm: z.string().default(''),
  selectedTickerSymbol: z.string().optional(),
  selectedInterval: z.string().default('1h'),

  // Chart Appearance
  chartType: ChartTypeSchema.default(ChartType.Candles),
  chartBackgroundColor: z.string().default('rgba(23, 23, 23, 1)'),
  chartLayoutLineColor: z.string().default('rgba(41, 41, 41, 1)'),
  chartTextColor: z.string().default('rgba(212, 212, 212, 1)'),
  chartFontFamily: z.string().default('Inter'),
  chartFontSize: z.number().default(12),
  chartBorderColor: z.string().default('rgba(41, 41, 41, 0.5)'),

  // Chart Grid
  chartGridVertLinesVisible: z.boolean().default(true),
  chartGridHorzLinesVisible: z.boolean().default(true),
  chartGridVertLinesColor: z.string().default('rgba(41, 41, 41, 0.5)'),
  chartGridHorzLinesColor: z.string().default('rgba(41, 41, 41, 0.5)'),
  
  // Chart Scales
  autoHideScalesEnabled: z.boolean().default(true),

  // Candlestick Series
  chartCandleBodyEnabled: z.boolean().default(true),
  chartCandleBorderEnabled: z.boolean().default(true),
  chartCandleWickEnabled: z.boolean().default(true),
  chartCandleBodyUpColor: z.string().default('rgba(38, 166, 154, 1)'),
  chartCandleBodyDownColor: z.string().default('rgba(239, 83, 80, 1)'),
  chartCandleBorderUpColor: z.string().default('rgba(38, 166, 154, 1)'),
  chartCandleBorderDownColor: z.string().default('rgba(239, 83, 80, 1)'),
  chartCandleWickUpColor: z.string().default('rgba(38, 166, 154, 1)'),
  chartCandleWickDownColor: z.string().default('rgba(239, 83, 80, 1)'),

  // Line Series
  chartLineColor: z.string().default('rgba(33, 150, 243, 1)'),
  chartLineWidth: z.union([z.literal(1), z.literal(2), z.literal(3), z.literal(4)]).default(2),

  // Area Series
  chartAreaLineColor: z.string().default('rgba(33, 150, 243, 1)'),
  chartAreaTopColor: z.string().default('rgba(33, 150, 243, 0.4)'),
  chartAreaBottomColor: z.string().default('rgba(33, 150, 243, 0)'),
  chartAreaLineWidth: z.union([z.literal(1), z.literal(2), z.literal(3), z.literal(4)]).default(2),

  // Crosshair
  chartCrosshairColor: z.string().default('rgba(150, 150, 150, 0.5)'),
  chartCrosshairLabelBackgroundColor: z.string().default('rgba(40, 40, 40, 1)'),
  chartCrosshairLabelTextColor: z.string().default('rgba(255, 255, 255, 1)'),

  // Time and Price Scales
  chartTimeScaleBorderColor: z.string().default('rgba(41, 41, 41, 0.5)'),
  chartTimeScaleTextColor: z.string().default('rgba(212, 212, 212, 1)'),
  chartPriceScaleBorderColor: z.string().default('rgba(41, 41, 41, 0.5)'),
  chartPriceScaleTextColor: z.string().default('rgba(212, 212, 212, 1)'),
  
  // Price Line
  chartPriceLineVisible: z.boolean().default(true),
  chartLastValueVisible: z.boolean().default(true),
  chartPriceLineWidth: z.union([z.literal(1), z.literal(2), z.literal(3), z.literal(4)]).default(1),
  chartPriceLineStyle: z.union([z.literal(0), z.literal(1), z.literal(2), z.literal(3), z.literal(4)]).default(LineStyle.Dotted),
  chartPriceLineColor: z.string().default('rgba(212, 212, 212, 0.5)'),

  // Volume Pane
  volumeEnabled: z.boolean().default(true),
  chartVolumeHeightRatio: z.number().default(0.2), // 20% of main pane height
  chartVolumeUpColor: z.string().default('rgba(38, 166, 154, 0.5)'),
  chartVolumeDownColor: z.string().default('rgba(239, 83, 80, 0.5)'),

  // Chart Layout and Scaling
  chartRightOffset: z.number().default(5),
  chartBarSpacing: z.number().default(6.5),

  // Table Settings
  selectedPairs: z.array(z.string()).default(['BTC/USDT']),
  availablePairs: z.array(z.string()).default([]),
  marketTypes: z.array(MarketTypeSchema).default([MarketType.Spot, MarketType.Futures]),
  minVolume: z.number().default(1000000),
  minTrades: z.number().default(100),
  showVolumeInUSD: z.boolean().default(true),
  aggregateVolumeAndTrades: z.boolean().default(false),
  tableCompactness: z.number().min(0).max(20).default(0),
  sortConfigs: z.array(SortConfigSchema).default([{ id: 'volume', desc: true }]),
  visibleColumns: VisibleColumnsSchema.default({}),
  columnWidths: ColumnWidthsSchema.default({
    symbol: 150, price: 120, change: 100, volume: 140, trade: 100, spread: 100
  }),
  columnOrder: z.array(z.string()).default(['symbol', 'price', 'change', 'volume', 'trade', 'spread']),
  columnFilters: ColumnFiltersSchema.default({}),
  isTableCollapsed: z.boolean().default(false),
  indicatorColumns: z.array(IndicatorColumnConfigSchema).default([]),
  
  // Mode Control
  modeControl: ModeControlSettingsSchema.default({
    screenerSettings: {},
    layoutTypes: {
      focus: 'single',
      screener: 'grid_16',
    },
    currentMode: 'focus',
  }),
});

export type AppSettings = z.infer<typeof AppSettingsSchema>; 
--- END FILE: src\shared\schemas\settings.schema.ts ---

--- START FILE: src\shared\store\index.ts ---
export * from './marketStore';
export * from './settingsStore';
export * from './indicatorStore';
export * from './StoreInitializer';

// В будущем здесь могут быть другие хранилища
// export * from './settingsStore';
// export * from './userStore';
// и т.д. 
--- END FILE: src\shared\store\index.ts ---

--- START FILE: src\shared\store\indicatorStore.ts ---
import { create, StateCreator } from 'zustand'; // Add StateCreator
import { immer } from 'zustand/middleware/immer';
import { shallow } from 'zustand/shallow';
import { persist, createJSONStorage, PersistOptions } from 'zustand/middleware'; // Add PersistOptions
import { v4 as uuidv4 } from 'uuid'; // Для генерации уникальных ID экземпляров
import { LineStyle } from 'lightweight-charts'; // Add import
import { type WritableDraft } from 'immer';
import { 
  IndicatorDefinition, 
  IndicatorInstance, 
  IndicatorParams, 
  IndicatorParamValue, 
  IndicatorStyle 
} from '@/shared/index';
import { getAvailableIndicators, findIndicatorDefinitionById } from '@/features/Indicators/registry';

// Helper function to get default parameters and styles separately
export const getDefaultIndicatorParams = <T extends IndicatorParams>(
  indicator: IndicatorDefinition<T>
): { parameters: T, styleOverrides: Record<string, Partial<IndicatorStyle>>, tableOutputIds: string[], tableTimeframe: string } => {
  // Здесь params.id обязательно должно быть строкой, поэтому дженерик работает
  const parameters = indicator.params.reduce((acc, param) => {
    acc[param.id] = param.defaultValue;
    return acc;
  }, {} as Record<string, IndicatorParamValue>) as T;

  const styleOverrides: Record<string, Partial<IndicatorStyle>> = {};
  if (indicator.defaultStyle) {
    Object.entries(indicator.defaultStyle).forEach(([outputId, style]) => {
      styleOverrides[outputId] = { ...style };
    });
  }
  // Default styles from output definitions themselves, if not in defaultStyle block
  indicator.outputs.forEach(output => {
    if (!styleOverrides[output.id]) {
      styleOverrides[output.id] = {
        color: output.color,
        lineWidth: output.lineWidth,
        lineStyle: output.lineStyle,
        opacity: output.opacity, 
      };
    }
  });

  // Default table settings (e.g., show first output, default timeframe)
  const tableOutputIds = indicator.outputs.length > 0 ? [indicator.outputs[0].id] : [];
  const tableTimeframe = '1h'; // Or some other sensible default

  return { parameters, styleOverrides, tableOutputIds, tableTimeframe };
};

interface IndicatorState {
  /**
   * Активные экземпляры индикаторов, сгруппированные по ID графика.
   * key: chartId
   * value: Массив экземпляров индикаторов на этом графике.
   */
  indicatorsByChartId: Record<string, IndicatorInstance[]>;

  /**
   * Глобальные индикаторы, которые отображаются на всех графиках
   * независимо от chartId.
   */
  globalIndicators: IndicatorInstance[];
}

interface IndicatorActions {
  /**
   * Добавляет новый экземпляр индикатора на указанный график.
   * @param chartId - ID графика.
   * @param definition - Определение добавляемого индикатора.
   * @param params - Параметры для этого экземпляра индикатора.
   * @param isGlobal - Флаг, указывающий, должен ли индикатор быть глобальным (отображаться на всех графиках).
   */
  addIndicator: <T extends IndicatorParams>(
    chartId: string,
    definition: IndicatorDefinition<T>,
    params?: Partial<T>,
    isGlobal?: boolean
  ) => void;

  /**
   * Удаляет экземпляр индикатора с графика или из глобальных индикаторов.
   * @param instanceId - ID удаляемого экземпляра.
   * @param chartId - ID графика (необязательно для глобальных индикаторов).
   */
  removeIndicator: (instanceId: string, chartId?: string) => void;

  /**
   * Обновляет параметры существующего экземпляра индикатора.
   * @param instanceId - ID обновляемого экземпляра.
   * @param updates - Новые значения параметров и стилей.
   * @param chartId - ID графика (необязательно для глобальных индикаторов).
   */
  updateIndicatorInstance: (instanceId: string, updates: IndicatorInstanceUpdate, chartId?: string) => void;

  /**
   * Преобразует обычный индикатор в глобальный, который будет отображаться на всех графиках.
   * @param instanceId - ID экземпляра индикатора.
   * @param chartId - ID графика, с которого берется индикатор.
   */
  makeIndicatorGlobal: (instanceId: string, chartId: string) => void;

  /**
   * Преобразует глобальный индикатор в обычный, привязанный к конкретному графику.
   * @param instanceId - ID экземпляра индикатора.
   * @param targetChartId - ID графика, к которому будет привязан индикатор.
   */
  makeIndicatorLocal: (instanceId: string, targetChartId: string) => void;

  /**
   * Удаляет все индикаторы с указанного графика.
   * @param chartId - ID графика.
   */
  resetIndicatorsForChart: (chartId: string) => void;

  /**
   * Удаляет все индикаторы со всех графиков, включая глобальные.
   */
  resetAllIndicators: () => void;
}

// Define a type for the updates allowed in updateIndicatorInstance
// Include table settings fields
type IndicatorInstanceUpdate = Partial<Pick<IndicatorInstance, 'params' | 'styleOverrides' | 'visible' | 'tableOutputIds' | 'tableTimeframe'>>;

// Define the initial state with explicit types
const initialState: IndicatorState = {
  indicatorsByChartId: {},
  globalIndicators: [],
};

// --- Persist Configuration ---
const LS_INDICATOR_SETTINGS_KEY = 'cryptoDashboardIndicatorSettings';

// Type for the persist middleware, ensuring it correctly wraps the immer-modified StateCreator
type IndicatorPersist = (
  config: StateCreator<IndicatorState & IndicatorActions, [["zustand/immer", never]], []>, // Expects immer's signature
  options: PersistOptions<IndicatorState, Partial<IndicatorState>>
) => StateCreator<IndicatorState & IndicatorActions, [], [["zustand/persist", Partial<IndicatorState>]]>; // Returns persist's signature

export const useIndicatorStore = create<IndicatorState & IndicatorActions>()(
  persist( // persist is the outer middleware
    immer((set) => ({ // immer is the inner middleware
      // Use the explicitly typed initial state
      ...initialState,

      addIndicator: (chartId, definition, params = {}, isGlobal = false) => {
        console.log('[indicatorStore] addIndicator вызван:', { chartId, indicatorId: definition?.id, params, isGlobal });
        set((state) => {
          if (!definition) {
            console.error('[indicatorStore] Ошибка: Попытка добавить индикатор без определения!');
            return; // Ничего не делаем, если нет определения
          }
          // Get all defaults first
          const defaults = getDefaultIndicatorParams(definition);

          // Merge default parameters with provided params
          const mergedParams = { ...defaults.parameters, ...params };

          const instanceName = definition.getShortName
            ? definition.getShortName(mergedParams as any) // Cast needed if mergedParams is not strictly TParams
            : definition.name;

          const newInstance: IndicatorInstance = {
            instanceId: uuidv4(),
            chartId: isGlobal ? 'global' : chartId,
            indicatorId: definition.id,
            name: instanceName, // Added name property
            // Use the merged parameters
            params: mergedParams,
            visible: true,
            // Use default styles and table settings
            styleOverrides: defaults.styleOverrides,
            tableOutputIds: defaults.tableOutputIds,
            tableTimeframe: defaults.tableTimeframe,
          };

          if (isGlobal) {
            state.globalIndicators.push(newInstance);
            console.log('[indicatorStore] Добавлен глобальный индикатор:', newInstance);
          } else {
            if (!state.indicatorsByChartId[chartId]) {
              state.indicatorsByChartId[chartId] = [];
            }
            state.indicatorsByChartId[chartId].push(newInstance);
            console.log('[indicatorStore] Состояние обновлено для chartId:', chartId, state.indicatorsByChartId[chartId]);
          }
        });
      },

      removeIndicator: (instanceId, chartId) =>
        set((state) => {
          // Проверяем сначала в глобальных индикаторах
          const globalIndex = state.globalIndicators.findIndex(
            (indicator) => indicator.instanceId === instanceId
          );
          
          if (globalIndex !== -1) {
            state.globalIndicators.splice(globalIndex, 1);
            return;
          }
          
          // Если не найден в глобальных и задан chartId, ищем в конкретном графике
          if (chartId && state.indicatorsByChartId[chartId]) {
            state.indicatorsByChartId[chartId] = state.indicatorsByChartId[chartId]
              .filter((indicator) => indicator.instanceId !== instanceId);
          } else if (!chartId) {
            // Если chartId не задан, ищем во всех графиках
            Object.keys(state.indicatorsByChartId).forEach((id) => {
              state.indicatorsByChartId[id] = state.indicatorsByChartId[id]
                .filter((indicator) => indicator.instanceId !== instanceId);
            });
          }
        }),

      updateIndicatorInstance: (instanceId, updates, chartId) =>
        set((state) => {
          // Find and update logic (global first, then specific or all charts)
          const updateInstance = (instance: IndicatorInstance): IndicatorInstance => {
            // Create a new object with the merged updates
            const updatedInstance = { ...instance };

            // Merge top-level properties
            if (updates.visible !== undefined) updatedInstance.visible = updates.visible;
            if (updates.tableTimeframe !== undefined) updatedInstance.tableTimeframe = updates.tableTimeframe;
            if (updates.tableOutputIds !== undefined) updatedInstance.tableOutputIds = updates.tableOutputIds;

            // Deep merge parameters
            if (updates.params) {
              updatedInstance.params = { ...instance.params, ...updates.params };
            }

            // Deep merge styleOverrides
            if (updates.styleOverrides) {
              updatedInstance.styleOverrides = { ...instance.styleOverrides, ...updates.styleOverrides };
            }

            return updatedInstance;
          };

          let found = false;

          // Проверяем сначала в глобальных индикаторах
          const globalIndex = state.globalIndicators.findIndex(
            (indicator) => indicator.instanceId === instanceId
          );
          
          if (globalIndex !== -1) {
            state.globalIndicators[globalIndex] = updateInstance(state.globalIndicators[globalIndex]);
            found = true;
            return;
          }
          
          // Если не найден в глобальных и задан chartId, ищем в конкретном графике
          if (chartId && state.indicatorsByChartId[chartId]) {
            const indicatorIndex = state.indicatorsByChartId[chartId].findIndex(
              (indicator) => indicator.instanceId === instanceId
            );
            if (indicatorIndex !== -1) {
              state.indicatorsByChartId[chartId][indicatorIndex] = updateInstance(state.indicatorsByChartId[chartId][indicatorIndex]);
              found = true;
            }
          } else if (!chartId) {
            // Если chartId не задан, ищем во всех графиках
            Object.keys(state.indicatorsByChartId).forEach((id) => {
              const indicatorIndex = state.indicatorsByChartId[id].findIndex(
                (indicator) => indicator.instanceId === instanceId
              );
              if (indicatorIndex !== -1) {
                state.indicatorsByChartId[id][indicatorIndex] = updateInstance(state.indicatorsByChartId[id][indicatorIndex]);
                found = true;
              }
            });
          }

          if (!found) {
            console.warn(`[IndicatorStore] updateIndicatorInstance: Instance with ID ${instanceId} not found.`);
          }
        }),

      makeIndicatorGlobal: (instanceId, chartId) =>
        set((state) => {
          const chartIndicators = state.indicatorsByChartId[chartId];
          if (!chartIndicators) return;

          const indicatorIndex = chartIndicators.findIndex((indicator) => indicator.instanceId === instanceId);
          if (indicatorIndex !== -1) {
            const [indicatorToMove] = chartIndicators.splice(indicatorIndex, 1);
            // Убедимся, что индикатор не дублируется в глобальных
            if (!state.globalIndicators.find((gi) => gi.instanceId === indicatorToMove.instanceId)) {
              state.globalIndicators.push({ ...indicatorToMove, chartId: 'global' }); // Update chartId for clarity
            }
          }
        }),

      makeIndicatorLocal: (instanceId, targetChartId) =>
        set((state) => {
          const globalIndicatorIndex = state.globalIndicators.findIndex((indicator) => indicator.instanceId === instanceId);

          if (globalIndicatorIndex !== -1) {
            const [indicatorToMove] = state.globalIndicators.splice(globalIndicatorIndex, 1);
            if (!state.indicatorsByChartId[targetChartId]) {
              state.indicatorsByChartId[targetChartId] = [];
            }
            // Убедимся, что индикатор не дублируется в локальных для этого графика
            if (!state.indicatorsByChartId[targetChartId].find((li) => li.instanceId === indicatorToMove.instanceId)) {
              state.indicatorsByChartId[targetChartId].push({ ...indicatorToMove, chartId: targetChartId });
            }
          }
        }),

      resetIndicatorsForChart: (chartId) =>
        set((state) => {
          delete state.indicatorsByChartId[chartId];
        }),

      resetAllIndicators: () => set(initialState), // Reset to the initial state object
    })),
    { // Options for persist middleware
      name: LS_INDICATOR_SETTINGS_KEY,
      storage: createJSONStorage(() => {
        // Проверяем, что код выполняется в браузере
        const isClient = typeof window !== 'undefined';
        if (!isClient) return {
          getItem: () => Promise.resolve(null),
          setItem: () => Promise.resolve(),
          removeItem: () => Promise.resolve()
        };
        
        // Очищаем хранилище от старых несовместимых данных
        try {
          localStorage.removeItem(LS_INDICATOR_SETTINGS_KEY);
        } catch (e) {
          console.warn('[IndicatorStore] Error removing old settings:', e);
        }
        return localStorage;
      }),
      partialize: (state) => ({
        indicatorsByChartId: state.indicatorsByChartId,
        globalIndicators: state.globalIndicators,
      }),
      merge: (persisted, current) => {
        if (!persisted || typeof persisted !== 'object') {
          console.log('[IndicatorStore] Missing or invalid persisted state');
          return current;
        }
        
        try {
          // Базовое слияние с сохранением методов из текущего состояния
          const result = {
            ...current
          };
          
          // Безопасное копирование indicatorsByChartId с правильным типом
          const typedPersisted = persisted as Partial<IndicatorState>;
          
          if (typedPersisted.indicatorsByChartId && typeof typedPersisted.indicatorsByChartId === 'object') {
            result.indicatorsByChartId = {};
            // Копируем каждую группу индикаторов для каждого chartId
            for (const chartId in typedPersisted.indicatorsByChartId) {
              if (Object.prototype.hasOwnProperty.call(typedPersisted.indicatorsByChartId, chartId)) {
                const indicators = typedPersisted.indicatorsByChartId[chartId];
                if (Array.isArray(indicators)) {
                  result.indicatorsByChartId[chartId] = [...indicators];
                }
              }
            }
          } else {
            // Если нет данных в persisted, используем пустой объект
            result.indicatorsByChartId = {};
          }
          
          // Безопасное копирование globalIndicators
          if (typedPersisted.globalIndicators && Array.isArray(typedPersisted.globalIndicators)) {
            result.globalIndicators = [...typedPersisted.globalIndicators];
          } else {
            // Если нет данных в persisted, используем пустой массив
            result.globalIndicators = [];
          }
          
          return result;
        } catch (error) {
          console.error('[IndicatorStore] Error during state merge:', error);
          return current;
        }
      },
    }
  )
);

--- END FILE: src\shared\store\indicatorStore.ts ---

--- START FILE: src\shared\store\marketStore.ts ---
import { create } from 'zustand';
import { Kline, MarketType, ProcessedTicker, Ticker } from '@/shared/index';

// Default market type
export const DEFAULT_MARKET_TYPE: MarketType = MarketType.Spot;

// Типы для ключей кеша
type KlineCacheKey = string;

// Interface for the market data state
interface MarketState {
  // Ticker data
  tickers: Record<string, Ticker>;
  // Processed tickers by market type
  processedSpotTickers: ProcessedTicker[];
  processedFuturesTickers: ProcessedTicker[];
  // Selected market type
  selectedMarketType: MarketType;
  // Kline data by symbol, interval and market type
  klines: Record<KlineCacheKey, Kline[]>;
  // Loading states
  isLoadingTickers: boolean;
  isLoadingKlines: Record<KlineCacheKey, boolean>;
  // Error states
  tickersError: string | null;
  klinesError: Record<KlineCacheKey, string | null>;
  // Last update timestamps
  tickersLastUpdate: number | null;
  klinesLastUpdate: Record<KlineCacheKey, number | null>;

  // Actions
  setTickers: (tickers: Ticker[]) => void;
  setSelectedMarketType: (marketType: MarketType) => void;
  setKlines: (symbol: string, interval: string, klines: Kline[]) => void;
  appendKlines: (symbol: string, interval: string, klines: Kline[]) => void;
  updateKline: (symbol: string, interval: string, kline: Kline) => void;
  clearData: () => void;
  setLoadingTickers: (isLoading: boolean) => void;
  setLoadingKlines: (symbol: string, interval: string, isLoading: boolean) => void;
  setTickersError: (error: string | null) => void;
  setKlinesError: (symbol: string, interval: string, error: string | null) => void;
}

// Helper function to create cache key for klines
export const getKlinesCacheKey = (symbol: string, interval: string, marketType: MarketType = DEFAULT_MARKET_TYPE): KlineCacheKey => {
  return `${symbol}:${interval}:${marketType}`;
};

// Initialize the market store
export const useMarketStore = create<MarketState>((set) => ({
  // Initial state
  tickers: {},
  processedSpotTickers: [],
  processedFuturesTickers: [],
  selectedMarketType: DEFAULT_MARKET_TYPE,
  klines: {},
  isLoadingTickers: false,
  isLoadingKlines: {},
  tickersError: null,
  klinesError: {},
  tickersLastUpdate: null,
  klinesLastUpdate: {},

  // Actions
  setTickers: (tickers: Ticker[]) => set((state) => {
    const newTickers = { ...state.tickers };
    tickers.forEach((ticker) => {
      newTickers[ticker.symbol] = ticker;
    });

    // Обновляем также обработанные тикеры
    const processedTickers = tickers.map(ticker => {
      const symbol = ticker.symbol;
      const parts = symbol.match(/^(.+?)([A-Z]{3,}|[1-9A-Z]{4,})$/);
      const baseAsset = parts ? parts[1] : symbol;
      const quoteAsset = parts ? parts[2] : '';
      const priceChangeAbs = Math.abs(ticker.priceChange);

      return {
        ...ticker,
        baseAsset,
        quoteAsset,
        priceChangeAbs,
      } as ProcessedTicker;
    });

    const processedSpotTickers = processedTickers.filter(ticker => ticker.marketType === MarketType.Spot);
    const processedFuturesTickers = processedTickers.filter(ticker => ticker.marketType === MarketType.Futures);

    return { 
      tickers: newTickers, 
      processedSpotTickers,
      processedFuturesTickers,
      tickersLastUpdate: Date.now(), 
      tickersError: null 
    };
  }),

  setSelectedMarketType: (marketType: MarketType) => set({ selectedMarketType: marketType }),

  setKlines: (symbol: string, interval: string, klines: Kline[]) => set((state) => {
    const cacheKey = getKlinesCacheKey(symbol, interval);
    const newKlines = { ...state.klines };
    newKlines[cacheKey] = klines;

    const newLastUpdate = { ...state.klinesLastUpdate };
    newLastUpdate[cacheKey] = Date.now();

    const newErrors = { ...state.klinesError };
    newErrors[cacheKey] = null;

    return { 
      klines: newKlines, 
      klinesLastUpdate: newLastUpdate, 
      klinesError: newErrors 
    };
  }),

  appendKlines: (symbol: string, interval: string, newKlinesToAppend: Kline[]) => set((state) => {
    const cacheKey = getKlinesCacheKey(symbol, interval);
    const existingKlines = state.klines[cacheKey] || [];
    const combinedKlines = [...existingKlines, ...newKlinesToAppend];

    // Sort by openTime for consistency
    combinedKlines.sort((a, b) => a.openTime - b.openTime);

    // Remove duplicates based on openTime
    const uniqueKlines = combinedKlines.filter((kline, index, array) => {
      return index === 0 || kline.openTime !== array[index - 1].openTime;
    });

    const newKlines = { ...state.klines };
    newKlines[cacheKey] = uniqueKlines;

    const newLastUpdate = { ...state.klinesLastUpdate };
    newLastUpdate[cacheKey] = Date.now();

    return { 
      klines: newKlines, 
      klinesLastUpdate: newLastUpdate 
    };
  }),

  updateKline: (symbol: string, interval: string, updatedKline: Kline) => set((state) => {
    const cacheKey = getKlinesCacheKey(symbol, interval);
    const existingKlines = state.klines[cacheKey] || [];
    
    // Find if this kline already exists (by openTime)
    const index = existingKlines.findIndex((k: Kline) => k.openTime === updatedKline.openTime);
    
    let newKlinesList;
    if (index >= 0) {
      // Update existing kline
      newKlinesList = [...existingKlines];
      newKlinesList[index] = updatedKline;
    } else {
      // Add new kline and sort
      newKlinesList = [...existingKlines, updatedKline];
      newKlinesList.sort((a, b) => a.openTime - b.openTime);
    }
    
    const newKlines = { ...state.klines };
    newKlines[cacheKey] = newKlinesList;

    const newLastUpdate = { ...state.klinesLastUpdate };
    newLastUpdate[cacheKey] = Date.now();

    return { 
      klines: newKlines, 
      klinesLastUpdate: newLastUpdate 
    };
  }),
  
  clearData: () => set({
    tickers: {},
    processedSpotTickers: [],
    processedFuturesTickers: [],
    klines: {},
    tickersLastUpdate: null,
    klinesLastUpdate: {},
    tickersError: null,
    klinesError: {}
  }),

  setLoadingTickers: (isLoading: boolean) => set({ isLoadingTickers: isLoading }),
  
  setLoadingKlines: (symbol: string, interval: string, isLoading: boolean) => set((state) => {
    const cacheKey = getKlinesCacheKey(symbol, interval);
    const newLoadingState = { ...state.isLoadingKlines };
    newLoadingState[cacheKey] = isLoading;
    return { isLoadingKlines: newLoadingState };
  }),
  
  setTickersError: (error: string | null) => set({ tickersError: error }),
  
  setKlinesError: (symbol: string, interval: string, error: string | null) => set((state) => {
    const cacheKey = getKlinesCacheKey(symbol, interval);
    const newErrors = { ...state.klinesError };
    newErrors[cacheKey] = error;
    return { klinesError: newErrors };
  })
}));

--- END FILE: src\shared\store\marketStore.ts ---

--- START FILE: src\shared\store\settingsStore.ts ---
import { create, StateCreator } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import {
  AppSettings,
  VisibleColumns,
  ColumnWidths,
  ChartType,
  IndicatorColumnConfig,
  ViewMode,
  ScreenerSettings,
  LayoutType,
  MarketType,
  AppSettingsSchema,
} from '@/shared/index';
import { LineStyle, LineWidth } from 'lightweight-charts';
import { SortingState } from '@tanstack/react-table';

const LS_SETTINGS_KEY = 'cryptoDashboardSettings';

// Derive default settings from the Zod schema to ensure consistency.
export const defaultSettings: AppSettings = AppSettingsSchema.parse({});

interface AppSettingsState extends AppSettings {
  setSetting: <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => void;
  toggleColumnVisibility: (key: keyof VisibleColumns | string) => void;
  updateSortConfigs: (newSortingState: SortingState) => void;
  setColumnOrder: (newOrder: string[]) => void;
  setColumnWidth: (key: keyof ColumnWidths | string, width: number) => void;
  setColumnWidths: (newWidths: ColumnWidths) => void;
  setColumnFilter: (key: string, value: string | number | null) => void;
  setTableCollapsed: (isCollapsed: boolean) => void;
  resetSettings: () => void;
  toggleAutoHideScales: () => void;
  setGlobalSearchTerm: (term: string) => void;
  addIndicatorColumn: (config: IndicatorColumnConfig) => void;
  removeIndicatorColumn: (instanceId: string) => void;
  updateIndicatorColumn: (instanceId: string, updates: Partial<IndicatorColumnConfig>) => void;
  setViewMode: (mode: ViewMode) => void;
  updateScreenerSettings: (updates: Partial<ScreenerSettings>) => void;
  setLayoutTypeForMode: (mode: ViewMode, layoutType: LayoutType) => void;
}

const createAppSettingsActions = (
  set: (partial: Partial<AppSettingsState> | ((state: AppSettingsState) => Partial<AppSettingsState>)) => void,
  get: () => AppSettingsState
): Omit<AppSettingsState, keyof AppSettings> => ({
  setSetting: (key, value) => set({ [key]: value }),
  toggleColumnVisibility: (key) => {
    if (key === 'symbol' && get().visibleColumns[key]) return;
    set((state) => ({
      visibleColumns: { ...state.visibleColumns, [key]: !state.visibleColumns[key] },
    }));
  },
  updateSortConfigs: (newSortingState) => set({ sortConfigs: newSortingState }),
  setColumnOrder: (newOrder) => set({ columnOrder: newOrder }),
  setColumnWidth: (key, width) => {
    set((state) => ({
      columnWidths: { ...state.columnWidths, [key]: Math.max(width, 40) },
    }));
  },
  setColumnWidths: (newWidths) => set({ columnWidths: newWidths }),
  setColumnFilter: (key, value) => {
    set((state) => ({
      columnFilters: { ...state.columnFilters, [key]: value },
    }));
  },
  setTableCollapsed: (isCollapsed) => set({ isTableCollapsed: isCollapsed }),
  resetSettings: () => {
    const newDefaults = AppSettingsSchema.parse({});
    set(newDefaults);
  },
  toggleAutoHideScales: () => set(state => ({ autoHideScalesEnabled: !state.autoHideScalesEnabled })),
  setGlobalSearchTerm: (term) => set({ globalSearchTerm: term }),
  addIndicatorColumn: (config) => {
    set((state) => ({
      indicatorColumns: [...state.indicatorColumns, config],
    }));
  },
  removeIndicatorColumn: (instanceId) => {
    set((state) => ({
      indicatorColumns: state.indicatorColumns.filter(c => c.instanceId !== instanceId),
    }));
  },
  updateIndicatorColumn: (instanceId, updates) => {
    set((state) => ({
      indicatorColumns: state.indicatorColumns.map(c =>
        c.instanceId === instanceId ? { ...c, ...updates } : c
      ),
    }));
  },
  setViewMode: (mode) => {
    set((state) => ({
      modeControl: { ...state.modeControl, currentMode: mode },
    }));
  },
  updateScreenerSettings: (updates) => {
    set((state) => ({
    modeControl: {
      ...state.modeControl,
        screenerSettings: { ...state.modeControl.screenerSettings, ...updates },
      },
    }));
  },
  setLayoutTypeForMode: (mode, layoutType) => {
    set((state) => ({
    modeControl: {
      ...state.modeControl,
        layoutTypes: { ...state.modeControl.layoutTypes, [mode]: layoutType },
      },
    }));
  },
});

export const useAppSettingsStore = create<AppSettingsState>()(
  persist(
    (set, get) => ({
      ...defaultSettings,
      ...createAppSettingsActions(set, get),
    }),
    {
      name: LS_SETTINGS_KEY,
      storage: createJSONStorage(() => localStorage),
      migrate: (persistedState, version) => {
        if (!persistedState || version < 1) {
          return defaultSettings;
        }
        const validation = AppSettingsSchema.safeParse(persistedState);
        if (validation.success) {
          return validation.data as AppSettingsState;
              }
        console.warn("Persisted settings validation failed, resetting to default.");
        return defaultSettings;
      },
      version: 1,
    }
  )
);

// === SELECTORS ===
export const selectIsDarkMode = (state: AppSettingsState) => state.isDarkMode;
export const selectIsFiltersShown = (state: AppSettingsState) => state.isFiltersShown;
export const selectIsGridShown = (state: AppSettingsState) => state.isGridShown;
export const selectIsDebugMode = (state: AppSettingsState) => state.isDebugMode;
export const selectChartType = (state: AppSettingsState) => state.chartType;
export const selectChartBackgroundColor = (state: AppSettingsState) => state.chartBackgroundColor;
export const selectChartLayoutLineColor = (state: AppSettingsState) => state.chartLayoutLineColor;
export const selectChartTextColor = (state: AppSettingsState) => state.chartTextColor;
export const selectChartFontFamily = (state: AppSettingsState) => state.chartFontFamily;
export const selectChartFontSize = (state: AppSettingsState) => state.chartFontSize;
export const selectChartBorderColor = (state: AppSettingsState) => state.chartBorderColor;
export const selectChartGridVertLinesVisible = (state: AppSettingsState) => state.chartGridVertLinesVisible;
export const selectChartGridHorzLinesVisible = (state: AppSettingsState) => state.chartGridHorzLinesVisible;
export const selectChartGridVertLinesColor = (state: AppSettingsState) => state.chartGridVertLinesColor;
export const selectChartGridHorzLinesColor = (state: AppSettingsState) => state.chartGridHorzLinesColor;
export const selectChartCandleBodyEnabled = (state: AppSettingsState) => state.chartCandleBodyEnabled;
export const selectChartCandleBorderEnabled = (state: AppSettingsState) => state.chartCandleBorderEnabled;
export const selectChartCandleWickEnabled = (state: AppSettingsState) => state.chartCandleWickEnabled;
export const selectChartCandleBodyUpColor = (state: AppSettingsState) => state.chartCandleBodyUpColor;
export const selectChartCandleBodyDownColor = (state: AppSettingsState) => state.chartCandleBodyDownColor;
export const selectChartCandleBorderUpColor = (state: AppSettingsState) => state.chartCandleBorderUpColor;
export const selectChartCandleBorderDownColor = (state: AppSettingsState) => state.chartCandleBorderDownColor;
export const selectChartCandleWickUpColor = (state: AppSettingsState) => state.chartCandleWickUpColor;
export const selectChartCandleWickDownColor = (state: AppSettingsState) => state.chartCandleWickDownColor;
export const selectChartLineColor = (state: AppSettingsState) => state.chartLineColor;
export const selectChartLineWidth = (state: AppSettingsState): LineWidth => state.chartLineWidth as LineWidth;
export const selectChartAreaLineColor = (state: AppSettingsState) => state.chartAreaLineColor;
export const selectChartAreaTopColor = (state: AppSettingsState) => state.chartAreaTopColor;
export const selectChartAreaBottomColor = (state: AppSettingsState) => state.chartAreaBottomColor;
export const selectChartAreaLineWidth = (state: AppSettingsState): LineWidth => state.chartAreaLineWidth as LineWidth;
export const selectChartCrosshairColor = (state: AppSettingsState) => state.chartCrosshairColor;
export const selectChartCrosshairLabelBackgroundColor = (state: AppSettingsState) => state.chartCrosshairLabelBackgroundColor;
export const selectChartCrosshairLabelTextColor = (state: AppSettingsState) => state.chartCrosshairLabelTextColor;
export const selectChartTimeScaleBorderColor = (state: AppSettingsState) => state.chartTimeScaleBorderColor;
export const selectChartTimeScaleTextColor = (state: AppSettingsState) => state.chartTimeScaleTextColor;
export const selectChartPriceScaleBorderColor = (state: AppSettingsState) => state.chartPriceScaleBorderColor;
export const selectChartPriceScaleTextColor = (state: AppSettingsState) => state.chartPriceScaleTextColor;
export const selectChartPriceLineVisible = (state: AppSettingsState) => state.chartPriceLineVisible;
export const selectChartLastValueVisible = (state: AppSettingsState) => state.chartLastValueVisible;
export const selectChartPriceLineWidth = (state: AppSettingsState) => state.chartPriceLineWidth;
export const selectChartPriceLineStyle = (state: AppSettingsState) => state.chartPriceLineStyle;
export const selectChartPriceLineColor = (state: AppSettingsState) => state.chartPriceLineColor;
export const selectVolumeEnabled = (state: AppSettingsState) => state.volumeEnabled;
export const selectChartVolumeHeightRatio = (state: AppSettingsState) => state.chartVolumeHeightRatio;
export const selectChartVolumeUpColor = (state: AppSettingsState) => state.chartVolumeUpColor;
export const selectChartVolumeDownColor = (state: AppSettingsState) => state.chartVolumeDownColor;
export const selectChartRightOffset = (state: AppSettingsState) => state.chartRightOffset;
export const selectChartBarSpacing = (state: AppSettingsState) => state.chartBarSpacing;
export const selectSelectedPairs = (state: AppSettingsState) => state.selectedPairs;
export const selectMarketTypes = (state: AppSettingsState) => state.marketTypes;
export const selectMinVolume = (state: AppSettingsState) => state.minVolume;
export const selectMinTrades = (state: AppSettingsState) => state.minTrades;
export const selectShowVolumeInUSD = (state: AppSettingsState) => state.showVolumeInUSD;
export const selectAggregateVolumeAndTrades = (state: AppSettingsState) => state.aggregateVolumeAndTrades;
export const selectTableCompactness = (state: AppSettingsState) => state.tableCompactness;
export const selectIsSyncEnabled = (state: AppSettingsState) => state.isSyncEnabled;
export const selectUiFontFamily = (state: AppSettingsState) => state.uiFontFamily;
export const selectUiFontSize = (state: AppSettingsState) => state.uiFontSize;
export const selectSortConfigs = (state: AppSettingsState) => state.sortConfigs;
export const selectVisibleColumns = (state: AppSettingsState) => state.visibleColumns;
export const selectColumnWidths = (state: AppSettingsState) => state.columnWidths;
export const selectColumnOrder = (state: AppSettingsState) => state.columnOrder;
export const selectColumnFilters = (state: AppSettingsState) => state.columnFilters;
export const selectIsTableCollapsed = (state: AppSettingsState) => state.isTableCollapsed;
export const selectSelectedTickerSymbol = (state: AppSettingsState) => state.selectedTickerSymbol;
export const selectGlobalSearchTerm = (state: AppSettingsState) => state.globalSearchTerm;
export const selectChartAppearance = (state: AppSettingsState): Partial<AppSettings> => ({
  chartType: state.chartType,
  chartBackgroundColor: state.chartBackgroundColor,
  chartLayoutLineColor: state.chartLayoutLineColor,
  chartTextColor: state.chartTextColor,
  chartFontFamily: state.chartFontFamily,
  chartFontSize: state.chartFontSize,
  chartBorderColor: state.chartBorderColor,
  chartGridVertLinesVisible: state.chartGridVertLinesVisible,
  chartGridHorzLinesVisible: state.chartGridHorzLinesVisible,
  chartGridVertLinesColor: state.chartGridVertLinesColor,
  chartGridHorzLinesColor: state.chartGridHorzLinesColor,
  chartCandleBodyEnabled: state.chartCandleBodyEnabled,
  chartCandleBorderEnabled: state.chartCandleBorderEnabled,
  chartCandleWickEnabled: state.chartCandleWickEnabled,
  chartCandleBodyUpColor: state.chartCandleBodyUpColor,
  chartCandleBodyDownColor: state.chartCandleBodyDownColor,
  chartCandleBorderUpColor: state.chartCandleBorderUpColor,
  chartCandleBorderDownColor: state.chartCandleBorderDownColor,
  chartCandleWickUpColor: state.chartCandleWickUpColor,
  chartCandleWickDownColor: state.chartCandleWickDownColor,
  chartLineColor: state.chartLineColor,
  chartLineWidth: state.chartLineWidth as LineWidth,
  chartAreaLineColor: state.chartAreaLineColor,
  chartAreaTopColor: state.chartAreaTopColor,
  chartAreaBottomColor: state.chartAreaBottomColor,
  chartAreaLineWidth: state.chartAreaLineWidth as LineWidth,
  chartCrosshairColor: state.chartCrosshairColor,
  chartCrosshairLabelBackgroundColor: state.chartCrosshairLabelBackgroundColor,
  chartCrosshairLabelTextColor: state.chartCrosshairLabelTextColor,
  chartTimeScaleBorderColor: state.chartTimeScaleBorderColor,
  chartTimeScaleTextColor: state.chartTimeScaleTextColor,
  chartPriceScaleBorderColor: state.chartPriceScaleBorderColor,
  chartPriceScaleTextColor: state.chartPriceScaleTextColor,
  chartPriceLineVisible: state.chartPriceLineVisible,
  chartLastValueVisible: state.chartLastValueVisible,
  chartPriceLineWidth: state.chartPriceLineWidth,
  chartPriceLineStyle: state.chartPriceLineStyle,
  chartPriceLineColor: state.chartPriceLineColor,
  volumeEnabled: state.volumeEnabled,
  chartVolumeHeightRatio: state.chartVolumeHeightRatio,
  chartVolumeUpColor: state.chartVolumeUpColor,
  chartVolumeDownColor: state.chartVolumeDownColor,
  chartRightOffset: state.chartRightOffset,
  chartBarSpacing: state.chartBarSpacing,
  autoHideScalesEnabled: state.autoHideScalesEnabled,
});
export const selectSetSetting = (state: AppSettingsState) => state.setSetting;
export const selectToggleColumnVisibility = (state: AppSettingsState) => state.toggleColumnVisibility;
export const selectUpdateSortConfigs = (state: AppSettingsState) => state.updateSortConfigs;
export const selectSetColumnOrder = (state: AppSettingsState) => state.setColumnOrder;
export const selectSetColumnWidth = (state: AppSettingsState) => state.setColumnWidth;
export const selectSetColumnFilter = (state: AppSettingsState) => state.setColumnFilter;
export const selectSetTableCollapsed = (state: AppSettingsState) => state.setTableCollapsed;
export const selectResetSettings = (state: AppSettingsState) => state.resetSettings;
export const selectSetGlobalSearchTerm = (state: AppSettingsState) => state.setGlobalSearchTerm;
export const selectAutoHideScalesEnabled = (state: AppSettingsState) => state.autoHideScalesEnabled;
export const selectToggleAutoHideScales = (state: AppSettingsState) => state.toggleAutoHideScales; 
export const selectSetColumnWidths = (state: AppSettingsState) => state.setColumnWidths; 
export const selectSelectedInterval = (state: AppSettingsState) => state.selectedInterval;
export const selectAvailablePairs = (state: AppSettingsState) => state.availablePairs;
export const selectIndicatorColumns = (state: AppSettingsState) => state.indicatorColumns;
export const selectAddIndicatorColumn = (state: AppSettingsState) => state.addIndicatorColumn;
export const selectRemoveIndicatorColumn = (state: AppSettingsState) => state.removeIndicatorColumn;
export const selectUpdateIndicatorColumn = (state: AppSettingsState) => state.updateIndicatorColumn;
export const selectModeControl = (state: AppSettingsState) => state.modeControl;
export const selectViewMode = (state: AppSettingsState) => state.modeControl.currentMode;
export const selectScreenerSettings = (state: AppSettingsState) => state.modeControl.screenerSettings;
export const selectSetViewMode = (state: AppSettingsState) => state.setViewMode;
export const selectUpdateScreenerSettings = (state: AppSettingsState) => state.updateScreenerSettings;
export const selectLayoutTypesForModes = (state: AppSettingsState) => state.modeControl.layoutTypes;
export const selectLayoutTypeForCurrentMode = (state: AppSettingsState) => {
  const mode = state.modeControl.currentMode;
    return state.modeControl.layoutTypes[mode];
};
export const selectSetLayoutTypeForMode = (state: AppSettingsState) => state.setLayoutTypeForMode;
--- END FILE: src\shared\store\settingsStore.ts ---

--- START FILE: src\shared\store\StoreInitializer.tsx ---
'use client';

import React, { useEffect, useState, ReactNode } from 'react';
import { useMarketStore } from './marketStore';
import { useAppSettingsStore } from './settingsStore';
import { useIndicatorStore } from './indicatorStore';

interface StoreInitializerProps {
  children: ReactNode;
}

// Define helper type for subscribing with two arguments
type SubscribeWithSelector<T, U> = (selector: (state: T) => U, callback: (selectedState: U) => void) => () => void;

export function StoreInitializer({ children }: StoreInitializerProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  // initError state is kept for potential future global checks during initialization.
  // Currently, it's not actively set by this component after removal of custom load functions.
  const [initError, setInitError] = useState<string | null>(null); 

  useEffect(() => {
    // This effect ensures that children are rendered after a brief moment,
    // allowing Zustand's persist middleware to hydrate stores from localStorage.
    const timer = setTimeout(() => {
      setIsInitialized(true);
      if (process.env.NODE_ENV === 'development') {
        console.log('[StoreInitializer] Initialized: Persisted states should now be loaded by stores.');
      }
    }, 0); // Timeout 0 to defer execution until after the current call stack clears.
    return () => clearTimeout(timer);
  }, []);

  if (initError) {
    return (
      <div style={{ padding: '20px', color: 'red', textAlign: 'center' }}>
        <h2>Application Initialization Error:</h2>
        <p>{initError}</p>
        <p>Please try refreshing the page. If the problem persists, contact support.</p>
      </div>
    );
  }

  if (!isInitialized) {
    // TODO: Replace with a proper global loading indicator component if desired for better UX
    // For example: <GlobalAppSpinner /> or a skeleton screen.
    // Returning null prevents children from rendering prematurely and avoids potential FOUC.
    return null; 
  }

  return <>{children}</>;
}

// Development helper to reset all stores and clear relevant localStorage
export function resetAllStores() {
  if (process.env.NODE_ENV === 'development') {
    console.log('[StoreInitializer] Attempting to reset all stores and clear localStorage...');
    // Clear localStorage for known store keys
    const appSettingsKey = 'cryptoDashboardSettings'; // Key from appSettingsStore
    const marketDataKey = 'market-storage'; // Correct key from marketStore persist config
    const indicatorStoreKey = 'cryptoDashboardIndicatorSettings'; // TODO: Verify this key against your indicatorStore persist config
    
    localStorage.removeItem(appSettingsKey);
    localStorage.removeItem(marketDataKey);
    localStorage.removeItem(indicatorStoreKey); 
    // Add other store keys here if you have more persisted stores
    console.log(`[StoreInitializer] Removed localStorage items for keys: ${appSettingsKey}, ${marketDataKey}, ${indicatorStoreKey}`);
    
    // Reset store states by calling their respective reset actions
    if (useAppSettingsStore.getState().resetSettings) {
      useAppSettingsStore.getState().resetSettings();
    } else {
      console.warn('[StoreInitializer] appSettingsStore does not have resetSettings method.');
    }

    // if (useMarketStore.getState().clearCache) { // Old check
    //   useMarketStore.getState().clearCache();
    // } else {
    //   console.warn('[StoreInitializer] marketDataStore does not have clearCache method.');
    // }
    // Use reset() method for marketStore as it clears relevant cache fields
    if (useMarketStore.getState().reset) {
      useMarketStore.getState().reset(); 
      console.log('[StoreInitializer] marketStore reset method called.');
    } else {
      console.warn('[StoreInitializer] marketStore does not have a reset method.');
    }

    const indicatorStoreState = useIndicatorStore.getState() as any; // Cast to any to check for methods
    if (typeof indicatorStoreState.resetState === 'function') {
      indicatorStoreState.resetState();
    } else if (typeof indicatorStoreState.clearCache === 'function') {
      indicatorStoreState.clearCache(); 
    } else {
      console.warn('[StoreInitializer] indicatorStore does not have a known resetState or clearCache method. Check your indicatorStore implementation.');
    }
    
    console.log('[StoreInitializer] All stores have been requested to reset.');
    // Optionally, reload the page to ensure a completely clean state and re-initialization
    // This can be useful if components hold onto some state that isn't cleared by store resets alone.
    // window.location.reload(); 
  } else {
    console.warn('[StoreInitializer] resetAllStores called in non-development environment. Action aborted.');
  }
}

// Development helper to export current store state
export function exportStoreState() {
  if (process.env.NODE_ENV === 'development') {
    const state = {
      marketData: useMarketStore.getState(),
      settings: useAppSettingsStore.getState(),
      indicators: useIndicatorStore.getState(),
      timestamp: Date.now(),
    };
    
    const dateTime = new Date(state.timestamp).toISOString().replace(/:/g, '-').slice(0, -5); // Format: YYYY-MM-DDTHH-MM-SS
    const blob = new Blob([JSON.stringify(state, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mct-store-state-${dateTime}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    console.log('[StoreInitializer] Store state exported', state);
  } else {
    console.warn('[StoreInitializer] exportStoreState called in non-development environment. Action aborted.');
  }
}

// Make helpers available globally in development for easier debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).__MCT_RESET_STORES__ = resetAllStores;
  (window as any).__MCT_EXPORT_STATE__ = exportStoreState;
  console.log('[StoreInitializer] Development helpers (__MCT_RESET_STORES__, __MCT_EXPORT_STATE__) attached to window.');
}
--- END FILE: src\shared\store\StoreInitializer.tsx ---