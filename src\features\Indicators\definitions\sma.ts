// src/indicators/definitions/sma.ts
import type {
  IndicatorDefinition,
  IndicatorParam,
  IndicatorOutputInfo,
  CalculatedIndicatorResult,
  KlineData,
  IndicatorParams
} from '@/shared/index';
import { LineStyle } from 'lightweight-charts';

// --- Calculation Function (Moved from calculations.ts) ---
/**
 * Calculates Simple Moving Average (SMA).
 * @param data - Array of numbers (e.g., closing prices).
 * @param period - The period over which to calculate the SMA.
 * @returns Array of SMA values or undefined for periods where calculation isn't possible.
 */
const calculateSMA = (
  data: number[],
  period: number
): (number | undefined)[] => {
  if (period <= 0 || data.length < period) {
    return Array(data.length).fill(undefined);
  }

  const results: (number | undefined)[] = Array(period - 1).fill(undefined);
  let sum = 0;

  // Calculate sum for the first period
  for (let i = 0; i < period; i++) {
    sum += data[i];
  }
  results.push(sum / period);

  // Calculate subsequent SMAs using a rolling window
  for (let i = period; i < data.length; i++) {
    sum -= data[i - period];
    sum += data[i];
    results.push(sum / period);
  }

  return results;
};

// Define specific parameter types for SMA
interface SmaParams extends IndicatorParams {
  source: 'open' | 'high' | 'low' | 'close'; // Добавим выбор источника цены
  length: number;
}

const smaParameters: IndicatorParam[] = [
  {
    id: 'source',
    name: 'Source',
    type: 'select',
    defaultValue: 'close',
    options: [
      { value: 'open', label: 'Open' },
      { value: 'high', label: 'High' },
      { value: 'low', label: 'Low' },
      { value: 'close', label: 'Close' }
    ],
  },
  {
    id: 'length',
    name: 'Length',
    type: 'number',
    defaultValue: 14,
    min: 1,
    step: 1,
  },
];

const smaOutputs: IndicatorOutputInfo[] = [
  {
    id: 'sma', // Unique ID for this output line
    name: 'SMA', // Display name for this output
    type: 'line',
    color: '#2962FF', // Example blue color
    lineWidth: 1,
    lineStyle: LineStyle.Solid,
    visibleInStatistics: true,
  },
];

// This function now acts as a wrapper to call the local calculateSMA
const calculateSmaWrapper = (
  data: KlineData[] | number[],
  params: SmaParams
): CalculatedIndicatorResult[] => {
  // Если data - это массив KlineData, извлекаем нужные значения
  let sourceData: number[];
  if (Array.isArray(data) && data.length > 0 && typeof data[0] === 'object' && 'close' in data[0]) {
    // Это массив KlineData
    sourceData = (data as KlineData[]).map((kline) => kline[params.source as keyof KlineData] as number);
  } else {
    // Это уже массив чисел
    sourceData = data as number[];
  }

  const smaValues = calculateSMA(sourceData, params.length); // Calls the local function

  // Преобразуем результаты в формат CalculatedIndicatorResult
  return smaValues.map((value, i) => {
    const time = Array.isArray(data) && data.length > i && typeof data[i] === 'object' && 'time' in data[i]
      ? (data as KlineData[])[i].time
      : i;
      
    return {
      time,
      values: {
        sma: value !== undefined ? value : null
      }
    };
  });
};

export const SMA: IndicatorDefinition<SmaParams> = {
  id: 'SMA',
  name: 'Simple Moving Average',
  shortName: 'SMA', // Added short name
  description: 'Calculates the average of a selected range of prices over a period.',
  category: 'trend',
  hasPane: false,
  plotOnMainPane: true,
  params: smaParameters,
  outputs: smaOutputs,
  defaultStyle: {
    sma: {
      color: '#2962FF',
      lineWidth: 1,
      lineStyle: LineStyle.Solid,
    }
  },
  defaultParams: {
    source: 'close',
    length: 14
  },
  calculate: calculateSmaWrapper,
  getShortName: (params: SmaParams) => `SMA(${params.length})`,
}; 