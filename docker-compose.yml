services:
  # Основное приложение MCT (Next.js + Bun)
  app:
    image: oven/bun:latest # Используем готовый образ Bun
    # build:
    #   context: .
    #   dockerfile: Dockerfile
    container_name: metacharts_app
    ports:
      - "3000:3000" # Next.js app (Docker)
      - "3005:3005" # Bun server (API & WebSocket)
    depends_on:
      - redis
      - questdb
      - postgres
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
      - QUESTDB_URL=***********************************/qdb
      - DATABASE_URL=****************************************/mct_db
      - WATCHPACK_POLLING=true # Важно для Windows + Next.js
      - CHOKIDAR_USEPOLLING=true # Используется Chokidar для отслеживания файлов
      - FAST_REFRESH=true # Обеспечивает быстрое обновление React компонентов
      - NEXT_WEBPACK_USEPOLLING=1 # Альтернативное указание для webpack
    volumes:
      - .:/app:delegated # Основные файлы проекта
      - /app/node_modules # Исключаем node_modules из синхронизации
      - /app/.next # Исключаем .next из синхронизации
    working_dir: /app # Указываем рабочую директорию
    command: >
      sh -c "bun install --global concurrently && bun install && concurrently 'bun --hot src/server/main.ts' 'next dev -p 3000'"
    networks:
      - mct_network
    # Настройка Docker Compose Watch (для версии 2.22.0+)
    # Это особенно полезно для Windows + WSL2 пользователей
    develop:
      watch:
        # Исходные файлы приложения
        - action: sync
          path: ./src
          target: /app/src
          ignore:
            - node_modules/
            - .next/
        # Файлы UI компонентов
        - action: sync
          path: ./features
          target: /app/features
          ignore:
            - node_modules/
        # Общие ресурсы
        - action: sync
          path: ./shared
          target: /app/shared
          ignore:
            - node_modules/
        # Перезапуск при изменении package.json
        - action: rebuild
          path: ./package.json

  # Redis для кеширования
  redis:
    image: redis:7.0-alpine
    container_name: metacharts_redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - mct_network

  # QuestDB для временных рядов
  questdb:
    image: questdb/questdb:8.0.0 # QuestDB версии 8.0.0
    container_name: metacharts_questdb
    ports:
      - "8812:8812" # HTTP (REST API и Web Console)
      - "9000:9000" # PostgreSQL wire protocol
      - "9009:9009" # InfluxDB Line Protocol (ILP) over TCP
      - "9003:9003" # Min Health Server Port
    environment:
      QDB_CAIRO_COMMIT_LAG: 100 # Уменьшаем задержку коммита для более быстрой видимости данных
      QDB_CAIRO_SQL_COPY_ROOT: /var/lib/questdb/public # Директория для CSV импорта/экспорта
      QDB_LINE_TCP_NET_CONNECTION_LIMIT: 100 # Увеличиваем лимит подключений для ILP
    volumes:
      - questdb-data:/var/lib/questdb
    networks:
      - mct_network

  # PostgreSQL для основных данных
  postgres:
    image: postgres:15-alpine
    container_name: metacharts_postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: mct_db
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - mct_network

  # Prometheus для сбора метрик
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: metacharts_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    command: --config.file=/etc/prometheus/prometheus.yml
    depends_on:
      - app
    networks:
      - mct_network

  # Grafana для визуализации метрик
  grafana:
    image: grafana/grafana:9.5.3
    container_name: metacharts_grafana
    ports:
      - "4000:3000" # Grafana UI
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/provisioning/:/etc/grafana/provisioning/
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
    depends_on:
      - prometheus
    networks:
      - mct_network

  # k6 для комплексного нагрузочного тестирования
  k6:
    image: grafana/k6:0.52.0 # Актуальная версия k6
    container_name: metacharts_k6
    restart: "no" # Обычно тесты запускаются по требованию, а не постоянно
    volumes:
      - ./load-tests:/scripts # Монтируем директорию со скриптами тестов
    networks:
      - mct_network
    environment:
      # Настройки для комплексного тестирования
      - K6_TARGET_HOST=app # Имя контейнера с приложением
      - K6_API_PORT=3005 # HTTP API порт
      - K6_WS_PORT=3005 # WebSocket порт
      - K6_VUS=20 # Общее количество виртуальных пользователей
      - K6_DURATION=120s # Продолжительность теста (2 минуты)
      - K6_RAMP_UP=30s # Время на увеличение нагрузки
      - K6_RAMP_DOWN=30s # Время на снижение нагрузки
    depends_on:
      - app # Зависимость от основного приложения
    profiles:
      - testing # Запускать только при профиле testing

networks:
  mct_network:
    driver: bridge

volumes:
  redis-data:
  questdb-data:
  postgres-data:
  grafana-data:
