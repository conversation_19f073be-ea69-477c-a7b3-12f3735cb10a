import { z } from 'zod';

/**
 * -----------------------------------------------
 * Market Data Type Definitions
 * -----------------------------------------------
 */

/**
 * Schema for market types (spot or futures)
 */
export const MarketTypeSchema = z.enum(['spot', 'futures']);
export type MarketTypeZod = z.infer<typeof MarketTypeSchema>;

/**
 * Schema for kline/candlestick intervals
 */
export const KlineIntervalSchema = z.enum([
  '1m', '3m', '5m', '15m', '30m',
  '1h', '2h', '4h', '6h', '8h', '12h',
  '1d', '3d', '1w', '1M'
]);
export type KlineIntervalZod = z.infer<typeof KlineIntervalSchema>;

/**
 * Schema for candlestick/kline data
 */
export const KlineSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  interval: KlineIntervalSchema,
  openTime: z.number(),
  open: z.number(),
  high: z.number(),
  low: z.number(),
  close: z.number(),
  volume: z.number(),
  closeTime: z.number(),
  quoteVolume: z.number(),
  trades: z.number(),
  isClosed: z.boolean().default(true)
});
export type KlineZod = z.infer<typeof KlineSchema>;

/**
 * Schema for detailed ticker data
 */
export const TickerSchema = z.object({
  symbol: z.string(),
  marketType: MarketTypeSchema,
  lastPrice: z.number(),
  priceChange: z.number(),
  priceChangePercent: z.number(),
  weightedAvgPrice: z.number(),
  prevClosePrice: z.number(),
  lastQty: z.number(),
  bidPrice: z.number(),
  bidQty: z.number(),
  askPrice: z.number(),
  askQty: z.number(),
  openPrice: z.number(),
  highPrice: z.number(),
  lowPrice: z.number(),
  volume: z.number(),
  quoteVolume: z.number(),
  openTime: z.number(),
  closeTime: z.number(),
  firstId: z.number(),
  lastId: z.number(),
  count: z.number(),
  lastUpdated: z.number(),
});
export type FullTickerZod = z.infer<typeof TickerSchema>;


