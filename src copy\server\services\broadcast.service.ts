import { logger } from '../lib/logger';
import { AppEvent } from './ingest.service';

// Create a logger instance for the BroadcastService
const log = logger.child({ service: 'BroadcastService' });

/**
 * BroadcastService is responsible for:
 * 1. Managing client subscriptions to topics
 * 2. Publishing data to subscribed clients
 * 3. Acting as a bridge between IngestService and WebSocket clients
 */
export class BroadcastService {
  private publishFunction: ((topic: string, data: any) => void) | null = null;
  private isInitialized = false;
  
  /**
   * Initialize the BroadcastService with a publish function
   * @param publishFunction Function to publish data to WebSocket clients
   */
  public initialize(publishFunction: (topic: string, data: any) => void): void {
    if (this.isInitialized) {
      log.warn('BroadcastService already initialized');
      return;
    }
    
    if (!publishFunction || typeof publishFunction !== 'function') {
      throw new Error('Valid publish function is required to initialize BroadcastService');
    }
    
    this.publishFunction = publishFunction;
    this.isInitialized = true;
    log.info('BroadcastService initialized');
  }
  
  /**
   * Check if the service is initialized
   */
  public isReady(): boolean {
    return this.isInitialized && this.publishFunction !== null;
  }
  
  /**
   * Handle application events from IngestService
   * @param event The application event to broadcast
   */
  public handleAppEvent(event: AppEvent): void {
    if (!this.isReady()) {
      log.warn('BroadcastService not ready, cannot handle app event');
      return;
    }
    
    const { topic, type, data } = event;
    
    // Create a message object with the event data
    const message = {
      type: this.mapEventTypeToMessageType(type),
      topic,
      data
    };
    
    // Publish the message to the appropriate topic
    try {
      this.publishFunction!(topic, message);
      // log.debug(`Published ${type} event to topic: ${topic}`); // Commented out to reduce log spam
    } catch (error) {
      log.error(`Error publishing ${type} event to topic ${topic}:`, error);
    }
  }
  
  /**
   * Map application event types to WebSocket message types
   */
  private mapEventTypeToMessageType(eventType: string): string {
    switch (eventType) {
      case 'kline_update':
        return 'kline_data';
      case 'tickers_update':
        return 'ticker_data';
      case 'symbols_update':
        return 'symbols_data';
      default:
        return eventType;
    }
  }
  
  /**
   * Shutdown the BroadcastService
   */
  public shutdown(): void {
    this.publishFunction = null;
    this.isInitialized = false;
    log.info('BroadcastService shutdown');
  }
}

// Create singleton instance
const broadcastService = new BroadcastService();

export default broadcastService; 