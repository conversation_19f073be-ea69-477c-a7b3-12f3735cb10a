import {
	IChartApi,
	ISeriesApi,
	ISeriesPrimitive,
	IPrimitivePaneView,
	IPrimitivePaneRenderer,
	SeriesAttachedParameter,
	PrimitivePaneViewZOrder,
	Time,
	UTCTimestamp,
	Logical,
	SeriesType,
	Coordinate,
	AutoscaleInfo,
} from 'lightweight-charts';
import { useEffect, useRef, useCallback } from 'react';
import { CanvasRenderingTarget2D } from 'fancy-canvas';

interface RulerPoint {
	time: Time;
	logical: Logical;
	price: number;
	x: Coordinate;
	y: Coordinate;
	timestamp: UTCTimestamp | null;
}

function getTimeAsTimestamp(time: Time): UTCTimestamp | null {
	if (typeof time === 'string') {
		const parsed = Date.parse(time);
		if (!isNaN(parsed)) return Math.floor(parsed / 1000) as UTCTimestamp;
		const match = time.match(/^(\d{4})-(\d{2})-(\d{2})$/);
		if (match) {
			const date = new Date(Date.UTC(parseInt(match[1], 10), parseInt(match[2], 10) - 1, parseInt(match[3], 10)));
			if (!isNaN(date.getTime())) return Math.floor(date.getTime() / 1000) as UTCTimestamp;
		}
		console.warn('Could not parse date string:', time);
		return null;
	}
	if (typeof time === 'number') return time as UTCTimestamp;
	if (typeof time === 'object' && time !== null && 'timestamp' in time) return time.timestamp as UTCTimestamp;
	if (typeof time === 'object' && time !== null && 'year' in time && 'month' in time && 'day' in time) {
		const date = new Date(Date.UTC(time.year, time.month - 1, time.day));
		if (!isNaN(date.getTime())) return Math.floor(date.getTime() / 1000) as UTCTimestamp;
		console.warn('Could not parse BusinessDay object:', time);
		return null;
	}
	console.warn('Unhandled time format:', time);
	return null;
}

interface RulerPrimitiveOptions {
	lineColor: string;
	lineWidth: number;
	labelBackgroundColor: string;
	labelTextColor: string;
	labelFont: string;
}

const defaultRulerOptions: RulerPrimitiveOptions = {
	lineColor: 'rgba(0, 150, 255, 0.7)',
	lineWidth: 1,
	labelBackgroundColor: 'rgba(0, 0, 0, 0.7)',
	labelTextColor: 'white',
	labelFont: '12px monospace',
};

class RulerPaneRenderer implements IPrimitivePaneRenderer {
	private _start: RulerPoint | null = null;
	private _end: RulerPoint | null = null;
	private _options: RulerPrimitiveOptions;
	private _series: ISeriesApi<SeriesType> | null = null;

	constructor(start: RulerPoint | null, end: RulerPoint | null, options: RulerPrimitiveOptions) {
		this._start = start;
		this._end = end;
		this._options = options;
	}

	draw(target: CanvasRenderingTarget2D) {
		if (!this._start || !this._end) return;

		target.useBitmapCoordinateSpace(scope => {
			const ctx = scope.context;
			if (isNaN(this._start!.x) || isNaN(this._start!.y) || isNaN(this._end!.x) || isNaN(this._end!.y)) return;

			const startX = Math.round(this._start!.x * scope.horizontalPixelRatio);
			const startY = Math.round(this._start!.y * scope.verticalPixelRatio);
			const endX = Math.round(this._end!.x * scope.horizontalPixelRatio);
			const endY = Math.round(this._end!.y * scope.verticalPixelRatio);

			// Draw rectangle background
			const rectX = Math.min(startX, endX);
			const rectY = Math.min(startY, endY);
			const rectWidth = Math.abs(startX - endX);
			const rectHeight = Math.abs(startY - endY);
			ctx.fillStyle = 'rgba(0, 150, 255, 0.1)';
			if (rectWidth > 0 && rectHeight > 0) ctx.fillRect(rectX, rectY, rectWidth, rectHeight);

			// Draw line
			ctx.lineWidth = this._options.lineWidth * scope.verticalPixelRatio;
			ctx.strokeStyle = this._options.lineColor;
			ctx.lineCap = 'round';
			ctx.beginPath();
			ctx.moveTo(startX, startY);
			ctx.lineTo(endX, endY);
			ctx.stroke();

			if (this._start!.logical === this._end!.logical && this._start!.price === this._end!.price) return;

			// Calculate measurements
			const deltaPrice = this._end!.price - this._start!.price;
			const absDeltaPrice = Math.abs(deltaPrice);
			const priceDirection = deltaPrice > 0 ? '▲' : deltaPrice < 0 ? '▼' : '◆';
			const deltaPricePercent = this._start!.price === 0 ? 0 : (deltaPrice / this._start!.price) * 100;
			const absDeltaPricePercent = Math.abs(deltaPricePercent);
			const deltaBars = Math.abs(this._end!.logical - this._start!.logical);

			let deltaTimeStr = '';
			if (this._start!.timestamp !== null && this._end!.timestamp !== null) {
				const deltaTimeSeconds = Math.abs(this._end!.timestamp - this._start!.timestamp);
				const days = Math.floor(deltaTimeSeconds / (60 * 60 * 24));
				const hours = Math.floor((deltaTimeSeconds % (60 * 60 * 24)) / (60 * 60));
				const minutes = Math.floor((deltaTimeSeconds % (60 * 60)) / 60);

				if (days > 0) deltaTimeStr += `${days}d `;
				if (hours > 0) deltaTimeStr += `${hours}h `;
				if (minutes > 0 || (days === 0 && hours === 0 && deltaTimeSeconds > 0)) deltaTimeStr += `${minutes}m`;
				if (deltaTimeStr === '' && deltaTimeSeconds > 0) deltaTimeStr = '< 1m';
				if (deltaTimeStr === '' && deltaTimeSeconds === 0) deltaTimeStr = '0m';
				deltaTimeStr = deltaTimeStr.trim();
			}

			const directionColor = deltaPrice > 0 ? '#26a69a' : deltaPrice < 0 ? '#ef5350' : '#bbbbbb';
			const priceFormat = this._series?.priceFormatter();
			const formattedAbsDeltaPrice = priceFormat ? priceFormat.format(absDeltaPrice) : absDeltaPrice.toFixed(5);
			const line1 = `${priceDirection} ${absDeltaPricePercent.toFixed(2)}% (${formattedAbsDeltaPrice})`;
			const line2 = `Bars: ${deltaBars}${deltaTimeStr ? ` | Time: ${deltaTimeStr}` : ''}`;

			// Draw measurement box
			ctx.font = this._options.labelFont;
			const scaledPadding = 5 * scope.horizontalPixelRatio;
			const scaledLineSpacing = 4 * scope.verticalPixelRatio;
			const textMetrics = ctx.measureText('Mg');
			const scaledTextHeight = (textMetrics.actualBoundingBoxAscent ?? 12 * scope.verticalPixelRatio) + (textMetrics.actualBoundingBoxDescent ?? 0);

			const textWidth1 = ctx.measureText(line1).width;
			const textWidth2 = ctx.measureText(line2).width;
			const boxWidth = Math.max(textWidth1, textWidth2) + scaledPadding * 2;
			const boxHeight = (scaledTextHeight * 2 + scaledLineSpacing) + scaledPadding * 2;

			let boxX = endX + scaledPadding * 2;
			let boxY = endY - boxHeight / 2;

			if (boxX + boxWidth > scope.bitmapSize.width) boxX = endX - boxWidth - scaledPadding * 2;
			if (boxY < scaledPadding) boxY = scaledPadding;
			else if (boxY + boxHeight > scope.bitmapSize.height) boxY = scope.bitmapSize.height - boxHeight - scaledPadding;

			ctx.fillStyle = this._options.labelBackgroundColor;
			ctx.beginPath();
			ctx.roundRect(boxX, boxY, boxWidth, boxHeight, 4 * scope.horizontalPixelRatio);
			ctx.fill();

			ctx.textAlign = 'left';
			ctx.textBaseline = 'top';
			let currentY = boxY + scaledPadding;

			ctx.fillStyle = directionColor;
			ctx.fillText(line1, boxX + scaledPadding, currentY);

			currentY += scaledTextHeight + scaledLineSpacing;
			ctx.fillStyle = this._options.labelTextColor;
			ctx.fillText(line2, boxX + scaledPadding, currentY);
		});
	}

	public setSeries(series: ISeriesApi<SeriesType>) {
		this._series = series;
	}
}

class RulerPaneView implements IPrimitivePaneView {
	private _source: RulerPrimitive;
	private _start: RulerPoint | null = null;
	private _end: RulerPoint | null = null;
	private _renderer: RulerPaneRenderer;

	constructor(source: RulerPrimitive) {
		this._source = source;
		this._renderer = new RulerPaneRenderer(null, null, source.getOptions());
		if (source.series) this._renderer.setSeries(source.series);
	}

	update() {
		const chart = this._source.chart;
		const series = this._source.series;
		if (!chart || !series) {
			this._start = this._end = null;
			this._renderer = new RulerPaneRenderer(null, null, this._source.getOptions());
			return;
		}

		const timeScale = chart.timeScale();
		const startData = this._source.getStartData();
		const endData = this._source.getEndData();

		this._start = null;
		if (startData) {
			const startY = series.priceToCoordinate(startData.price);
			const startX = timeScale.timeToCoordinate(startData.time);
			if (startX !== null && startY !== null) this._start = { ...startData, x: startX, y: startY };
		}

		this._end = null;
		if (endData) {
			const endY = series.priceToCoordinate(endData.price);
			const endX = timeScale.timeToCoordinate(endData.time);
			if (endX !== null && endY !== null) this._end = { ...endData, x: endX, y: endY };
		}
		this._renderer = new RulerPaneRenderer(this._start, this._end || this._start, this._source.getOptions());
		this._renderer.setSeries(series);
	}

	renderer(): IPrimitivePaneRenderer | null {
		return this._start ? this._renderer : null;
	}

	zOrder?(): PrimitivePaneViewZOrder {
		return 'top';
	}
}

type RulerPointData = Omit<RulerPoint, 'x' | 'y'>;

class RulerPrimitive implements ISeriesPrimitive<Time> {
	private _chart: IChartApi | null = null;
	private _series: ISeriesApi<SeriesType> | null = null;
	private _requestUpdate: (() => void) | null = null;
	private _startData: RulerPointData | null = null;
	private _endData: RulerPointData | null = null;
	private _paneViews: RulerPaneView[];
	private _options: RulerPrimitiveOptions;

	constructor(chart: IChartApi, series: ISeriesApi<SeriesType>, options?: Partial<RulerPrimitiveOptions>) {
		this._chart = chart;
		this._series = series;
		this._options = { ...defaultRulerOptions, ...options };
		this._paneViews = [new RulerPaneView(this)];
	}

	public attached(param: SeriesAttachedParameter): void {
		if (this._series !== param.series) {
			console.warn('RulerPrimitive attached to a different series than initialized with.');
			this._series = param.series as ISeriesApi<SeriesType>;
		}
		this._chart = param.chart;
		this._requestUpdate = param.requestUpdate;
		this.updateAllViews();
	}

	public detached(): void {
		this._startData = this._endData = null;
		this._requestUpdate = this._chart = this._series = null;
	}

	paneViews() {
		return this._paneViews;
	}

	updateAllViews() {
		this._paneViews.forEach(view => view.update());
		this.requestUpdate();
	}

	public requestUpdate(): void {
		this._requestUpdate?.();
	}

	autoscaleInfo(startTime: Logical, endTime: Logical): AutoscaleInfo | null {
		return null;
	}

	public get chart(): IChartApi | null {
		return this._chart;
	}

	public get series(): ISeriesApi<SeriesType> | null {
		return this._series;
	}

	public setPoints(start?: RulerPointData, end?: RulerPointData): void {
		this._startData = start || null;
		this._endData = end || null;
		this.updateAllViews();
	}

	public setStartPoint(point: RulerPointData | null): void {
		this._startData = this._endData = point;
		this.updateAllViews();
	}

	public setEndPoint(point: RulerPointData | null): void {
		this._endData = point;
		this.updateAllViews();
	}

	public getStartData(): RulerPointData | null {
		return this._startData;
	}

	public getEndData(): RulerPointData | null {
		return this._endData;
	}

	public getOptions(): RulerPrimitiveOptions {
		return this._options;
	}
}

function getChartPointDataFromMouseEvent(event: MouseEvent, chart: IChartApi, series: ISeriesApi<SeriesType>): RulerPointData | null {
	const chartElement = chart.chartElement();
	if (!chartElement) return null;

	const rect = chartElement.getBoundingClientRect();
	const x = event.clientX - rect.left;
	const y = event.clientY - rect.top;

	if (x < 0 || x > rect.width || y < 0 || y > rect.height) return null;

	const logical = chart.timeScale().coordinateToLogical(x);
	if (logical === null) return null;

	const time = chart.timeScale().coordinateToTime(x);
	if (time === null) return null;

	const price = series.coordinateToPrice(y);
	if (price === null) return null;

	return { time, logical, price, timestamp: getTimeAsTimestamp(time) };
}

export function useRuler(chart: IChartApi | null, mainSeries: ISeriesApi<SeriesType> | null) {
	const rulerPrimitiveRef = useRef<RulerPrimitive | null>(null);
	const isDrawingRef = useRef<boolean>(false);

	const detachRuler = useCallback(() => {
		if (rulerPrimitiveRef.current) {
			try {
				const currentSeries = rulerPrimitiveRef.current.series || mainSeries;
				currentSeries?.detachPrimitive(rulerPrimitiveRef.current);
			} catch (e) {
				console.error('Failed to detach ruler primitive:', e);
			}
			rulerPrimitiveRef.current = null;
		}
	}, [mainSeries]);

	const handleMouseDown = useCallback((event: MouseEvent) => {
		if (!chart || !mainSeries || event.button !== 1) return;
		event.preventDefault();
		detachRuler();

		const pointData = getChartPointDataFromMouseEvent(event, chart, mainSeries);
		if (pointData) {
			const newRuler = new RulerPrimitive(chart, mainSeries);
			try {
				mainSeries.attachPrimitive(newRuler);
				rulerPrimitiveRef.current = newRuler;
				newRuler.setStartPoint(pointData);
				isDrawingRef.current = true;
				chart.chartElement().style.cursor = 'crosshair';
			} catch (e) {
				console.error('Failed to attach ruler primitive:', e);
				rulerPrimitiveRef.current = null;
			}
		}
	}, [chart, mainSeries, detachRuler]);

	const handleMouseMove = useCallback((event: MouseEvent) => {
		if (isDrawingRef.current && rulerPrimitiveRef.current && chart && mainSeries) {
			event.preventDefault();
			const pointData = getChartPointDataFromMouseEvent(event, chart, mainSeries);
			rulerPrimitiveRef.current.setEndPoint(pointData);
		}
	}, [chart, mainSeries]);

	const handleMouseUp = useCallback((event: MouseEvent) => {
		if (event.button === 1 && isDrawingRef.current && chart && rulerPrimitiveRef.current) {
			event.preventDefault();
			isDrawingRef.current = false;
			chart.chartElement().style.cursor = 'default';

			const pointData = getChartPointDataFromMouseEvent(event, chart, mainSeries!);
			rulerPrimitiveRef.current.setEndPoint(pointData);

			const startData = rulerPrimitiveRef.current.getStartData();
			const endData = rulerPrimitiveRef.current.getEndData();

			if (startData && endData && startData.logical === endData.logical) {
				detachRuler();
			} else {
				rulerPrimitiveRef.current.requestUpdate();
			}
		}
	}, [chart, mainSeries, detachRuler]);

	useEffect(() => {
		if (!chart || !mainSeries) {
			detachRuler();
			return;
		}

		const chartElement = chart.chartElement();
		if (!chartElement) return;

		chartElement.addEventListener('mousedown', handleMouseDown);
		document.addEventListener('mousemove', handleMouseMove);
		document.addEventListener('mouseup', handleMouseUp);
		// Удаляем обработчик contextmenu

		return () => {
			chartElement.removeEventListener('mousedown', handleMouseDown);
			// Удаляем удаление обработчика contextmenu
			chartElement.style.cursor = 'default';
			document.removeEventListener('mousemove', handleMouseMove);
			document.removeEventListener('mouseup', handleMouseUp);
			detachRuler();
		};
	}, [chart, mainSeries, detachRuler, handleMouseDown, handleMouseMove, handleMouseUp]);
}
