import { EventEmitter } from 'events';
import pino from 'pino';
import WebSocket from 'ws';
import { z } from 'zod';

import {
  ExchangeService,
  SymbolInfo,
  KlineOptions,
  KlineEvent,
  TickerEvent,
} from './exchange.interface';
import {
  MarketType,
  CoreTicker,
  CoreKline,
  KlineIntervalSchema,
} from '@/shared/index';
import { binanceAdapter } from '../adapters/binance.adapter';

// Local logger for this service
const logger = pino({ name: 'BinanceService', level: process.env.LOG_LEVEL || 'info' });

// Binance API configuration
const API_CONFIG = {
  baseUrls: {
    spot: 'https://api.binance.com',
    futures: 'https://fapi.binance.com',
  },
  wsUrls: {
    spot: 'wss://stream.binance.com:9443',
    futures: 'wss://fstream.binance.com',
  },
};

// Supported intervals on Binance for validation
const SUPPORTED_INTERVALS = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d', '3d', '1w', '1M'];

// WebSocket connection states
const WebSocketState = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
};

type MarketTypeConfig = typeof API_CONFIG.baseUrls;

/**
 * Implements the ExchangeService interface for Binance.
 * Handles both REST API calls for historical data and WebSocket connections
 * for real-time data streams. It emits standardized events for data updates.
 */
export class BinanceService extends EventEmitter implements ExchangeService {
  public readonly exchangeName = 'binance';
  private static instance: BinanceService;

  private wsConnections: Record<MarketType, WebSocket | null> = {
    [MarketType.Spot]: null,
    [MarketType.Futures]: null,
  };
  private connecting: Record<MarketType, boolean> = {
    [MarketType.Spot]: false,
    [MarketType.Futures]: false,
  };
  private reconnectTimers: Record<MarketType, NodeJS.Timeout | null> = {
    [MarketType.Spot]: null,
    [MarketType.Futures]: null,
  };
  private reconnectAttempts: Record<MarketType, number> = {
    [MarketType.Spot]: 0,
    [MarketType.Futures]: 0,
  };
  
  // Tracks active subscriptions by stream name and their reference count
  private activeSubscriptions: Map<string, { marketType: MarketType; count: number }> = new Map();

  private constructor() {
    super();
    // Increase max listeners to handle multiple subscriptions
    this.setMaxListeners(100);
  }

  /**
   * Gets the singleton instance of the BinanceService.
   */
  public static getInstance(): BinanceService {
    if (!BinanceService.instance) {
      BinanceService.instance = new BinanceService();
    }
    return BinanceService.instance;
  }

  /**
   * Initializes WebSocket connections to the exchange.
   */
  public async initialize(): Promise<void> {
    logger.info('Initializing BinanceService...');
    await Promise.all([this.connect(MarketType.Spot), this.connect(MarketType.Futures)]);
    logger.info('BinanceService initialized successfully.');
  }

  /**
   * Closes all connections and cleans up resources.
   */
  public async shutdown(): Promise<void> {
    logger.info('Shutting down BinanceService...');
    // Clear all event listeners to prevent memory leaks
    this.removeAllListeners();
    await Promise.all([this.disconnect(MarketType.Spot), this.disconnect(MarketType.Futures)]);
    // Clear any pending reconnect timers
    Object.values(this.reconnectTimers).forEach(timer => timer && clearTimeout(timer));
    logger.info('BinanceService shut down successfully.');
  }

  // --- REST API Methods ---

  public async fetchSymbols(marketType: MarketType): Promise<SymbolInfo[]> {
    const url = marketType === MarketType.Spot 
      ? `${API_CONFIG.baseUrls.spot}/api/v3/exchangeInfo`
      : `${API_CONFIG.baseUrls.futures}/fapi/v1/exchangeInfo`;
    
    const response = await this.fetchAPI(url);
    
    // Zod schema for validation
    const SymbolSchema = z.object({
      symbol: z.string(),
      status: z.string(),
      baseAsset: z.string(),
      quoteAsset: z.string(),
    });

    const ExchangeInfoSchema = z.object({
      symbols: z.array(SymbolSchema),
    });

    const parsed = ExchangeInfoSchema.safeParse(response);
    if (!parsed.success) {
      logger.error({ error: parsed.error }, `Failed to parse symbols for ${marketType}`);
      throw new Error(`Invalid symbols data received from exchange for ${marketType}.`);
    }

    return parsed.data.symbols
      .filter(s => s.status === 'TRADING')
      .map(s => binanceAdapter.adaptSymbol(s, marketType));
  }

  public async fetchTickers(marketType: MarketType): Promise<CoreTicker[]> {
    const url = marketType === MarketType.Spot
      ? `${API_CONFIG.baseUrls.spot}/api/v3/ticker/24hr`
      : `${API_CONFIG.baseUrls.futures}/fapi/v1/ticker/24hr`;

    try {
      const response = await this.fetchAPI(url);
      
      if (!Array.isArray(response)) {
        logger.error(`Invalid response format for ${marketType} tickers: expected array`);
        throw new Error(`Invalid response format for ${marketType} tickers`);
      }
      
      // Use the adapter to convert Binance data to our internal format
      const tickers = binanceAdapter.adaptTickers(response, marketType);
      
      logger.debug(`Successfully fetched ${tickers.length} ${marketType} tickers`);
      return tickers;
    } catch (error) {
      logger.error({ error }, `Failed to fetch ${marketType} tickers`);
      throw new Error(`Failed to fetch ${marketType} tickers: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async fetchKlines(
    marketType: MarketType,
    symbol: string,
    interval: string,
    options: KlineOptions = { limit: 500 }
  ): Promise<CoreKline[]> {
    const parsedInterval = KlineIntervalSchema.safeParse(interval);
    if (!parsedInterval.success) {
      throw new Error(`Invalid interval provided to fetchKlines: ${interval}`);
    }

    const baseUrl = API_CONFIG.baseUrls[marketType];
    const endpoint = marketType === MarketType.Spot ? '/api/v3/klines' : '/fapi/v1/klines';
    const url = new URL(endpoint, baseUrl);
    
    url.searchParams.append('symbol', symbol);
    url.searchParams.append('interval', interval);
    if (options.limit) url.searchParams.append('limit', String(options.limit));
    if (options.startTime) url.searchParams.append('startTime', String(options.startTime));
    if (options.endTime) url.searchParams.append('endTime', String(options.endTime));

    const response = await this.fetchAPI(url.toString());
    
    const KlineSchema = z.tuple([
      z.number(), // Open time
      z.string(), // Open
      z.string(), // High
      z.string(), // Low
      z.string(), // Close
      z.string(), // Volume
      z.number(), // Close time
      z.string(), // Quote asset volume
      z.number(), // Number of trades
      z.string(), // Taker buy base asset volume
      z.string(), // Taker buy quote asset volume
      z.string(), // Ignore
    ]);

    const KlinesSchema = z.array(KlineSchema);
    const parsed = KlinesSchema.safeParse(response);
    
    if (!parsed.success) {
      logger.error({ error: parsed.error }, `Failed to parse klines for ${symbol}`);
      throw new Error(`Invalid kline data received for ${symbol}.`);
    }

    return binanceAdapter.adaptKlines(parsed.data, marketType, interval);
  }

  // --- WebSocket Subscription Methods ---

  public subscribeTickers(marketType: MarketType): () => void {
    const streamName = '!ticker@arr';
    return this.manageSubscription(marketType, streamName);
  }

  public subscribeKline(marketType: MarketType, symbol: string, interval: string): () => void {
    if (!SUPPORTED_INTERVALS.includes(interval)) {
      const errorMsg = `Unsupported interval: ${interval}. Cannot subscribe.`;
      logger.warn({ marketType, symbol, interval }, errorMsg);
      // Return a no-op function for invalid subscriptions
      return () => {}; 
    }
    const streamName = `${symbol.toLowerCase()}@kline_${interval}`;
    return this.manageSubscription(marketType, streamName);
  }

  // --- Private Helper Methods ---

  /**
   * Generic fetch wrapper for Binance REST API.
   */
  private async fetchAPI(url: string): Promise<any> {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed with status ${response.status}: ${errorBody}`);
      }
      return await response.json();
    } catch (error) {
      logger.error({ error, url }, 'API call failed');
      throw error;
    }
  }

  /**
   * Manages the logic for subscribing and returning an unsubscribe function.
   */
  private manageSubscription(marketType: MarketType, streamName: string): () => void {
    this.subscribeToStream(marketType, streamName);
    
    // Return a function that can be called to unsubscribe
    return () => {
      this.unsubscribeFromStream(marketType, streamName);
    };
  }

  // --- Internal WebSocket Connection and Subscription Management ---
  // (Adapted from ExchangeDataCollectorService)

  private subscribeToStream(marketType: MarketType, streamName: string): void {
    const sub = this.activeSubscriptions.get(streamName) || { marketType, count: 0 };
    sub.count++;
    this.activeSubscriptions.set(streamName, sub);
    
    // Only send subscribe message on first subscription
    if (sub.count === 1) {
      this.sendSubscriptionMessage(marketType, [streamName], 'SUBSCRIBE');
    }
  }

  private unsubscribeFromStream(marketType: MarketType, streamName: string): void {
    const sub = this.activeSubscriptions.get(streamName);
    if (!sub) return;

    sub.count--;
    if (sub.count <= 0) {
      this.activeSubscriptions.delete(streamName);
      this.sendSubscriptionMessage(marketType, [streamName], 'UNSUBSCRIBE');
    } else {
      this.activeSubscriptions.set(streamName, sub);
    }
  }

  private sendSubscriptionMessage(
    marketType: MarketType, 
    streams: string[],
    method: 'SUBSCRIBE' | 'UNSUBSCRIBE'
  ): void {
    const ws = this.wsConnections[marketType];
    if (!ws || ws.readyState !== WebSocketState.OPEN) {
      logger.warn({ marketType, streams, method }, 'Cannot send subscription message, WebSocket not connected.');
      // If the connection is down, the resubscribe logic will handle it upon reconnection.
      return;
    }
    const message = { method, params: streams, id: Date.now() };
    ws.send(JSON.stringify(message));
    logger.info({ marketType, method, streams: streams.length }, `Sent ${method} request.`);
  }

  private connect(marketType: MarketType): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.wsConnections[marketType] || this.connecting[marketType]) {
        logger.info({ marketType }, 'WebSocket connection already exists or is in progress.');
        return resolve();
      }

      this.connecting[marketType] = true;
      const url = `${API_CONFIG.wsUrls[marketType as keyof MarketTypeConfig]}/stream`;
      logger.info({ marketType, url }, 'Connecting to WebSocket...');
      
      const ws = new WebSocket(url);
      
      ws.on('open', () => {
        logger.info({ marketType }, 'WebSocket connection established.');
        clearTimeout(connectionTimeout);
        this.connecting[marketType] = false;
        this.wsConnections[marketType] = ws;
        this.reconnectAttempts[marketType] = 0;
        this.resubscribe(marketType);
        this.emit('reconnect', { marketType }); // Emit reconnect event
        resolve();
      });

      ws.on('message', (data: Buffer) => this.handleMessage(marketType, data));
      ws.on('close', (code, reason) => this.handleClose(marketType, code, reason.toString()));
      ws.on('error', (error) => this.handleError(marketType, error));
      
      const connectionTimeout = setTimeout(() => {
        if (this.connecting[marketType]) {
          this.connecting[marketType] = false;
          ws.terminate();
          const err = new Error(`WebSocket connection timeout for ${marketType}`);
          logger.error({ marketType }, err.message);
          reject(err);
        }
      }, 10000);
    });
  }

  private disconnect(marketType: MarketType): Promise<void> {
    return new Promise(resolve => {
      const ws = this.wsConnections[marketType];
      if (!ws) return resolve();

      // Clear reconnect timer to prevent auto-reconnect on manual disconnect
      if (this.reconnectTimers[marketType]) {
        clearTimeout(this.reconnectTimers[marketType] as NodeJS.Timeout);
        this.reconnectTimers[marketType] = null;
      }
      
      if (ws.readyState === WebSocketState.OPEN || ws.readyState === WebSocketState.CONNECTING) {
        ws.once('close', () => {
          this.wsConnections[marketType] = null;
          logger.info({ marketType }, 'WebSocket disconnected.');
          resolve();
        });
        ws.close(1000, 'Client-initiated shutdown');
      } else {
        this.wsConnections[marketType] = null;
        resolve();
      }
    });
  }

  private handleMessage(marketType: MarketType, data: Buffer): void {
    try {
      const message = JSON.parse(data.toString());
      if (message.stream) {
        // This is a multiplexed stream message
        const streamType = message.stream.split('@')[1];

        if (streamType.startsWith('kline')) {
          this.processKlineUpdate(marketType, message.data);
        } else if (streamType === 'ticker_1h' || streamType === 'ticker' || streamType === 'ticker@arr') {
          this.processTickerUpdate(marketType, message.data);
        }
      }
    } catch (error) {
      logger.error({ marketType, error }, 'Error processing WebSocket message');
    }
  }

  private processKlineUpdate(marketType: MarketType, data: any): void {
    const klineData = binanceAdapter.adaptKline(data.k, marketType, data.k.i);
    if (klineData) {
      const event: KlineEvent = {
        marketType,
        symbol: klineData.symbol,
        interval: klineData.interval,
        data: [klineData], // Emitting kline data as an array
      };
      this.emit('kline', event);
    }
  }

  private processTickerUpdate(marketType: MarketType, data: any | any[]): void {
    const tickerData = Array.isArray(data) ? data : [data];
    const tickers = binanceAdapter.adaptTickers(tickerData, marketType);
    
    if (tickers.length > 0) {
      const event: TickerEvent = {
        marketType,
        data: tickers,
      };
      this.emit('ticker', event);
    }
  }

  private handleClose(marketType: MarketType, code: number, reason: string): void {
    logger.warn({ marketType, code, reason }, 'WebSocket connection closed.');
    this.wsConnections[marketType] = null;
    this.emit('error', { type: 'error', marketType, message: `WebSocket closed: ${reason} (code: ${code})` });
    // Don't auto-reconnect on normal closure (1000) by client
    if (code !== 1000) {
      this.scheduleReconnect(marketType);
    }
  }

  private handleError(marketType: MarketType, error: Error): void {
    logger.error({ marketType, error }, 'WebSocket error occurred.');
    this.emit('error', { type: 'error', marketType, message: error.message || 'Unknown WebSocket error' });
    // The 'close' event will usually follow, which will trigger reconnect logic.
  }

  private scheduleReconnect(marketType: MarketType): void {
    if (this.reconnectTimers[marketType]) return; // Already scheduled

    this.reconnectAttempts[marketType]++;
    const delay = Math.min(30000, 1000 * Math.pow(1.5, this.reconnectAttempts[marketType]));
    logger.info({ marketType, attempt: this.reconnectAttempts[marketType], delay }, `Scheduling reconnect...`);
    
    this.reconnectTimers[marketType] = setTimeout(() => {
      this.reconnectTimers[marketType] = null;
      this.connect(marketType).catch(err => {
        logger.error({ marketType, err }, 'Reconnect attempt failed.');
        // If connect fails, it will call handleClose/handleError, which can schedule another reconnect
      });
    }, delay);
  }

  private resubscribe(marketType: MarketType): void {
    const streamsToResubscribe = Array.from(this.activeSubscriptions.entries())
      .filter(([, sub]) => sub.marketType === marketType)
      .map(([streamName]) => streamName);

    if (streamsToResubscribe.length > 0) {
      logger.info({ marketType, count: streamsToResubscribe.length }, 'Resubscribing to active streams...');
      this.sendSubscriptionMessage(marketType, streamsToResubscribe, 'SUBSCRIBE');
    }
  }
}

export const binanceService = BinanceService.getInstance();
