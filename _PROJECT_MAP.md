# PROJECT MAP - Generated: 2025-06-16T11:23:07.856Z

## 📊 Project Overview
- **Total Files**: 58
- **Total Folders**: 22
- **Total Characters**: 502 885
- **Total Lines**: 12 085
- **Total Functions/Methods**: 314
- **Total Classes**: 8
- **Total Constants**: 54
- **Total Variables**: 146
- **Legend**: `■` Class, `▸` Method/Constructor, `ƒ` Function, `•` Variable/Property


## ✨ Feature: `Chart`
### 📁 `src/features/Chart/`  # 1948 LoC | 90095 chars
#### 📄 Chart.tsx  # 804 LoC | 33576 chars
*Dependencies:*
  - `./useChartIndicators`
  - `./plugins/usePriceAndTimer`
  - `./useChartData`
    • AVAILABLE_TIMEFRAMES *--- Constants ---*
    • EMPTY_KLINE_DATA
    • MINIMUM_MOVE
    • DEFAULT_HISTORICAL_LIMIT
    • KLINE_UPDATE_DEBOUNCE_MS
    • COUNTDOWN_LABEL_PIXEL_OFFSET
    • SCALE_DIMENSION_UPDATE_DELAY_MS
    • SCALE_VISIBILITY_UPDATE_DELAY_MS
    • HIDDEN_SCALE_BAR_SPACING_ADJUSTMENT
    ƒ getIntervalDurationSeconds(interval) *Calculates interval duration in seconds*
    ƒ getPrecision(price) *Determines price precision based on magnitude*
    ƒ formatTimeToBarClose(ms, interval) *Formats time remaining until the next bar closes*
    ƒ formatChartPrice(price) *Formats price with dynamic precision*
    ƒ createGlobalPriceFormatter *Creates a global price formatter function*
    ƒ calculateTimeToBarCloseFromCandle(candleTime, interval, closeTime?) *Calculates time until next bar closes based on candle data and inte...*
    ƒ safeRemovePriceLine(series, line) *Safely removes a price line from a series*
    • ChartContext
    ƒ ChartContextProvider({ children, chartId })
    ƒ useChartContext
    ƒ useChartCore(chartContainerRef, settings) *Hook to manage chart initialization, options, and cleanup*
    ƒ useCountdown(lastCandleRef, interval, isReady, setTimeToBarClose) *Hook to manage the countdown timer*
    ƒ useScaleInteraction(chartRef, isReady, autoHideScalesEnabled, baseBarSpacing) *Hook to manage scale visibility, dimensions, and interaction*
    • EyeIconSvg *--- UI Components ---*
    • ToggleScalesVisibilityButton
    • ChartContent *--- Main Chart Component Logic ---*
    • SimpleChart *--- Entry Point Component ---*
#### 📄 ChartDisplay.tsx  # 143 LoC | 5775 chars
    • MemoizedSimpleChart
    • DEFAULT_TIMEFRAME_CONST
    ƒ ChartDisplayManager({ isScreenerMode, screenerTickers, screenerTimeframe, screenerCurrentPage, selectedTickerSymbol, fullscreenChartIndex, chartIndices, chartIntervals, getMarketTypeForSymbol, intervalHandlers, fullscreenHandlers, emptyChartMessage, totalCharts, })
#### 📄 useChartData.ts  # 377 LoC | 16740 chars
    • KLINE_UPDATE_DEBOUNCE_MS
    ƒ useChartData({ chartRef, marketType, symbol, interval, seriesRef, isReady, // uniqueChartId, // Not directly used in this hook's logic after refactor, consider removing if not needed for logging/debugging onDataUpdate, onSubscriptionError, }) *Custom hook to manage data loading (historical + WebSocket) for a c...*
#### 📄 useChartIndicators.ts  # 624 LoC | 34004 chars
    • EMPTY_INSTANCES
    • MIN_MAIN_PANE_HEIGHT
    • TARGET_MAIN_PANE_HEIGHT
    • typeToSeriesMap
    ƒ isValidLineWidth(width)
    ƒ useChartIndicatorsIntegration(chartRef, chartId, klineData, symbol, interval, paneOptions?) *Hook to manage the integration of technical indicators onto a Light...*
    ƒ hexOrRgbToRgba(color, alpha) *Helper function to convert hex/rgb to rgba with specific alpha*

## ✨ Feature: `ControlPanel`
### 📁 `src/features/ControlPanel/`  # 140 LoC | 5199 chars
#### 📄 ControlPanel.tsx  # 140 LoC | 5199 chars
*Dependencies:*
  - `../GridSelector/Grid`
  - `../StatusBar/StatusBar`
  - `../Indicators/Indicators`
  - `../SettingButton/Setting`
  - `../TickerSearch/TickerSearch`
  - `../TickerManagement/Table`
  - `../ModeControl`
    • ControlPanelComponentInternal *--- Component ---*
    • ControlPanel *--- Export memoized component ---*

## ✨ Feature: `GridSelector`
### 📁 `src/features/GridSelector/`  # 213 LoC | 9390 chars
#### 📄 Grid.tsx  # 213 LoC | 9390 chars
    ƒ LayoutSelector({ currentLayout, onLayoutChange }) *Компонент для выбора типа компоновки графиков.*

## ✨ Feature: `Indicators`
### 📁 `src/features/Indicators/`  # 605 LoC | 27485 chars
#### 📄 Indicators.tsx  # 523 LoC | 24612 chars
*Dependencies:*
  - `./registry`
    ƒ getLocalDefaultIndicatorParams(indicator) *Локальная функция для получения параметров индикатора*
    • lineStyleOptions *Mapping LineStyle enum to display names remains the same*
    • DIALOG_DEBOUNCE_MS
    ƒ IndicatorSettingsDialog({ indicatorInstance, open, onOpenChange, })
    • CombinedIndicatorItem
    ƒ selectGlobalIndicators(state)
    ƒ selectChartIndicators(chartId)
    ƒ IndicatorsList({ chartId, onOpenSettings })
    • panelVariants
    ƒ IndicatorsPanel({ chartId, isOpen, setIsOpen, onOpenSettings, initialPosition, })
    ƒ IndicatorsPanelTrigger({ chartId })
#### 📄 registry.ts  # 32 LoC | 1130 chars
*src/indicators/registry.ts*
*Dependencies:*
  - `./definitions/sma`
  - `./definitions/rsi`
    • indicatorRegistry *Централизованный реестр всех доступных индикаторов в системе.*
    ƒ getAvailableIndicators *Получает список всех определений индикаторов из реестра.*
    ƒ findIndicatorDefinitionById(indicatorId) *Находит определение индикатора по его ID.*
#### 📄 sources.ts  # 50 LoC | 1743 chars
    • INDICATOR_SOURCES *Standard data sources available for indicator calculations.*
    ƒ getIndicatorSourceValue(dataPoint, source) *Calculates the value for a given data point based on the selected s...*

## ✨ Feature: `ModeControl`
### 📁 `src/features/ModeControl/`  # 502 LoC | 20998 chars
#### 📄 index.ts  # 2 LoC | 103 chars
#### 📄 ModeControl.tsx  # 354 LoC | 15595 chars
*Dependencies:*
  - `./useScreenerTickers`
    • ScreenerSortOptions
    ƒ ModeControl({ className })
#### 📄 useScreenerTickers.ts  # 146 LoC | 5300 chars
    ƒ useScreenerTickers *Хук для получения топ тикеров для скринера*

## ✨ Feature: `StatusBar`
### 📁 `src/features/StatusBar/`  # 214 LoC | 10923 chars
#### 📄 StatusBar.tsx  # 214 LoC | 10923 chars
*Dependencies:*
  - `../../shared/ui/button`
  - `../../shared/ui/tooltip`
  - `../../shared/ui/popover`
    • statusStyles *Map connection status to Tailwind background and shadow colors*
    • statusText *Map connection status to human-readable text*
    ƒ StatusBarComponent({ loading, error, lastUpdate, onRefresh, connectionDetails = [], totalTickers = 0, filteredTickers = 0, })
    • StatusBar

## ✨ Feature: `TickerManagement`
### 📁 `src/features/TickerManagement/`  # 1228 LoC | 51380 chars
#### 📄 index.ts  # 3 LoC | 162 chars
#### 📄 Table.tsx  # 854 LoC | 34125 chars
*Dependencies:*
  - `./useTableUIstate`
  - `./useTableData`
    • DND_ITEM_TYPE *--- Constants ---*
    • BASE_BODY_ROW_HEIGHT
    • MIN_BODY_ROW_HEIGHT
    • BASE_BODY_CELL_PADDING_X
    • MIN_BODY_CELL_PADDING_X
    • BASE_HEADER_CELL_PADDING_X
    • MIN_HEADER_CELL_PADDING_X
    • SKELETON_ROW_COUNT
    ƒ formatNumber(value, fractionDigits) *--- Helper Functions ---*
    ƒ formatPercent(value)
    ƒ formatAbbreviatedInteger(value)
    ƒ formatDynamicPrecisionPrice(value)
    ƒ renderCellContent(cell, selectedPairs, indicatorColumns, indicatorDataMap, indicatorQueryStatusMap) *--- Table Row Cell Rendering ---*
    • TableRowComponent
    ƒ TableHeaderCellComponent({ header, columnOrder, reorderColumn })
    • TableHeaderCell
    • TickerTable
#### 📄 useTableData.ts  # 286 LoC | 13056 chars
    • MIN_ACTIVITY_INTERVAL_MS *--- Constants for filtering ---*
    ƒ filterTickers(tickers, selectedMarketTypes, selectedPairs, availablePairs, minVolume, minTrades, showVolumeInUSD, searchQuery?) *Filters an array of tickers based on various criteria like market t...*
    ƒ aggregateTickers(tickers, spotTickers, futuresTickers) *Aggregates volume and trade counts for tickers that exist in both s...*
    ƒ useProcessedTableData({ searchQuery, aggregateVolumeAndTrades, selectedPairs: propsSelectedPairs }) *Custom hook to process and filter ticker data for the table.*
    ƒ useIndicatorDataForTable *Custom hook to prepare indicator data for the table.*
#### 📄 useTableUIstate.ts  # 85 LoC | 4037 chars
*Hooks specific to the TickerManagement feature will go here.*
    ƒ useTableStateConfig *Custom hook to manage and retrieve table state configuration from Z...*

## ✨ Feature: `TickerSearch`
### 📁 `src/features/TickerSearch/`  # 101 LoC | 3813 chars
#### 📄 TickerSearch.tsx  # 101 LoC | 3813 chars
    ƒ TickerSearch({ initialValue = '', onSearchChange, placeholder = "", className, debounceMs = 300, selectedSymbol, isFiltering, ...props })

## ✨ Feature: `core`
### 📁 `src/app/`  # 483 LoC | 20962 chars
#### 📄 layout.tsx  # 31 LoC | 1079 chars
*Dependencies:*
  - `./providers`
  - `./globals.css`
    • metadata *import "@/styles/global.scss";  Оставляем закомментированным*
    ƒ RootLayout({ children, })
#### 📄 page.tsx  # 406 LoC | 18034 chars
    • MemoizedSimpleChart *Memoize the SimpleChart component*
    • DEFAULT_TIMEFRAME *--- Constants ---*
    • DEFAULT_TICKER
    • DEFAULT_MARKET_TYPE
    • AUTO_UPDATE_INTERVAL
    • FOCUS_AUTO_RECONNECT
    ƒ HomePageContent
    ƒ HomePage
#### 📄 providers.tsx  # 46 LoC | 1849 chars
    ƒ makeQueryClient *Optional: If you want React Query DevTools*
    • browserQueryClient
    ƒ getQueryClient
    ƒ Providers({ children })
### 📁 `src/app/test/`  # 806 LoC | 27850 chars
#### 📄 page.tsx  # 806 LoC | 27850 chars
    ƒ getWsStatus *Убедимся, что wsClient существует, прежде чем использовать его*
    ƒ connectWs
    ƒ disconnectWs
    • apiTests *Определение API тестов*
    ƒ getStatusIcon(status) *Helper functions*
    ƒ getStatusColor(status)
    ƒ TestConsoleHeader({ successCount, errorCount, totalTests, completedTests, wsConnectionStatus, wsLastError, wsConnectionAttempts, wsStats, copiedStates, isRunning, progress, onCopyAllResults, onRunAllTests, onResetWsConnection, onResetStores, onExportState, })
    ƒ TestList({ tests, selectedTestId, isRunning, onRunTest, onCopyTest, onSelectTest, copiedStates })
    ƒ TestDetailsPanel({ selectedTest, onCopyText, copiedStates })
    ƒ useApiTestConsole
    ƒ TestConsole
### 📁 `src/db/`  # 73 LoC | 3638 chars
#### 📄 index.ts  # 10 LoC | 413 chars
*Dependencies:*
  - `./schema`
    • client *Create a connection to the database*
    • db *Create the Drizzle instance without passing the schema*
#### 📄 migrate.ts  # 19 LoC | 696 chars
    ƒ async main
#### 📄 schema.ts  # 44 LoC | 2529 chars
*This file will be populated by drizzle-kit introspect*
    • candles
    • tickers24h
### 📁 `src/server/`  # 89 LoC | 3102 chars
#### 📄 main.ts  # 89 LoC | 3102 chars
*Dependencies:*
  - `./api/v1/klineRoutes.js`
  - `./api/v1/tickerRoutes.js`
  - `./api/v1/websocketRoutes.js`
  - `./lib/redis.js`
  - `./services/exchangeDataCollectorService.js`
  - `./services/dataCollector.js`
    • fastify *Initialize Fastify app*
    ƒ async start *Function to start the server*
    ƒ async shutdown(signal) *Handle graceful shutdown*
### 📁 `src/server/adapters/`  # 244 LoC | 8986 chars
#### 📄 binance.adapter.ts  # 133 LoC | 4911 chars
*Dependencies:*
  - `./exchange.adapter`
    ■ BinanceAdapter *Adapter for Binance exchange data*
      • exchangeName
      ▸ adaptTicker(data, marketType) *Convert Binance ticker data to core ticker format*
      ▸ adaptKline(data, marketType, interval) *Convert Binance kline data to core kline format*
      ▸ adaptSymbol(data, marketType) *Convert Binance symbol data to core symbol format*
    • binanceAdapter *Export singleton instance*
#### 📄 exchange.adapter.ts  # 111 LoC | 4075 chars
    ■ BaseExchangeAdapter *Abstract base class for exchange adapters*
      • exchangeName
      ▸ adaptTicker(data, marketType) *Convert a single ticker from exchange format to core format*
      ▸ adaptKline(data, marketType, interval) *Convert a single kline from exchange format to core format*
      ▸ adaptSymbol(data, marketType) *Convert a single symbol from exchange format to core format*
      ▸ adaptTickers(data, marketType) *Convert an array of tickers from exchange format to core format*
      ▸ adaptKlines(data, marketType, interval) *Convert an array of klines from exchange format to core format*
      ▸ adaptSymbols(data, marketType) *Convert an array of symbols from exchange format to core format*
      ▸ safeParseFloat(value) *Safely parse a string to number, returning 0 if invalid*
      ▸ toNumber(value, defaultValue) *Safely convert a value to number, with a default fallback*
### 📁 `src/server/api/v1/`  # 188 LoC | 8154 chars
#### 📄 klineRoutes.ts  # 68 LoC | 2745 chars
    • GetKlinesSchema *Zod schema for kline query parameters validation*
    • GetKlinesJsonSchema *Convert Zod schema to JSON schema for Fastify*
    ƒ async klineRoutes(fastify) *@description Registers the kline routes.
@param {FastifyInstance} f...*
#### 📄 tickerRoutes.ts  # 105 LoC | 4654 chars
    • GetTickersSchema *Zod schema for ticker query parameters validation*
    • GetTickersJsonSchema *Convert Zod schema to JSON schema for Fastify*
    ƒ async tickerRoutes(fastify) *@description Registers the ticker routes.
@param {FastifyInstance} ...*
#### 📄 websocketRoutes.ts  # 15 LoC | 755 chars
*Dependencies:*
  - `../../services/webSocketService.js`
    ƒ async websocketRoutes(fastify) *WebSocket маршруты*
### 📁 `src/server/lib/`  # 140 LoC | 4743 chars
#### 📄 redis.ts  # 140 LoC | 4743 chars
    • logger
    • REDIS_HOST
    • REDIS_PORT
    • REDIS_PASSWORD
    • REDIS_DB
    • redisConfig
    • redisClient *Main Redis client for commands (GET, SET, etc.)*
    • redisPubSub *Separate client for subscription (blocking)*
    ƒ async connectRedis *Connect to Redis and setup event handlers*
    ƒ async getFromCache(key, ttl) *Cache helper functions*
    ƒ async setInCache(key, value, ttlSeconds)
    ƒ async publishMessage(channel, message) *PubSub pattern functions*
    ƒ subscribeToChannel(channel, callback) *Function to subscribe to a channel and register a callback*
    ƒ unsubscribeFromChannel(channel) *Function to unsubscribe from a channel*
    ƒ async closeRedisConnections *Close Redis clients*
### 📁 `src/server/services/`  # 2254 LoC | 87683 chars
#### 📄 binance.service.ts  # 398 LoC | 16526 chars
*Dependencies:*
  - `./exchange.interface`
  - `../adapters/binance.adapter`
    ■ BinanceService *Implements the ExchangeService interface for Binance.*
      • exchangeName
      • instance
      • wsConnections
      • connecting
      • reconnectTimers
      • reconnectAttempts
      • activeSubscriptions *Tracks active subscriptions by stream name and their reference count*
      ▸ constructor
      ▸ getInstance *Gets the singleton instance of the BinanceService.*
      ▸ async initialize *Initializes WebSocket connections to the exchange.*
      ▸ async shutdown *Closes all connections and cleans up resources.*
      ▸ async fetchSymbols(marketType) *--- REST API Methods ---*
      ▸ async fetchTickers(marketType)
      ▸ async fetchKlines(marketType, symbol, interval, options)
      ▸ subscribeTickers(marketType) *--- WebSocket Subscription Methods ---*
      ▸ subscribeKline(marketType, symbol, interval)
      ▸ async fetchAPI(url) *Generic fetch wrapper for Binance REST API.*
      ▸ manageSubscription(marketType, streamName) *Manages the logic for subscribing and returning an unsubscribe func...*
      ▸ subscribeToStream(marketType, streamName) *--- Internal WebSocket Connection and Subscription Management ---*
      ▸ unsubscribeFromStream(marketType, streamName)
      ▸ sendSubscriptionMessage(marketType, streams, method)
      ▸ connect(marketType)
      ▸ disconnect(marketType)
      ▸ handleMessage(marketType, data)
      ▸ processKlineUpdate(marketType, data)
      ▸ processTickerUpdate(marketType, data)
      ▸ handleClose(marketType, code, reason)
      ▸ handleError(marketType, error)
      ▸ scheduleReconnect(marketType)
      ▸ resubscribe(marketType)
    • logger *Local logger for this service*
    • API_CONFIG *Binance API configuration*
    • SUPPORTED_INTERVALS *Supported intervals on Binance for validation*
    • WebSocketState *WebSocket connection states*
    • binanceService
#### 📄 dataCollector.ts  # 233 LoC | 9279 chars
*Dependencies:*
  - `./binance.service.js`
  - `./tickerService.js`
  - `./exchangeDataCollectorService.js`
    • logger *Configure logger*
    • UPDATE_INTERVALS *Intervals for data updates*
    • symbolsUpdateInterval
    • tickersUpdateInterval
    • klineSubscriptions
    • tickerSubscriptions
    • WATCHED_SYMBOLS *Symbols we want to subscribe to for real-time updates*
    • WATCHED_INTERVALS *Intervals we want to track*
    ƒ convertCoreTickerToTicker(coreTicker) *Convert CoreTicker to Ticker format expected by saveTickers*
    ƒ async initializeDataCollection *Initialize data collection from exchanges*
    ƒ async fetchInitialData *Fetch initial data from exchanges*
    ƒ setupPeriodicUpdates *Set up periodic updates for all data*
    ƒ async fetchAllSymbols *Fetch all symbols from exchanges*
    ƒ async fetchAllTickers *Fetch tickers for all markets*
    ƒ async subscribeToRealTimeData *Subscribe to real-time data updates*
    ƒ async subscribeToKlines *Subscribe to kline/candlestick updates for watched symbols using ba...*
    ƒ unsubscribeAllKlines *Unsubscribe from all kline updates*
    ƒ async shutdownDataCollection *Shutdown data collection*
#### 📄 exchange.interface.ts  # 80 LoC | 1865 chars
#### 📄 exchangeDataCollectorService.ts  # 621 LoC | 22979 chars
*Dependencies:*
  - `../lib/redis.js`
  - `./webSocketService.js`
  - `./tickerService.js`
    ■ ExchangeDataCollectorService *Service for collecting data from cryptocurrency exchanges using Fas...*
      • instance
      • wsConnections
      • connecting
      • reconnectTimers
      • reconnectAttempts
      • activeSubscriptions *Track active subscriptions*
      ▸ constructor
      ▸ getInstance *Get the singleton instance of ExchangeDataCollectorService*
      ▸ async initialize *Initialize the service*
      ▸ async shutdown *Shutdown the service*
      ▸ connect(marketType) *Connect to exchange WebSocket for the specified market type*
      ▸ disconnect(marketType) *Disconnect from exchange WebSocket*
      ▸ handleOpen(marketType, connection) *Handle WebSocket open event*
      ▸ handleMessage(marketType, data) *Handle WebSocket message event*
      ▸ handleClose(marketType, code, reason) *Handle WebSocket close event*
      ▸ handleError(marketType, error) *Handle WebSocket error event*
      ▸ scheduleReconnect(marketType) *Schedule reconnection with exponential backoff*
      ▸ async subscribe(marketType, streamName) *Subscribe to a stream on exchange WebSocket*
      ▸ async subscribeBatch(marketType, streamNames) *Subscribe to multiple streams on exchange WebSocket in batches*
      ▸ unsubscribe(marketType, streamName) *Unsubscribe from a stream on exchange WebSocket*
      ▸ async resubscribe(marketType) *Resubscribe to all active streams after reconnection*
      ▸ async subscribeKline(marketType, symbol, interval) *Subscribe to kline updates for a symbol and interval*
      ▸ async subscribeKlinesBatch(marketType, symbolIntervalPairs) *Subscribe to kline updates for multiple symbols and intervals in batch*
      ▸ async processKlineUpdate(marketType, message) *Process kline update from WebSocket*
      ▸ async saveKlineToDB(kline) *Save a kline to the database.*
      ▸ publishKlineUpdate(kline) *Publish kline update to Redis*
      ▸ async processTickerUpdate(marketType, message) *Add a new method to process ticker updates*
      ▸ publishTickerUpdate(ticker) *Publish ticker update to Redis*
      ▸ async fetchAndSaveHistoricalKlines(marketType, symbol, interval, limit, startTime?, endTime?) *Fetch historical klines from the exchange API and save them to the ...*
    • logger *Configure logger*
    • SUPPORTED_INTERVALS *Supported intervals on Binance*
    • RATE_LIMITS *Rate limiting constants based on Binance documentation*
    • API_CONFIG
    • WebSocketState *Константы для состояния WebSocket (стандартные значения из специфик...*
    • exchangeDataCollector *Create a singleton instance*
#### 📄 klineService.ts  # 68 LoC | 2553 chars
    ƒ async getKlines(params) *Fetches historical Kline data for a given symbol, interval, and mar...*
#### 📄 tickerService.ts  # 505 LoC | 21490 chars
*Dependencies:*
  - `../lib/redis.js`
    ■ TickerService *Service for handling ticker data persistence*
      ▸ async getTickersByMarket(marketType) *Fetches all tickers for a specific market type from the database*
      ▸ async upsertTickers(tickers) *Upserts multiple tickers into the database*
      ▸ mapDbRowToTicker(dbRow) *Maps a database row to a Ticker object*
    • logger *Configure logger*
    ƒ getTickersCacheKey(params) *Cache key builder*
    • tickerService *Export a singleton instance of the service*
    ƒ async saveTicker(ticker) *Save a ticker to the database*
    ƒ async saveTickers(tickers) *Save multiple tickers to the database in a single batch*
    ƒ extractQuoteAsset(symbol) *Extract quote asset from symbol (e.g., BTCUSDT -> USDT)*
    ƒ async getTickers(params) *Get tickers with filtering, sorting and pagination*
    ƒ async processTickerUpdate(ticker) *Process ticker update event and save to database*
    ƒ async processTickersBatchUpdate(tickers) *Process batch ticker update event and save to database*
#### 📄 webSocketService.ts  # 349 LoC | 12991 chars
*Dependencies:*
  - `../lib/redis.js`
    ■ WebSocketManager *WebSocketManager - синглтон для управления WebSocket соединениями*
      • instance
      • clients
      • subscriptions
      • clientSubscriptions
      • channelSubscribers
      ▸ constructor
      ▸ getInstance *Получить экземпляр WebSocketManager (синглтон)*
      ▸ handleConnection(connection, request) *Обработать новое WebSocket соединение*
      ▸ handleMessage(clientId, message) *Обработать сообщение от клиента*
      ▸ handleSubscription(clientId, message) *Обработать подписку клиента*
      ▸ handleUnsubscription(clientId, message) *Обработать отписку клиента*
      ▸ unsubscribeClientFromChannel(clientId, channel) *Отписать клиента от канала*
      ▸ handleDisconnect(clientId) *Обработать отключение клиента*
      ▸ subscribeToRedisChannel(channel) *Подписаться на Redis-канал и настроить обработку сообщений*
      ▸ unsubscribeFromRedisChannel(channel) *Отписаться от Redis-канала*
      ▸ getChannelName(type, symbol, interval, marketType) *Создать имя канала на основе типа данных и параметров*
      ▸ sendMessageToClient(clientId, message) *Отправить сообщение клиенту*
      ▸ sendErrorToClient(clientId, errorMessage) *Отправить сообщение об ошибке клиенту*
      ▸ publishKlineUpdate(kline) *Публиковать обновление свечи*
    • logger *Создаем логгер*
    • SubscribeKlineMessageSchema *Define message schemas*
    • UnsubscribeKlineMessageSchema
    • MessageSchema
    ƒ getWebSocket(connection) *Helper functions to work with SocketStream union type*
### 📁 `src/shared/`  # 5 LoC | 122 chars
#### 📄 index.ts  # 5 LoC | 122 chars
### 📁 `src/shared/lib/`  # 388 LoC | 16472 chars
#### 📄 DevTools.tsx  # 10 LoC | 366 chars
    ƒ DevTools
#### 📄 ErrorBoundary.tsx  # 196 LoC | 8610 chars
    ■ ErrorBoundary
      • state
      ▸ getDerivedStateFromError(error)
      ▸ componentDidCatch(error, errorInfo)
      • handleRetry
      • handleReload
      • handleGoHome
      ▸ render
    • errorMonitoring *--- Placeholder for your actual error monitoring service integratio...*
    ƒ withErrorBoundary(Component, boundaryProps?) *HOC for wrapping components with error boundary*
    ƒ useErrorHandler *Hook for error reporting in functional components*
#### 📄 index.ts  # 4 LoC | 187 chars
#### 📄 utils.ts  # 167 LoC | 6800 chars
    ƒ cn(inputs) *Utility function to merge Tailwind CSS classes with clsx.*
    ƒ throttle(func, limit) *Throttles a function, ensuring it's called at most once per limit m...*
    ƒ debounce(func, wait) *Debounces a function, delaying its execution until after wait milli...*
    ƒ hexFromRgba(rgbaString)
    ƒ rgbaFromHex(hex, alpha)
    ƒ formatPrice(price) *Placeholder for other formatting functions, assuming they exist or ...*
    ƒ formatPercent(percent)
    ƒ formatVolume(volume)
    ƒ formatCount(count)
    ƒ formatSpread(spread)
    ƒ haveSameKeys(mapA, mapB) *--- Helper function to compare map keys (moved from page.tsx) ---*
    ƒ isError(value) *Type guard to check if a value is an Error object*
#### 📄 wdyr.ts  # 11 LoC | 509 chars
    • whyDidYouRender
### 📁 `src/shared/market/`  # 726 LoC | 25859 chars
#### 📄 hooks.ts  # 322 LoC | 12298 chars
*Dependencies:*
  - `../store/marketStore`
    • GetKlinesSchema *Zod schema for kline query parameters, should match backend*
    • API_BASE_URL
    • WS_BASE_URL
    ƒ parseKline(serializedKline) *@description Parses a serialized kline from the backend into the ap...*
    ƒ async fetchHistoricalKlines({ pageParam, ...params }) *@description Fetches historical klines from the backend API.*
    ƒ async fetchTickers(marketTypes?) *Функция для загрузки тикеров с бэкенда*
    ƒ getKlinesQueryKey(symbol, interval, marketType)
    ƒ useHistoricalKlinesQuery(symbol, interval, marketType) *@description A TanStack Query hook to fetch historical kline data w...*
    • wsClient *WebSocket client*
    ƒ useWebSocketConnection *Хук для работы с WebSocket соединением*
    ƒ useWebSocketAutoConnectWithUrl(url?) *Хук для автоматического подключения к WebSocket с URL*
    ƒ useMarketDataManager *Хук для управления рыночными данными*
#### 📄 index.ts  # 1 LoC | 24 chars
#### 📄 websocket.ts  # 403 LoC | 13537 chars
    ■ WebSocketManager *Singleton class to manage WebSocket connection and subscriptions*
      • instance
      • ws
      • state
      • reconnectAttempts
      • reconnectTimer
      • reconnectInterval
      • maxReconnectInterval
      • subscriptions
      • pendingMessages
      • autoReconnect
      • statusCallback *Status callback to notify UI of connection changes*
      ▸ constructor
      ▸ getInstance *Get the singleton instance*
      ▸ setStatusCallback(callback) *Set a callback to be notified about connection status changes*
      ▸ connect *Connect to the WebSocket server*
      ▸ disconnect *Disconnect from the WebSocket server*
      ▸ subscribeKline(symbol, interval, marketType, callback) *Subscribe to kline updates*
      ▸ unsubscribeKline(symbol, interval, marketType) *Unsubscribe from kline updates*
      ▸ sendOrQueueMessage(message) *Send a message or queue it if not connected*
      ▸ handleOpen *Handle WebSocket open event*
      ▸ handleMessage(event) *Handle WebSocket message event*
      ▸ handleClose(event) *Handle WebSocket close event*
      ▸ handleError(event) *Handle WebSocket error event*
      ▸ scheduleReconnect *Schedule a reconnection attempt with exponential backoff*
      ▸ clearReconnectTimer *Clear the reconnect timer*
      ▸ sendQueuedMessages *Send all queued messages*
      ▸ resubscribeAll *Resubscribe to all active subscriptions*
      ▸ updateState(newState) *Update the connection state and notify listeners*
      ▸ getKlineChannel(symbol, interval, marketType) *Generate a kline channel name from parameters*
      ▸ getState *Get the current connection state*
      ▸ getSubscriptionCount *Get the number of active subscriptions*
      ▸ isConnected *Check if the client is connected*
      ▸ enableAutoReconnect *Enable auto-reconnect behavior*
      ▸ disableAutoReconnect *Disable auto-reconnect behavior*
    • logger *Configure logger*
    • WS_BASE_URL *WebSocket URL configuration*
    • wsManager *Export singleton instance*
    ƒ useWebSocketAutoConnect *Hook to ensure WebSocket is connected when component is mounted*
### 📁 `src/shared/schemas/`  # 736 LoC | 27780 chars
#### 📄 index.ts  # 3 LoC | 105 chars
#### 📄 indicators.schema.ts  # 117 LoC | 5374 chars
*Dependencies:*
  - `./market.schema`
    • IndicatorParamValueSchema *-----------------------------------------------*
    • TimeFrameSchema *Schema for timeframe identifiers*
    • IndicatorParamSchema *Schema for describing an indicator's input parameter*
    • IndicatorStyleSchema *Schema for visual styling of indicator plots*
    • IndicatorOutputInfoSchema *Schema for describing a single output of an indicator (e.g., a line...*
    • IndicatorParamsSchema *Schema for a generic map of indicator parameters*
    • CalculatedIndicatorResultSchema *Schema for the result of an indicator calculation for a single time...*
    • IndicatorInstanceSchema *Schema for an instance of an indicator on a chart*
    • IndicatorDefinitionSchema *Schema for the full definition of an indicator*
    • CalculatedIndicatorWithMetadataSchema *Schema for calculated indicator data bundled with its metadata*
#### 📄 market.schema.ts  # 304 LoC | 10465 chars
    • MarketTypeSchema
    • KlineIntervalSchema *Schema for kline/candlestick time intervals.*
    • ExchangeNameSchema *Schema for supported exchange identifiers.*
    • WSConnectionStatusSchema
    • ViewModeSchema *Schema for UI View Modes, such as focused view or market screener.*
    • ExchangeTickerDataSchema *Schema for raw ticker data from any exchange.*
    • ExchangeKlineDataSchema *Schema for raw kline/candlestick data from any exchange.*
    • ExchangeSymbolDataSchema *Schema for raw symbol data from any exchange.*
    • CoreTickerSchema *Schema for core ticker data, representing a standardized ticker obj...*
    • CoreKlineSchema *Schema for core kline/candlestick data, standardized for internal use.*
    • CoreSymbolSchema *Schema for core symbol information, detailing asset properties.*
    • KlineDbSchema *Schema for candlestick/kline data from the database.*
    • KlineSchema *Schema for the primary kline/candlestick data representation used i...*
    • KlineParamsSchema *Schema for parameters used when fetching kline data.*
    • TickerSchema *Schema for full 24-hour ticker statistics.*
    • SimpleTickerSchema *Schema for a simplified ticker object, often used in high-level UI ...*
    • ProcessedTickerSchema *Schema for a processed ticker with additional calculated fields, li...*
    • FullTickerSchema *Schema for full ticker data with all possible fields from exchanges*
    • AppSymbolInfoSchema *Schema for detailed symbol information used within the application.*
    • KlineDataSchema *Schema for kline data specifically formatted for use in technical i...*
    • TradingViewSymbolSchema *Schema for TradingView-compatible symbol information.*
    • CHART_EVENT_NAMESPACE *-----------------------------------------------*
    • ChartEventTypeSchema
#### 📄 settings.schema.ts  # 312 LoC | 11836 chars
*Dependencies:*
  - `./market.schema`
    • TimeframeOptionSchema *Schema for a single timeframe option used in UI selectors.*
    • DEFAULT_TIMEFRAMES *Default list of timeframes for UI components.*
    • SCREENER_CHART_COUNTS *Predefined chart count options for the screener view.*
    • ScreenerChartCountSchema
    • SCREENER_UPDATE_INTERVALS *Predefined update intervals for the screener's auto-update feature.*
    • ChartTypeSchema
    • LayoutTypeSchema *Zod schema for LayoutType.*
    • ScreenerSortBySchema *Schema for Screener Sort By options.*
    • DEFAULT_TABLE_COLUMNS *Default table columns configuration - single source of truth*
    • VisibleColumnsSchema *Schema for visible columns in tables.*
    • SortConfigSchema *Schema for sorting configuration.*
    • DEFAULT_COLUMN_WIDTHS *Default column widths configuration*
    • ColumnWidthsSchema *Schema for column widths in tables.*
    • ColumnFiltersSchema *Schema for column filters in tables.*
    • IndicatorColumnConfigSchema *Schema for a single indicator column configuration.*
    • ScreenerSettingsSchema *Schema for Screener settings.*
    • ModeControlSettingsSchema *Schema for Mode Control settings, managing different UI modes.*
    • AppSettingsSchema *-----------------------------------------------*
### 📁 `src/shared/store/`  # 915 LoC | 46055 chars
#### 📄 index.ts  # 8 LoC | 260 chars
#### 📄 indicatorStore.ts  # 342 LoC | 16256 chars
    ƒ getDefaultIndicatorParams(indicator) *Helper function to get default parameters and styles separately*
    • initialState *Define the initial state with explicit types*
    • LS_INDICATOR_SETTINGS_KEY *--- Persist Configuration ---*
    • useIndicatorStore
#### 📄 marketStore.ts  # 171 LoC | 7049 chars
    • DEFAULT_MARKET_TYPE *Default market type*
    ƒ getKlinesCacheKey(symbol, interval, marketType) *Helper function to create cache key for klines*
    • useMarketStore *Initialize the market store*
#### 📄 settingsStore.ts  # 276 LoC | 16357 chars
    • LS_SETTINGS_KEY
    • defaultSettings *Derive default settings from the Zod schema to ensure consistency.*
    ƒ createAppSettingsActions(set, get)
    • useAppSettingsStore
    ƒ selectIsDarkMode(state) *=== SELECTORS ===*
    ƒ selectIsFiltersShown(state)
    ƒ selectIsGridShown(state)
    ƒ selectIsDebugMode(state)
    ƒ selectChartType(state)
    ƒ selectChartBackgroundColor(state)
    ƒ selectChartLayoutLineColor(state)
    ƒ selectChartTextColor(state)
    ƒ selectChartFontFamily(state)
    ƒ selectChartFontSize(state)
    ƒ selectChartBorderColor(state)
    ƒ selectChartGridVertLinesVisible(state)
    ƒ selectChartGridHorzLinesVisible(state)
    ƒ selectChartGridVertLinesColor(state)
    ƒ selectChartGridHorzLinesColor(state)
    ƒ selectChartCandleBodyEnabled(state)
    ƒ selectChartCandleBorderEnabled(state)
    ƒ selectChartCandleWickEnabled(state)
    ƒ selectChartCandleBodyUpColor(state)
    ƒ selectChartCandleBodyDownColor(state)
    ƒ selectChartCandleBorderUpColor(state)
    ƒ selectChartCandleBorderDownColor(state)
    ƒ selectChartCandleWickUpColor(state)
    ƒ selectChartCandleWickDownColor(state)
    ƒ selectChartLineColor(state)
    ƒ selectChartLineWidth(state)
    ƒ selectChartAreaLineColor(state)
    ƒ selectChartAreaTopColor(state)
    ƒ selectChartAreaBottomColor(state)
    ƒ selectChartAreaLineWidth(state)
    ƒ selectChartCrosshairColor(state)
    ƒ selectChartCrosshairLabelBackgroundColor(state)
    ƒ selectChartCrosshairLabelTextColor(state)
    ƒ selectChartTimeScaleBorderColor(state)
    ƒ selectChartTimeScaleTextColor(state)
    ƒ selectChartPriceScaleBorderColor(state)
    ƒ selectChartPriceScaleTextColor(state)
    ƒ selectChartPriceLineVisible(state)
    ƒ selectChartLastValueVisible(state)
    ƒ selectChartPriceLineWidth(state)
    ƒ selectChartPriceLineStyle(state)
    ƒ selectChartPriceLineColor(state)
    ƒ selectVolumeEnabled(state)
    ƒ selectChartVolumeHeightRatio(state)
    ƒ selectChartVolumeUpColor(state)
    ƒ selectChartVolumeDownColor(state)
    ƒ selectChartRightOffset(state)
    ƒ selectChartBarSpacing(state)
    ƒ selectSelectedPairs(state)
    ƒ selectMarketTypes(state)
    ƒ selectMinVolume(state)
    ƒ selectMinTrades(state)
    ƒ selectShowVolumeInUSD(state)
    ƒ selectAggregateVolumeAndTrades(state)
    ƒ selectTableCompactness(state)
    ƒ selectIsSyncEnabled(state)
    ƒ selectUiFontFamily(state)
    ƒ selectUiFontSize(state)
    ƒ selectSortConfigs(state)
    ƒ selectVisibleColumns(state)
    ƒ selectColumnWidths(state)
    ƒ selectColumnOrder(state)
    ƒ selectColumnFilters(state)
    ƒ selectIsTableCollapsed(state)
    ƒ selectSelectedTickerSymbol(state)
    ƒ selectGlobalSearchTerm(state)
    ƒ selectChartAppearance(state)
    ƒ selectSetSetting(state)
    ƒ selectToggleColumnVisibility(state)
    ƒ selectUpdateSortConfigs(state)
    ƒ selectSetColumnOrder(state)
    ƒ selectSetColumnWidth(state)
    ƒ selectSetColumnFilter(state)
    ƒ selectSetTableCollapsed(state)
    ƒ selectResetSettings(state)
    ƒ selectSetGlobalSearchTerm(state)
    ƒ selectAutoHideScalesEnabled(state)
    ƒ selectToggleAutoHideScales(state)
    ƒ selectSetColumnWidths(state)
    ƒ selectSelectedInterval(state)
    ƒ selectAvailablePairs(state)
    ƒ selectIndicatorColumns(state)
    ƒ selectAddIndicatorColumn(state)
    ƒ selectRemoveIndicatorColumn(state)
    ƒ selectUpdateIndicatorColumn(state)
    ƒ selectModeControl(state)
    ƒ selectViewMode(state)
    ƒ selectScreenerSettings(state)
    ƒ selectSetViewMode(state)
    ƒ selectUpdateScreenerSettings(state)
    ƒ selectLayoutTypesForModes(state)
    ƒ selectLayoutTypeForCurrentMode(state)
    ƒ selectSetLayoutTypeForMode(state)
#### 📄 StoreInitializer.tsx  # 118 LoC | 6133 chars
*Dependencies:*
  - `./marketStore`
  - `./settingsStore`
  - `./indicatorStore`
    ƒ StoreInitializer({ children })
    ƒ resetAllStores *Development helper to reset all stores and clear relevant localStorage*
    ƒ exportStoreState *Development helper to export current store state*
### 📁 `src/shared/types/`  # 87 LoC | 2196 chars
#### 📄 index.ts  # 87 LoC | 2196 chars
*Shared type definitions for MetaCharts application*
*Dependencies:*
  - `../schemas/market.schema`