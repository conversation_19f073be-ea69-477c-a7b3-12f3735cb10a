import { LineStyle, LineWidth } from "lightweight-charts";
import { SortingState } from "@tanstack/react-table";

export enum ChartType {
  Candles = "Candlestick",
  Line = "Line",
  Area = "Area",
  Bars = "Bars",
}

/**
 * Settings related to the appearance of the chart.
 */
export interface ChartAppearanceSettings {
  type: ChartType;
  volumeEnabled?: boolean;
  candles: {
    bodyVisible: boolean;
    borderVisible: boolean;
    wickVisible: boolean;
    upColor?: string;
    downColor?: string;
    borderUpColor?: string;
    borderDownColor?: string;
    wickUpColor?: string;
    wickDownColor?: string;
  };
  grid: {
    vertLinesVisible: boolean;
    horzLinesVisible: boolean;
    vertLinesColor?: string;
    horzLinesColor?: string;
  };
  layout: {
    fontFamily?: string;
    fontSize?: number;
    textColor?: string;
    backgroundColor?: string;
    lineColor?: string;
    crosshairColor?: string;
    crosshairLabelBackgroundColor?: string;
    crosshairLabelTextColor?: string;
  };
  volume?: {
    heightRatio?: number;
    upColor?: string;
    downColor?: string;
  };
  spacing?: {
    rightOffset?: number;
    barSpacing?: number;
  };
  priceLine?: {
    visible?: boolean;
    lastValueVisible?: boolean;
    width?: LineWidth;
    style?: LineStyle;
    color?: string;
  };
  line?: {
    color?: string;
    width?: LineWidth;
  };
  area?: {
    lineColor?: string;
    topColor?: string;
    bottomColor?: string;
    lineWidth?: LineWidth;
  };
  countdownLabelBackgroundColor?: string;
  countdownLabelTextColor?: string;
  lastPriceLabelBackgroundColor?: string;
  lastPriceLabelTextColor?: string;
  timeScaleBorderColor?: string;
  timeScaleTextColor?: string;
  priceScaleBorderColor?: string;
  priceScaleTextColor?: string;
  autoHideScalesEnabled?: boolean;
}

// Moved from common.types.ts
export type MarketType = "spot" | "futures";

// Moved from common.types.ts
export interface GenericErrorMessage {
  error: string;
  details?: unknown;
}

// Moved from common.types.ts
export interface TimeframeOption {
  value: string;
  label: string;
}

// Definition for a single indicator column configuration
export interface IndicatorColumnConfig {
  instanceId: string; // Unique ID of the indicator instance
  indicatorId: string; // Original ID of the indicator definition (e.g., "SMA", "RSI")
  name: string; // Display name for the column (e.g., "SMA(14)")
  outputId: string; // Which output of the indicator to display (e.g., "smaValue")
  timeframe: string; // Timeframe for this column's indicator calculation
  parameters: Record<string, string | number | boolean>; // Added from schema usage
  columnLabel?: string; // Added from schema usage
  // Optional: style overrides specifically for this column's display
  // style?: Partial<IndicatorStyle>; // Assuming IndicatorStyle is defined elsewhere
}

// Used in appSettingsStore for mode control
export type ViewMode = "focus" | "screener"; // Add more modes as needed

export interface ScreenerSettings {
  sortBy: string;
  sortOrder: "asc" | "desc";
  timeframe: string;
  autoUpdate: boolean;
  updateInterval: number; // in seconds
  markets: string[]; // e.g., ['spot', 'futures']
  pairs: string[]; // e.g., ['USDT', 'BTC']
  minVolume: number;
  minTrades: number;
  // Potentially add filters here, e.g., { price_gt: 100, rsi_lt: 30 }
  // filters?: Record<string, any>;
  currentPage: number;
}

export interface ModeControlSettings {
  currentMode: ViewMode;
  screenerSettings: ScreenerSettings;
  layoutTypes: {
    // Defines the layout for each mode
    focus: LayoutType;
    screener: LayoutType;
  };
}

// Layout type definition (example, adjust as needed)
export type LayoutType =
  | "1x1"
  | "1x2"
  | "1x3"
  | "1x4"
  | "1x5"
  | "1x6"
  | "1x7"
  | "2x1"
  | "2x2"
  | "2x3"
  | "2x4"
  | "2x5"
  | "2x6"
  | "2x7"
  | "3x1"
  | "3x2"
  | "3x3"
  | "3x4"
  | "3x5"
  | "3x6"
  | "3x7"
  | "4x1"
  | "4x2"
  | "4x3"
  | "4x4"
  | "4x5"
  | "4x6"
  | "4x7"
  | "5x1"
  | "5x2"
  | "5x3"
  | "5x4"
  | "5x5"
  | "5x6"
  | "5x7"
  | "6x1"
  | "6x2"
  | "6x3"
  | "6x4"
  | "6x5"
  | "6x6"
  | "6x7"
  | "7x1"
  | "7x2"
  | "7x3"
  | "7x4"
  | "7x5"
  | "7x6"
  | "7x7";

export interface VisibleColumns {
  [key: string]: boolean; // Allows for dynamic keys from indicators etc.
  symbol: boolean;
  price: boolean;
  change: boolean;
  volume: boolean;
  trade: boolean;
  spread: boolean; // Made non-optional
}

export interface ColumnWidths {
  [key: string]: number; // Allows for dynamic keys
  symbol: number;
  price: number;
  change: number;
  volume: number;
  trade: number;
  spread: number; // Made non-optional
}

/**
 * Main application settings structure.
 * Based on defaultSettings from appSettingsStore.ts
 */
export interface AppSettings {
  isFiltersShown: boolean;
  isGridShown: boolean;
  isDebugMode: boolean;
  isDarkMode: boolean;

  chartType: ChartType;
  chartFontFamily: string;
  chartFontSize: number;

  chartTextColor: string;
  chartBackgroundColor: string;
  chartLayoutLineColor: string;
  chartBorderColor: string;

  chartScaleLabelBackgroundColor: string;
  chartScaleLabelTextColor: string;

  chartGridVertLinesVisible: boolean;
  chartGridHorzLinesVisible: boolean;
  chartGridVertLinesColor: string;
  chartGridHorzLinesColor: string;

  chartCrosshairColor: string;
  chartCrosshairLabelBackgroundColor: string;
  chartCrosshairLabelTextColor: string;

  chartPriceLineVisible: boolean;
  chartLastValueVisible: boolean;
  chartPriceLineWidth: LineWidth; // Corrected to use LineWidth
  chartPriceLineStyle: LineStyle; // Corrected to use LineStyle
  chartPriceLineColor: string;

  chartLineColor: string;
  chartLineWidth: LineWidth; // Corrected

  chartAreaLineColor: string;
  chartAreaTopColor: string;
  chartAreaBottomColor: string;
  chartAreaLineWidth: LineWidth; // Corrected

  chartCandleBodyEnabled: boolean;
  chartCandleBodyUpColor: string;
  chartCandleBodyDownColor: string;

  chartCandleBorderEnabled: boolean;
  chartCandleBorderUpColor: string;
  chartCandleBorderDownColor: string;

  chartCandleWickEnabled: boolean;
  chartCandleWickUpColor: string;
  chartCandleWickDownColor: string;

  chartTimeScaleBorderColor: string; // Keep for compatibility, but prefer chartBorderColor
  chartPriceScaleBorderColor: string; // Keep for compatibility

  chartTimeScaleTextColor: string; // Keep for compatibility, prefer chartTextColor
  chartPriceScaleTextColor: string; // Keep for compatibility

  volumeEnabled: boolean;
  chartVolumeHeightRatio: number;
  chartVolumeUpColor: string;
  chartVolumeDownColor: string;

  chartRightOffset: number;
  chartBarSpacing: number;
  autoHideScalesEnabled: boolean;

  autoSaveEnabled: boolean;
  syncCrosshair: boolean;
  sortConfigs: SortingState; // Corrected to use SortingState
  visibleColumns: VisibleColumns;
  columnWidths: ColumnWidths;
  columnOrder: string[];
  columnFilters: Record<string, string | number | null | undefined>; // More precise type
  globalSearchTerm: string;
  showVolumeInUSD: boolean;
  aggregateVolumeAndTrades: boolean;
  minVolume: number;
  minTrades: number;
  selectedPairs: string[];
  selectedMarketTypes: MarketType[];
  tableCompactness: number;
  uiFontFamily: string;
  uiFontSize: number;
  isSyncEnabled: boolean;
  layoutMode: "grid" | "tabs"; // Example, adjust as needed
  layoutType: LayoutType;
  selectedInterval: string; // Renamed from activeTimeframe. Consider defining a TimeFrame type if not already
  isTableCollapsed: boolean;
  selectedTickerSymbol: string | null;
  availablePairs: string[];
  indicatorColumns: IndicatorColumnConfig[];
  modeControl: ModeControlSettings;
}
