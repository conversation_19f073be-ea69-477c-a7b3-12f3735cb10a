<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - User Price Alerts Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
		<style>
			#chart {
				height: 450px;
				max-width: 1000px;
			}
		</style>
	</head>
	<body>
		<div id="container">
			<div id="chart"></div>
		</div>
		<div id="description">
			<h1>User Price Alerts</h1>
			<p>
				Price alerts which can be added and removed via user interaction on the
				chart. Click the button near the crosshair label on the price scale to
				create a new Price Alert Line.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>
