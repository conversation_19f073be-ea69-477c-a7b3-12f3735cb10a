import React, { Suspense, useMemo, useState, useEffect } from 'react';
import { cn } from '@/shared/lib/utils';
import Image from 'next/image';

// -----------------------------------------------------------------------------
// 1. Icon Map (Using direct URL imports for SVGs)
// -----------------------------------------------------------------------------

// Helper type for SVGR components
type SvgComponent = React.ComponentType<React.SVGProps<SVGSVGElement>>;

// Store for dynamically imported SVGs
const icons: Record<string, string> = {
  // Define the paths to SVG files (relative to public directory)
  'BarChart': '/icons/bar-chart.svg',
  'CandlestickChart': '/icons/candlestick-chart.svg',
  'Check': '/icons/check.svg',
  'ChevronDown': '/icons/chevron-down.svg',
  'ChevronRight': '/icons/chevron-right.svg',
  'ChevronUp': '/icons/chevron-up.svg',
  'Circle': '/icons/circle.svg',
  'GridLayout': '/icons/grid-layout.svg',
  'Indicators': '/icons/indicators.svg',
  'PanelBottomClose': '/icons/panel-bottom-close.svg',
  'PanelTopClose': '/icons/panel-top-close.svg',
  'PlusCircle': '/icons/plus-circle.svg',
  'Refresh': '/icons/refresh.svg',
  'Search': '/icons/search.svg',
  'SettingsReset': '/icons/settings-reset.svg',
  'Settings': '/icons/settings.svg',
  'Trash': '/icons/trash.svg',
  'X': '/icons/x.svg',
  'TablePlus': '/icons/table-plus.svg',
  'Screener': '/icons/screener.svg',
  'Target': '/icons/target.svg',
  'TableCells': '/icons/table-cells.svg',
  'Sorting': '/icons/sorting.svg',
};

// Type for allowed icon names based on the keys of the populated map
export type IconName = keyof typeof icons;

// -----------------------------------------------------------------------------
// 2. Icon Default Styles Configuration
// -----------------------------------------------------------------------------

interface IconStyleDefaults {
  /** Default Tailwind classes for the icon (e.g., 'stroke-current fill-none size-5'). */
  defaultClassName?: string;
}

// Define default styles for each icon
const iconDefaults: Partial<Record<IconName, IconStyleDefaults>> = {
  // Consistent default style for all icons: size, stroke width.
  // Color (stroke) is handled by the component's props or defaults.
  // Fill is always none.
  BarChart:         { defaultClassName: 'size-5 stroke-[1.5]' },
  CandlestickChart: { defaultClassName: 'size-5 stroke-[1.5]' },
  Check:            { defaultClassName: 'size-5 stroke-[1.5]' },
  ChevronDown:      { defaultClassName: 'size-5 stroke-[1.5]' },
  ChevronRight:     { defaultClassName: 'size-5 stroke-[1.5]' },
  ChevronUp:        { defaultClassName: 'size-5 stroke-[1.5]' },
  Circle:           { defaultClassName: 'size-5 stroke-[1.5]' },
  GridLayout:       { defaultClassName: 'size-5 stroke-[0.7]' }, // User modification retained
  Indicators:       { defaultClassName: 'size-4.5 stroke-[1.5] transform -translate-y-[0px]' }, // User modification retained
  PanelBottomClose: { defaultClassName: 'size-5 stroke-[1.5]' },
  PanelTopClose:    { defaultClassName: 'size-5 stroke-[1.5]' },
  PlusCircle:       { defaultClassName: 'size-5 stroke-[1.5]' },
  Refresh:          { defaultClassName: 'size-5 stroke-[1.5]' },
  Search:           { defaultClassName: 'size-5 stroke-[1.5]' },
  SettingsReset:    { defaultClassName: 'size-5 stroke-[1.5]' },
  Settings:         { defaultClassName: 'size-5 stroke-[1.5]' },
  Trash:            { defaultClassName: 'size-5 stroke-[1.5]' },
  X:                { defaultClassName: 'size-5 stroke-[1.5]' },
  TablePlus:        { defaultClassName: 'size-5 stroke-[1.5]' },
  Screener:         { defaultClassName: 'size-5 stroke-[1.5]' },
  Target:           { defaultClassName: 'size-5 stroke-[1.5]' },
  TableCells:       { defaultClassName: 'size-6 stroke-[1.5]' },
  Sorting:          { defaultClassName: 'size-4 stroke-[1.5]' },
};

// -----------------------------------------------------------------------------
// 3. Icon Component (Using direct SVG markup)
// -----------------------------------------------------------------------------

export interface IconProps extends Omit<React.SVGProps<SVGSVGElement>, 'name' | 'ref' | 'size'> {
  /** The name of the icon to render (must match a key in the `icons` map). */
  name: IconName;
  /** Optional size prop (pixels or Tailwind class like 'size-4'). Overrides width/height AND default size class. */
  size?: number | string;
  /** Additional Tailwind classes. Overrides defaults. */
  className?: string;
  /** Fallback component or element to show if the icon is not found. */
  fallback?: React.ReactNode;
}

/**
 * Renders an SVG icon component dynamically based on the 'name' prop.
 * Uses inline SVG markup for maximum compatibility.
 * Applies icon-specific default styles from `iconDefaults`, allowing overrides via props.
 */
export const Icon = React.forwardRef<SVGSVGElement, IconProps>((
  { name, className, size, width, height, fallback = null, color, stroke = 'currentColor', fill = 'none', strokeWidth, ...rest },
  ref
) => {
  const [svgContent, setSvgContent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  const defaults = iconDefaults[name] || {}; // Get defaults for this icon
  const iconPath = icons[name];
  
  useEffect(() => {
    if (!iconPath) {
      setError(new Error(`Icon "${name}" not found.`));
      setIsLoading(false);
      return;
    }
    
    // Fetch the SVG content
    fetch(iconPath)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to load icon: ${response.statusText}`);
        }
        return response.text();
      })
      .then(svgText => {
        setSvgContent(svgText);
        setIsLoading(false);
      })
      .catch(err => {
        console.error(`Error loading icon ${name}:`, err);
        setError(err);
        setIsLoading(false);
      });
  }, [name, iconPath]);
  
  // --- Size Calculation ---
  // Universal logic: extract size from Tailwind classes and convert to inline styles
  const sizeStyle = useMemo(() => {
    if (size && typeof size === 'number') {
      return { width: `${size}px`, height: `${size}px` };
    }
    if (width || height) {
      return { width, height };
    }
    
    // Extract size from Tailwind classes (size-X, w-X, h-X)
    const allClasses = `${defaults.defaultClassName || ''} ${typeof size === 'string' ? size : ''} ${className || ''}`;
    const sizeMatch = allClasses.match(/size-(\d+(?:\.\d+)?)/);
    const wMatch = allClasses.match(/w-(\d+(?:\.\d+)?)/);
    const hMatch = allClasses.match(/h-(\d+(?:\.\d+)?)/);
    
    if (sizeMatch) {
      const sizeValue = parseFloat(sizeMatch[1]) * 4; // Convert to pixels (1 = 4px in Tailwind)
      return { width: `${sizeValue}px`, height: `${sizeValue}px` };
    }
    
    if (wMatch || hMatch) {
      const widthValue = wMatch ? parseFloat(wMatch[1]) * 4 : undefined;
      const heightValue = hMatch ? parseFloat(hMatch[1]) * 4 : undefined;
      return {
        ...(widthValue && { width: `${widthValue}px` }),
        ...(heightValue && { height: `${heightValue}px` })
      };
    }
    
    return { width: '1.25em', height: '1.25em' }; // Fallback
  }, [size, width, height, defaults.defaultClassName, className]);
  
  // --- Class Name Calculation ---
  const combinedClassName = useMemo(() => {
    // Remove size and stroke classes from all sources
    const classesToRemove = /\b(size-\d+(?:\.\d+)?|w-\d+(?:\.\d+)?|h-\d+(?:\.\d+)?|stroke-\[[\d.]+\])\b/g;
    const cleanDefaults = defaults.defaultClassName?.replace(classesToRemove, '').trim();
    const cleanSizeString = typeof size === 'string' ? size.replace(classesToRemove, '').trim() : '';
    const cleanClassName = className?.replace(classesToRemove, '').trim();
    
    return cn(
      // Base styles applied to all icons
      'inline-block flex-shrink-0 align-middle leading-none',
      // Icon-specific defaults (transform/other styles, no size/stroke)
      cleanDefaults,
      // Size prop string (no size/stroke classes)
      cleanSizeString,
      // User-provided className (no size/stroke classes)
      cleanClassName
    );
  }, [defaults.defaultClassName, size, className]);
  
  // --- Stroke Width Calculation ---
  const finalStrokeWidth = useMemo(() => {
    if (strokeWidth !== undefined) return strokeWidth;
    
    // Extract stroke-width from all Tailwind classes
    const allClasses = `${defaults.defaultClassName || ''} ${typeof size === 'string' ? size : ''} ${className || ''}`;
    const strokeMatch = allClasses.match(/stroke-\[([\d.]+)\]/);
    
    return strokeMatch ? parseFloat(strokeMatch[1]) : 1.5;
  }, [strokeWidth, defaults.defaultClassName, size, className]);
  
  // If loading or error, show fallback
  if (isLoading || error || !svgContent) {
    return fallback ? <>{fallback}</> : null;
  }
  
  // Use a simple wrapper with styling applied
  return (
    <div 
      className={combinedClassName}
      style={{
        ...sizeStyle,
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      dangerouslySetInnerHTML={{ 
        __html: svgContent
          .replace(/stroke="[^"]*"/g, `stroke="${color || stroke}"`) 
          .replace(/fill="[^"]*"/g, `fill="${fill}"`)
          .replace(/stroke-width="[^"]*"/g, `stroke-width="${finalStrokeWidth}"`)
      }}
    />
  );
});

Icon.displayName = 'Icon';
