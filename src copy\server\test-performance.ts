// Performance test for refactored components
import { WebSocket } from 'ws';
import { performance } from 'perf_hooks';
import { logger } from './lib/logger';
import { BinanceService } from './exchange/binance.service';
import { initializeIngestService, shutdownIngestService } from './services/ingest.service';
import broadcastService from './services/broadcast.service';
import { initializeQuestDB } from './services/questdb.service';
import { initializeDragonfly } from './services/dragonfly.service';
import { Elysia } from 'elysia';
import { apiHandlers } from './handlers/api.handlers';
import { wsHandlers } from './handlers/ws.handler';

// Configuration
const TEST_DURATION = 120000; // 2 minutes
const TEST_SYMBOLS = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'XRPUSDT', 'ADAUSDT']; // Test with these symbols
const TEST_INTERVALS = ['1m', '5m', '15m']; // Test with these intervals
const NUM_WEBSOCKET_CLIENTS = 50; // Number of concurrent WebSocket clients to simulate
const API_TEST_INTERVAL = 1000; // Interval between API calls (ms)

// Performance metrics
interface Metrics {
  wsLatency: number[];            // WebSocket message latency in ms
  apiResponseTime: number[];      // API response time in ms
  messageCounter: number;         // Total messages received
  apiCounter: number;             // Total API calls made
  errors: {                       // Error counters
    wsConnection: number;
    wsMessage: number;
    apiCall: number;
  };
}

/**
 * Run performance test for the refactored API and WebSocket components
 */
async function runPerformanceTest() {
  // Initialize metrics
  const metrics: Metrics = {
    wsLatency: [],
    apiResponseTime: [],
    messageCounter: 0,
    apiCounter: 0,
    errors: {
      wsConnection: 0,
      wsMessage: 0,
      apiCall: 0
    }
  };
  
  try {
    // Step 1: Initialize services
    logger.info('Step 1: Initializing services');
    
    // Initialize database connections
    const questDBClient = await initializeQuestDB();
    const dragonflyClient = await initializeDragonfly();
    
    // Setup broadcast service with publishFunction that will be called by the server
    const publishFunction = (topic: string, data: any) => {
      // The server calls this function when publishing messages
      logger.debug(`Server publishing to ${topic}`);
    };
    broadcastService.initialize(publishFunction);
    
    // Initialize ingest service
    await initializeIngestService();
    
    // Step 2: Create test server
    logger.info('Step 2: Creating test server');
    const app = new Elysia({ name: 'performance-test' })
      .decorate('logger', logger)
      .decorate('questDBPool', questDBClient)
      .decorate('dragonfly', dragonflyClient)
      .decorate('broadcast', broadcastService)
      .use(apiHandlers)
      .use(wsHandlers)
      .listen(3001);
    
    logger.info('Test server running at http://localhost:3001');
    
    // Step 3: Connect multiple WebSocket clients
    logger.info(`Step 3: Connecting ${NUM_WEBSOCKET_CLIENTS} WebSocket clients`);
    const wsClients: WebSocket[] = [];
    const pingIntervals: NodeJS.Timeout[] = [];
    
    // Create and connect WebSocket clients
    for (let i = 0; i < NUM_WEBSOCKET_CLIENTS; i++) {
      const ws = new WebSocket('ws://localhost:3001/ws');
      wsClients.push(ws);
      
      // Set up event handlers for this client
      ws.on('open', () => {
        // Subscribe to topics
        const clientTopics = [
          'tickers_spot',
          ...TEST_SYMBOLS.flatMap(symbol => 
            TEST_INTERVALS.map(interval => `kline_spot_${symbol.toLowerCase()}_${interval}`)
          ).slice(0, 5) // Limit subscriptions per client to prevent overload
        ];
        
        ws.send(JSON.stringify({ 
          type: 'subscribe', 
          topics: clientTopics
        }));
        
        // Set up ping interval to measure latency
        const pingInterval = setInterval(() => {
          const pingTime = Date.now();
          ws.send(JSON.stringify({
            type: 'ping',
            timestamp: pingTime
          }));
        }, 5000); // Send ping every 5 seconds
        
        pingIntervals.push(pingInterval);
      });
      
      // Track message latency
      ws.on('message', (data) => {
        try {
          metrics.messageCounter++;
          const message = JSON.parse(data.toString());
          
          // Calculate latency for pong messages
          if (message.type === 'pong' && message.timestamp) {
            const latency = Date.now() - message.timestamp;
            metrics.wsLatency.push(latency);
          }
        } catch (err) {
          metrics.errors.wsMessage++;
          logger.error('Failed to parse message:', err);
        }
      });
      
      ws.on('error', () => {
        metrics.errors.wsConnection++;
      });
    }
    
    // Step 4: Run API performance test
    logger.info('Step 4: Starting API performance test');
    const apiInterval = setInterval(async () => {
      try {
        const symbol = TEST_SYMBOLS[Math.floor(Math.random() * TEST_SYMBOLS.length)];
        const interval = TEST_INTERVALS[Math.floor(Math.random() * TEST_INTERVALS.length)];
        
        // Random selection of API endpoints to test
        const endpoint = Math.random() > 0.7 
          ? `http://localhost:3001/api/symbols/spot`
          : Math.random() > 0.5 
            ? `http://localhost:3001/api/tickers/spot`
            : `http://localhost:3001/api/candles/spot/${symbol}/${interval}?limit=50`;
        
        const start = performance.now();
        const response = await fetch(endpoint);
        const end = performance.now();
        
        // Record response time
        metrics.apiResponseTime.push(end - start);
        metrics.apiCounter++;
        
        await response.json(); // Ensure the response is fully consumed
      } catch (error) {
        metrics.errors.apiCall++;
        logger.error('API test error:', error);
      }
    }, API_TEST_INTERVAL);
    
    // Step 5: Collect metrics for specified duration
    logger.info(`Test will run for ${TEST_DURATION/1000} seconds...`);
    
    // Report intermediate metrics every 30 seconds
    const reportInterval = setInterval(() => {
      reportMetrics(metrics);
    }, 30000);
    
    // End the test after specified duration
    setTimeout(async () => {
      logger.info('Test duration complete. Collecting final metrics...');
      
      // Clear intervals
      clearInterval(apiInterval);
      clearInterval(reportInterval);
      pingIntervals.forEach(interval => clearInterval(interval));
      
      // Report final metrics
      reportMetrics(metrics);
      
      // Close WebSocket clients
      wsClients.forEach(ws => {
        try {
          ws.close();
        } catch (e) {
          // Ignore errors on close
        }
      });
      
      // Shutdown services
      await shutdownIngestService();
      broadcastService.shutdown();
      
      // Close server
      app.stop();
      
      logger.info('Performance test completed successfully');
      process.exit(0);
    }, TEST_DURATION);
    
  } catch (error) {
    logger.error('Performance test failed:', error);
    process.exit(1);
  }
}

/**
 * Report metrics summary
 */
function reportMetrics(metrics: Metrics) {
  // Calculate statistics
  const avgWsLatency = metrics.wsLatency.length > 0 
    ? metrics.wsLatency.reduce((a, b) => a + b, 0) / metrics.wsLatency.length 
    : 0;
  
  const avgApiResponse = metrics.apiResponseTime.length > 0 
    ? metrics.apiResponseTime.reduce((a, b) => a + b, 0) / metrics.apiResponseTime.length 
    : 0;
  
  // Get 95th percentile
  const p95WsLatency = getPercentile(metrics.wsLatency, 95);
  const p95ApiResponse = getPercentile(metrics.apiResponseTime, 95);
  
  // Log the results
  logger.info('------ PERFORMANCE METRICS ------');
  logger.info(`Total WebSocket messages: ${metrics.messageCounter}`);
  logger.info(`Total API calls: ${metrics.apiCounter}`);
  logger.info(`WebSocket latency: avg=${avgWsLatency.toFixed(2)}ms, p95=${p95WsLatency.toFixed(2)}ms`);
  logger.info(`API response time: avg=${avgApiResponse.toFixed(2)}ms, p95=${p95ApiResponse.toFixed(2)}ms`);
  logger.info(`Errors: ws_conn=${metrics.errors.wsConnection}, ws_msg=${metrics.errors.wsMessage}, api=${metrics.errors.apiCall}`);
  logger.info('--------------------------------');
}

/**
 * Calculate percentile from an array of numbers
 */
function getPercentile(array: number[], percentile: number): number {
  if (array.length === 0) return 0;
  
  const sorted = [...array].sort((a, b) => a - b);
  const index = Math.ceil((percentile / 100) * sorted.length) - 1;
  return sorted[index];
}

// Run the performance test
runPerformanceTest().catch(err => {
  logger.error('Fatal error:', err);
  process.exit(1);
}); 