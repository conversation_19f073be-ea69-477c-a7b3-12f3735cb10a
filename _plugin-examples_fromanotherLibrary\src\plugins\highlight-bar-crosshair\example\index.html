<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Lightweight Charts - Highlight Bar Crosshair Plugin Example</title>
		<link href="../../../examples-base.css" rel="stylesheet" />
	</head>
	<body>
		<div id="chart"></div>
		<div id="description">
			<h1>Highlight Bar Crosshair</h1>
			<p>
				Shades the background of the data point below the mouse cursor. The
				Highlight bar width matches the bar spacing.
			</p>
		</div>
		<script type="module" src="./example.ts"></script>
	</body>
</html>
