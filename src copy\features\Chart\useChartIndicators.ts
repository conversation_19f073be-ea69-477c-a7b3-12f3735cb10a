import {
  useEffect,
  useMemo,
  useRef,
  useCallback
} from 'react';
import {
  IChartApi,
  ISeriesApi,
  SeriesType,
  LineSeries,
  HistogramSeries,
  AreaSeries,
  SeriesDataItemTypeMap,
  LineWidth,
  IPaneApi,
  Time,
  PriceScaleOptions,
  LineStyle,
  CandlestickData,
  UTCTimestamp,
} from 'lightweight-charts';
import { useIndicatorStore } from '@/shared/store/indicatorStore';
import { findIndicatorDefinitionById } from '@/features/Indicators/registry';
import type { IndicatorInstance, CalculatedIndicatorResult, IndicatorOutputInfo, Kline } from '@/shared/index'; // Assuming types are exported from index

const EMPTY_INSTANCES: IndicatorInstance[] = [];
const MIN_MAIN_PANE_HEIGHT = 0.3;
const TARGET_MAIN_PANE_HEIGHT = 0.7;

const typeToSeriesMap: Record<IndicatorOutputInfo['type'], SeriesType> = {
  line: 'Line',
  histogram: 'Histogram',
  area: 'Area',
  point: 'Line', // <<< Added placeholder mapping for 'point'. Review if specific handling needed.
};

const isValidLineWidth = (width: number | undefined): width is LineWidth =>
  typeof width === 'number' && [1, 2, 3, 4].includes(width);

interface SeriesInfo {
  series: ISeriesApi<SeriesType>;
  paneIndex: number; // Use index instead of paneId
  outputId: string;
  instanceId: string;
}

// Renamed from PaneCustomizationOptions to align with Chart.tsx
interface PaneOptions {
  separatorColor?: string;
  separatorHoverColor?: string;
  enableResize?: boolean;
}

// Renamed and simplified SeriesOptions
type IndicatorSeriesOptions = {
  lastValueVisible?: boolean;
  priceLineVisible?: boolean;
  priceScaleId?: string;
  color?: string;
  lineStyle?: LineStyle;
  lineWidth?: LineWidth;
  // Histogram options (may need adjustment based on definition output)
  // Area options (may need adjustment)
  topColor?: string; // For Area
  bottomColor?: string; // For Area
  lineColor?: string; // For Area baseline? Check lightweight-charts docs
};

/**
 * Hook to manage the integration of technical indicators onto a Lightweight Chart instance.
 * Handles adding, removing, and updating indicator series and panes based on zustand store state.
 *
 * @param chartRef Ref to the IChartApi instance.
 * @param chartId Unique identifier for the chart.
 * @param klineData Processed candlestick data used for main series and indicator calculations.
 * @param symbol Symbol of the chart.
 * @param interval Interval of the chart.
 * @param paneOptions Optional configuration for pane appearance and behavior.
 */
export const useChartIndicatorsIntegration = (
  chartRef: React.RefObject<IChartApi | null>,
  chartId: string,
  // Input should be the processed CandlestickData[] ready for the main series,
  // but indicator calculations might still need the original KlineData structure.
  // We'll fetch KlineData within the effect if needed for calculations.
  klineData: ReadonlyArray<Kline>,
  symbol: string,
  interval: Kline['interval'],
  paneOptions?: PaneOptions
) => {
  const chartIndicators = useIndicatorStore(
    useMemo(() =>
      (state) => state.indicatorsByChartId[chartId] || EMPTY_INSTANCES,
      [chartId]
    )
  );
  const globalIndicators = useIndicatorStore(
    useMemo(() => (state) => state.globalIndicators || EMPTY_INSTANCES, [])
  );

  // Combine local and global indicators for this chart instance
  const indicators = useMemo(() => {
    const combined = [
      ...chartIndicators,
      ...globalIndicators.map(indicator => ({ ...indicator, chartId })) // Ensure local chartId context
    ];
    // console.log(`[useChartIndicators] Combined indicators for ${chartId}:`, combined.length);
    return combined;
  }, [chartIndicators, globalIndicators, chartId]);

  const indicatorSeriesMapRef = useRef<Record<string, Record<string, SeriesInfo>>>({}); // instanceId -> outputId -> SeriesInfo
  const panesRef = useRef<IPaneApi<Time>[]>([]);
  const paneHeightConfigRef = useRef<number[]>([]); // Target heights [main, pane1, pane2, ...]

  const updatePanesRef = useCallback((chart: IChartApi) => {
    try {
      panesRef.current = chart.panes();
    } catch (e) {
      console.error(`[Chart ${chartId}] Error getting panes:`, e);
    }
  }, [chartId]);

  // Function to create a series on a specific pane index
  const createIndicatorSeries = useCallback((
    chart: IChartApi,
    type: SeriesType,
    options: IndicatorSeriesOptions,
    targetPaneIndex: number
  ): ISeriesApi<SeriesType> | null => {
    try {
      // console.log(`[Chart ${chartId}] Creating ${type} series on pane ${targetPaneIndex} with options:`, options);
      if (type === 'Line') return chart.addSeries(LineSeries, options, targetPaneIndex);
      if (type === 'Histogram') return chart.addSeries(HistogramSeries, options, targetPaneIndex);
      if (type === 'Area') return chart.addSeries(AreaSeries, options, targetPaneIndex); // Assuming AreaSeries is needed
      console.warn(`[Chart ${chartId}] Unsupported series type: ${type}`);
      return null;
    } catch (e) {
      console.error(`[Chart ${chartId}] Error creating ${type} series on pane ${targetPaneIndex}:`, e);
      return null;
    }
  }, [chartId]);

  // Map instanceId to its target pane index (0 for main, 1+ for indicator panes)
  const paneInstanceMap = useMemo(() => {
    const map: Record<string, number> = {};
    let paneIndex = 1; // Start indicator panes from index 1
    indicators.forEach(instance => {
      const def = findIndicatorDefinitionById(instance.indicatorId);
      if (def && !def.plotOnMainPane) { // Changed from def?.pane === 'separate'
        map[instance.instanceId] = paneIndex++;
      } else {
        map[instance.instanceId] = 0; // Overlay on main pane (index 0)
      }
    });
    // console.log(`[useChartIndicators] Pane map for ${chartId}:`, map);
    return map;
  }, [indicators, chartId]);

  // Main effect for managing indicator lifecycles
  useEffect(() => {
    const chart = chartRef.current;
    // Ensure chart is valid and we have some base data to calculate from
    if (!chart || !klineData || klineData.length === 0) {
       // Clean up existing indicator series if chart becomes invalid or data disappears
       if (chart && Object.keys(indicatorSeriesMapRef.current).length > 0) {
           console.log(`[Chart ${chartId}] Cleaning up indicators due to invalid chart or no data.`);
           Object.values(indicatorSeriesMapRef.current).forEach(outputs => {
               Object.values(outputs).forEach(({ series }) => {
                   try { chart.removeSeries(series); } catch { /* ignore */ }
               });
           });
           indicatorSeriesMapRef.current = {};
           // Attempt to remove extra panes, assuming pane 0 is main
           try {
               const currentPanes = chart.panes();
               for (let i = currentPanes.length - 1; i > 0; i--) {
                   chart.removePane(i);
               }
               // Reset main pane height? Maybe not necessary if it's removed/recreated
               if (currentPanes[0]) currentPanes[0].setHeight(1);
               updatePanesRef(chart); // Update pane refs after potential removal
           } catch (e) {
               console.warn(`[Chart ${chartId}] Error cleaning up panes:`, e);
           }
       }
       return;
    }

    // Check chart validity
    try {
      chart.options();
    } catch {
      console.warn(`[Chart ${chartId}] Chart instance seems destroyed. Aborting indicator update.`);
      return;
    }

    // Convert CandlestickData back to KlineData if needed by calculation functions
    // This is potentially inefficient; ideally calculations accept CandlestickData or adapt
    const calculationInputData: ReadonlyArray<Kline> = klineData;

    const activeInstanceIds = new Set(indicators.map(i => i.instanceId));
    const currentSeriesMap = indicatorSeriesMapRef.current;
    let panesNeedUpdate = false; // Flag to trigger pane height adjustments

    // --- Step 1: Remove Stale Series & Panes ---
    const instancesToRemove = Object.keys(currentSeriesMap).filter(id => !activeInstanceIds.has(id));
    if (instancesToRemove.length > 0) {
      panesNeedUpdate = true; // Pane heights might change
      instancesToRemove.forEach(instanceId => {
        // console.log(`[Chart ${chartId}] Removing stale indicator instance: ${instanceId}`);
        Object.values(currentSeriesMap[instanceId]).forEach(({ series, outputId }) => {
          try {
            chart.removeSeries(series);
          } catch (e) {
            console.warn(`[Chart ${chartId}] Could not remove series ${outputId} for ${instanceId}:`, e);
          }
        });
        delete currentSeriesMap[instanceId];
      });
    }

    // --- Step 2: Calculate Target Pane Heights ---
    const paneIndicators = indicators.filter(i => {
      const def = findIndicatorDefinitionById(i.indicatorId);
      return def && !def.plotOnMainPane; // Changed from def?.pane === 'separate'
    });
    const targetPaneCount = 1 + paneIndicators.length; // Main pane + indicator panes
    const newPaneHeights: number[] = [];

    if (targetPaneCount === 1) {
      newPaneHeights.push(1); // Only main pane
    } else {
      const mainPaneHeight = Math.max(MIN_MAIN_PANE_HEIGHT, TARGET_MAIN_PANE_HEIGHT);
      const remainingHeight = 1 - mainPaneHeight;
      // Ensure non-negative height for indicators
      const indicatorPaneHeight = Math.max(0, remainingHeight / paneIndicators.length);

      newPaneHeights.push(mainPaneHeight);
      paneIndicators.forEach(() => newPaneHeights.push(indicatorPaneHeight));

      // Normalize heights in case of rounding errors or constraints
      const sum = newPaneHeights.reduce((acc, h) => acc + h, 0);
      if (Math.abs(sum - 1) > 1e-6) {
        console.warn(`[Chart ${chartId}] Normalizing pane heights (Sum: ${sum})`);
        for (let i = 0; i < newPaneHeights.length; i++) {
          newPaneHeights[i] /= sum;
        }
      }
    }
    // Store target heights, only apply later if needed
    paneHeightConfigRef.current = newPaneHeights;


    // --- Step 3: Apply General Chart/Pane Options (Minimal) ---
    // Avoid applying options that might conflict with Chart.tsx management
    // Only apply pane-specific options if provided
    try {
        if (paneOptions) {
            const layoutConfig: { panes?: { separatorColor?: string; separatorHoverColor?: string; enableResize?: boolean } } = { panes: {} };
            if (paneOptions.separatorColor) layoutConfig.panes!.separatorColor = paneOptions.separatorColor;
            if (paneOptions.separatorHoverColor) layoutConfig.panes!.separatorHoverColor = paneOptions.separatorHoverColor;
            layoutConfig.panes!.enableResize = paneOptions.enableResize !== undefined ? paneOptions.enableResize : true;
            //chart.applyOptions({ layout: layoutConfig }); // Be cautious applying layout here
        }

        // Adjust main pane scale margins slightly if indicator panes exist
        const mainPaneScaleMargins = { top: 0.1, bottom: targetPaneCount > 1 ? 0.05 : 0.1 };
        chart.priceScale('right').applyOptions({ scaleMargins: mainPaneScaleMargins });

    } catch (e) {
       console.error(`[Chart ${chartId}] Error applying general chart/pane options:`, e);
    }

    // --- Step 4: Process Active Indicators (Create/Update Series) ---
    indicators.forEach(instance => {
      if (instance.visible === false) {
          // console.log(`[Chart ${chartId}] Skipping calculation/drawing for non-visible instance: ${instance.instanceId}`);
          // Ensure any existing series for this hidden instance are removed (e.g., if visibility was toggled off)
          if (currentSeriesMap[instance.instanceId]) {
              console.log(`[Chart ${chartId}] Removing series for now hidden instance: ${instance.instanceId}`);
              panesNeedUpdate = true; // Pane might need removal/height adjustment
              Object.values(currentSeriesMap[instance.instanceId]).forEach(({ series }) => { try { chart.removeSeries(series); } catch {} });
              delete currentSeriesMap[instance.instanceId];
          }
          return; // Skip processing this non-visible indicator
      }

      const def = findIndicatorDefinitionById(instance.indicatorId);
      if (!def) {
        console.warn(`[Chart ${chartId}] Definition not found for indicatorId: ${instance.indicatorId}`);
        // Remove any lingering series if definition disappears
        if (currentSeriesMap[instance.instanceId]) {
           panesNeedUpdate = true;
           Object.values(currentSeriesMap[instance.instanceId]).forEach(({ series }) => { try { chart.removeSeries(series); } catch {} });
           delete currentSeriesMap[instance.instanceId];
        }
        return;
      }

      let calculatedData: Array<Record<string, number | null>> = []; // Ensure correct type based on calculate function's return
      try {
        // Pass the instance parameters directly
        // Prepare data for calculation: array of close prices as a common case
        const closePrices = calculationInputData.map(d => d.close);
        calculatedData = def.calculate([closePrices], instance.params); // Changed instance.parameters to instance.params and input data format

        // Ensure calculatedData length matches input data length by padding start with undefined/null objects
        // Calculation functions should ideally return arrays matching input length.
        if (calculatedData.length < calculationInputData.length) {
             const padding = Array(calculationInputData.length - calculatedData.length).fill({});
             calculatedData = [...padding, ...calculatedData];
        } else if (calculatedData.length > calculationInputData.length) {
             calculatedData = calculatedData.slice(calculatedData.length - calculationInputData.length);
        }

      } catch (e) {
        console.error(`[Chart ${chartId}] Error calculating indicator ${instance.indicatorId}:`, e);
        if (currentSeriesMap[instance.instanceId]) {
           panesNeedUpdate = true;
           Object.values(currentSeriesMap[instance.instanceId]).forEach(({ series }) => { try { chart.removeSeries(series); } catch {} });
           delete currentSeriesMap[instance.instanceId];
        }
        return; // Skip to next indicator
      }

      // If calculation results in no data points (e.g., insufficient history), remove existing series
      // We check the content of calculatedData later during formatting.
      const isEmptyCalculation = calculatedData.every(d => Object.values(d).every(v => v === undefined || v === null));
      if (isEmptyCalculation) {
         if (currentSeriesMap[instance.instanceId]) {
             console.log(`[Chart ${chartId}] Removing series for ${instance.indicatorId} due to empty calculation results.`);
             panesNeedUpdate = true;
             Object.values(currentSeriesMap[instance.instanceId]).forEach(({ series }) => { try { chart.removeSeries(series); } catch {} });
             delete currentSeriesMap[instance.instanceId];
         }
         return; // Skip to next indicator
      }


      const targetPaneIndex = paneInstanceMap[instance.instanceId]; // 0 for main, 1+ for indicator panes
      const isSeparatePane = def && !def.plotOnMainPane; // Changed from def.pane === 'separate'

      if (isSeparatePane && targetPaneIndex < 1) {
        console.error(`[Chart ${chartId}] Invalid targetPaneIndex (${targetPaneIndex}) for separate pane indicator ${instance.indicatorId}. Skipping.`);
        return; // Skip this indicator instance
      }

      if (!currentSeriesMap[instance.instanceId]) {
        // console.log(`[Chart ${chartId}] Initializing series map for new instance: ${instance.instanceId}`);
        currentSeriesMap[instance.instanceId] = {};
        panesNeedUpdate = true; // New instance might require pane height adjustment
      }

      // Process each output defined for the indicator (e.g., SMA line, RSI line)
      def.outputs.forEach(output => {
        const seriesType = typeToSeriesMap[output.type];
        if (!seriesType) {
          console.warn(`[Chart ${chartId}] Unsupported output type "${output.type}" for indicator ${instance.indicatorId}, output ${output.id}`);
          return; // Skip this output
        }

        // Format data for the specific series type
        // Match calculation results with the original klineData times
        const formattedData = klineData.map((kline, index) => {
           const calcResult = calculatedData[index]; // Assumes arrays are aligned
           const val = calcResult?.[output.id];

           // Basic validation: time must exist, value must be finite number
           // For Kline, time is openTime and should be in ms for lightweight-charts after conversion
           const timeForChart = kline?.openTime ? (Math.floor(kline.openTime / 1000) as UTCTimestamp) : undefined;

           if (timeForChart === undefined || val === undefined || val === null || !isFinite(Number(val))) {
             return null; // Skip invalid data points
           }

           switch (seriesType) {
               case 'Histogram':
                   let color: string | undefined = undefined;
                   const colorValue = calcResult?.[`${output.id}_color`];
                   if (typeof colorValue === 'string') {
                       color = colorValue;
                   }
                   return { time: timeForChart, value: Number(val), color }; // Include color if available
               case 'Line':
               case 'Area': // Area uses same basic data structure
               default:
                   return { time: timeForChart, value: Number(val) };
           }
        }).filter(Boolean) as SeriesDataItemTypeMap[typeof seriesType][]; // Filter out nulls


        // If no valid data points exist for this output after formatting, remove its series
        if (formattedData.length === 0) {
          const existingSeriesInfo = currentSeriesMap[instance.instanceId]?.[output.id];
          if (existingSeriesInfo) {
            // console.log(`[Chart ${chartId}] Removing series ${output.id} for ${instance.indicatorId} due to no formatted data.`);
            try { chart.removeSeries(existingSeriesInfo.series); } catch {}
            delete currentSeriesMap[instance.instanceId][output.id];
            // If this was the last output for the instance, remove the instance entry
            if (Object.keys(currentSeriesMap[instance.instanceId]).length === 0) {
               delete currentSeriesMap[instance.instanceId];
               panesNeedUpdate = true;
            }
          }
          return; // Skip to next output
        }

        // --- Prepare Series Options ---
        const priceScaleId = isSeparatePane ? 'right' : 'right'; // Use main right scale for overlay, dedicated right scale for separate panes
        const styleOverrides = instance.styleOverrides || {}; // Get style overrides or empty object

        const baseOptions: IndicatorSeriesOptions = {
          lastValueVisible: false, // Typically indicator values aren't shown on scale by default
          priceLineVisible: false, // Hide horizontal price line for indicator value
          priceScaleId: priceScaleId,
        };

        // Start with default styles from definition
        const defaultStyles: IndicatorSeriesOptions = {};
        const outputLineWidth = output.lineWidth; // Get from definition
        const lineWidth = isValidLineWidth(outputLineWidth) ? outputLineWidth : 2; // Default to 2 if invalid

        switch (seriesType) {
            case 'Line':
                defaultStyles.color = output.color ?? '#ffffff';
                defaultStyles.lineStyle = output.lineStyle ?? LineStyle.Solid;
                defaultStyles.lineWidth = lineWidth;
                break;
            case 'Histogram':
                defaultStyles.color = output.color ?? '#26a69a';
                // Histograms might have specific base values, check def/docs
                // styleOptions.base = output.base ?? 0;
                break;
            case 'Area':
                defaultStyles.lineColor = output.color ?? '#2962FF';
                defaultStyles.topColor = output.color ? `${output.color}33` : 'rgba(41, 98, 255, 0.2)';
                defaultStyles.bottomColor = output.color ? `${output.color}00` : 'rgba(41, 98, 255, 0)';
                defaultStyles.lineWidth = lineWidth;
                defaultStyles.lineStyle = output.lineStyle ?? LineStyle.Solid;
                break;
        }

        // Apply overrides from instance state
        const finalSeriesOptions = { ...baseOptions, ...defaultStyles };

        // Visibility override (applies to the series itself, not options)
        const isVisibleOverride = styleOverrides[`${output.id}_visible`];
        const isVisible = isVisibleOverride !== undefined ? Boolean(isVisibleOverride) : true; // Default to true if not overridden

        // Apply other style overrides to options
        const colorOverride = styleOverrides[`${output.id}_color`];
        const lineWidthOverride = styleOverrides[`${output.id}_lineWidth`];
        const lineStyleOverride = styleOverrides[`${output.id}_lineStyle`];
        const opacityOverridePercent = styleOverrides[`${output.id}_opacity`]; // Stored as 0-100

        if (colorOverride !== undefined) finalSeriesOptions.color = String(colorOverride);
        if (lineWidthOverride !== undefined && isValidLineWidth(Number(lineWidthOverride))) finalSeriesOptions.lineWidth = Number(lineWidthOverride) as LineWidth;
        if (lineStyleOverride !== undefined && Object.values(LineStyle).includes(Number(lineStyleOverride))) finalSeriesOptions.lineStyle = Number(lineStyleOverride) as LineStyle;

        // Handle opacity - needs careful application based on series type
        if (opacityOverridePercent !== undefined && finalSeriesOptions.color) { // Only apply if color exists
            const opacity = Math.max(0, Math.min(1, Number(opacityOverridePercent) / 100)); // Convert 0-100 to 0-1
            const rgbaColor = hexOrRgbToRgba(finalSeriesOptions.color, opacity); // Use helper
            finalSeriesOptions.color = rgbaColor;

            // Apply opacity to Area series colors too
            if (seriesType === 'Area') {
                 // This logic might need refinement based on how colors are defined/overridden
                 const baseLineColor = String(colorOverride ?? defaultStyles.lineColor ?? '#2962FF');
                 finalSeriesOptions.lineColor = hexOrRgbToRgba(baseLineColor, opacity); // Apply opacity to area line color too
                 finalSeriesOptions.topColor = hexOrRgbToRgba(baseLineColor, opacity * 0.2); // Adjust opacity factor as needed
                 finalSeriesOptions.bottomColor = hexOrRgbToRgba(baseLineColor, opacity * 0.05);
            }
        }

        // --- Create or Update Series ---
        const existingSeriesInfo = currentSeriesMap[instance.instanceId]?.[output.id];

        if (existingSeriesInfo) {
          // Update existing series
          try {
             // Check if pane index needs changing (though unlikely for existing series)
             if(existingSeriesInfo.paneIndex !== targetPaneIndex) {
                 console.warn(`[Chart ${chartId}] Pane index mismatch for existing series ${output.id}. Recreating.`)
                 // Recreate instead of moving - simpler? AddSeries handles pane target.
                 chart.removeSeries(existingSeriesInfo.series);
                 const newSeries = createIndicatorSeries(chart, seriesType, finalSeriesOptions, targetPaneIndex);
                 if (newSeries) {
                     newSeries.setData(formattedData);
                     currentSeriesMap[instance.instanceId][output.id] = {
                         series: newSeries,
                         paneIndex: targetPaneIndex,
                         outputId: output.id,
                         instanceId: instance.instanceId
                     };
                 } else {
                     delete currentSeriesMap[instance.instanceId][output.id]; // Remove if creation failed
                     panesNeedUpdate = true;
                 }
             } else {
                // Just apply options and data
                existingSeriesInfo.series.applyOptions(finalSeriesOptions);
                existingSeriesInfo.series.setData(formattedData);
                // console.log(`[Chart ${chartId}] Updated series ${output.id} for ${instance.indicatorId} on pane ${targetPaneIndex}`);
             }
          } catch (e) {
            console.error(`[Chart ${chartId}] Error updating series ${output.id} for ${instance.indicatorId}:`, e);
            // Attempt to remove problematic series
            try { chart.removeSeries(existingSeriesInfo.series); } catch {}
            delete currentSeriesMap[instance.instanceId][output.id];
            if (Object.keys(currentSeriesMap[instance.instanceId]).length === 0) {
                delete currentSeriesMap[instance.instanceId];
                panesNeedUpdate = true;
            }
          }
        } else {
          // Only create if visible according to override
          if (isVisible) {
              const newSeries = createIndicatorSeries(chart, seriesType, finalSeriesOptions, targetPaneIndex);
              if (newSeries) {
                newSeries.setData(formattedData);
                currentSeriesMap[instance.instanceId][output.id] = {
                  series: newSeries,
                  paneIndex: targetPaneIndex,
                  outputId: output.id,
                  instanceId: instance.instanceId
                };
                 panesNeedUpdate = true; // New series might require pane setup
              } else {
                 console.error(`[Chart ${chartId}] Failed to create series ${output.id} for ${instance.indicatorId}`);
              }
          } else {
              // console.log(`[Chart ${chartId}] Skipping creation of hidden series ${output.id} for ${instance.indicatorId}`);
          }
        }
      }); // End of output loop
    }); // End of indicator instance loop


     // --- Step 5: Adjust Panes (Heights and Scales) - Deferred ---
     // This needs careful coordination with Chart.tsx which might also manage panes/scales.
     // Only run if panesNeedUpdate is true.
     if (panesNeedUpdate) {
         // Use setTimeout to defer execution slightly, allowing chart internals to potentially settle
         // and avoiding potential conflicts if Chart.tsx is also modifying panes/scales in the same render cycle.
         setTimeout(() => {
             const currentChart = chartRef.current; // Re-check chart validity inside timeout
             if (!currentChart) return;

             try {
                 const currentPanes = currentChart.panes();
                 const targetHeights = paneHeightConfigRef.current;
                 const targetCount = targetHeights.length;
                 const currentCount = currentPanes.length;
                 // console.log(`[Chart ${chartId} Deferred] Adjusting panes. Current: ${currentCount}, Target: ${targetCount}, Heights:`, targetHeights);


                 // Remove excess panes (from bottom up)
                 if (currentCount > targetCount) {
                     for (let i = currentCount - 1; i >= targetCount; i--) {
                         try {
                             // console.log(`[Chart ${chartId} Deferred] Removing excess pane ${i}`);
                             currentChart.removePane(i);
                         } catch (e) {
                             console.error(`[Chart ${chartId} Deferred] Error removing excess pane ${i}:`, e);
                         }
                     }
                 }
                 // Note: Adding panes is implicitly handled by chart.addSeries(..., paneIndex)
                 // If a series is added to a non-existent pane index (e.g., 1 when only 0 exists),
                 // lightweight-charts should create the intermediate pane(s).

                 // Apply target heights AFTER potential removals/additions
                 const finalPanes = currentChart.panes(); // Get panes again
                 if (finalPanes.length !== targetCount) {
                     console.warn(`[Chart ${chartId} Deferred] Pane count mismatch after add/remove. Expected ${targetCount}, Got ${finalPanes.length}. Applying heights to available panes.`);
                 }

                 targetHeights.forEach((height, index) => {
                     if (finalPanes[index]) {
                         try {
                              // Avoid setting height if it's already correct (reduces flicker)
                             // This requires reading the current height, which might not be reliable immediately
                             // const currentHeight = finalPanes[index].height(); // Check if height() method exists
                             // if (Math.abs(currentHeight - height) > 1e-4) {
                                finalPanes[index].setHeight(height);
                             // }
                         } catch (e) {
                             console.error(`[Chart ${chartId} Deferred] Error setting height for pane ${index}:`, e);
                         }
                     } else {
                         console.warn(`[Chart ${chartId} Deferred] Tried to set height for non-existent pane index ${index}`);
                     }
                 });

                 // Update pane references after potential changes
                 updatePanesRef(currentChart);

                 // --- Configure Indicator Pane Price Scales ---
                 // Skip main pane (index 0), configure scales for index 1+
                 for (let i = 1; i < finalPanes.length; i++) {
                     const pane = finalPanes[i];
                     if (!pane) continue;

                     try {
                         // Get the right price scale for this specific pane
                         const rightPriceScale = pane.priceScale('right');
                         if (rightPriceScale) {
                             // Apply desired options for indicator scales
                             rightPriceScale.applyOptions({
                                 visible: true, // Ensure visible
                                 autoScale: true, // Usually desired for indicators
                                 scaleMargins: { top: 0.15, bottom: 0.15 }, // Generous margins for indicator panes
                                 // Potentially disable price formatter if indicators have own formatting needs?
                                 // Or inherit global formatter? Needs decision.
                             });
                         }
                     } catch (e) {
                         console.error(`[Chart ${chartId} Deferred] Error configuring right price scale for pane ${i}:`, e);
                     }
                 }

                 // --- Optional: Fit Content (Use with Caution) ---
                 // Fit content can reset zoom/scroll, which might be undesirable.
                 // Avoid if Chart.tsx handles zoom/scroll state.
                 // try {
                 //     // currentChart.timeScale().fitContent();
                 // } catch (e) {
                 //     console.error(`[Chart ${chartId} Deferred] Error fitting time scale content:`, e);
                 // }

             } catch (e) {
                 console.error(`[Chart ${chartId} Deferred] Error during pane height/scale configuration:`, e);
             }
         }, 0); // Delay slightly
     } // End if(panesNeedUpdate)

  }, [
      chartRef,
      chartId,
      indicators, // Combined list drives the updates
      klineData, // Base data for calculations
      paneOptions, // Pane styling options
      paneInstanceMap, // Mapping changes if indicators change
      createIndicatorSeries, // Callback dep
      updatePanesRef, // Callback dep
      symbol,
      interval,
    ]
  );

  // Return minimal state/actions needed by the Chart component, if any.
  // Currently, this hook primarily synchronizes store state to the chart.
  // We might need to return pane control functions if Chart.tsx doesn't handle it.
  // return {
  //   panes: panesRef.current, // Expose current panes? Use cautiously
  // };
}; 

// Helper function to convert hex/rgb to rgba with specific alpha
function hexOrRgbToRgba(color: string, alpha: number): string {
    alpha = Math.max(0, Math.min(1, alpha)); // Clamp alpha 0-1

    // Handle HEX (#RRGGBB or #RGB)
    let m = color.match(/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
    if (m) {
        return `rgba(${parseInt(m[1], 16)}, ${parseInt(m[2], 16)}, ${parseInt(m[3], 16)}, ${alpha})`;
    }
    m = color.match(/^#?([a-f\d])([a-f\d])([a-f\d])$/i);
    if (m) {
        return `rgba(${parseInt(m[1] + m[1], 16)}, ${parseInt(m[2] + m[2], 16)}, ${parseInt(m[3] + m[3], 16)}, ${alpha})`;
    }

    // Handle rgb(r, g, b)
    m = color.match(/^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/i);
    if (m) {
        return `rgba(${m[1]}, ${m[2]}, ${m[3]}, ${alpha})`;
    }

    // Handle rgba(r, g, b, a) - just replace alpha
    m = color.match(/^(rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*)[0-9\.]+(\s*\))$/i);
    if (m) {
        return `${m[1]}${alpha}${m[2]}`;
    }

    // Return original color if format is unknown
    console.warn(`[hexOrRgbToRgba] Could not parse color format: ${color}. Returning original.`);
    return color;
} 