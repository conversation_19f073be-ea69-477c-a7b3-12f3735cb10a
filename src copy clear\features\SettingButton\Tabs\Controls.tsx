import React, { useState, useEffect, useRef, memo, useCallback, useMemo } from 'react';
// import { ColorPicker } from 'antd'; // Закомментировано
// import type { Color } from 'antd/es/color-picker'; // Закомментировано

// Import shadcn/ui components
import { Slider as ShadSlider } from "@/shared/ui/slider"; 
import { Select as ShadSelect, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/ui/select";
import { Switch as ShadSwitch } from "@/shared/ui/switch";
import { Label as ShadLabel } from "@/shared/ui/label";
import { Input as UIInput } from "@/shared/ui/input";
import { cn, debounce, throttle } from '@/shared/lib/utils';

// --- Debounce Helper (Keep or move to utils) ---
// function debounce<F extends (...args: any[]) => any>(func: F, waitFor: number) { // REMOVED
//     let timeoutId: ReturnType<typeof setTimeout> | null = null; // REMOVED
// // REMOVED
//     return (...args: Parameters<F>): void => { // REMOVED
//         if (timeoutId !== null) { // REMOVED
//             clearTimeout(timeoutId); // REMOVED
//         } // REMOVED
//         timeoutId = setTimeout(() => func(...args), waitFor); // REMOVED
//     }; // REMOVED
// } // REMOVED

// --- Throttle Helper (Keep or move to utils) ---
// Note: This version might not return the last result correctly in all edge cases.
// Consider using a library like lodash.throttle if precise behavior is needed.
// function throttle<F extends (...args: any[]) => any>(func: F, limit: number) { // REMOVED
//     let inThrottle: boolean; // REMOVED
//     let lastResult: ReturnType<F>; // REMOVED
// // REMOVED
//     return function(this: ThisParameterType<F>, ...args: Parameters<F>): ReturnType<F> { // REMOVED
//         const context = this; // REMOVED
//         if (!inThrottle) { // REMOVED
//             inThrottle = true; // REMOVED
//             setTimeout(() => inThrottle = false, limit); // REMOVED
//             lastResult = func.apply(context, args); // REMOVED
//         } // REMOVED
//         // This might return undefined if the function hasn't run yet. // REMOVED
//         // Consider how to handle this based on usage. // REMOVED
//         return lastResult; // REMOVED
//     }; // REMOVED
// } // REMOVED

// --- HEX <-> RGBA Helper (Keep or move to utils) ---
const hexFromRgba = (rgbaString: string): string => {
    try {
        const match = rgbaString.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)$/);
        if (match) {
            const r = parseInt(match[1]).toString(16).padStart(2, '0');
            const g = parseInt(match[2]).toString(16).padStart(2, '0');
            const b = parseInt(match[3]).toString(16).padStart(2, '0');
            return `#${r}${g}${b}`; // input type="color" doesn't handle alpha
        }
        // Check if it's already a valid hex color
        if (/^#[0-9a-fA-F]{6}$/i.test(rgbaString)) {
            return rgbaString;
        }
        // Handle 3-digit hex
        if (/^#[0-9a-fA-F]{3}$/i.test(rgbaString)) {
            return `#${rgbaString[1]}${rgbaString[1]}${rgbaString[2]}${rgbaString[2]}${rgbaString[3]}${rgbaString[3]}`;
        }
    } catch (e) {
        console.error("Error parsing color:", rgbaString, e);
    }
    return '#000000'; // Default to black on error
};

// --- Simple Color Picker (Replaces AntColorPicker) ---
interface SimpleColorPickerProps {
    label?: string;
    value: string; // Assumes HEX input/output now based on usage
    onChange: (value: string) => void; // Expects HEX output
    "aria-label"?: string;
    className?: string;
}

export const SimpleColorPicker: React.FC<SimpleColorPickerProps> = memo(({ label, value, onChange, className, ...rest }) => {
    // Use a ref for the input to potentially trigger the picker programmatically if needed
    const inputRef = useRef<HTMLInputElement>(null);

    // Ensure the value passed to the input is always a valid HEX
    const validHexValue = useMemo(() => hexFromRgba(value || '#000000'), [value]);

    return (
        // Reduced margin, adjusted alignment
        <div className={cn("flex items-center gap-1.5", className)}> 
            {label && (
                <ShadLabel className="text-[11px] text-muted-foreground font-medium whitespace-nowrap">{label}</ShadLabel>
            )}
            <input
                ref={inputRef}
                type="color"
                value={validHexValue}
                onChange={(e) => onChange(e.target.value)}
                aria-label={rest["aria-label"] || label || "Select color"}
                // Smaller size, adjusted padding/border/focus
                className="appearance-none w-6 h-6 rounded-sm border border-border-ring focus:outline-none focus:ring-offset-1 focus:ring-offset-background transition-colors duration-150 ease-in-out focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-0"
                style={{ backgroundColor: validHexValue }}
            />
        </div>
    );
});

// --- MainToggleButton Component --- (Switch Style)
interface MainToggleButtonProps {
  label: string;
  active: boolean;
  onChange: (checked: boolean) => void; // shadcn Switch передает boolean
  className?: string;
  id?: string; // Добавляем id для связи Label и Switch
}

export const MainToggleButton: React.FC<MainToggleButtonProps> = memo(({ label, active, onChange, className = '', id }) => {
  const switchId = id || `switch-${label.toLowerCase().replace(/\s+/g, '-')}`;
  return (
    // Increased spacing, ensured vertical centering
    <div className={cn("flex items-center justify-between space-x-4 py-1.5", className)}> 
      <ShadLabel htmlFor={switchId} className="text-sm font-medium text-muted-foreground hover:text-foreground flex-grow cursor-pointer select-none">
        {label}
      </ShadLabel>
      <ShadSwitch
        id={switchId}
        checked={active}
        onCheckedChange={onChange} // Передаем обработчик
        aria-label={label} 
      />
    </div>
  );
});

// --- Select Component --- (Dropdown)
interface SelectProps { 
  label: string; 
  value: string; 
  onChange: (value: string) => void; 
  options: string[]; 
  className?: string; 
  isFont?: boolean; 
  placeholder?: string; // Добавляем плейсхолдер
}

export const Select: React.FC<SelectProps> = memo(({ label, value, onChange, options, className = '', isFont = false, placeholder = "Select..." }) => {
  const selectId = `select-${label.toLowerCase().replace(/\s+/g, '-')}`;
  return (
    // Reduced margin bottom
    <div className={cn("mb-2 flex flex-col gap-1", className)}> 
      <ShadLabel htmlFor={selectId} className="text-[11px] text-muted-foreground font-medium pl-0.5">{label}</ShadLabel>
      <ShadSelect value={value} onValueChange={onChange}> 
        {/* Adjusted trigger padding and height */}
        <SelectTrigger id={selectId} className="w-full text-xs h-8 py-1">
          <SelectValue placeholder={placeholder} style={isFont ? { fontFamily: value } : {}} />
        </SelectTrigger>
        <SelectContent className="z-40">
          {options.map((option) => (
            <SelectItem 
              key={option} 
              value={option} 
              style={isFont ? { fontFamily: option } : {}} 
              className="text-xs"
            >
              {option}
            </SelectItem>
          ))}
        </SelectContent>
      </ShadSelect>
    </div>
  );
});

// --- Slider Component ---
interface SliderProps { 
  label: string; 
  value: number; 
  onChange: (value: number) => void; // shadcn Slider возвращает number[]
  min: number; 
  max: number; 
  step?: number; 
  unit?: string; 
  className?: string; 
  debounceTimeout?: number; 
  valueDisplayFormatter?: (value: number) => string; // Опциональный форматер для значения
}

export const Slider: React.FC<SliderProps> = memo(({ 
  label, 
  value: externalValue,
  onChange: notifyChange,
  min, 
  max, 
  step = 1, 
  unit = '', 
  className = '', 
  debounceTimeout = 0, // По умолчанию убираем debounce
  valueDisplayFormatter
}) => {
  // Используем локальное состояние для немедленного отображения, если нужен debounce
  const [internalValue, setInternalValue] = useState(externalValue);

  // Обновляем внутреннее состояние при изменении внешнего
  useEffect(() => {
    setInternalValue(externalValue);
  }, [externalValue]);

  // Debounced обработчик для вызова notifyChange
  const debouncedNotify = useCallback(
    debounce((newValue: number) => {
      notifyChange(newValue);
    }, debounceTimeout),
    [notifyChange, debounceTimeout]
  );

  // Обработчик изменения значения из shadcn/ui Slider
  const handleValueChange = (newValueArr: number[]) => {
    const newValue = newValueArr[0]; // shadcn Slider возвращает массив
    setInternalValue(newValue); // Обновляем внутреннее состояние для отображения
    if (debounceTimeout > 0) {
      debouncedNotify(newValue);
    } else {
      notifyChange(newValue); // Вызываем сразу, если debounce не нужен
    }
  };

  // Форматируем значение для отображения
  const displayValue = useMemo(() => {
      const val = debounceTimeout > 0 ? internalValue : externalValue;
      if (valueDisplayFormatter) {
          return valueDisplayFormatter(val);
      } 
      // Стандартное форматирование
      const decimals = step < 1 ? (step.toString().split('.')[1]?.length || 0) : 0;
      return `${val.toFixed(decimals)}${unit}`;
  }, [internalValue, externalValue, debounceTimeout, valueDisplayFormatter, step, unit]);

  return (
    <div className={cn("mb-2 flex flex-col gap-1", className)}>
      <div className="flex justify-between items-center">
        <ShadLabel className="text-[11px] text-muted-foreground font-medium pl-0.5 select-none">
          {label}
        </ShadLabel>
        {/* Adjusted value display styling */}
        <span className="text-[11px] font-medium text-foreground bg-muted/50 px-1.5 py-0.5 rounded-sm min-w-[36px] text-center tabular-nums">
          {displayValue} 
        </span>
      </div>
      <ShadSlider
        value={[internalValue]} // shadcn Slider ожидает массив
        onValueChange={handleValueChange} // Используем обработчик
        min={min}
        max={max}
        step={step}
        // Removed duplicate className application
        className="w-full pt-1" 
      />
    </div>
  );
});
