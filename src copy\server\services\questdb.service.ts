import { Pool, QueryResult } from 'pg';
import fs from 'node:fs/promises'; // For reading the schema file
import path from 'node:path';     // For constructing the schema file path
import { QUESTDB_URL } from '../config';
import { logger } from '../lib/logger'; // Import the new logger

const log = logger.child({ service: 'QuestDBService' });

let pool: Pool | null = null;
let poolInitializationPromise: Promise<Pool> | null = null;

/**
 * Reads the schema.sql file and executes its content against the database.
 */
async function applySchemaFromFile(dbPool: Pool): Promise<void> {
    const schemaFilePath = path.join(__dirname, 'questdb.schema.sql'); // Assumes schema file is in the same directory
    log.info(`Applying schema from file: ${schemaFilePath}`);
    try {
        const schemaSql = await fs.readFile(schemaFilePath, 'utf-8');
        // Split SQL commands by semicolon, handling potential comments and empty lines
        const commands = schemaSql
            .split(';')
            .map(cmd => cmd.trim())
            .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));

        for (const command of commands) {
            log.debug(`Executing schema command: ${command.substring(0, 100)}${command.length > 100 ? '...' : ''}`);
            await dbPool.query(command);
        }
        log.info('Schema applied successfully from file.');
    } catch (error) {
        log.error('Error applying schema from file:', { path: schemaFilePath, error });
        throw error; // Re-throw to indicate failure in initialization
    }
}

/**
 * Initializes and returns the QuestDB connection pool.
 * Ensures that pool initialization is attempted only once.
 */
async function initializeOrGetPool(): Promise<Pool> {
    if (pool) {
        // Pool already initialized and healthy (basic check)
        try {
            await pool.query('SELECT 1').timeout(2000); // Quick health check
            return pool;
        } catch (e) {
            log.warn('Existing pool health check failed. Attempting to re-initialize.', e);
            // Nullify to force re-initialization below
            if (pool) {
                pool.end().catch(err => log.error('Error closing unhealthy pool before re-init:', err));
            }
            pool = null;
            poolInitializationPromise = null; // Reset promise to allow re-init
        }
    }

    if (!poolInitializationPromise) {
        log.info('QuestDB pool not initialized or needs re-initialization. Starting initialization...');
        poolInitializationPromise = new Promise<Pool>((resolve, reject) => {
            const maskedUrl = QUESTDB_URL.replace(/:[^:]+@/, ':********@');
            log.info(`Creating new QuestDB pool. Target: ${maskedUrl}`);

            const newPoolInstance = new Pool({
                connectionString: QUESTDB_URL,
                max: 100,
                min: 5, // Start with a few connections
                idleTimeoutMillis: 60000, // Close idle clients after 1 minute
                connectionTimeoutMillis: 30000, // Fail to connect after 30 seconds
                statement_timeout: 120000, // Abort statements taking longer than 2 minutes
                query_timeout: 60000,      // Abort queries taking longer than 1 minute (redundant if statement_timeout is shorter)
                allowExitOnIdle: true,
            });

            newPoolInstance.on('connect', (client) => {
                log.info('New client connected to QuestDB.');
                // Optional: Set session-level parameters if needed
                // client.query('SET search_path TO my_schema'); 
            });

            newPoolInstance.on('error', (err, client) => {
                log.error('Unexpected error on idle client in QuestDB pool:', err);
                // pg-pool should handle removing bad clients. 
                // If errors persist, queryQuestDB retries might trigger pool re-creation if health checks fail.
            });
            
            newPoolInstance.on('remove', () => log.debug('Client removed from QuestDB pool.'));

            // Initial connection test
            newPoolInstance.query('SELECT 1')
                .then(() => {
                    log.info('QuestDB pool connection test (SELECT 1) successful. Pool is ready.');
                    pool = newPoolInstance; // Assign to module-level variable
                    resolve(newPoolInstance);
                })
                .catch(err => {
                    log.error('Failed to connect to QuestDB or initial test query failed:', err);
                    newPoolInstance.end().catch(endErr => 
                        log.error('Error closing pool after connection/test failure:', endErr)
                    );
                    poolInitializationPromise = null; // Allow re-attempt on next call
                    reject(err);
                });
        });
    }

    return poolInitializationPromise;
}

/**
 * Initialize the QuestDB connection, create tables from schema file.
 */
export async function initializeQuestDB(): Promise<Pool> {
    try {
        log.info('Initializing QuestDB connection and applying schema...');
        const pgPool = await initializeOrGetPool();
        await applySchemaFromFile(pgPool); // Apply schema after pool is ready
        log.info('QuestDB connection pool initialized and schema applied successfully.');
        return pgPool;
    } catch (error) {
        log.error('Failed to initialize QuestDB connection pool or apply schema:', error);
        // Ensure pool is cleaned up if schema application fails after pool init
        if (pool) {
            await pool.end().catch(e => log.error('Error closing pool during failed initialization:', e));
            pool = null;
            poolInitializationPromise = null;
        }
        throw error;
    }
}

/**
 * Execute a query against QuestDB
 */
export async function queryQuestDB<T extends any[] = any[]>(
    queryString: string, 
    params: any[] = []
): Promise<QueryResult<T>> {
    let retries = 3; // Reduced default retries, can be adjusted
    let lastError: Error | unknown = null;
    const queryStartTime = Date.now();
    const truncatedQuery = queryString.length > 100 ? `${queryString.substring(0, 100)}...` : queryString;
    
    while (retries > 0) {
        try {
            const currentPool = await initializeOrGetPool(); // Ensures pool is ready or tries to init
            
            const result = await currentPool.query<T>(queryString, params);
            
            const queryDuration = Date.now() - queryStartTime;
            if (queryDuration > 1000) { // Log if query takes more than 1 second
                log.warn(`Slow query (${queryDuration}ms): ${truncatedQuery}`);
            }
            
            return result;
        } catch (err) {
            lastError = err;
            const error = err as Error;
            
            const isConnectionError = error.message && (
                error.message.includes('timeout') || 
                error.message.includes('connection') ||
                error.message.includes('connect') ||
                error.message.includes('terminated unexpectedly') ||
                error.message.includes('Connection terminated') ||
                error.message.includes('does not exist') || // Pool might have been closed
                error.message.includes('Pool is shutting down')
            );
            
            const isPoolError = error.message && (
                error.message.includes('too many clients') ||
                error.message.includes('pool is full')
            );
            
            retries--;
            
            if ((isConnectionError || isPoolError) && retries > 0) {
                const backoffMs = 1000 * (3 - retries); // Adjusted backoff
                
                log.warn(
                    `${isConnectionError ? 'Connection' : 'Pool'} error executing query. Retrying in ${backoffMs}ms (${retries} attempts left). Error: ${error.message}`,
                    { query: truncatedQuery }
                );
                
                if (isConnectionError) {
                    // For connection errors, it's good to assume the pool might be compromised.
                    // We'll force a re-evaluation of the pool on the next attempt.
                    if (pool) {
                       await pool.end().catch(e => log.error('Error ending pool during retry logic', e));
                    }
                    pool = null; 
                    poolInitializationPromise = null;
                }
                
                await new Promise(resolve => setTimeout(resolve, backoffMs));
                continue;
            }
            
            log.error('Query error after retries or for non-recoverable issue:', { query: truncatedQuery, params, error: error.message });
            throw error; 
        }
    }
    
    log.error('Exhausted all retries for query:', { query: truncatedQuery, params, lastError: (lastError as Error)?.message });
    throw lastError; 
}

/**
 * Get the QuestDB connection pool.
 * This function ensures the pool is initialized before returning it.
 */
export async function getQuestDBPool(): Promise<Pool> {
    return initializeOrGetPool();
}

/**
 * Gracefully disconnect the QuestDB connection pool.
 * This should be called on application shutdown.
 */
export async function disconnectQuestDB(): Promise<void> {
    log.info('Attempting to disconnect QuestDB pool...');
    if (poolInitializationPromise) {
        try {
            // Await the current initialization promise to ensure we're closing the correct pool
            const currentPool = await poolInitializationPromise;
            await currentPool.end();
            log.info('QuestDB pool disconnected successfully.');
        } catch (error) {
            log.error('Error during QuestDB pool disconnection (pool might not have initialized or already ended):', error);
        } finally {
            pool = null;
            poolInitializationPromise = null;
        }
    } else if (pool) { // Fallback if promise is null but pool somehow exists
        try {
            await pool.end();
            log.info('QuestDB pool (fallback) disconnected successfully.');
        } catch (error) { 
            log.error('Error during QuestDB pool (fallback) disconnection:', error);
        } finally {
            pool = null;
        }
    } else {
        log.info('No active QuestDB pool or initialization attempt to disconnect.');
    }
}

// Add timeout method to promises to avoid hanging
declare global {
    interface Promise<T> {
        timeout(ms: number): Promise<T>;
    }
}

if (!Promise.prototype.timeout) {
    Promise.prototype.timeout = function<T>(ms: number): Promise<T> {
        // Make sure 'this' is a Promise
        if (typeof this.then !== 'function') {
            throw new Error('Timeout can only be called on a Promise.');
        }
        return Promise.race([
            this,
            new Promise<T>((_, reject) => 
                setTimeout(() => reject(new Error(`Promise timed out after ${ms}ms`)), ms)
            )
        ]);
    };
} 