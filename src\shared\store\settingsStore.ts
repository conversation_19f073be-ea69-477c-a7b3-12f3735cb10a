import { create, StateCreator } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import {
  AppSettings,
  VisibleColumns,
  ColumnWidths,
  ChartType,
  IndicatorColumnConfig,
  ViewMode,
  ScreenerSettings,
  LayoutType,
  MarketType,
  AppSettingsSchema,
} from '@/shared/index';
import { LineStyle, LineWidth } from 'lightweight-charts';
import { SortingState } from '@tanstack/react-table';

const LS_SETTINGS_KEY = 'cryptoDashboardSettings';

// Derive default settings from the Zod schema to ensure consistency.
export const defaultSettings: AppSettings = AppSettingsSchema.parse({});

interface AppSettingsState extends AppSettings {
  setSetting: <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => void;
  toggleColumnVisibility: (key: keyof VisibleColumns | string) => void;
  updateSortConfigs: (newSortingState: SortingState) => void;
  setColumnOrder: (newOrder: string[]) => void;
  setColumnWidth: (key: keyof ColumnWidths | string, width: number) => void;
  setColumnWidths: (newWidths: ColumnWidths) => void;
  setColumnFilter: (key: string, value: string | number | null) => void;
  setTableCollapsed: (isCollapsed: boolean) => void;
  resetSettings: () => void;
  toggleAutoHideScales: () => void;
  setGlobalSearchTerm: (term: string) => void;
  addIndicatorColumn: (config: IndicatorColumnConfig) => void;
  removeIndicatorColumn: (instanceId: string) => void;
  updateIndicatorColumn: (instanceId: string, updates: Partial<IndicatorColumnConfig>) => void;
  setViewMode: (mode: ViewMode) => void;
  updateScreenerSettings: (updates: Partial<ScreenerSettings>) => void;
  setLayoutTypeForMode: (mode: ViewMode, layoutType: LayoutType) => void;
}

const createAppSettingsActions = (
  set: (partial: Partial<AppSettingsState> | ((state: AppSettingsState) => Partial<AppSettingsState>)) => void,
  get: () => AppSettingsState
): Omit<AppSettingsState, keyof AppSettings> => ({
  setSetting: (key, value) => set({ [key]: value }),
  toggleColumnVisibility: (key) => {
    if (key === 'symbol' && get().visibleColumns[key]) return;
    set((state) => ({
      visibleColumns: { ...state.visibleColumns, [key]: !state.visibleColumns[key] },
    }));
  },
  updateSortConfigs: (newSortingState) => set({ sortConfigs: newSortingState }),
  setColumnOrder: (newOrder) => set({ columnOrder: newOrder }),
  setColumnWidth: (key, width) => {
    set((state) => ({
      columnWidths: { ...state.columnWidths, [key]: Math.max(width, 40) },
    }));
  },
  setColumnWidths: (newWidths) => set({ columnWidths: newWidths }),
  setColumnFilter: (key, value) => {
    set((state) => ({
      columnFilters: { ...state.columnFilters, [key]: value },
    }));
  },
  setTableCollapsed: (isCollapsed) => set({ isTableCollapsed: isCollapsed }),
  resetSettings: () => {
    const newDefaults = AppSettingsSchema.parse({});
    set(newDefaults);
  },
  toggleAutoHideScales: () => set(state => ({ autoHideScalesEnabled: !state.autoHideScalesEnabled })),
  setGlobalSearchTerm: (term) => set({ globalSearchTerm: term }),
  addIndicatorColumn: (config) => {
    set((state) => ({
      indicatorColumns: [...state.indicatorColumns, config],
    }));
  },
  removeIndicatorColumn: (instanceId) => {
    set((state) => ({
      indicatorColumns: state.indicatorColumns.filter(c => c.instanceId !== instanceId),
    }));
  },
  updateIndicatorColumn: (instanceId, updates) => {
    set((state) => ({
      indicatorColumns: state.indicatorColumns.map(c =>
        c.instanceId === instanceId ? { ...c, ...updates } : c
      ),
    }));
  },
  setViewMode: (mode) => {
    set((state) => ({
      modeControl: { ...state.modeControl, currentMode: mode },
    }));
  },
  updateScreenerSettings: (updates) => {
    set((state) => ({
    modeControl: {
      ...state.modeControl,
        screenerSettings: { ...state.modeControl.screenerSettings, ...updates },
      },
    }));
  },
  setLayoutTypeForMode: (mode, layoutType) => {
    set((state) => ({
    modeControl: {
      ...state.modeControl,
        layoutTypes: { ...state.modeControl.layoutTypes, [mode]: layoutType },
      },
    }));
  },
});

export const useAppSettingsStore = create<AppSettingsState>()(
  persist(
    (set, get) => ({
      ...defaultSettings,
      ...createAppSettingsActions(set, get),
    }),
    {
      name: LS_SETTINGS_KEY,
      storage: createJSONStorage(() => localStorage),
      migrate: (persistedState, version) => {
        if (!persistedState || version < 3) {
          console.log("Resetting settings due to version upgrade or missing state");
          return defaultSettings;
        }
        const validation = AppSettingsSchema.safeParse(persistedState);
        if (validation.success) {
          return validation.data as AppSettingsState;
              }
        console.warn("Persisted settings validation failed, resetting to default.");
        return defaultSettings;
      },
      version: 3,
    }
  )
);

// === SELECTORS ===
export const selectIsDarkMode = (state: AppSettingsState) => state.isDarkMode;
export const selectIsFiltersShown = (state: AppSettingsState) => state.isFiltersShown;
export const selectIsGridShown = (state: AppSettingsState) => state.isGridShown;
export const selectIsDebugMode = (state: AppSettingsState) => state.isDebugMode;
export const selectChartType = (state: AppSettingsState) => state.chartType;
export const selectChartBackgroundColor = (state: AppSettingsState) => state.chartBackgroundColor;
export const selectChartLayoutLineColor = (state: AppSettingsState) => state.chartLayoutLineColor;
export const selectChartTextColor = (state: AppSettingsState) => state.chartTextColor;
export const selectChartFontFamily = (state: AppSettingsState) => state.chartFontFamily;
export const selectChartFontSize = (state: AppSettingsState) => state.chartFontSize;
export const selectChartBorderColor = (state: AppSettingsState) => state.chartBorderColor;
export const selectChartGridVertLinesVisible = (state: AppSettingsState) => state.chartGridVertLinesVisible;
export const selectChartGridHorzLinesVisible = (state: AppSettingsState) => state.chartGridHorzLinesVisible;
export const selectChartGridVertLinesColor = (state: AppSettingsState) => state.chartGridVertLinesColor;
export const selectChartGridHorzLinesColor = (state: AppSettingsState) => state.chartGridHorzLinesColor;
export const selectChartCandleBodyEnabled = (state: AppSettingsState) => state.chartCandleBodyEnabled;
export const selectChartCandleBorderEnabled = (state: AppSettingsState) => state.chartCandleBorderEnabled;
export const selectChartCandleWickEnabled = (state: AppSettingsState) => state.chartCandleWickEnabled;
export const selectChartCandleBodyUpColor = (state: AppSettingsState) => state.chartCandleBodyUpColor;
export const selectChartCandleBodyDownColor = (state: AppSettingsState) => state.chartCandleBodyDownColor;
export const selectChartCandleBorderUpColor = (state: AppSettingsState) => state.chartCandleBorderUpColor;
export const selectChartCandleBorderDownColor = (state: AppSettingsState) => state.chartCandleBorderDownColor;
export const selectChartCandleWickUpColor = (state: AppSettingsState) => state.chartCandleWickUpColor;
export const selectChartCandleWickDownColor = (state: AppSettingsState) => state.chartCandleWickDownColor;
export const selectChartLineColor = (state: AppSettingsState) => state.chartLineColor;
export const selectChartLineWidth = (state: AppSettingsState): LineWidth => state.chartLineWidth as LineWidth;
export const selectChartAreaLineColor = (state: AppSettingsState) => state.chartAreaLineColor;
export const selectChartAreaTopColor = (state: AppSettingsState) => state.chartAreaTopColor;
export const selectChartAreaBottomColor = (state: AppSettingsState) => state.chartAreaBottomColor;
export const selectChartAreaLineWidth = (state: AppSettingsState): LineWidth => state.chartAreaLineWidth as LineWidth;
export const selectChartCrosshairColor = (state: AppSettingsState) => state.chartCrosshairColor;
export const selectChartCrosshairLabelBackgroundColor = (state: AppSettingsState) => state.chartCrosshairLabelBackgroundColor;
export const selectChartCrosshairLabelTextColor = (state: AppSettingsState) => state.chartCrosshairLabelTextColor;
export const selectChartTimeScaleBorderColor = (state: AppSettingsState) => state.chartTimeScaleBorderColor;
export const selectChartTimeScaleTextColor = (state: AppSettingsState) => state.chartTimeScaleTextColor;
export const selectChartPriceScaleBorderColor = (state: AppSettingsState) => state.chartPriceScaleBorderColor;
export const selectChartPriceScaleTextColor = (state: AppSettingsState) => state.chartPriceScaleTextColor;
export const selectChartPriceLineVisible = (state: AppSettingsState) => state.chartPriceLineVisible;
export const selectChartLastValueVisible = (state: AppSettingsState) => state.chartLastValueVisible;
export const selectChartPriceLineWidth = (state: AppSettingsState) => state.chartPriceLineWidth;
export const selectChartPriceLineStyle = (state: AppSettingsState) => state.chartPriceLineStyle;
export const selectChartPriceLineColor = (state: AppSettingsState) => state.chartPriceLineColor;
export const selectVolumeEnabled = (state: AppSettingsState) => state.volumeEnabled;
export const selectChartVolumeHeightRatio = (state: AppSettingsState) => state.chartVolumeHeightRatio;
export const selectChartVolumeUpColor = (state: AppSettingsState) => state.chartVolumeUpColor;
export const selectChartVolumeDownColor = (state: AppSettingsState) => state.chartVolumeDownColor;
export const selectChartRightOffset = (state: AppSettingsState) => state.chartRightOffset;
export const selectChartBarSpacing = (state: AppSettingsState) => state.chartBarSpacing;
export const selectSelectedPairs = (state: AppSettingsState) => state.selectedPairs;
export const selectMarketTypes = (state: AppSettingsState) => state.marketTypes;
export const selectMinVolume = (state: AppSettingsState) => state.minVolume;
export const selectMinTrades = (state: AppSettingsState) => state.minTrades;
export const selectShowVolumeInUSD = (state: AppSettingsState) => state.showVolumeInUSD;
export const selectAggregateVolumeAndTrades = (state: AppSettingsState) => state.aggregateVolumeAndTrades;
export const selectTableCompactness = (state: AppSettingsState) => state.tableCompactness;
export const selectIsSyncEnabled = (state: AppSettingsState) => state.isSyncEnabled;
export const selectUiFontFamily = (state: AppSettingsState) => state.uiFontFamily;
export const selectUiFontSize = (state: AppSettingsState) => state.uiFontSize;
export const selectSortConfigs = (state: AppSettingsState) => state.sortConfigs;
export const selectVisibleColumns = (state: AppSettingsState) => state.visibleColumns;
export const selectColumnWidths = (state: AppSettingsState) => state.columnWidths;
export const selectColumnOrder = (state: AppSettingsState) => state.columnOrder;
export const selectColumnFilters = (state: AppSettingsState) => state.columnFilters;
export const selectIsTableCollapsed = (state: AppSettingsState) => state.isTableCollapsed;
export const selectSelectedTickerSymbol = (state: AppSettingsState) => state.selectedTickerSymbol;
export const selectGlobalSearchTerm = (state: AppSettingsState) => state.globalSearchTerm;
export const selectChartAppearance = (state: AppSettingsState): Partial<AppSettings> => ({
  chartType: state.chartType,
  chartBackgroundColor: state.chartBackgroundColor,
  chartLayoutLineColor: state.chartLayoutLineColor,
  chartTextColor: state.chartTextColor,
  chartFontFamily: state.chartFontFamily,
  chartFontSize: state.chartFontSize,
  chartBorderColor: state.chartBorderColor,
  chartGridVertLinesVisible: state.chartGridVertLinesVisible,
  chartGridHorzLinesVisible: state.chartGridHorzLinesVisible,
  chartGridVertLinesColor: state.chartGridVertLinesColor,
  chartGridHorzLinesColor: state.chartGridHorzLinesColor,
  chartCandleBodyEnabled: state.chartCandleBodyEnabled,
  chartCandleBorderEnabled: state.chartCandleBorderEnabled,
  chartCandleWickEnabled: state.chartCandleWickEnabled,
  chartCandleBodyUpColor: state.chartCandleBodyUpColor,
  chartCandleBodyDownColor: state.chartCandleBodyDownColor,
  chartCandleBorderUpColor: state.chartCandleBorderUpColor,
  chartCandleBorderDownColor: state.chartCandleBorderDownColor,
  chartCandleWickUpColor: state.chartCandleWickUpColor,
  chartCandleWickDownColor: state.chartCandleWickDownColor,
  chartLineColor: state.chartLineColor,
  chartLineWidth: state.chartLineWidth as LineWidth,
  chartAreaLineColor: state.chartAreaLineColor,
  chartAreaTopColor: state.chartAreaTopColor,
  chartAreaBottomColor: state.chartAreaBottomColor,
  chartAreaLineWidth: state.chartAreaLineWidth as LineWidth,
  chartCrosshairColor: state.chartCrosshairColor,
  chartCrosshairLabelBackgroundColor: state.chartCrosshairLabelBackgroundColor,
  chartCrosshairLabelTextColor: state.chartCrosshairLabelTextColor,
  chartTimeScaleBorderColor: state.chartTimeScaleBorderColor,
  chartTimeScaleTextColor: state.chartTimeScaleTextColor,
  chartPriceScaleBorderColor: state.chartPriceScaleBorderColor,
  chartPriceScaleTextColor: state.chartPriceScaleTextColor,
  chartPriceLineVisible: state.chartPriceLineVisible,
  chartLastValueVisible: state.chartLastValueVisible,
  chartPriceLineWidth: state.chartPriceLineWidth,
  chartPriceLineStyle: state.chartPriceLineStyle,
  chartPriceLineColor: state.chartPriceLineColor,
  volumeEnabled: state.volumeEnabled,
  chartVolumeHeightRatio: state.chartVolumeHeightRatio,
  chartVolumeUpColor: state.chartVolumeUpColor,
  chartVolumeDownColor: state.chartVolumeDownColor,
  chartRightOffset: state.chartRightOffset,
  chartBarSpacing: state.chartBarSpacing,
  autoHideScalesEnabled: state.autoHideScalesEnabled,
});
export const selectSetSetting = (state: AppSettingsState) => state.setSetting;
export const selectToggleColumnVisibility = (state: AppSettingsState) => state.toggleColumnVisibility;
export const selectUpdateSortConfigs = (state: AppSettingsState) => state.updateSortConfigs;
export const selectSetColumnOrder = (state: AppSettingsState) => state.setColumnOrder;
export const selectSetColumnWidth = (state: AppSettingsState) => state.setColumnWidth;
export const selectSetColumnFilter = (state: AppSettingsState) => state.setColumnFilter;
export const selectSetTableCollapsed = (state: AppSettingsState) => state.setTableCollapsed;
export const selectResetSettings = (state: AppSettingsState) => state.resetSettings;
export const selectSetGlobalSearchTerm = (state: AppSettingsState) => state.setGlobalSearchTerm;
export const selectAutoHideScalesEnabled = (state: AppSettingsState) => state.autoHideScalesEnabled;
export const selectToggleAutoHideScales = (state: AppSettingsState) => state.toggleAutoHideScales; 
export const selectSetColumnWidths = (state: AppSettingsState) => state.setColumnWidths; 
export const selectSelectedInterval = (state: AppSettingsState) => state.selectedInterval;
export const selectAvailablePairs = (state: AppSettingsState) => state.availablePairs;
export const selectIndicatorColumns = (state: AppSettingsState) => state.indicatorColumns;
export const selectAddIndicatorColumn = (state: AppSettingsState) => state.addIndicatorColumn;
export const selectRemoveIndicatorColumn = (state: AppSettingsState) => state.removeIndicatorColumn;
export const selectUpdateIndicatorColumn = (state: AppSettingsState) => state.updateIndicatorColumn;
export const selectModeControl = (state: AppSettingsState) => state.modeControl;
export const selectViewMode = (state: AppSettingsState) => state.modeControl.currentMode;
export const selectScreenerSettings = (state: AppSettingsState) => state.modeControl.screenerSettings;
export const selectSetViewMode = (state: AppSettingsState) => state.setViewMode;
export const selectUpdateScreenerSettings = (state: AppSettingsState) => state.updateScreenerSettings;
export const selectLayoutTypesForModes = (state: AppSettingsState) => state.modeControl.layoutTypes;
export const selectLayoutTypeForCurrentMode = (state: AppSettingsState) => {
  const mode = state.modeControl.currentMode;
    return state.modeControl.layoutTypes[mode];
};
export const selectSetLayoutTypeForMode = (state: AppSettingsState) => state.setLayoutTypeForMode;