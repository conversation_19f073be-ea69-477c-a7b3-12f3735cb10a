import React, { useEffect, useState, useRef, useMemo, memo, useCallback } from 'react';
import { WSConnectionStatus as ConnectionStatus } from '@/shared/index'; // NEW: Explicitly import and alias
import { motion, AnimatePresence } from 'framer-motion'; // Оставляем для анимации индикатора
import { Icon } from '@/shared/ui/icons/all_Icon'; // NEW IMPORT
import { Button } from '../../shared/ui/button'; // Компонент кнопки shadcn
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../shared/ui/tooltip'; // Компоненты Tooltip shadcn
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../shared/ui/popover';
import { cn } from '@/shared/lib/utils';

export interface StatusBarProps {
  loading?: boolean;
  error?: any;
  lastUpdate?: number | null;
  onRefresh?: () => void;
  connectionDetails?: {
    name: string;
    status: ConnectionStatus; // This uses the type alias
    details?: string;
  }[];
  totalTickers?: number;
  filteredTickers?: number;
}

// Map connection status to Tailwind background and shadow colors
const statusStyles: Record<ConnectionStatus | 'default', { bg: string; shadow: string; text: string }> = {
  [ConnectionStatus.OPEN]: { bg: 'bg-green-500', shadow: 'shadow-[0_0_4px_1px_rgba(34,197,94,0.5)]', text: 'text-green-500' },
  [ConnectionStatus.CLOSED]: { bg: 'bg-red-500', shadow: 'shadow-[0_0_4px_1px_rgba(239,68,68,0.5)]', text: 'text-red-500' },
  [ConnectionStatus.CONNECTING]: { bg: 'bg-yellow-500', shadow: 'shadow-[0_0_4px_1px_rgba(234,179,8,0.5)]', text: 'text-yellow-500' },
  [ConnectionStatus.CLOSING]: { bg: 'bg-orange-500', shadow: 'shadow-[0_0_4px_1px_rgba(249,115,22,0.5)]', text: 'text-orange-500' },
  [ConnectionStatus.ERROR]: { bg: 'bg-red-600', shadow: 'shadow-[0_0_4px_1px_rgba(220,38,38,0.5)]', text: 'text-red-600' },
  default: { bg: 'bg-gray-400', shadow: 'shadow-[0_0_4px_1px_rgba(156,163,175,0.5)]', text: 'text-gray-400' },
};

// Map connection status to human-readable text
const statusText: Record<ConnectionStatus, string> = {
  [ConnectionStatus.OPEN]: 'Connected',
  [ConnectionStatus.CONNECTING]: 'Connecting...',
  [ConnectionStatus.CLOSING]: 'Closing...',
  [ConnectionStatus.CLOSED]: 'Disconnected',
  [ConnectionStatus.ERROR]: 'Error',
};

const StatusBarComponent: React.FC<StatusBarProps> = ({
  loading,
  error,
  lastUpdate,
  onRefresh,
  connectionDetails = [],
  totalTickers = 0,
  filteredTickers = 0,
}) => {
  const prevLastUpdateRef = useRef<number | null>(null);
  const [shouldAnimate, setShouldAnimate] = useState<boolean>(false);

  // Determine the primary status for the indicator
  const primaryStatus = useMemo((): ConnectionStatus => {
      if (error) return ConnectionStatus.ERROR;
      if (connectionDetails.length > 0) {
          // Prioritize based on severity
          if (connectionDetails.some(d => d.status === ConnectionStatus.ERROR)) return ConnectionStatus.ERROR;
          if (connectionDetails.some(d => d.status === ConnectionStatus.CLOSED)) return ConnectionStatus.CLOSED;
          if (connectionDetails.some(d => d.status === ConnectionStatus.CLOSING)) return ConnectionStatus.CLOSING;
          if (connectionDetails.some(d => d.status === ConnectionStatus.CONNECTING)) return ConnectionStatus.CONNECTING;
          return ConnectionStatus.OPEN;
      }
      return ConnectionStatus.CLOSED;
  }, [connectionDetails, error]);

  const { bg: indicatorBg, shadow: indicatorShadow } = statusStyles[primaryStatus] ?? statusStyles.default;

  const indicatorTitle = useMemo(() => {
    if (primaryStatus === ConnectionStatus.ERROR) return `Error: ${error?.message || (typeof error === 'string' ? error : 'Unknown')}`;
    if (primaryStatus === ConnectionStatus.OPEN) return 'All connections established';
    if (primaryStatus === ConnectionStatus.CONNECTING) return 'Attempting to connect...';
    if (primaryStatus === ConnectionStatus.CLOSED) return 'Connections lost';
    if (primaryStatus === ConnectionStatus.CLOSING) return 'Closing connections...';
    return 'Status unavailable';
  }, [primaryStatus, error]);

  // Animation variants for the indicator pulse
  const indicatorAnimation = {
    initial: { scale: 1, opacity: 0.8 },
    animate: {
      scale: [1, 1.3, 1],
      opacity: [0.8, 1, 0.8],
      transition: {
        duration: 1,
        ease: "easeInOut",
        repeat: 1, // Pulse once
      },
    },
  };

  // Trigger animation on lastUpdate change
  useEffect(() => {
    if (lastUpdate && lastUpdate !== prevLastUpdateRef.current) {
      prevLastUpdateRef.current = lastUpdate;
      setShouldAnimate(true);
      // Reset animation state after it completes (duration * repeat count)
      const timerId = setTimeout(() => setShouldAnimate(false), indicatorAnimation.animate.transition.duration * 1000 * (indicatorAnimation.animate.transition.repeat || 0));
      return () => clearTimeout(timerId);
    }
  }, [lastUpdate]);

  // --- Memoize Tooltip Content ---
  const connectionDetailsContent = useMemo(() => {
    // Function defined inside useMemo to capture current props/state
    const renderDetails = () => (
        <div className="w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2">
           {/* Flex container for Heading and Refresh Button */}
           <div className="mb-2 flex items-center justify-between border-b pb-2">
             <h4 className="text-sm font-semibold text-foreground">
               Connection Status
             </h4>
             {/* Refresh Button - Moved here, removed inner Tooltip */}
             {onRefresh && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onRefresh}
                  disabled={loading}
                  className={cn(
                  )}
                  aria-label="Refresh Data"
                >
                  <Icon name="Refresh" className={cn(
                      "h-4 w-4",
                      loading ? 'animate-spin' : ''
                  )} />
                </Button>
             )}
           </div>
          <ul className="list-none p-0">
            {connectionDetails.length > 0 ? connectionDetails.map((conn, index) => {
              const { text: statusColor } = statusStyles[conn.status] ?? statusStyles.default;
              const text = statusText[conn.status] ?? 'Unknown';
              // Use a more stable key if possible, e.g., conn.name if unique
              return (
                <li key={conn.name || index} className={`flex flex-wrap items-center gap-x-2 py-1 text-xs ${statusColor}`}>
                  <span className="w-[90px] flex-shrink-0 font-medium text-foreground">{conn.name}:</span>
                  <span className="font-medium">{text}</span>
                  {conn.status === ConnectionStatus.ERROR && conn.details && (
                    <span className="mt-0.5 w-full pl-[98px] text-xs text-muted-foreground">
                      ({conn.details})
                    </span>
                  )}
                </li>
              );
            }) : (
              <li className={`flex flex-wrap items-center gap-x-2 py-1 text-xs ${statusStyles[primaryStatus]?.text ?? statusStyles.default.text}`}>
                  <span className="w-[90px] flex-shrink-0 font-medium text-foreground">Overall:</span>
                  <span className="font-medium">{statusText[primaryStatus as keyof typeof statusText] ?? 'Unknown'}</span>
                  {primaryStatus === ConnectionStatus.ERROR && error && (
                    <span className="mt-0.5 w-full pl-[98px] text-xs text-muted-foreground">
                      ({typeof error === 'string' ? error : error?.message || 'Unknown error'})
                    </span>
                  )}
                </li>
            )}
          </ul>

          {/* Ticker Information */}
          {(totalTickers > 0 || filteredTickers > 0) && (
              <div className="mt-3 border-t pt-2">
                <h4 className="mb-2 text-sm font-semibold text-foreground">Ticker Information</h4>
                <div className="flex flex-col gap-1">
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">Total fetched:</span>
                    <span className="font-mono font-medium text-foreground">{totalTickers}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">Displayed:</span>
                    <span className="font-mono font-medium text-foreground">{filteredTickers}</span>
                  </div>
                  {totalTickers > 0 && filteredTickers < totalTickers && totalTickers !== 0 && ( // Added check for totalTickers !== 0 to prevent NaN
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Filtered out:</span>
                      <span className="font-mono font-medium text-foreground">
                        {totalTickers - filteredTickers}
                        ({Math.round(((totalTickers - filteredTickers) / totalTickers) * 100)}%)
                      </span>
                    </div>
                  )}
                </div>
              </div>
          )}
        </div>
    );
    return renderDetails(); // Execute the function to get the JSX
  }, [connectionDetails, primaryStatus, error, totalTickers, filteredTickers, onRefresh, loading]); // Dependencies for useMemo

  return (
    <TooltipProvider delayDuration={300}> {/* Keep Provider for potential other tooltips */}
      <div className="ml-auto flex h-8 items-center gap-2 px-2 font-mono text-xs">
        {/* Popover wraps the indicator */}
        <Popover>
          <PopoverTrigger asChild>
            <motion.div
              key={shouldAnimate ? 'animate' : 'idle'} // Trigger re-render for animation restart
              className={`h-2 w-2 flex-shrink-0 cursor-pointer rounded-full ${indicatorBg} ${indicatorShadow} transition-all duration-300 ease-in-out`} // Added cursor-pointer
              title={indicatorTitle} // Basic HTML title for accessibility
              variants={indicatorAnimation}
              initial="initial"
              animate={shouldAnimate ? 'animate' : 'initial'}
            />
          </PopoverTrigger>
          <PopoverContent side="bottom" align="end" className="p-0 border-none bg-transparent shadow-none">
            {/* Use the memoized content */}
            {connectionDetailsContent}
          </PopoverContent>
        </Popover>
      </div>
    </TooltipProvider>
  );
};

const StatusBar = memo(StatusBarComponent);

export default StatusBar; 