import { logger } from '../lib/logger';
import { binanceService } from '../exchange/binance.service'; // Actual Binance service
import { MarketType } from '../../shared/types';

const log = logger.child({ service: 'ExchangeStreamManager' });

interface ActiveStreamInfo {
  clientCount: number;
  // We can store the unsubscribe function returned by binanceService if needed for more direct control,
  // but for now, we'll rely on calling subscribe/unsubscribe on binanceService directly.
  // unsubscribeFromExchange?: () => void; 
  unsubscribeFn?: () => void; // Added: To store the unsubscribe function from BinanceService
}

// Map to store active kline stream subscriptions and their client counts
// Key format: `${marketType}_${symbol.toUpperCase()}_${interval}`
const activeKlineStreams = new Map<string, ActiveStreamInfo>();

function getStreamKey(marketType: MarketType, symbol: string, interval: string): string {
  return `${marketType}_${symbol.toUpperCase()}_${interval}`;
}

export const exchangeStreamManager = {
  /**
   * Called when a WebSocket client subscribes to a kline topic.
   * Manages the subscription to the actual exchange stream via BinanceService.
   * @param marketType - e.g., 'spot' or 'futures'
   * @param symbol - e.g., 'BTCUSDT'
   * @param interval - e.g., '1h'
   */
  async subscribeToExchangeKlineStream(marketType: MarketType, symbol: string, interval: string): Promise<void> {
    const streamKey = getStreamKey(marketType, symbol, interval);
    let streamInfo = activeKlineStreams.get(streamKey);

    if (!streamInfo) {
      log.info(`[Subscribe] First client for stream ${streamKey}. Subscribing via BinanceService.`);
      try {
        // BinanceService.subscribeKline should handle the actual WebSocket subscription to Binance
        // It is assumed to be idempotent or manage its own state of actual exchange subscriptions.
        // Corrected: Store the returned unsubscribe function
        const unsubscribeFromBinance = binanceService.subscribeKline(marketType, symbol, interval);
        
        streamInfo = { clientCount: 1, unsubscribeFn: unsubscribeFromBinance }; // Modified: Store unsubscribeFn
        activeKlineStreams.set(streamKey, streamInfo);
        log.info(`[Subscribe] BinanceService subscription initiated for ${streamKey}. Client count: 1.`);
      } catch (error) {
        log.error(`[Subscribe] Error subscribing to ${streamKey} via BinanceService:`, error);
        // Optionally re-throw or handle, so ws.handler.ts can notify the client
        throw error; 
      }
    } else {
      streamInfo.clientCount++;
      log.info(`[Subscribe] Additional client for stream ${streamKey}. Client count: ${streamInfo.clientCount}.`);
    }
  },

  /**
   * Called when a WebSocket client unsubscribes from a kline topic.
   * Manages unsubscription from the actual exchange stream if no clients are left.
   * @param marketType - e.g., 'spot' or 'futures'
   * @param symbol - e.g., 'BTCUSDT'
   * @param interval - e.g., '1h'
   */
  async unsubscribeFromExchangeKlineStream(marketType: MarketType, symbol: string, interval: string): Promise<void> {
    const streamKey = getStreamKey(marketType, symbol, interval);
    const streamInfo = activeKlineStreams.get(streamKey);

    if (!streamInfo) {
      log.warn(`[Unsubscribe] Attempted to unsubscribe from stream ${streamKey} with no active clients or existing subscription info.`);
      return;
    }

    streamInfo.clientCount--;
    log.info(`[Unsubscribe] Client removed from stream ${streamKey}. Remaining clients: ${streamInfo.clientCount}.`);

    if (streamInfo.clientCount <= 0) {
      log.info(`[Unsubscribe] Last client for stream ${streamKey}. Unsubscribing via BinanceService.`);
      try {
        // BinanceService.unsubscribeKline should handle the actual WebSocket unsubscription from Binance
        // Corrected: Call the stored unsubscribe function
        if (streamInfo.unsubscribeFn) {
          streamInfo.unsubscribeFn();
        } else {
          log.warn(`[Unsubscribe] No unsubscribe function found for stream ${streamKey}. Manual intervention might be needed if BinanceService did not provide one.`);
          // Fallback or error? For now, we assume binanceService.subscribeKline always returns a function.
          // If it might not, we might need to call a generic unsubscribe on binanceService as a last resort,
          // but that would require binanceService.unsubscribe to be public or have a public wrapper.
        }
        
        activeKlineStreams.delete(streamKey);
        log.info(`[Unsubscribe] BinanceService unsubscription initiated for ${streamKey}.`);
      } catch (error) {
        log.error(`[Unsubscribe] Error unsubscribing from ${streamKey} via BinanceService:`, error);
        // If unsubscription fails, we might end up with a lingering exchange subscription.
        // For robustness, we'll remove it from our active list anyway.
        activeKlineStreams.delete(streamKey); 
        throw error;
      }
    }
  },

  /**
   * Retrieves the number of active WebSocket clients for a given kline stream.
   * Useful for diagnostics or potentially for smarter unsubscription logic.
   * @param marketType
   * @param symbol
   * @param interval
   * @returns number of clients, or 0 if stream is not tracked.
   */
  getActiveClientCount(marketType: MarketType, symbol: string, interval: string): number {
    const streamKey = getStreamKey(marketType, symbol, interval);
    const streamInfo = activeKlineStreams.get(streamKey);
    return streamInfo ? streamInfo.clientCount : 0;
  },

  /**
   * Gets a list of all currently tracked active kline streams and their client counts.
   * Useful for debugging.
   * @returns Map<string, number>
   */
  getAllActiveStreamsClientCounts(): Map<string, number> {
    const counts = new Map<string, number>();
    activeKlineStreams.forEach((info, key) => {
      counts.set(key, info.clientCount);
    });
    return counts;
  }
};

// Optional: Graceful shutdown logic if needed, e.g., to log active streams on shutdown.
// process.on('SIGINT', () => {
//   log.info('[ExchangeStreamManager] Shutting down. Active kline streams being managed:');
//   activeKlineStreams.forEach((info, key) => {
//     log.info(`  - ${key}: ${info.clientCount} clients`);
//   });
//   // No actual unsubscriptions here as binanceService would be shutting down too.
// }); 