import { useEffect, useRef, useMemo } from 'react';
import {
	IChartApi,
	ISeriesApi,
	ISeriesPrimitive,
	SeriesAttachedParameter,
	Time,
	SeriesType,
	Coordinate,
    Logical,
    AutoscaleInfo,
    ISeriesPrimitiveAxisView,
} from 'lightweight-charts';

// --- Helper Functions ---

/** Calculates interval duration in seconds */
function getIntervalDurationSeconds(interval: string): number {
	const match = interval.match(/^(\d+)(\w)$/);
	if (!match) return 60; // Default to 1 minute if format is unexpected
	const value = parseInt(match[1], 10);
	const unit = match[2].toLowerCase();
	const unitMultipliers: Record<string, number> = { s: 1, m: 60, h: 3600, d: 86400, w: 604800 };
	return (unitMultipliers[unit] || 60) * value;
}

/** Determines price precision based on magnitude */
const getPrecision = (price: number): number => {
    if (!isFinite(price)) return 2;
    const absPrice = Math.abs(price);
    if (absPrice < 1e-7) return 2; // Display near-zero as 0.00
    const thresholds = [1e-6, 1e-4, 0.01, 1, 10, 100, 1000];
    const precisions = [8, 7, 6, 5, 4, 3, 2, 1];
    for (let i = 0; i < thresholds.length; i++) {
        if (absPrice < thresholds[i]) return precisions[i];
    }
    return precisions[precisions.length - 1];
};

/** Formats time remaining until the next bar closes */
function formatTimeToBarClose(ms: number, interval: string): string {
  if (!isFinite(ms) || ms < 0) {
    const unit = interval?.slice(-1)?.toLowerCase();
    const valStr = interval?.slice(0, -1);
    const val = valStr ? parseInt(valStr, 10) : NaN;

    if (!unit || isNaN(val)) return '00:00';

    if (unit === 'd' || unit === 'w') return '0d 0h';
    if (unit === 'h' || (unit === 'm' && val >= 60)) return '0h 0m';
    return '00:00';
  }

  const sec = Math.floor(ms / 1000);

  if (sec < 60) {
      return `00:${sec.toString().padStart(2, '0')}`;
  } else if (sec < 3600) {
    const m = Math.floor(sec / 60);
    const s = sec % 60;
    return `${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  } else if (sec < 86400) {
    const h = Math.floor(sec / 3600);
    const m = Math.floor((sec % 3600) / 60);
    return `${h.toString()}h ${m.toString().padStart(2, '0')}m`;
  } else {
    const d = Math.floor(sec / 86400);
    const h = Math.floor((sec % 86400) / 3600);
    return `${d}d ${h}h`;
  }
}

/** Formats price with dynamic precision */
function formatChartPrice(price: number): string {
  return isFinite(price) ? price.toFixed(getPrecision(price)) : '';
}

// --- Plugin Options ---

export interface PriceScaleCountdownLabelOptions {
	priceTextColor: string;
	countdownTextColor: string;
	backgroundColor: string; // Цвет фона для AxisView
    verticalSpacing: number; // Вертикальное смещение в пикселях для AxisView
}

const defaultOptions: PriceScaleCountdownLabelOptions = {
	priceTextColor: '#d1d5db',
	countdownTextColor: '#9ca3af',
	backgroundColor: '#1f2937cc',
	verticalSpacing: 12,
};

// --- Axis View (Internal implementation detail) ---
class PriceScaleCountdownLabelAxisView implements ISeriesPrimitiveAxisView {
    private _source: PriceScaleCountdownLabelPrimitive;
    private _yCoord: Coordinate | null = null;
    private _showCountdown: boolean = true;
    private _offsetY: number = 0;
    private _overrideText: string | null = null;
    private _textColorOverride: string | null = null;
    private _backColorOverride: string | null = null;
    private _useTransparentBackground: boolean = false;

    constructor(
        source: PriceScaleCountdownLabelPrimitive,
        showCountdown: boolean = true,
        offsetY: number = 0,
        textColorOverride: string | null = null,
        backColorOverride: string | null = null,
        useTransparentBackground: boolean = false
    ) {
        this._source = source;
        this._showCountdown = showCountdown;
        this._offsetY = offsetY;
        this._textColorOverride = textColorOverride;
        this._backColorOverride = backColorOverride;
        this._useTransparentBackground = useTransparentBackground;
    }

    public update(): void {
        const series = this._source.series;
        const data = this._source.getData();
        if (!series || data.price === null || !isFinite(data.price)) {
            this._yCoord = null;
            return;
        }
        const baseY = series.priceToCoordinate(data.price);
        if (baseY !== null) {
            this._yCoord = (baseY + this._offsetY) as Coordinate;
        } else {
            this._yCoord = null;
        }
    }

    public coordinate(): Coordinate {
        return this._yCoord !== null ? this._yCoord : 0 as Coordinate;
    }

    public text(): string {
        if (this._overrideText !== null) {
            return this._overrideText;
        }
        const data = this._source.getData();
        const price = data.price ?? 0;
        if (this._showCountdown) {
            return data.interval ? formatTimeToBarClose(data.timeToBarClose, data.interval) : '';
        } else {
            return formatChartPrice(price);
        }
    }

    public textColor(): string {
        if (this._textColorOverride) return this._textColorOverride;
        const options = this._source.getOptions();
        return this._showCountdown ? options.countdownTextColor : options.priceTextColor;
    }

    public backColor(): string {
        if (this._useTransparentBackground) return 'rgba(0,0,0,0)';
        if (this._backColorOverride) return this._backColorOverride;
        return this._source.getOptions().backgroundColor;
    }

    public setOverrideText(text: string | null): void {
        this._overrideText = text;
    }

    public visible(): boolean {
        return this._yCoord !== null;
    }

    public tickVisible?(): boolean {
        return false;
    }
}

// --- Primitive Data Structure ---
export interface PrimitiveData {
    price: number | null;
    timeToBarClose: number;
    interval: string;
}

// --- Primitive (Internal implementation detail) ---
class PriceScaleCountdownLabelPrimitive implements ISeriesPrimitive<Time> {
	public series: ISeriesApi<SeriesType> | null = null; // Make series public for detachment
	private _chart: IChartApi | null = null;
	private _requestUpdate: (() => void) | null = null;
	private _priceView!: PriceScaleCountdownLabelAxisView;
    private _countdownView!: PriceScaleCountdownLabelAxisView;
    private _ghostPriceView!: PriceScaleCountdownLabelAxisView;
    private _ghostCountdownView!: PriceScaleCountdownLabelAxisView;
	private _options: PriceScaleCountdownLabelOptions;
    private _data: PrimitiveData;

	constructor(options: Partial<PriceScaleCountdownLabelOptions>, initialData: PrimitiveData) {
		this._options = { ...defaultOptions, ...options };
        this._data = { ...initialData, interval: initialData.interval || '' };
        this._recreateAxisViews(); // Initialize views here
	}

	public attached(param: SeriesAttachedParameter): void {
		this._chart = param.chart;
		this.series = param.series as ISeriesApi<SeriesType>; // Store series reference
		this._requestUpdate = param.requestUpdate;
        this.updateOptions(this._options); // Apply initial options
	}

	public detached(): void {
		this._requestUpdate = null;
        this.series = null; // Clear series reference
        this._chart = null;
	}

    public updateOptions(newOptions: Partial<PriceScaleCountdownLabelOptions>): void {
        const oldOptions = this._options;
        this._options = { ...this._options, ...newOptions };
        const optionsChanged =
            oldOptions.verticalSpacing !== this._options.verticalSpacing ||
            oldOptions.backgroundColor !== this._options.backgroundColor ||
            oldOptions.priceTextColor !== this._options.priceTextColor ||
            oldOptions.countdownTextColor !== this._options.countdownTextColor;

        if (optionsChanged) {
            console.warn('[PriceScaleCountdownLabel] Visual options changed, recreating Axis Views.');
            this._recreateAxisViews();
        } else {
             this.updateAllViews();
        }
    }

    private _recreateAxisViews(): void {
        const countdownOffsetY = this._options.verticalSpacing;
        const transparentTextColor = 'rgba(0,0,0,0)';

        this._priceView = new PriceScaleCountdownLabelAxisView(this, false, 0, null, null, true);
        this._countdownView = new PriceScaleCountdownLabelAxisView(this, true, countdownOffsetY, null, null, true);
        this._ghostPriceView = new PriceScaleCountdownLabelAxisView(this, false, 0, transparentTextColor, null, false);
        this._ghostCountdownView = new PriceScaleCountdownLabelAxisView(this, true, countdownOffsetY, transparentTextColor, null, false);
        this.updateAllViews();
    }

	// Make getters internal if not needed externally
	// public get chart(): IChartApi | null { return this._chart; }
	// public get series(): ISeriesApi<SeriesType> | null { return this._series; }
	public getOptions(): PriceScaleCountdownLabelOptions { return this._options; }
    public getData(): PrimitiveData { return this._data; }

    public priceAxisViews?(): readonly ISeriesPrimitiveAxisView[] {
        return [this._ghostPriceView, this._ghostCountdownView, this._priceView, this._countdownView];
    }

	public updateAllViews() {
        if (!this._requestUpdate) return;
        const data = this.getData();
        const price = data.price ?? 0;
        const priceText = formatChartPrice(price);
        const countdownText = data.interval ? formatTimeToBarClose(data.timeToBarClose, data.interval) : '';
        const longestText = priceText.length >= countdownText.length ? priceText : countdownText;

        this._ghostPriceView.setOverrideText(longestText);
        this._ghostCountdownView.setOverrideText(longestText);
        this._priceView.setOverrideText(null);
        this._countdownView.setOverrideText(null);

		this._priceView.update();
        this._countdownView.update();
        this._ghostPriceView.update();
        this._ghostCountdownView.update();
		this._requestUpdate();
	}

    public updateData(newData: Partial<PrimitiveData>) {
        let changed = false;
        if (newData.price !== undefined && newData.price !== this._data.price) {
            this._data.price = newData.price;
            changed = true;
        }
        if (newData.timeToBarClose !== undefined && newData.timeToBarClose !== this._data.timeToBarClose) {
            this._data.timeToBarClose = newData.timeToBarClose;
            changed = true;
        }
         if (newData.interval !== undefined && newData.interval !== this._data.interval) {
            this._data.interval = newData.interval;
            changed = true;
        }
        if (changed) {
            this.updateAllViews();
        }
    }

    autoscaleInfo(startTime: Logical, endTime: Logical): AutoscaleInfo | null {
        if (this._data.price === null || !isFinite(this._data.price) || !this.series) {
            return null;
        }
        const verticalSpacing = this._options.verticalSpacing;
        const priceCoord = this.series.priceToCoordinate(this._data.price);
        if (priceCoord === null) {
            return null;
        }
        const countdownCoord = (priceCoord + verticalSpacing) as Coordinate;
        const priceValue = this.series.coordinateToPrice(priceCoord);
        const countdownEdgePrice = this.series.coordinateToPrice(countdownCoord);
        if (priceValue === null || countdownEdgePrice === null) return null;
        return {
            priceRange: {
                minValue: Math.min(priceValue, countdownEdgePrice),
                maxValue: Math.max(priceValue, countdownEdgePrice),
            },
        };
    }
}


// --- React Hook ---

export function usePriceScaleCountdownLabel(
	chart: IChartApi | null,
	series: ISeriesApi<SeriesType> | null,
	price: number | null,
	timeToBarClose: number,
	interval: string,
	options?: Partial<PriceScaleCountdownLabelOptions>
) {
	const primitiveRef = useRef<PriceScaleCountdownLabelPrimitive | null>(null);
	const optionsJson = useMemo(() => JSON.stringify(options ?? {}), [options]);

	useEffect(() => {
		if (!chart || !series || !interval) {
            // Ensure detachment happens correctly even if primitiveRef.current is already null
			const currentPrimitive = primitiveRef.current;
			if (currentPrimitive && currentPrimitive.series) {
				try {
					currentPrimitive.series.detachPrimitive(currentPrimitive);
                    primitiveRef.current = null; // Clear ref *after* successful detach
				} catch (e) {
                     console.error("Error detaching primitive:", e);
                     primitiveRef.current = null; // Clear ref even on error
                }
			} else if (currentPrimitive) {
                // Primitive exists but series doesn't (shouldn't happen often with current logic)
                primitiveRef.current = null;
            }
			return;
		}

		if (!primitiveRef.current) {
			const initialData: PrimitiveData = { price, timeToBarClose, interval };
			const parsedOptions = JSON.parse(optionsJson);
			const newPrimitive = new PriceScaleCountdownLabelPrimitive(parsedOptions, initialData);
			try {
				series.attachPrimitive(newPrimitive);
				primitiveRef.current = newPrimitive;
			} catch (e) {
				console.error("Failed to attach PriceScaleCountdownLabelPrimitive:", e);
			}
		}

		return () => {
			const currentPrimitive = primitiveRef.current;
            primitiveRef.current = null; // Clear ref *before* detaching
			if (currentPrimitive && currentPrimitive.series) {
				try {
                    // Use the series reference stored in the primitive for detachment
					currentPrimitive.series.detachPrimitive(currentPrimitive);
				} catch (e) {
                     console.error("Error detaching primitive on cleanup:", e);
                }
			}
		};
	}, [chart, series, interval]); // Only recreate/attach/detach when chart/series/interval changes

	// Update data effect
	useEffect(() => {
		if (primitiveRef.current && chart && series) {
            // Check interval consistency if necessary, or assume it matches the attached primitive
			primitiveRef.current.updateData({ price, timeToBarClose, interval });
		}
	}, [chart, series, price, timeToBarClose, interval]); // Add interval here if it can change data meaning

	// Update options effect
	useEffect(() => {
		if (primitiveRef.current && chart && series) {
			const parsedOptions = JSON.parse(optionsJson);
			primitiveRef.current.updateOptions(parsedOptions);
		}
	}, [chart, series, optionsJson]); // Update only when options JSON changes
}
