services:
  # High-performance, in-memory data store for caching and pub/sub
  redis:
    image: redis:7-alpine
    container_name: metacharts_redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - mct_network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
    environment:
      - REDIS_MAXMEMORY=1GB
      - REDIS_MAXMEMORY_POLICY=allkeys-lru

  # PostgreSQL for primary data
  postgres:
    image: postgres:16-alpine
    container_name: metacharts_postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: mct_db
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - mct_network

  # Monitoring stack
  prometheus:
    image: prom/prometheus:latest
    container_name: metacharts_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/:/etc/prometheus/
    command: --config.file=/etc/prometheus/prometheus.yml
    networks:
      - mct_network

  # Grafana for metric visualization
  grafana:
    image: grafana/grafana:latest
    container_name: metacharts_grafana
    ports:
      - "4000:3000"
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/provisioning/:/etc/grafana/provisioning/
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
    depends_on:
      - prometheus
    networks:
      - mct_network

  # k6 for comprehensive load testing
  k6:
    image: grafana/k6:0.52.0
    container_name: metacharts_k6
    restart: "no"
    volumes:
      - ./load-tests:/scripts
    networks:
      - mct_network
    environment:
      - K6_TARGET_HOST=host.docker.internal
      - K6_API_PORT=3005
      - K6_WS_PORT=3005
      - K6_VUS=20
      - K6_DURATION=120s
      - K6_RAMP_UP=30s
      - K6_RAMP_DOWN=30s
    profiles:
      - testing

networks:
  mct_network:
    driver: bridge

volumes:
  redis-data:
  postgres-data:
  grafana-data: 