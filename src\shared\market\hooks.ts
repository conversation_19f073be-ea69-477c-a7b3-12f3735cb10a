import { useInfiniteQuery } from '@tanstack/react-query';
import {
  Kline,
  Ticker,
  MarketType,
  WSConnectionStatus,
} from '@/shared/schemas/market.schema';
import { z } from 'zod';
import { useEffect, useState, useCallback } from 'react';
import { useMarketStore } from '../store/marketStore';

// Zod schema for kline query parameters, should match backend
const GetKlinesSchema = z.object({
  symbol: z.string(),
  interval: z.string(),
  marketType: z.enum(['spot', 'futures']),
  limit: z.number().int().positive().optional(),
  endTime: z.number().int().positive().optional(),
});

type GetKlinesQuery = z.infer<typeof GetKlinesSchema>;

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api/v1';
const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3000/ws/market';

// Kline type as it comes from the API (with string prices)
type SerializedKline = Omit<Kline, 'open' | 'high' | 'low' | 'close' | 'volume'> & {
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
};

/**
 * @description Parses a serialized kline from the backend into the application's Kline format.
 * @param {SerializedKline} serializedKline - The kline object from the API response.
 * @returns {Kline} The parsed kline object with correct types (numbers).
 */
const parseKline = (serializedKline: SerializedKline): Kline => ({
  ...serializedKline,
  openTime: new Date(serializedKline.openTime).getTime(),
  open: parseFloat(serializedKline.open),
  high: parseFloat(serializedKline.high),
  low: parseFloat(serializedKline.low),
  close: parseFloat(serializedKline.close),
  volume: parseFloat(serializedKline.volume),
});

/**
 * @description Fetches historical klines from the backend API.
 * @param {GetKlinesQuery} params - The query parameters.
 * @returns {Promise<Kline[]>} A promise that resolves to an array of parsed klines.
 */
const fetchHistoricalKlines = async ({ pageParam, ...params }: GetKlinesQuery & { pageParam?: number }): Promise<Kline[]> => {
  const urlParams = new URLSearchParams({
    symbol: params.symbol,
    interval: params.interval,
    marketType: params.marketType,
    limit: String(params.limit || 1000),
  });

  if (pageParam) {
    urlParams.append('endTime', String(pageParam));
  }

  const response = await fetch(`${API_BASE_URL}/klines?${urlParams.toString()}`);

  if (!response.ok) {
    const errorBody = await response.json();
    throw new Error(errorBody.message || 'Failed to fetch klines');
  }

  const data: SerializedKline[] = await response.json();
  return data.map(parseKline);
};

// Функция для загрузки тикеров с бэкенда
const fetchTickers = async (marketTypes?: MarketType[]): Promise<Ticker[]> => {
  const urlParams = new URLSearchParams();
  
  if (marketTypes && marketTypes.length > 0) {
    marketTypes.forEach(type => urlParams.append('marketTypes', type));
  }
  
  const response = await fetch(`${API_BASE_URL}/tickers?${urlParams.toString()}`);
  
  if (!response.ok) {
    const errorBody = await response.json();
    throw new Error(errorBody.message || 'Failed to fetch tickers');
  }
  
  return await response.json();
};

export const getKlinesQueryKey = (symbol: string, interval: string, marketType: MarketType) => ['klines', marketType, symbol, interval];

/**
 * @description A TanStack Query hook to fetch historical kline data with infinite scrolling.
 * @param {string} symbol - The trading symbol.
 * @param {string} interval - The kline interval.
 * @param {MarketType} marketType - The market type ('spot' or 'futures').
 * @returns The result of the useInfiniteQuery hook.
 */
export function useHistoricalKlinesQuery(symbol: string, interval: string, marketType: MarketType) {
  return useInfiniteQuery({
    queryKey: getKlinesQueryKey(symbol, interval, marketType),
    queryFn: ({ pageParam }) => fetchHistoricalKlines({ symbol, interval, marketType, pageParam: pageParam as number | undefined }),
    getNextPageParam: (lastPage) => {
      // If the last page is empty or has fewer than 10 items, we've likely reached the end.
      if (!lastPage || lastPage.length < 10) {
        return undefined;
      }
      // The next page starts from the openTime of the oldest kline in the current page.
      return lastPage[0].openTime;
    },
    initialPageParam: undefined as number | undefined,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    enabled: !!symbol && !!interval && !!marketType,
  });
}

// WebSocket client
export const wsClient = {
  socket: null as WebSocket | null,
  status: WSConnectionStatus.Disconnected,
  onMessage: null as ((data: any) => void) | null,
  onStatusChange: null as ((status: WSConnectionStatus) => void) | null,

  connect(url: string = WS_BASE_URL) {
    if (this.socket) {
      this.disconnect();
    }

    try {
      this.socket = new WebSocket(url);
      this.status = WSConnectionStatus.Connecting;
      
      if (this.onStatusChange) {
        this.onStatusChange(WSConnectionStatus.Connecting);
      }

      this.socket.onopen = () => {
        this.status = WSConnectionStatus.Connected;
        if (this.onStatusChange) {
          this.onStatusChange(WSConnectionStatus.Connected);
        }
      };

      this.socket.onclose = () => {
        this.status = WSConnectionStatus.Disconnected;
        if (this.onStatusChange) {
          this.onStatusChange(WSConnectionStatus.Disconnected);
        }
      };

      this.socket.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.status = WSConnectionStatus.Error;
        if (this.onStatusChange) {
          this.onStatusChange(WSConnectionStatus.Error);
        }
      };

      this.socket.onmessage = (event) => {
        if (this.onMessage) {
          try {
            const data = JSON.parse(event.data);
            this.onMessage(data);
          } catch (err) {
            console.error('Failed to parse WebSocket message:', err);
          }
        }
      };
    } catch (err) {
      console.error('Failed to connect WebSocket:', err);
      this.status = WSConnectionStatus.Error;
      if (this.onStatusChange) {
        this.onStatusChange(WSConnectionStatus.Error);
      }
    }
  },

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
      this.status = WSConnectionStatus.Disconnected;
      if (this.onStatusChange) {
        this.onStatusChange(WSConnectionStatus.Disconnected);
      }
    }
  },

  send(data: any) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
      return true;
    }
    return false;
  },

  subscribe(channel: string, params: any = {}) {
    return this.send({
      action: 'subscribe',
      channel,
      params
    });
  },

  unsubscribe(channel: string, params: any = {}) {
    return this.send({
      action: 'unsubscribe',
      channel,
      params
    });
  }
};

// Хук для работы с WebSocket соединением
export const useWebSocketConnection = () => {
  const [status, setStatus] = useState<WSConnectionStatus>(wsClient.status);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    wsClient.onStatusChange = setStatus;

    return () => {
      wsClient.onStatusChange = null;
    };
  }, []);

  const connect = useCallback((url: string = WS_BASE_URL) => {
    setError(null);
    try {
      wsClient.connect(url);
    } catch (err) {
      setError((err as Error).message);
    }
  }, []);

  const disconnect = useCallback(() => {
    wsClient.disconnect();
  }, []);

  return {
    status,
    error,
    connect,
    disconnect,
    send: wsClient.send.bind(wsClient),
    subscribe: wsClient.subscribe.bind(wsClient),
    unsubscribe: wsClient.unsubscribe.bind(wsClient)
  };
};

// Хук для автоматического подключения к WebSocket с URL
export const useWebSocketAutoConnectWithUrl = (url?: string) => {
  const { connect, disconnect, status } = useWebSocketConnection();

  useEffect(() => {
    if (url) {
      connect(url);
    } else {
      connect();
    }
    return () => disconnect();
  }, [url, connect, disconnect]);

  return status;
};

// Интерфейс для MarketDataManager
export interface MarketDataManager {
  wsStatus: WSConnectionStatus;
  isInitialLoading: boolean;
  isWsConnected: boolean;
  error: string | null;
  connectWebSocket: (url?: string) => void;
  loadInitialTickers: (marketTypes?: MarketType[]) => Promise<void>;
  subscribeToSymbol: (symbol: string, interval: string, marketType: MarketType) => () => void;
  subscribeToTicker: (symbol: string, marketType: MarketType) => boolean;
  unsubscribeFromTicker: (symbol: string, marketType: MarketType) => boolean;
  subscribeToKlines: (symbol: string, interval: string, marketType: MarketType) => boolean;
  unsubscribeFromKlines: (symbol: string, interval: string, marketType: MarketType) => boolean;
  setTickers: (tickers: Ticker[]) => void;
  setKlines: (symbol: string, interval: string, klines: Kline[]) => void;
  appendKlines: (symbol: string, interval: string, klines: Kline[]) => void;
  updateKline: (symbol: string, interval: string, kline: Kline) => void;
  clearData: () => void;
  setLoadingTickers: (isLoading: boolean) => void;
  setLoadingKlines: (symbol: string, interval: string, isLoading: boolean) => void;
  setTickersError: (error: string | null) => void;
  setKlinesError: (symbol: string, interval: string, error: string | null) => void;
}

// Хук для управления рыночными данными
export const useMarketDataManager = (): MarketDataManager => {
  const marketStore = useMarketStore();
  const [isInitialLoading, setIsInitialLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { status: wsStatus, connect, subscribe, unsubscribe } = useWebSocketConnection();
  
  const connectWebSocket = useCallback((url?: string) => {
    connect(url);
  }, [connect]);
  
  const loadInitialTickers = useCallback(async (marketTypes?: MarketType[]) => {
    try {
      setIsInitialLoading(true);
      setError(null);
      
      const tickers = await fetchTickers(marketTypes);
      marketStore.setTickers(tickers);
      
      setIsInitialLoading(false);
    } catch (err) {
      setError((err as Error).message);
      setIsInitialLoading(false);
    }
  }, [marketStore]);
  
  const subscribeToTicker = useCallback((symbol: string, marketType: MarketType) => {
    return subscribe('ticker', { symbol, marketType });
  }, [subscribe]);
  
  const unsubscribeFromTicker = useCallback((symbol: string, marketType: MarketType) => {
    return unsubscribe('ticker', { symbol, marketType });
  }, [unsubscribe]);
  
  const subscribeToKlines = useCallback((symbol: string, interval: string, marketType: MarketType) => {
    return subscribe('kline', { symbol, interval, marketType });
  }, [subscribe]);
  
  const unsubscribeFromKlines = useCallback((symbol: string, interval: string, marketType: MarketType) => {
    return unsubscribe('kline', { symbol, interval, marketType });
  }, [unsubscribe]);
  
  // Метод для подписки на данные символа
  const subscribeToSymbol = useCallback((symbol: string, interval: string, marketType: MarketType) => {
    // Подписываемся на обновления свечей
    subscribeToKlines(symbol, interval, marketType);
    
    // Возвращаем функцию отписки для использования в useEffect cleanup
    return () => {
      unsubscribeFromKlines(symbol, interval, marketType);
    };
  }, [subscribeToKlines, unsubscribeFromKlines]);
  
  return {
    ...marketStore,
    wsStatus,
    isInitialLoading,
    isWsConnected: wsStatus === WSConnectionStatus.Connected,
    error,
    connectWebSocket,
    loadInitialTickers,
    subscribeToSymbol,
    subscribeToTicker,
    unsubscribeFromTicker,
    subscribeToKlines,
    unsubscribeFromKlines
  };
}; 