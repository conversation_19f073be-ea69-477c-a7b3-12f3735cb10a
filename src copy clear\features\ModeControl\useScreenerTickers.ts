import { useMemo } from 'react';
import { Ticker, ProcessedTicker } from '@/shared/index'; // Changed back from direct import
// import type { Ticker } from '@/shared/types/market.types'; // Removed direct import
// import type { ProcessedTicker } from '@/shared/index'; 
import { type ScreenerSortByZod as ScreenerSortBy } from '@/shared/schemas';
import { 
  useAppSettingsStore,
  selectViewMode,
  selectScreenerSettings,
  selectSelectedPairs,
  selectMarketTypes
} from '@/shared/store/settingsStore';
import { 
  useMarketStore
} from '@/shared/store/marketStore';

/**
 * Хук для получения топ тикеров для скринера
 * Фильтрует и сортирует тикеры согласно настройкам скринера
 */
export const useScreenerTickers = () => {
  // Store access
  const currentMode = useAppSettingsStore(selectViewMode);
  const screenerSettings = useAppSettingsStore(selectScreenerSettings);
  const selectedPairs = useAppSettingsStore(selectSelectedPairs);
  const selectedMarketTypes = useAppSettingsStore(selectMarketTypes);
  
  // Market data
  const spotTickers = useMarketStore(state => state.spotTickers || []);
  const futuresTickers = useMarketStore(state => state.futuresTickers || []);

  // Объединяем все тикеры и фильтруем по selectedMarketTypes
  const allTickers = useMemo(() => {
    // Объединяем все тикеры
    const allTickersArray: Ticker[] = [...spotTickers, ...futuresTickers];
    
    if (!selectedMarketTypes || selectedMarketTypes.length === 0) {
      return allTickersArray; // If no market types selected, return all (should not happen based on UI)
    }

    return allTickersArray.filter(ticker => 
      selectedMarketTypes.includes(ticker.marketType)
    );
  }, [spotTickers, futuresTickers, selectedMarketTypes]);

  // Фильтрация и сортировка тикеров для скринера
  const screenerTickers = useMemo(() => {
    if (currentMode !== 'screener') {
      return []; // Не возвращаем тикеры если не в режиме скринера
    }

    // Фильтрация по торговым парам
    let filteredTickers = allTickers.filter(ticker => {
      // Если нет выбранных пар, показываем все
      if (!selectedPairs.length) return true;
      
      // Проверяем есть ли символ с выбранными парами
      return selectedPairs.some(pair => ticker.symbol.endsWith(pair));
    });

    // Фильтрация по минимальным значениям из настроек скринера
    filteredTickers = filteredTickers.filter(ticker => {
      if (screenerSettings.minVolume && ticker.volume && ticker.volume < screenerSettings.minVolume) {
        return false;
      }
      if (screenerSettings.minTrades && ticker.count && ticker.count < screenerSettings.minTrades) {
        return false;
      }
      return true;
    });

    // Сортировка по выбранной метрике
    const sortedTickers = [...filteredTickers].sort((a, b) => {
      let aValue: number;
      let bValue: number;

      switch (screenerSettings.sortBy) {
        case 'volume':
          aValue = a.quoteVolume || 0; // Используем quoteVolume (объем в USDT)
          bValue = b.quoteVolume || 0;
          break;
        case 'trades':
          aValue = a.count || 0;
          bValue = b.count || 0;
          break;
        case 'price_change':
          aValue = a.priceChangePercent || 0;
          bValue = b.priceChangePercent || 0;
          break;
        case 'volume_change':
          // Для изменения объема пока используем просто объем
          // В будущем можно добавить отдельное поле для изменения объема
          aValue = a.volume || 0;
          bValue = b.volume || 0;
          break;
        default:
          aValue = a.quoteVolume || 0;
          bValue = b.quoteVolume || 0;
      }

      // Применяем направление сортировки
      if (screenerSettings.sortOrder === 'desc') {
        return bValue - aValue; // От большего к меньшему
      } else {
        return aValue - bValue; // От меньшего к большему
      }
    });

    // Возвращаем все отсортированные тикеры - количество ограничивается сеткой в main page
    return sortedTickers;
  }, [
    currentMode,
    allTickers,
    selectedPairs,
    screenerSettings
  ]);

  // Дополнительная информация для отладки
  const screenerInfo = useMemo(() => {
    if (currentMode !== 'screener') {
      return null;
    }

    return {
      totalTickers: allTickers.length,
      filteredCount: screenerTickers.length,
      sortBy: screenerSettings.sortBy,
      sortOrder: screenerSettings.sortOrder,
      timeframe: screenerSettings.timeframe,
    };
  }, [
    currentMode,
    allTickers.length,
    screenerTickers.length,
    screenerSettings.sortBy,
    screenerSettings.sortOrder,
    screenerSettings.timeframe
  ]);

  return {
    /** Топ тикеры для отображения в скринере */
    screenerTickers,
    /** Информация о скринере для отладки */
    screenerInfo,
    /** Находимся ли в режиме скринера */
    isScreenerMode: currentMode === 'screener',
    /** Настройки скринера */
    screenerSettings
  };
}; 